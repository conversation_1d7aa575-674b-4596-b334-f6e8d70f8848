<?php

namespace $NAMESPACE$;

use Webkul\Shipping\Carriers\AbstractShipping;
use Webkul\Checkout\Models\CartShippingRate;

class $CLASS$ extends AbstractShipping
{
    /**
     * Shipping method code
     *
     * @var string
     */
    protected $code  = '$LOWER_NAME$';

    /**
     * Returns rate for shipping method
     *
     * @return CartShippingRate|false
     */
    public function calculate()
    {
        if (! $this->isAvailable()) {
            return false;
        }

        $object = new CartShippingRate;

        $object->carrier = '$LOWER_NAME$';
        $object->carrier_title = $this->getConfigData('title');
        $object->method = '$LOWER_NAME$_$LOWER_NAME$';
        $object->method_title = $this->getConfigData('title');
        $object->method_description = $this->getConfigData('description');
        $object->price = 0;
        $object->base_price = 0;

        return $object;
    }
}