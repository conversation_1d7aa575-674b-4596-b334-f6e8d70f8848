@extends( 'Tests::test_01.master' )

@if(View::exists('Tests::test_01.left-side-menu'))
    @include( 'Tests::test_01.left-side-menu' )
@endif
@if(View::exists('Tests::test_01.scripts'))
    @include( 'Tests::test_01.scripts' )
@endif
@if(View::exists('Tests::test_01.components'))
    @include( 'Tests::test_01.components' )
@endif
@if(View::exists('Tests::test_01.components.main-component'))
    @include( 'Tests::test_01.components.main-component' )
@endif
@if(View::exists('Tests::test_01.components.modal-component'))
    @include( 'Tests::test_01.components.modal-component' )
@endif
@if(View::exists('Tests::test_01.components.toast-component'))
    @include( 'Tests::test_01.components.toast-component' )
@endif
@if(View::exists('Tests::test_01.components.leftside-component'))
    @include( 'Tests::test_01.components.leftside-component' )
@endif
@if(View::exists('Tests::test_01.components.requestadd-component'))
    @include( 'Tests::test_01.components.requestadd-component' )
@endif