@section( 'requestadd-component' )
    <script type="text/x-template" id="requestadd-template">
        <transition name="requestadd-component">
            <div id="newEndpoint">
                <div class="mb-3">
                    <label for="exampleFormControlInput1" class="form-label">Grup Adı</label>
                    <input
                        type="text"
                        name="group_name"
                        v-model="group_name"
                        class="form-control"
                        id="exampleFormControlInput1"
                        placeholder="Grup Adı"
                    />
                </div>
                <div class="mb-3">
                    <label for="exampleFormControlInput1" class="form-label">Request Adı</label>
                    <input
                        type="text"
                        name="request_title"
                        v-model="request_title"
                        class="form-control"
                        id="exampleFormControlInput1"
                        placeholder="Request Adı"
                    />
                </div>
                <div class="mb-3">
                    <label for="basic-url" class="form-label">Kısa EndPoint Yolu</label>
                    <div class="input-group">
                        <select
                            name="request_method"
                            v-model="request_method"
                            class="border-danger border-[#dee2e6]"
                        >
                            <option value="get">GET</option>
                            <option value="post">POST</option>
                            <option value="put">PUT</option>
                            <option value="patch">PATCH</option>
                            <option value="delete">DELETE</option>
                            <option value="head">HEAD</option>
                            <option value="options">OPTIONS</option>
                        </select>
                        <span class="input-group-text bg-white border-success border-[#198754]" id="basic-addon3">{!! config('app.web3_ep') !!}/</span>
                        <input
                            type="text"
                            name="request_pathname"
                            v-model="request_pathname"
                            id="basic-url"
                            class="form-control border-l-0"
                            aria-describedby="basic-addon3 basic-addon4"
                        />
                    </div>
                    <div class="form-text" id="basic-addon4"></div>
                </div>
                <div class="mb-3">
                    <label for="exampleFormControlTextarea1" class="form-label">BodyData (json Format)</label>
                    <textarea
                        name="request_bodydata"
                        v-model="request_bodydata"
                        class="form-control"
                        id="exampleFormControlTextarea1"
                        rows="9"
                        placeholder='{
    "userId": 250,
    "tenantId": 100,
    "chainId": "sepolia"
}'
                    ></textarea>
                </div>
            </div>
        </transition>
    </script>
@endsection