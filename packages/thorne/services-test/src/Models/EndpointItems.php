<?php

namespace Thorne\ServicesTest\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class EndpointItems extends Model
{
    use HasFactory, Notifiable;

    protected $connection = 'mysql';

    protected $primaryKey = 'id';

    protected $table      = 'endpoint_items';

    protected $guarded    = [];

    protected $appends    = [];

    protected $casts      = [
        'request_bodydata'  => 'array',
        'created_at'        => 'datetime:d-m-Y H:i:s',
        'updated_at'        => 'datetime:d-m-Y H:i:s',
    ];

    /** ======================================================================== **/
    /** ------------------------------------------------------------------------ **/
    /** ======================================================================== **/
}   /** end class EndpointItems extends Model **/
