<?php

namespace Thorne\ServicesTest;

use Illuminate\Support\ServiceProvider;

class ServicesTestServiceProvider extends ServiceProvider
{
    public function register(): void {}

    /** end register(): void **/
    public function boot(): void
    {

        $path = realpath(__DIR__.'/../');
        $this->loadRoutesFrom($path.'/routers/web.php');
        $this->loadRoutesFrom($path.'/routers/api.php');
        $this->loadMigrationsFrom($path.'/database/migrations');
        $this->loadViewsFrom($path.'/resources/views', 'Tests');
        $this->loadTranslationsFrom($path.'/language', 'TestLng');

        if ($this->app->runningInConsole()) {
            $this->commands([]);
        }

    }

    /** end boot(): void **/
    public function registerMigrations(): void {} /** end registerMigrations(): void **/
}   /** end class ServicesTestServiceProvider extends ServiceProvider **/
