<?php

namespace Thorne\ServicesTest\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected $pageItems;

    public function __construct()
    {

        Carbon::setLocale(config('app.locale'));
        $this->pageItems             = collect([]);
        $this->pageItems->domain     = request()->getSchemeAndHttpHost();
        $this->pageItems->homethemes = 'test_01';

    } /** end __construct()  **/
}   /** end class Controller extends BaseController **/
