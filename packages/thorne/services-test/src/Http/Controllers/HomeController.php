<?php

namespace Thorne\ServicesTest\Http\Controllers;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Http\Request;
use Thorne\ServicesTest\Models\EndpointGroups;
use <PERSON>\ServicesTest\Models\EndpointItems;

class HomeController extends ApiController
{
    public function __construct(
        protected ApiClientAuth10Service $apiServices
    ) {

        parent::__construct();

    }

    /** end __construct() **/
    public function requestLeftMenu(Request $request): mixed
    {

        $leftMenu = EndpointGroups::with('leftSideMenus')->get();

        return $this->apiSuccess($leftMenu, 'Left Side Menu');

    }

    /** end requestLeftMenu( Request $request ) **/
    public function requestSendData(Request $request): mixed
    {

        $data = $this->apiServices->baseUrl('/'.$request->request_pathname)
            ->baseMethod($request->request_method)
            ->baseClient(json_decode($request->request_bodydata));

        if ($data->successful()) {
            return $this->apiSuccess($data->json(), 'deneme');
        } else {
            throw new Exception('Error RequestSendData');
        }

    }

    /** end requestSendData( Request $request ) **/
    public function requestUpdateOrCreate(Request $request): mixed
    {

        if ($request->has(['group_name', 'request_title', 'request_method', 'request_pathname', 'request_bodydata'])) {
            $groupTitle = EndpointGroups::updateOrCreate([
                'group_name' => $request->group_name,
            ], [
                'group_name' => $request->group_name,
            ]);
            if ($groupTitle) {
                $itemsData = EndpointItems::updateOrCreate([
                    'id' => $request->request_id,
                ], [
                    'parent_id'        => $groupTitle->id,
                    'request_title'    => $request->request_title,
                    'request_method'   => $request->request_method,
                    'request_pathname' => $request->request_pathname,
                    'request_bodydata' => $request->request_bodydata,
                ]);
                if ($itemsData) {
                    return $this->apiSuccess([
                        'status' => true,
                    ], 'Request Kayıt Başarılı');
                } else {
                    return $this->apiError([
                        'status' => false,
                    ], 'Request Kayıt Yapılamadı.');
                }
            } else {
                return $this->apiError([
                    'status' => false,
                ], 'Grup Adı Kayıt Yapılamadı.');
            }
        }

        return $this->apiWaring([
            'status' => false,
        ], 'İşlem Yetkiniz Yok');

    } /** end requestUpdateOrCreate( Request $request ) **/
}   /** end class HomeController extends ApiController **/
