<?php

namespace Thorne\MobileApi\Libraries;

use Illuminate\Support\Facades\Http;

class ApiClientAuth10Service
{
    /**
     * @var string
     */
    private $baseURL = '';

    /**
     * @var string
     */
    private $method  = '';

    /**
     * @var null
     */
    private $timeout = null;

    /**
     * @return $this
     *               //config('app.web3_ep')
     */
    public function baseUrl($baseURL = null): object
    {

        $this->baseURL = 'http://134.209.247.1:16060'.$baseURL;

        return $this;

    } /** end baseUrl( $baseURL = null ) **/

    /**
     * @return $this
     */
    public function baseUrl2($baseURL = null): object
    {

        $this->baseURL = config('app.web3_ep-2').$baseURL;

        return $this;

    } /** end baseUrl2( $baseURL = null ) **/

    /**
     * @return $this
     */
    public function baseMethod($method = null): object
    {

        $this->method = $method;

        return $this;

    } /** end baseMethod( $method = null ) **/

    /**
     * @return $this
     */
    public function baseTimeout($timeout = null): object
    {

        $this->timeout = $timeout;

        return $this;

    }

    /** end baseTimeout( $timeout = null ) **/
    public function baseClient($bodyData = null): mixed
    {

        if ($this->method == 'post') {
            if ($this->timeout == null) {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->authorizationSignature($bodyData),
                ])
                    ->{$this->method}($this->baseURL, $bodyData);
            } else {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->authorizationSignature($bodyData),
                ])
                    ->timeout((int) $this->timeout)
                    ->{$this->method}($this->baseURL, $bodyData);
            }
        } elseif ($this->method == 'get') {
            if ($this->timeout == null) {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->authorizationSignature($bodyData),
                ])
                    ->{$this->method}($this->baseURL);
            } else {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->authorizationSignature($bodyData),
                ])
                    ->timeout((int) $this->timeout)
                    ->{$this->method}($this->baseURL);
            }
        }

        return $data;

    }

    /** end baseClient( $bodyData = null ) **/
    public function getHash($message = null): string
    {

        $privateKeyPath = base_path('private-key.pem');
        $privateKey     = openssl_pkey_get_private(file_get_contents($privateKeyPath));
        if (openssl_sign($message, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {

        } else {

        }

        return $signature;

    } /** end getHash( $message = null ) **/

    /**
     * @throws \Random\RandomException
     */
    public function randNonce($length = 8): string
    {

        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce      = '';
        $max        = strlen($characters) - 1;
        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $max)];
        }

        return $nonce;

    } /** end randNonce( $length = 8 ) **/

    /**
     * @return array|string|void
     */
    public function urlParser($type = null)
    {

        if ($type == 'url') {
            $parse = parse_url($this->baseURL);

            return $parse['scheme'].'://'.$parse['host'].':'.$parse['port'].$parse['path'];
        } elseif ($type == 'query') {
            $parse = parse_url($this->baseURL);
            if (isset($parse['query'])) {
                parse_str($parse['query'], $output);

                return $output;
            }
        }

    }

    /** end urlParser( $type = null ) **/
    public function authorizationHeader($authParams = null, $signature = null, $r = null): mixed
    {

        $h                             = [];
        $authParams['oauth_signature'] = $signature;

        foreach ($authParams as $k => $v) {
            if (empty($h)) {
                $h = ['OAuth '];
            } else {
                $h[] = ', ';
            }
            $h[] = $k;
            $h[] = '="';
            $h[] = $v;
            $h[] = '"';
        }

        return implode('', $h);

    } /** end authorizationHeader( $authParams = null, $signature = null, $r = null ) **/

    /**
     * @throws \Random\RandomException
     */
    public function authorizationSignature($bodyData = null): mixed
    {

        $endPointUrl   = $this->urlParser('url');
        $endPointQuery = $this->urlParser('query');
        $jsonData      = $bodyData != null ? json_encode($bodyData) : '';
        $hash          = hash('sha256', $jsonData, true);
        $base64Data    = base64_encode($hash);
        $headerArray   = [
            'oauth_consumer_key'     => config('app.oauth_consumer_key'),
            'oauth_signature_method' => 'RSA-SHA256',
            'oauth_timestamp'        => strval(time()),
            'oauth_nonce'            => $this->randNonce(11),
            'oauth_version'          => '1.0',
            'oauth_body_hash'        => $base64Data,
        ];
        foreach ($headerArray as $key => $value) {
            if ($key == 'oauth_signature') {
                continue;
            }
            $paramArray[] = urlencode($key).'='.urlencode($value);
        }
        if (isset($endPointQuery)) {
            foreach ($endPointQuery as $keys => $values) {
                $paramArray[] = urlencode($keys).'='.urlencode($values);
            }
        }

        sort($paramArray);
        $paramStr   = implode('&', $paramArray);
        $baseString = sprintf('%s&%s&%s', strtoupper($this->method), urlencode($endPointUrl), urlencode($paramStr));
        $signature  = base64_encode($this->getHash($baseString));

        return $this->authorizationHeader($headerArray, $signature, request());

    } /** end authorizationSignature( $bodyData = null ) **/
}   /** end class ApiClientAuth10Service **/
