@extends('paywithcrypto::layouts.miligold-default')

@section('page_title')
    {{ __('velocity::app-static.pay-with-crypto.summary.page-title') }}
@stop

@section('body')
    <!-- breadcrumb-area -->
    <section class="breadcrumb-area breadcrumb-bg">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="breadcrumb-content">
                        <h2 class="title">
                            {{ __('velocity::app-static.pay-with-crypto.summary.heading') }}
                        </h2>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <div class="area-bg" style="background-position: top;">
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <crypto_authorization></crypto_authorization>
            </div>
        </section>
    </div>
@endsection

@push('scripts')
    <script type="text/x-template" id="crypto_authorization_template">
    <div class="mx-auto max-w-5xl">
        <form action="{{ route('crypto.payment.authorization') }}" method="POST" class="gap-2 flex flex-wrap w-full justify-between my-5 pt-12 pb-24 lg:py-24" @submit.prevent="onSubmit"
              id="payment-authorization">
            @csrf
            @method('POST')
            <div class="w-full md:w-6/12">
                <section class="contact-aera">
                    <div class="container custom-container-four p-0">
                        <div class="rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700">
                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <div class="wallet-list-item transfer-details flex-column">
                                        <div class=" row select-btns w-100">
                                            <div class="w-full"><h4 class="mb-4 font-display text-3xl text-jacarta-700 dark:text-white text-center border-b-2 border-[#D0AA49] pb-4">{{ __('velocity::app-static.pay-with-crypto.summary.select-payment-type') }}</h4></div>
                                            <div class="w-full mb-3">
                                                <label for="chain" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                                    {{ __('velocity::app-static.pay-with-crypto.summary.select-chain') }}
                                                </label>
                                                <div class="relative">
                                                    <select class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700"
                                                            v-model="selectedChain" @change="getCoins" id="chain" name="chain" required>
                                                        <option value="" selected disabled>{{ __('velocity::app-static.pay-with-crypto.summary.select-chain-placeholder') }}</option>
                                                        <option v-for="chain in chains" :value="chain.chainId">@{{ chain.chainName }}</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="w-full mb-3">
                                                <label for="coin" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                                    {{ __('velocity::app-static.pay-with-crypto.summary.select-coin') }}
                                                </label>
                                                <div class="relative">
                                                    <select class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700"
                                                            v-model="selectedCoin" id="coin" name="coin" required @change="getBalance">
                                                        <option value="" selected disabled>{{ __('velocity::app-static.pay-with-crypto.summary.select-coin-placeholder') }}</option>
                                                        <option v-for="coin in coins" :value="coin.symbol" :disabled="coin.symbol === productSKU">
                                                            @{{ coin.symbol }}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="!isLoading" class="w-100 row">
                                            <div class="col-md-6 mt-2" v-if="isHaveBalance">
                                                <input type="hidden" name="cryptoPaymentResponse" v-model="cryptoPaymentResponse">
                                                <button class="@if(session()->has('error')) disabled-btn @endif rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                                        type="submit">
                                                    {{ __('velocity::app-static.pay-with-crypto.summary.continue') }}
                                                </button>
                                            </div>
                                            <div class="col-md-6 mt-2" v-if="isHaveBalance === false">
                                                <button type="button" @click="getOpenModal" class="@if(session()->has('error')) disabled-btn @endif rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);">
                                                    {{ __('velocity::app-static.pay-with-crypto.summary.add-balance') }}
                                                </button>
                                            </div>
                                        </div>
                                        <div v-else class="row justify-content-center">
                                            <div class="position-relative">
                                                <div class="spinner my-0">
                                                    <div class="rect1"></div>
                                                    <div class="rect2"></div>
                                                    <div class="rect3"></div>
                                                    <div class="rect4"></div>
                                                    <div class="rect5"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <div class="w-full md:w-5/12">
                <div class="rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700">

                    <h6 class="mb-4 font-display text-3xl text-jacarta-700 dark:text-white text-center border-b-2 border-[#D0AA49] pb-4">
                        {{ __('velocity::app-static.checkout.onepage.cart') }}
                    </h6>

                    <div class="flex items-center mb-4 space-x-3 w-full justify-center">
                        <img style="max-width: 50px" src="/miligold/img/mili-ikon.png" alt="">
                        <div class="font-display text-xl text-jacarta-700 dark:text-white text-center">
                            {{ $cartQuantity }} {{ $cart->items->first()->product->name }}
                        </div>
                    </div>
                    <div class="font-display text-lg text-jacarta-700 dark:text-white">
                        {{ __('velocity::app-static.checkout.onepage.total') }}:
                        <span class="text-2xl">
                                {{ core()->formatPrice($cartTotal, $cartCurrency) }}
                            </span>
                    </div>
                </div>
            </div>
        </form>
        <div v-if="isModalStatus" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title">
                    </h2>
                    <button @click="getCloseModal" class="modal__close white" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                    <div class="popup-info-item">
                        <div class="d-flex align-items-center mb-5">
                            <div class="icon">
                                <span class="icon-background"></span>
                                <i class="fas fa-piggy-bank"></i>
                            </div>
                            <div class="content">
                                <h4>{{ __('velocity::app-static.pay-with-crypto.summary.deposit-with-crypto-description') }}</h4>
                            </div>
                        </div>
                        <div class="content">
                            <p>
                                {{ __('velocity::app-static.pay-with-crypto.summary.deposit-with-crypto-warning') }}
                            </p>
                            <div style="border-bottom: 1px solid #212a32;padding: 10px;"></div>
                            <div class="position-relative mt-4">
                                <input
                                    type="text"
                                    id="walletAddress"
                                    class="ml-0 w-100 popup-input"
                                    disabled
                                    v-model="walletAddress"
                                />
                                <div @click="functionCopyWalletAddress" class="position-absolute" style="top:4px;right: 10px;cursor: pointer;">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="27px" width="27px" version="1.1" id="Layer_1" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve" fill="#FF9700">
                                        <g id="Text-files">
                                            <path d="M53.9791489,9.1429005H50.010849c-0.0826988,0-0.1562004,0.0283995-0.2331009,0.0469999V5.0228   C49.7777481,2.253,47.4731483,0,44.6398468,0h-34.422596C7.3839517,0,5.0793519,2.253,5.0793519,5.0228v46.8432999   c0,2.7697983,2.3045998,5.0228004,5.1378999,5.0228004h6.0367002v2.2678986C16.253952,61.8274002,18.4702511,64,21.1954517,64   h32.783699c2.7252007,0,4.9414978-2.1725998,4.9414978-4.8432007V13.9861002   C58.9206467,11.3155003,56.7043495,9.1429005,53.9791489,9.1429005z M7.1110516,51.8661003V5.0228   c0-1.6487999,1.3938999-2.9909999,3.1062002-2.9909999h34.422596c1.7123032,0,3.1062012,1.3422,3.1062012,2.9909999v46.8432999   c0,1.6487999-1.393898,2.9911003-3.1062012,2.9911003h-34.422596C8.5049515,54.8572006,7.1110516,53.5149002,7.1110516,51.8661003z    M56.8888474,59.1567993c0,1.550602-1.3055,2.8115005-2.9096985,2.8115005h-32.783699   c-1.6042004,0-2.9097996-1.2608986-2.9097996-2.8115005v-2.2678986h26.3541946   c2.8333015,0,5.1379013-2.2530022,5.1379013-5.0228004V11.1275997c0.0769005,0.0186005,0.1504021,0.0469999,0.2331009,0.0469999   h3.9682999c1.6041985,0,2.9096985,1.2609005,2.9096985,2.8115005V59.1567993z"></path>
                                            <path d="M38.6031494,13.2063999H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0158005   c0,0.5615997,0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4542999,1.0158997-1.0158997   C39.6190491,13.6606998,39.16465,13.2063999,38.6031494,13.2063999z"></path>
                                            <path d="M38.6031494,21.3334007H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0157986   c0,0.5615005,0.4544001,1.0159016,1.0159006,1.0159016h22.3491974c0.5615005,0,1.0158997-0.454401,1.0158997-1.0159016   C39.6190491,21.7877007,39.16465,21.3334007,38.6031494,21.3334007z"></path>
                                            <path d="M38.6031494,29.4603004H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4543991,1.0158997-1.0158997   S39.16465,29.4603004,38.6031494,29.4603004z"></path>
                                            <path d="M28.4444485,37.5872993H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h12.1904964c0.5615025,0,1.0158005-0.4543991,1.0158005-1.0158997   S29.0059509,37.5872993,28.4444485,37.5872993z"></path>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <footer class="modal__footer text-center">
                </footer>
            </div>
        </div>
        <!-- end Modals alanı -->
        <div v-if="walletAddressStatus" style="top: 50px;right: 15px;z-index: 99999;position: fixed;display: block;width: 430px;height: 100px;font-size: 16px;">
            <div class="alert alert-success alert-dismissible" id="504">
                <a @click="walletAddressStatusClick" href="#" class="close" data-dismiss="alert" aria-label="close">×</a>
                <strong>{{ __('velocity::app-static.walletPage.panel-modal-toast-title') }} </strong> {{ __('velocity::app-static.walletPage.panel-model-toast-body') }} @{{ walletAddressCopied }}
            </div>
        </div>
    </div>
    </script>
    <script>
        Vue.component('crypto_authorization', {
            template: '#crypto_authorization_template',
            data: function() {
                return {
                    productSKU: '{{ $productSKU }}',
                    isLoading: false,
                    chains: [],
                    selectedChain: '',
                    coins: [],
                    selectedCoin: '',
                    walletBalance: '',
                    walletAddress: '{{ $walletAddress }}',
                    walletAddressCopied: null,
                    walletAddressStatus: false,
                    acceptRules: true,
                    isHaveBalance: null,
                    isModalStatus: false,
                }
            },
            mounted() {
                try {
                    this.getChains().then(async () => {
                        console.log(this.chains);
                        const defaultChain = await this.chains.find(chain => chain.chainId.toLowerCase() == "mirum-testnet");

                        if (defaultChain) {
                            this.selectedChain = defaultChain.chainId;
                        }

                        if (this.selectedChain) {
                            this.getCoins().then(async () => {
                                console.log(this.coins);
                                const defaultCoin = await this.coins.find(coin => coin?.symbol == "EURM");

                                if (defaultCoin) {
                                    this.selectedCoin = defaultCoin.symbol;
                                }

                                if (this.selectedCoin) {
                                    this.getBalance();
                                }
                            });
                        }
                    });

                    this.acceptRules = false;
                } catch (error) {
                    console.error('Error in mounted hook:', error.message);
                }
            }
            ,
            methods: {
                getOpenModal() {
                    console.log("modal aç");
                    this.isModalStatus = true;
                },
                getCloseModal() {
                    console.log("modal kapat");
                    this.isModalStatus = false;
                },
                walletAddressStatusClick() {
                    this.walletAddressStatus = false;
                },
                functionCopyWalletAddress() {
                    this.walletAddressStatus = false;
                    let element = document.getElementById('walletAddress');
                    var tempInput = document.createElement('input');
                    tempInput.value = element.value.trim();
                    this.walletAddressCopied = element.value.trim();
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    try {
                        var successful = document.execCommand('copy');
                        if (successful) {
                            console.log("copyalandı");
                            this.walletAddressStatus = true;
                        } else {
                            this.walletAddressStatus = false;
                        }
                    } catch (err) {

                    }
                    setTimeout(() => this.walletAddressStatus = false, 2000);
                },
                getChains() {
                    this.isLoading = true;
                    return axios.get('{{ route('crypto.chains.list') }}')
                        .then(response => {
                            this.chains = response.data;
                            this.isLoading = false;
                        })
                        .catch(error => {
                            console.error('Error fetching chains', error);
                        });
                },
                getCoins() {
                    this.isLoading = true;
                    return axios.get(`{{ route('crypto.chains.coins') }}?chainid=${this.selectedChain}`)
                        .then(response => {
                            this.coins = response.data;
                            this.isLoading = false;
                            this.selectedCoin = '';
                        })
                        .catch(error => {
                            console.error('Error fetching coins', error);
                        });
                },
                getBalance() {
                    this.isLoading = true;
                    this.isHaveBalance = null;
                    axios.get(`{{ route('crypto.wallet.balance') }}?chain=${this.selectedChain}&coin=${this.selectedCoin}`)
                        .then(response => {
                            this.cryptoPaymentResponse = response.data.cryptoPaymentResponse;
                            this.isHaveBalance = response.data.isHaveBalance ?? false;
                            this.walletAddress = response.data.walletAddress;
                            this.isLoading = false;
                        })
                        .catch(error => {
                            console.error('Error fetching balance', error);
                        });
                },
                updateAcceptRules() {
                    this.acceptRules = !!this.acceptRules;
                    console.log(this.acceptRules);
                },
                onSubmit() {
                    document.querySelector('form#payment-authorization').submit();
                }
            }
        });
    </script>
@endpush
