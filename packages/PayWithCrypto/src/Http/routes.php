<?php

use Illuminate\Support\Facades\Route;
use PayWithCrypto\Http\Controllers\ChainController;
use PayWithCrypto\Http\Controllers\PaymentController;

Route::group(['middleware' => ['web']], function () {
    Route::prefix('payment')->name('crypto.payment.')->group(function () {
        Route::get('summary', [PaymentController::class, 'paymentSummary'])->name('summary');
        Route::post('authorization', [PaymentController::class, 'paymentAuthorization'])->name('authorization');
        Route::post('confirmation', [PaymentController::class, 'paymentConfirmation'])->name('confirmation');
        Route::post('withdrawal', [PaymentController::class, 'paymentWithdrawal'])->name('withdrawal');
        Route::post('check-withdrawal', [PaymentController::class, 'paymentCheckWithdrawal'])->name('check-withdrawal');
    });

    Route::prefix('result')->name('crypto.result.')->group(function () {
        Route::get('processing/{orderId}', [PaymentController::class, 'resultProcessing'])->name('processing');
        Route::get('pending', [PaymentController::class, 'resultPending'])->name('pending');
        Route::get('pending-balance', [PaymentController::class, 'resultPendingBalance'])->name('pending-balance');
        Route::get('completed', [PaymentController::class, 'resultCompleted'])->name('completed');
        Route::get('failed', [PaymentController::class, 'resultFailed'])->name('failed');
    });

    Route::get('chains', [ChainController::class, 'listChains'])->name('crypto.chains.list');
    Route::get('chains/balance', [ChainController::class, 'getBalance'])->name('crypto.wallet.balance');
    Route::get('chains/coins', [ChainController::class, 'listCoins'])->name('crypto.chains.coins');
});
