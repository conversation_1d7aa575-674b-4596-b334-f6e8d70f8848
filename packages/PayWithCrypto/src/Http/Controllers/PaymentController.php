<?php

namespace PayWithCrypto\Http\Controllers;

use App\Jobs\CryptoPaymentCheckTx;
use App\OrderMeta;
use App\Services\CurrencyConverter;
use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Webkul\Checkout\Facades\Cart;
use Webkul\Checkout\Models\CartAddress;
use Webkul\Sales\Models\OrderAddress;
use Webkul\Sales\Repositories\OrderRepository;

class PaymentController extends Controller
{
    protected OrderRepository $orderRepository;

    public function __construct(
        OrderRepository $orderRepository,
        protected ApiClientAuth10Service $apiServices
    ) {
        $this->orderRepository = $orderRepository;
    }

    public function paymentSummary()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        if ($cart->grand_total < config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount')) {
            session()->flash('error', 'Minimum order amount is '.config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount').' '.$cart->cart_currency_code);

            return redirect()->route('shop.checkout.onepage.index');
        }

        $cartTotal     = 0;
        $cartCurrency  = $cart->cart_currency_code;
        $cartQuantity  = (float) $cart->items_qty ?? 0;
        $walletAddress = auth()->guard('customer')->user()->wallet;
        $productSKU    = $cart->items()->first()->product->sku;

        foreach ($cart['items'] as $item) {
            $cartTotal += $item['total'];
        }

        return view('paywithcrypto::checkout.payment.summary', compact('cart', 'cartTotal', 'cartCurrency', 'cartQuantity', 'walletAddress', 'productSKU'));
    }

    public function paymentAuthorization()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        $cartTotal     = 0;
        $cartCurrency  = $cart->cart_currency_code;
        $cartQuantity  = (float) $cart->items_qty ?? 0;
        $walletAddress = auth()->guard('customer')->user()->wallet;

        $cryptoChain = request()->input('chain');
        $cryptoCoin  = request()->input('coin');

        foreach ($cart['items'] as $item) {
            $cartTotal += $item['total'];
        }

        $currencyConverter = app(CurrencyConverter::class);

        $cryptoPaymentData = [
            'userId'         => auth()->guard('customer')->user()->id,
            'tenantId'       => 50,
            'amount'         => $currencyConverter->convertToMinorUnits((string) $cartTotal, 2),
            'exponent'       => 2,
            'currency'       => 'USDT',
            'chainId'        => $cryptoChain,
            'cryptoCurrency' => $cryptoCoin,
        ];

        $txCryptoPayment = [];
        try {
            $cryptoPaymentResponse = json_decode(base64_decode(request('cryptoPaymentResponse')), true);

            if (isset($cryptoPaymentResponse) && $cryptoPaymentResponse['hasAvailableBalance']) {
                $txCryptoPayment = [
                    'requiredAmount' => (float) $currencyConverter->convertToMajorUnits(
                        (string) $cryptoPaymentResponse['requiredAmount'],
                        $cryptoPaymentResponse['precision']
                    ),
                    'paymentCurrency'   => $cryptoPaymentResponse['paymentCurrency'],
                    'currency'          => $cryptoPaymentResponse['currency'],
                    'gasCoin'           => $cryptoPaymentResponse['gasCoin'],
                    'chainId'           => $cryptoPaymentResponse['chainId'],
                    'minRequiredGasFee' => (float) $currencyConverter->convertToMajorUnits(
                        (string) $cryptoPaymentResponse['minRequiredGasFee'],
                        $cryptoPaymentResponse['precision']
                    ),
                    'totalRequiredAmount' => (float) $currencyConverter->convertToMajorUnits(
                        (string) $cryptoPaymentResponse['totalRequiredAmount'],
                        $cryptoPaymentResponse['precision']
                    ),
                    'hasAvailableBalance' => $cryptoPaymentResponse['hasAvailableBalance'],
                    'walletBalance'       => (float) $currencyConverter->convertToMajorUnits(
                        (string) $cryptoPaymentResponse['walletBalance'],
                        $cryptoPaymentResponse['precision']
                    ),
                    'pendingBalance' => (float) $currencyConverter->convertToMajorUnits(
                        (string) $cryptoPaymentResponse['pendingBalance'],
                        $cryptoPaymentResponse['precision']
                    ),
                    'message' => $cryptoPaymentResponse['message'],
                    'default' => $cryptoPaymentResponse,
                ];

            } else {
                throw new Exception('Crypto payment failed');
            }
        } catch (\Exception $e) {
            throw new Exception('Crypto payment failed with error: '.$e->getMessage());
        }

        return view('paywithcrypto::checkout.payment.authorization', compact('cart', 'cartTotal', 'cartCurrency', 'cartQuantity', 'walletAddress', 'txCryptoPayment'));
    }

    public function paymentCheckAuthorization() {}

    public function paymentConfirmation()
    {
        $cart = Cart::getCart();
        if (! $cart) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        $user                   = auth()->guard('customer')->user();
        $txCryptoPaymentRaw     = request('txCryptoPayment');
        $txCryptoPaymentDecoded = base64_decode($txCryptoPaymentRaw);
        $txCryptoPayment        = json_decode($txCryptoPaymentDecoded, true);

        if (! $txCryptoPayment || ! isset($txCryptoPayment['default'])) {
            session()->flash('error', 'Ödeme verileri geçersiz.');

            return redirect()->route('crypto.payment.summary');
        }

        $cryptoWithdrawalData = [
            'tenantId'          => 50,
            'userId'            => $user->id,
            'payeeType'         => 'CRYPTO',
            'countryCode'       => 'TRY',
            'amount'            => $txCryptoPayment['default']['requiredAmount'] ?? null,
            'exponent'          => $txCryptoPayment['default']['precision']      ?? null,
            'currency'          => $txCryptoPayment['default']['currency']       ?? null,
            'chainId'           => $txCryptoPayment['chainId']                   ?? null,
            'cryptoPaymentTxId' => $txCryptoPayment['default']['txId']           ?? null,
        ];

        $cryptoWithdrawalResponse = $this->apiServices->baseUrl2('/tx/withdrawal')
            ->baseMethod('post')
            ->baseClient($cryptoWithdrawalData);

        if (! $cryptoWithdrawalResponse->json('isSuccessful')) {
            session()->flash('error', 'Ödeme başarısız, lütfen tekrar deneyiniz.');

            return redirect()->route('crypto.payment.summary');
        }

        $order = $this->orderRepository->create(Cart::prepareDataForOrder());

        if ($cart->addresses()->exists()) {
            $cart->addresses->each(function ($address) use ($order) {
                try {
                    $address->update([
                        'order_id'     => $order?->id,
                        'address_type' => match ($address->address_type) {
                            CartAddress::ADDRESS_TYPE_BILLING  => OrderAddress::ADDRESS_TYPE_BILLING,
                            CartAddress::ADDRESS_TYPE_SHIPPING => OrderAddress::ADDRESS_TYPE_SHIPPING,
                            default                            => throw new \Exception('Unknown address type')
                        },
                    ]);
                } catch (\Exception $e) {
                    //
                }

                return $address;
            });
        }

        if ($cryptoWithdrawalResponse->json('isSuccessful')) {
            $order->update(['status' => 'processing']);

            $order->meta()->createMany([
                ['meta_key' => 'txId', 'meta_value' => request('txId', $cryptoWithdrawalResponse->json('txId'))],
                ['meta_key' => 'txData', 'meta_value' => json_encode($cryptoWithdrawalResponse->json())],
                ['meta_key' => 'txSuccess', 'meta_value' => null],
            ]);

            dispatch(new CryptoPaymentCheckTx($order))->onQueue('launchpad');

            return match ($order->status) {
                'success', 'completed' => redirect()->route('crypto.result.completed'),
                'processing'           => redirect()->route('crypto.result.processing', $order->id),
                'pending'              => redirect()->route('crypto.result.pending'),
                default                => redirect()->route('crypto.result.failed'),
            };
        } elseif ($cryptoWithdrawalResponse->json('errorCode') === 'E0000006') {
            $this->orderRepository->cancel($order->id);

            return redirect()->route('crypto.result.pending-balance');
        } else {
            $this->orderRepository->cancel($order->id);
            session()->flash('error', 'Ödeme işleme sırasında bir hata oluştu, lütfen tekrar deneyiniz. <br> Hata kodu: '.$cryptoWithdrawalResponse->json('errorCode').' - '.$cryptoWithdrawalResponse->json('errorMessage'));

            return redirect()->route('crypto.payment.summary');
        }
    }

    public function paymentCheckWithdrawal()
    {
        $orderMeta = OrderMeta::where('meta_key', 'txId')->where('meta_value', request('txId'))->first();
        $order     = $orderMeta->order;

        return response()->json([
            'isSuccessful' => $order?->status === 'completed' ?? false,
            'txId'         => request('txId'),
            'errorMessage' => request('errorMessage') ?? '',
            'txnDate'      => request('txnDate')      ?? '',
            'userId'       => auth()->guard('customer')->user()->id,
            'tenantId'     => 50,
        ]);
    }

    public function paymentLockToken()
    {
        $cart = Cart::getCart();

        $cartQuantity        = (float) $cart->items_qty ?? 0;
        $productCurrency     = $cart->items()->first()->product->sku;
        $productChain        = $cart->items()->first()->product?->chain_id       ?? 'bsc-testnet';
        $productPlan         = $cart->items()->first()->product?->product_number ?? 'SEED';
        $cryptoLockTokenData = [
            'tenantId'        => 50,
            'userId'          => auth()->guard('customer')->user()->id,
            'chainId'         => $productChain,
            'amount'          => $cartQuantity,
            'exponent'        => 8,
            'currency'        => $productCurrency,
            'lockTokenDetail' => [
                'lockPlan' => $productPlan,
            ],
        ];

        $cryptoLockTokenResponse = $this->apiServices->baseUrl2('/wallet/launchtoken')
            ->baseMethod('post')
            ->baseClient($cryptoLockTokenData);

        if ($cryptoLockTokenResponse->successful()) {
            return response()->json([
                'isSuccessful' => $cryptoLockTokenResponse->json('status') === 1,
                'message'      => $cryptoLockTokenResponse->json('message') ?? 'Token lock failed',
            ]);
        } else {
            throw new Exception('Token lock failed');
        }
    }

    public function resultProcessing($orderId)
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }
        $order = $this->orderRepository->findOrFail($orderId);
        Cart::deActivateCart();

        return view('paywithcrypto::checkout.payment.result.processing', compact('order'));
    }

    public function resultPendingBalance()
    {
        return view('paywithcrypto::checkout.payment.result.pending-balance');
    }

    public function resultPending()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        Cart::deActivateCart();

        return view('paywithcrypto::checkout.payment.result.pending');
    }

    public function resultCompleted()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        Cart::deActivateCart();

        return view('paywithcrypto::checkout.payment.result.completed');
    }

    public function resultFailed()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        Cart::deActivateCart();

        return view('paywithcrypto::checkout.payment.result.failed');
    }
}
