<?php

namespace Thorne\SecurityLayer\Traits;

use Illuminate\Support\Facades\Log;

trait LogsMiddleware
{
    protected function logInfo(string $channel, string $message, array $context = []): void
    {
        if (! $this->shouldLog()) {
            return;
        }

        Log::channel('security-layer')->info("[{$channel}] {$message}", $context);
    }

    protected function logWarning(string $channel, string $message, array $context = []): void
    {
        if (! $this->shouldLog()) {
            return;
        }

        Log::channel('security-layer')->warning("[{$channel}] {$message}", $context);
    }

    protected function logError(string $channel, string $message, array $context = []): void
    {
        if (! $this->shouldLog()) {
            return;
        }

        Log::channel('security-layer')->error("[{$channel}] {$message}", $context);
    }

    protected function shouldLog(): bool
    {
        return config('security-layer.logging.enabled', true);
    }
}
