<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Thorne\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Traits\LogsMiddleware;

class HttpsMiddleware
{
    use LogsMiddleware;

    protected SecurityLayerExceptionHandler $handler;

    public function __construct(SecurityLayerExceptionHandler $handler)
    {
        $this->handler = $handler;
    }

    public function handle(Request $request, Closure $next): Response|JsonResponse
    {
        $httpsEnabled = config('security-layer.features.https.enabled', true);
        $secure       = $request->secure();
        $url          = $request->fullUrl();
        $method       = $request->method();

        if (! $secure && $httpsEnabled) {
            $this->logWarning('https', 'Insecure Request', [
                'url'    => $url,
                'method' => $method,
                'env'    => app()->environment(),
            ]);

            return $this->handler->handle($request, 'https');
        }

        $this->logInfo('https', 'Checked: "https" middleware', [
            'url'    => $url,
            'method' => $method,
            'secure' => $secure,
        ]);

        return $next($request);
    }
}
