<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illum<PERSON>\Http\Request;
use <PERSON>sonException;
use Thorne\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Traits\LogsMiddleware;
use Thorne\SecurityLayer\Traits\ResolvesModule;

class SignatureMiddleware
{
    use LogsMiddleware, ResolvesModule;

    protected SecurityLayerExceptionHandler $handler;

    public function __construct(SecurityLayerExceptionHandler $handler)
    {
        $this->handler = $handler;
    }

    public function handle(Request $request, Closure $next)
    {
        $moduleKey = $this->resolveModuleKey($request, 'signature');
        $config    = $this->resolveModuleConfig($moduleKey, 'signature');

        if (! ($config['enabled'] ?? false)) {
            return $next($request);
        }

        if (! $moduleKey) {
            $this->logWarning('signature', 'Missing Module Key', [
                'request' => [
                    'path'   => $request->path(),
                    'method' => $request->method(),
                ],
            ]);

            return $this->handler->handle($request, 'signature', 'missing_module');
        }

        $originalMethod = $request->method();
        $content        = $request->getContent();

        $request->setMethod($originalMethod);
        if ($content) {
            $request->initialize(
                $request->query->all(),
                $request->request->all(),
                $request->attributes->all(),
                $request->cookies->all(),
                $request->files->all(),
                $request->server->all(),
                $content
            );
        }

        $signature = $request->header($config['header_name']);
        if (! $signature) {
            $this->logWarning('signature', 'Missing: "signature" middleware', [
                'moduleKey' => $moduleKey,
                'header'    => $config['header_name'],
            ]);

            return $this->handler->handle($request, 'signature', 'missing');
        }

        if (! $this->validateSignature($request, $moduleKey, $config)) {
            return $this->handler->handle($request, 'signature', 'invalid', [
                'error' => 'Signature mismatch. Please ensure headers are in the correct order: '.implode(', ', $config['signed_headers'] ?? []),
            ]);
        }

        $this->logInfo('signature', 'Valid: "signature" middleware', [
            'moduleKey' => $moduleKey,
            'signature' => $signature,
        ]);

        return $next($request);
    }

    protected function getSignedHeaders(Request $request, array $config): array
    {
        $headers = [];
        foreach ($config['signed_headers'] ?? [] as $headerKey) {
            if ($request->hasHeader($headerKey)) {
                $headers[$headerKey] = trim($request->header($headerKey));
            }
        }
        ksort($headers);

        return $headers;
    }

    protected function validateSignature(Request $request, string $moduleKey, array $config): bool
    {
        $algorithm = $config['algorithm'];
        $secret    = $config['secret'];
        $clockSkew = $config['clock_skew'];

        $this->logInfo('signature', 'Starting signature validation', [
            'moduleKey' => $moduleKey,
            'algorithm' => $algorithm,
            'hasSecret' => ! empty($secret),
            'config'    => $config,
        ]);

        $timestamp = $request->header('X-Timestamp');
        if (! $timestamp) {
            $this->logWarning('signature', 'Missing Timestamp', [
                'moduleKey' => $moduleKey,
            ]);

            return false;
        }

        $now = time();
        if (abs($now - $timestamp) > $clockSkew) {
            $this->logWarning('signature', 'Clock Skew', [
                'moduleKey' => $moduleKey,
                'timestamp' => $timestamp,
                'now'       => $now,
                'skew'      => $clockSkew,
            ]);

            return false;
        }

        $headers = $this->getSignedHeaders($request, $config);

        $this->logInfo('signature', 'Collected headers', [
            'moduleKey'     => $moduleKey,
            'headers'       => $headers,
            'signedHeaders' => $config['signed_headers'] ?? [],
        ]);

        $payload = $request->getContent() ?: '';

        try {
            $data = json_encode([
                'headers' => $headers,
                'payload' => $payload,
            ], JSON_THROW_ON_ERROR);

            $this->logInfo('signature', 'Generated data to sign', [
                'moduleKey' => $moduleKey,
                'data'      => $data,
            ]);
        } catch (JsonException $exception) {
            $this->logError('signature', 'JSON Encode Error', [
                'moduleKey' => $moduleKey,
                'error'     => $exception->getMessage(),
                'headers'   => $headers,
                'payload'   => $payload,
            ]);

            return false;
        }

        if (! $secret) {
            $this->logError('signature', 'Configuration Error', [
                'moduleKey' => $moduleKey,
                'config'    => $config,
            ]);

            return false;
        }

        $serverSignature = hash_hmac($algorithm, $data, $secret, false);
        $clientSignature = $request->header($config['header_name']);

        $this->logInfo('signature', 'Signature comparison', [
            'moduleKey'       => $moduleKey,
            'clientSignature' => $clientSignature,
            'serverSignature' => $serverSignature,
            'algorithm'       => $algorithm,
        ]);

        if (! hash_equals($serverSignature, $clientSignature)) {
            $this->logWarning('signature', 'Signature Mismatch', [
                'moduleKey'       => $moduleKey,
                'clientSignature' => $clientSignature,
                'serverSignature' => $serverSignature,
                'headers'         => $headers,
                'payload'         => $payload,
                'dataToSign'      => $data,
            ]);

            return false;
        }

        return true;
    }
}
