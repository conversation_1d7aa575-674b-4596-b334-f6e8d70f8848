<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Thorne\SecurityLayer\Traits\LogsMiddleware;
use Thorne\SecurityLayer\Traits\ResolvesModule;

class TraceMiddleware
{
    use LogsMiddleware, ResolvesModule;

    protected array $config;

    protected float $startTime;

    protected array $configLog = [];

    private const HTTP_STATUS_TEXTS = [
        200 => 'OK',
        201 => 'Created',
        202 => 'Accepted',
        204 => 'No Content',
        400 => 'Bad Request',
        401 => 'Unauthorized',
        403 => 'Forbidden',
        404 => 'Not Found',
        405 => 'Method Not Allowed',
        422 => 'Unprocessable Entity',
        429 => 'Too Many Requests',
        500 => 'Internal Server Error',
        502 => 'Bad Gateway',
        503 => 'Service Unavailable',
        504 => 'Gateway Timeout',
    ];

    public function handle(Request $request, Closure $next): Response
    {
        $this->startTime = microtime(true);
        $moduleKey       = $this->resolveModuleKey($request, 'trace');

        $this->logConfig($moduleKey);
        $this->config = $this->resolveModuleConfig($moduleKey, 'trace');

        if (! ($this->config['enabled'] ?? false)) {
            return $next($request);
        }

        $requestId = $request->header('X-Request-ID') ?? Str::uuid()->toString();
        $request->headers->set('X-Request-ID', $requestId);

        $requestData  = $this->getRequestData($request, $moduleKey ?? 'global');
        $response     = $next($request);
        $responseData = $this->getResponseData($response, $request, $moduleKey ?? 'global');

        $response->headers->set('X-Request-ID', $requestId);

        $this->logRequestResponse($moduleKey, $requestData, $responseData);

        return $response;
    }

    protected function logConfig(?string $moduleKey): void
    {
        $globalConfig  = config('security-layer.features.trace', []);
        $moduleConfig  = $moduleKey ? config("security-layer.modules.{$moduleKey}", []) : null;
        $featureConfig = $moduleConfig['features']['trace'] ?? null;

        $this->configLog = [
            'global'  => $globalConfig,
            'module'  => $moduleConfig,
            'feature' => $featureConfig,
            'merged'  => $featureConfig ? array_merge($globalConfig, $featureConfig, ['enabled' => true]) : $globalConfig,
        ];
    }

    protected function logRequestResponse(?string $moduleKey, array $requestData, array $responseData): void
    {
        $this->logInfo('trace', 'Request/Response: "trace" middleware', [
            'moduleKey' => $moduleKey ?? 'global',
            'request'   => $requestData,
            'response'  => $responseData,
            'config'    => $this->configLog,
        ]);
    }

    protected function getRequestData(Request $request, string $moduleKey): array
    {
        return [
            'method'         => $request->method(),
            'url'            => $request->fullUrl(),
            'path'           => $request->path(),
            'query'          => $request->query(),
            'headers'        => $this->filterHeaders($request->headers->all()),
            'cookies'        => $this->filterCookies($request->cookies->all()),
            'files'          => $this->formatFiles($request->allFiles()),
            'input'          => $this->formatInput($request->all()),
            'payload'        => $this->formatRequestContent($request),
            'ip'             => $request->ip(),
            'ips'            => $request->ips(),
            'user_agent'     => $request->userAgent(),
            'content_type'   => $request->header('Content-Type'),
            'accept'         => $request->header('Accept'),
            'content_length' => $request->header('Content-Length'),
            'route'          => $this->getRouteData($request),
            'session'        => $request->hasSession() ? $this->formatSession($request->session()->all()) : null,
            'auth'           => $this->getAuthData($request),
            'server'         => $this->getServerData($request),
            'module_key'     => $moduleKey,
        ];
    }

    protected function getRouteData(Request $request): array
    {
        return [
            'name'       => $request->route()?->getName(),
            'action'     => $request->route()?->getActionName(),
            'parameters' => $request->route()?->parameters(),
        ];
    }

    protected function getAuthData(Request $request): array
    {
        return [
            'user'  => $request->user()?->id,
            'guard' => $request->user()?->getAuthIdentifierName(),
        ];
    }

    protected function getServerData(Request $request): array
    {
        return [
            'protocol'   => $request->server('SERVER_PROTOCOL'),
            'software'   => $request->server('SERVER_SOFTWARE'),
            'port'       => $request->server('SERVER_PORT'),
            'time'       => $request->server('REQUEST_TIME'),
            'time_float' => $request->server('REQUEST_TIME_FLOAT'),
        ];
    }

    protected function getResponseData(Response $response, Request $request, string $moduleKey): array
    {
        $endTime     = microtime(true);
        $duration    = round(($endTime - $this->startTime) * 1000, 2);
        $memoryUsage = round(memory_get_usage() / 1024 / 1024, 2);

        return [
            'status'      => $response->getStatusCode(),
            'status_text' => $this->getStatusText($response->getStatusCode()),
            'headers'     => $this->filterHeaders($response->headers->all()),
            'content'     => $this->formatResponseContent($response),
            'cookies'     => $this->formatResponseCookies($response->headers->getCookies()),
            'duration_ms' => $duration,
            'memory_mb'   => $memoryUsage,
            'user_id'     => auth()->id(),
            'module_key'  => $moduleKey,
        ];
    }

    protected function formatRequestContent(Request $request): ?string
    {
        if (! in_array($request->method(), $this->config['methods'] ?? [])) {
            return null;
        }

        $content = $request->getContent();
        if (empty($content)) {
            return null;
        }

        return $this->formatContent($content, $request->header('Content-Type'));
    }

    protected function formatResponseContent(Response $response): mixed
    {
        return $this->formatContent($response->getContent(), $response->headers->get('Content-Type'));
    }

    protected function formatContent(?string $content, ?string $contentType): mixed
    {
        if (empty($content)) {
            return null;
        }

        $contentTypes = $this->config['content_types'] ?? [];

        if (str_starts_with($contentType, 'application/json') && in_array('application/json', $contentTypes)) {
            return json_decode($content, true);
        }

        if (str_starts_with($contentType, 'text/html') && in_array('text/html', $contentTypes)) {
            return $content;
        }

        if (str_starts_with($contentType, 'text/plain') && in_array('text/plain', $contentTypes)) {
            return $content;
        }

        return $content;
    }

    protected function filterCookies(array $cookies): array
    {
        $sensitiveCookies = $this->config['sensitive']['cookies'] ?? [];

        return collect($cookies)->map(function ($value, $key) use ($sensitiveCookies) {
            return in_array(strtolower($key), $sensitiveCookies) ? '***' : $value;
        })->all();
    }

    protected function getStatusText(int $statusCode): string
    {
        return self::HTTP_STATUS_TEXTS[$statusCode] ?? 'Unknown';
    }

    protected function filterHeaders(array $headers): array
    {
        $sensitiveHeaders = $this->config['sensitive']['headers'] ?? [];

        return collect($headers)->map(function ($value, $key) use ($sensitiveHeaders) {
            return in_array(strtolower($key), $sensitiveHeaders) ? '***' : $value;
        })->all();
    }

    protected function formatFiles(array $files): array
    {
        return collect($files)->map(function ($file) {
            if (is_array($file)) {
                return $this->formatFiles($file);
            }

            return [
                'name'      => $file->getClientOriginalName(),
                'size'      => $file->getSize(),
                'mime'      => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension(),
            ];
        })->all();
    }

    protected function formatInput(array $input): array
    {
        $sensitiveFields = $this->config['sensitive']['fields'] ?? [];

        return collect($input)->map(function ($value, $key) use ($sensitiveFields) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                return '*SENSITIVE*';
            }

            return is_array($value) ? $this->formatInput($value) : $value;
        })->all();
    }

    protected function formatSession(?array $session): ?array
    {
        if (! $session) {
            return null;
        }

        $sensitiveFields = $this->config['sensitive']['fields'] ?? [];

        return collect($session)->map(function ($value, $key) use ($sensitiveFields) {
            return in_array(strtolower($key), $sensitiveFields) ? '***' : $value;
        })->all();
    }

    protected function formatResponseCookies(array $cookies): array
    {
        return collect($cookies)->map(function ($cookie) {
            return [
                'name'      => $cookie->getName(),
                'path'      => $cookie->getPath(),
                'domain'    => $cookie->getDomain(),
                'secure'    => $cookie->isSecure(),
                'http_only' => $cookie->isHttpOnly(),
                'expires'   => $cookie->getExpiresTime(),
            ];
        })->all();
    }
}
