<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use <PERSON>\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Traits\LogsMiddleware;
use Thorne\SecurityLayer\Traits\ResolvesModule;

class RateLimitMiddleware
{
    use LogsMiddleware, ResolvesModule;

    protected SecurityLayerExceptionHandler $handler;

    protected string $cachePrefix;

    protected string $cacheStore;

    public function __construct(SecurityLayerExceptionHandler $handler)
    {
        $this->handler     = $handler;
        $this->cachePrefix = config('security-layer.cache.prefix');
        $this->cacheStore  = config('security-layer.cache.store');
    }

    public function handle(Request $request, Closure $next)
    {
        $moduleKey = $this->resolveModuleKey($request, 'rate_limit');
        $config    = $this->resolveModuleConfig($moduleKey, 'rate_limit');

        if (! ($config['enabled'] ?? false)) {
            return $next($request);
        }

        $bypassHeader = $config['secure_header'];
        if ($request->header($bypassHeader)) {
            $this->logInfo('rate_limit', 'Bypassed: "rate_limit" middleware', [
                'moduleKey' => $moduleKey,
                'header'    => $bypassHeader,
            ]);

            return $next($request);
        }

        $customerId    = $request->header('X-Customer-Id');
        $limiterResult = $this->checkRateLimits($request, $moduleKey, $config, $customerId);

        if ($limiterResult['exceeded']) {
            $this->logWarning('rate_limit', 'Exceeded: "rate_limit" middleware', [
                'moduleKey'        => $moduleKey,
                'key'              => $limiterResult['key'],
                'limit'            => $limiterResult['limit'],
                'type'             => $limiterResult['type'],
                'retryAfter'       => $limiterResult['retryAfter'],
                'globalExceeded'   => $limiterResult['globalExceeded'],
                'customerExceeded' => $limiterResult['customerExceeded'],
            ]);

            $response = $this->handler->handle($request, 'rate_limit', null, [
                'retry_after' => $limiterResult['retryAfter'],
                'limit'       => $limiterResult['limit'],
                'remaining'   => 0,
                'type'        => $limiterResult['type'],
            ]);

            $response->headers->add([
                'Retry-After' => $limiterResult['retryAfter'],
            ]);

            return $response;
        }

        $this->updateRateLimiters($limiterResult);

        $this->logInfo('rate_limit', 'Checked: "rate_limit" middleware', [
            'moduleKey'   => $moduleKey,
            'customerKey' => $limiterResult['customerKey'],
            'globalKey'   => $limiterResult['globalKey'],
            'key'         => $limiterResult['key'],
            'limit'       => $limiterResult['limit'],
            'window'      => $limiterResult['window'],
            'type'        => $limiterResult['type'],
            'customerId'  => $customerId,
            'ip'          => $request->ip(),
        ]);

        $response = $next($request);

        $remaining = RateLimiter::remaining($this->cachePrefix.$limiterResult['key'], $limiterResult['limit']);
        $reset     = RateLimiter::availableIn($this->cachePrefix.$limiterResult['key']);

        $this->logInfo('rate_limit', 'Updated: "rate_limit" middleware', [
            'moduleKey' => $moduleKey,
            'key'       => $limiterResult['key'],
            'limit'     => $limiterResult['limit'],
            'type'      => $limiterResult['type'],
            'remaining' => $remaining,
            'reset'     => $reset,
        ]);

        $response->headers->add([
            'X-RateLimit-Limit'     => $limiterResult['limit'],
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset'     => $reset,
            'X-RateLimit-Type'      => $limiterResult['type'],
            'X-RateLimit-Policy'    => "{$limiterResult['limit']} r/{$limiterResult['window']}s per {$limiterResult['type']}",
        ]);

        return $response;
    }

    protected function checkRateLimits(Request $request, ?string $moduleKey, array $config, ?string $customerId): array
    {
        $result = [
            'type'             => 'global',
            'exceeded'         => false,
            'globalExceeded'   => false,
            'customerExceeded' => false,
            'retryAfter'       => 0,
            'customerKey'      => null,
            'key'              => null,
            'limit'            => 0,
            'window'           => 0,
        ];

        $result['globalKey'] = $this->resolveLimiterKey($request, null, $moduleKey, 'global');
        $globalLimit         = $config['global']['limit'];
        $globalWindow        = $config['global']['window'];

        if (RateLimiter::tooManyAttempts($this->cachePrefix.$result['globalKey'], $globalLimit)) {
            $result['globalExceeded'] = true;
            $result['exceeded']       = true;
            $globalRetryAfter         = RateLimiter::availableIn($this->cachePrefix.$result['globalKey']);
            $result['retryAfter']     = $globalRetryAfter;
            $result['type']           = 'global';
            $result['key']            = $result['globalKey'];
            $result['limit']          = $globalLimit;
            $result['window']         = $globalWindow;
        }

        if ($customerId) {
            $result['customerKey'] = $this->resolveLimiterKey($request, $customerId, $moduleKey, 'customer');
            $customerLimit         = $config['customer']['limit'];
            $customerWindow        = $config['customer']['window'];

            if (RateLimiter::tooManyAttempts($this->cachePrefix.$result['customerKey'], $customerLimit)) {
                $result['customerExceeded'] = true;
                $result['exceeded']         = true;
                $customerRetryAfter         = RateLimiter::availableIn($this->cachePrefix.$result['customerKey']);

                if ($result['globalExceeded']) {
                    if ($customerRetryAfter > $result['retryAfter']) {
                        $result['retryAfter'] = $customerRetryAfter;
                        $result['type']       = 'customer';
                        $result['key']        = $result['customerKey'];
                        $result['limit']      = $customerLimit;
                        $result['window']     = $customerWindow;
                    }
                } else {
                    $result['retryAfter'] = $customerRetryAfter;
                    $result['type']       = 'customer';
                    $result['key']        = $result['customerKey'];
                    $result['limit']      = $customerLimit;
                    $result['window']     = $customerWindow;
                }
            }
        }

        if (! $result['exceeded']) {
            if ($customerId) {
                $result['type']   = 'customer';
                $result['key']    = $result['customerKey'];
                $result['limit']  = $config['customer']['limit'];
                $result['window'] = $config['customer']['window'];
            } else {
                $result['key']    = $result['globalKey'];
                $result['limit']  = $config['global']['limit'];
                $result['window'] = $config['global']['window'];
            }
        }

        return $result;
    }

    protected function updateRateLimiters(array $limiterResult): void
    {
        if ($limiterResult['globalKey']) {
            RateLimiter::hit($this->cachePrefix.$limiterResult['globalKey'], $limiterResult['window']);
        }

        if ($limiterResult['customerKey']) {
            RateLimiter::hit($this->cachePrefix.$limiterResult['customerKey'], $limiterResult['window']);
        }
    }

    protected function resolveLimiterKey(Request $request, ?string $customerId, ?string $moduleKey, ?string $type = null): string
    {
        $parts = [];

        if ($moduleKey) {
            $parts[] = $moduleKey;
        }

        if ($type) {
            $parts[] = $type;
        }

        if ($type === 'customer' && $customerId) {
            $parts[] = 'customer_'.$customerId;
        } else {
            $parts[] = 'ip_'.$request->ip();
        }

        $uniqueIdentifier = implode(':', $parts);

        return $this->cachePrefix.'rate_limit:'.md5($uniqueIdentifier);
    }
}
