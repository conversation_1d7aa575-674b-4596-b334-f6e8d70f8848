<?php

namespace Thorne\SecurityLayer\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Services\SessionService;

class SessionController
{
    protected SessionService $sessionService;

    protected SecurityLayerExceptionHandler $handler;

    public function __construct(SessionService $sessionService, SecurityLayerExceptionHandler $handler)
    {
        $this->sessionService = $sessionService;
        $this->handler        = $handler;
    }

    public function create(Request $request): JsonResponse
    {
        $customerId = $request->header('X-Customer-Id');

        if (! $customerId) {
            return $this->handler->handle($request, 'session', 'missing', [
                'header' => 'X-Customer-Id',
            ]);
        }

        if (! $this->isValidCustomerId($customerId)) {
            return $this->handler->handle($request, 'session', 'invalid', [
                'reason' => 'Invalid customer ID format or value',
            ]);
        }

        $session = $this->sessionService->createSession($customerId, $request);

        return response()->json($session);
    }

    protected function isValidCustomerId(string $customerId): bool
    {
        return is_numeric($customerId) && $customerId > 0;
    }
}
