<?php

use Illuminate\Support\Facades\Route;
use Thorne\SecurityLayer\Http\Controllers\SessionController;

Route::middleware(['security-layer.cors', 'security-layer.https', 'security-layer.trace'])
    ->group(function () {
        Route::post('session', [SessionController::class, 'create'])
            ->middleware([
                'security-layer.nonce',
                'security-layer.timestamp',
                'security-layer.signature',
                'security-layer.rate_limit',
            ])
            ->name('session.create');

        Route::prefix('tests')->group(function () {
            // 1. security-layer.cors
            Route::get('s1', fn () => response()->json([
                'message'        => 'This is a sample test endpoint',
                'input'          => request()->all(),
                'headers'        => request()->headers->all(),
                'payload'        => request()->getContent(),
                'secure_context' => request()->secureContext(),
            ]))->middleware('security-layer.cors');

            // 2. security-layer.cors:{"allowed_origins":["https://inline-global.example.com"]}
            Route::get('s2', fn () => response()->json([
                'message'        => 'This is a sample test endpoint',
                'input'          => request()->all(),
                'headers'        => request()->headers->all(),
                'payload'        => request()->getContent(),
                'secure_context' => request()->secureContext(),
            ]))->middleware('security-layer.cors:{"allowed_origins":["https://inline-global.example.com"]}');

            // 3. security-layer.test-module.cors
            Route::get('s3', fn () => response()->json([
                'message'        => 'This is a sample test endpoint',
                'input'          => request()->all(),
                'headers'        => request()->headers->all(),
                'payload'        => request()->getContent(),
                'secure_context' => request()->secureContext(),
            ]))->middleware('security-layer.test-module.cors');

            // 4. security-layer.test-module.cors:{"allowed_origins":["https://inline-module.example.com"]}
            Route::get('s4', fn () => response()->json([
                'message'        => 'This is a sample test endpoint',
                'input'          => request()->all(),
                'headers'        => request()->headers->all(),
                'payload'        => request()->getContent(),
                'secure_context' => request()->secureContext(),
            ]))->middleware('security-layer.test-module.cors:{"allowed_origins":["https://inline-module.example.com"]}');

            // 5. security-layer.session
            Route::get('s5', fn () => response()->json([
                'message'        => 'This is a sample session endpoint',
                'input'          => request()->all(),
                'headers'        => request()->headers->all(),
                'payload'        => request()->getContent(),
                'secure_context' => request()->secureContext(),
            ]))->middleware('security-layer.session');
        });
    });
