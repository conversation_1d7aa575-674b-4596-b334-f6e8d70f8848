# thornebilisim/security-layer

`security-layer` p<PERSON><PERSON>, <PERSON><PERSON>'in middleware mimarisini kullanarak API'leriniz için gelişmiş güvenlik özellikleri sunar. CORS, Nonce, Timestamp, Signature, Rate Limiting, Idempotency ve API Oturum Yönetimi gibi kritik güvenlik mekanizmalarını tek bir çatı altında birleştirirken, tüm istek ve yanıtları detaylı bir şekilde izleme yeteneği de sağlar. Modüler ve esnek yapılandırma seçenekleri sayesinde, farklı API segmentleri için özelleştirilmiş güvenlik politikaları uygulayabilirsiniz.

## Kurulum

Paketi projenize Composer ile ekleyebilirsiniz:

```bash
composer require thornebilisim/security-layer
```

<PERSON>et, Laravel'in otomatik keşif özelliğini destekler. Kurulumdan sonra, paketin konfigürasyon dosyalarını projenize yayınlamak isteyebilirsiniz:

```bash
php artisan vendor:publish --tag=security-layer
```

Bu komut aşağıdaki dosyaları yayınlayacaktır:

*   `config/security-layer.php`
*   `routes/api.php` (mevcut `routes/api.php` dosyanızla birleştirmeyi unutmayın)
*   `config/logging.php` (mevcut `config/logging.php` dosyanızla birleştirmeyi unutmayın)

Ortam değişkenlerinizi `.env` dosyanızda yapılandırmayı unutmayın. Örneğin:

```dotenv
SECURITY_LAYER_ENABLED=true
SECURITY_LAYER_SIGNATURE_SECRET=YOUR_SECRET_KEY_HERE
SECURITY_LAYER_CORS_ALLOWED_ORIGINS="https://api.example.com,https://your-frontend.com"
```

## Hızlı Başlangıç

Paket kurulduktan ve yapılandırma dosyaları yayınlandıktan sonra, middleware'leri rotalarınıza atayarak güvenlik özelliklerini kullanmaya başlayabilirsiniz. `security-layer.enabled` konfigürasyonunun `true` olarak ayarlandığından emin olun.

Yaygın kullanım için örnek bir rota grubu:

```php
// routes/api.php
use Illuminate\Support\Facades\Route;
use Thorne\SecurityLayer\Http\Controllers\SessionController;

Route::middleware(['security-layer.cors', 'security-layer.https', 'security-layer.trace'])
    ->group(function () {
        Route::post('session', [SessionController::class, 'create'])
            ->middleware([
                'security-layer.nonce',
                'security-layer.timestamp',
                'security-layer.signature',
                'security-layer.rate_limit',
            ])
            ->name('session.create');

        // Modüler veya inline konfigürasyon örnekleri için diğer test rotaları
        Route::prefix('tests')->group(function () {
            // Global CORS konfigürasyonu
            Route::get('s1', fn () => response()->json(['message' => 'CORS testi']))
                ->middleware('security-layer.cors');

            // Inline CORS konfigürasyonu ile belirli origin'e izin verme
            Route::get('s2', fn () => response()->json(['message' => 'Inline CORS testi']))
                ->middleware('security-layer.cors:{"allowed_origins":["https://inline.example.com"]}');

            // Modül bazlı CORS konfigürasyonu
            Route::get('s3', fn () => response()->json(['message' => 'Modül bazlı CORS testi']))
                ->middleware('security-layer.test-module.cors');
        });
    });
```

## Özellikler

*   **HTTPS Zorunluluğu**: Tüm API isteklerinin güvenli HTTPS bağlantılar üzerinden gelmesini sağlar ve HTTP isteklerini yönlendirir.
*   **CORS Yönetimi**: Cross-Origin Resource Sharing (Çapraz Kaynak Paylaşımı) politikalarını etkin bir şekilde yönetir, izin verilen origin'leri, metotları ve başlıkları belirler.
*   **Nonce Doğrulaması**: Tek kullanımlık numaralar (Nonce) kullanarak tekrar oynatma (replay) saldırılarını engeller.
*   **Zaman Damgası Doğrulaması**: İsteklerin belirli bir zaman dilimi içinde yapıldığını kontrol eder ve saat kayması toleransı uygular.
*   **İstek İmza Doğrulaması**: HMAC tabanlı dijital imzalarla isteklerin bütünlüğünü ve kimliğini doğrular.
*   **İstek Sınırlama**: IP adresi veya müşteri ID'si bazında API isteklerine istek sınırlaması uygulayarak kötüye kullanımı önler.
*   **İdempotans Desteği**: Yinelenen isteklerin yalnızca bir kez işlenmesini garanti ederek işlemsel tutarlılık sağlar.
*   **API Oturum Yönetimi**: JWT (JSON Web Token) tabanlı güvenli API oturumları oluşturur ve doğrular.
*   **Detaylı İstek İzleme (Tracing)**: Her API isteği ve yanıtı hakkında detaylı günlükler tutar, hassas verileri maskeleyerek güvenliği sağlar.

## Yapılandırma

Paket, `config/security-layer.php` dosyası üzerinden kapsamlı bir şekilde yapılandırılabilir. Bu dosya, her bir güvenlik özelliğinin kendi bölümünü içerir. Yapılandırma, genel (global) ayarlar, modül bazlı ayarlar ve hatta rota tanımı üzerinde inline JSON parametreleri ile geçersiz kılınabilir. Bu katmanlı yapı, uygulamanızın ihtiyaçlarına göre maksimum esneklik sunar.

Özel günlükleme ayarları için `config/logging.php` dosyasındaki `security-layer` kanalını inceleyebilirsiniz.

## Middleware Yaşam Döngüsü

Paket içindeki tüm güvenlik özellikleri, Laravel'in HTTP middleware'leri olarak uygulanır. Her middleware, bir isteği işleme başlamadan önce veya tamamlandıktan sonra belirli bir görevi yerine getirir. `SecurityLayerServiceProvider` tarafından otomatik olarak kaydedilen `security-layer.` ön ekli takma adlar (örneğin `security-layer.cors`, `security-layer.nonce`) sayesinde bu middleware'leri rotalarınıza kolayca ekleyebilirsiniz.

## Modüler Mimari

`security-layer.php` konfigürasyonundaki `modules` bölümü, uygulamanızın farklı kısımları (örneğin farklı mikroservisler veya API versiyonları) için ayrı güvenlik politikaları tanımlamanıza olanak tanır. Her modül, kendi özelliklerini (CORS, Rate Limit vb.) bağımsız olarak yapılandırabilir ve genel ayarları geçersiz kılabilir. Bu, büyük ve karmaşık API'ler için oldukça kullanışlı bir özelliktir.

## Günlükleme

Paket, `storage/logs/security-layer.log` dosyasına özel olarak yapılandırılmış, JSON formatında ve okunabilir ön eklerle zenginleştirilmiş detaylı günlükler kaydeder. Bu günlükler, güvenlik olaylarını izlemek, performans sorunlarını gidermek ve denetim kayıtları tutmak için kritik öneme sahiptir. Günlükleme seviyesi ve etkinliği yapılandırma üzerinden ayarlanabilir.

## Hata Yönetimi

`security-layer` paketi, tüm güvenlik doğrulama hatalarını ve HTTP istisnalarını tutarlı bir şekilde işleyen merkezi bir hata yönetimi sistemine sahiptir. API istekleri için özel olarak yapılandırılmış JSON hata yanıtları döndürülür, bu da istemcilerin hata durumlarını kolayca anlamasını ve işlemesini sağlar. Hata yanıtları, `code`, `message` ve ilgili `context` bilgilerini içerir.

## Dokümantasyon Linkleri

Paket özelliklerinin ve yapılandırmasının daha detaylı incelenmesi için aşağıdaki dokümantasyon dosyalarına göz atabilirsiniz:

*   [Ortam Değişkenleri ve İlk Kurulum](docs/environment.md)
*   [Yapılandırma Dosyaları](docs/config.md)
*   [Rota Tanımlamaları ve Middleware Kullanımı](docs/routing.md)
*   [Günlükleme Ayarları](docs/logging.md)
*   [Modüler Yapı](docs/module.md)
*   [Hata Yönetimi](docs/exceptions.md)
*   [Özellik: İstek İzleme (trace)](docs/features/trace.md)
*   [Özellik: HTTPS Zorunluluğu (https)](docs/features/https.md)
*   [Özellik: CORS Yönetimi (cors)](docs/features/cors.md)
*   [Özellik: Nonce Doğrulaması (nonce)](docs/features/nonce.md)
*   [Özellik: Zaman Damgası Doğrulaması (timestamp)](docs/features/timestamp.md)
*   [Özellik: İstek İmza Doğrulaması (signature)](docs/features/signature.md)
*   [Özellik: İstek Sınırlama (rate_limit)](docs/features/rate_limit.md)
*   [Özellik: İdempotans Desteği (idempotancy)](docs/features/idempotency.md)
*   [Özellik: API Oturum Yönetimi (session)](docs/features/session.md)
