<?php

return [
    'enabled'      => env('SECURITY_LAYER_ENABLED', true),
    'cache'        => [
        'prefix' => env('SECURITY_LAYER_CACHE_PREFIX', 'security_layer:'),
        'store'  => env('SECURITY_LAYER_CACHE_STORE', 'redis'),
    ],
    'logging' => [
        'enabled' => env('SECURITY_LAYER_LOG_ENABLED', true),
        'level'   => env('SECURITY_LAYER_LOG_LEVEL', 'info'),
    ],
    'middleware_aliases' => [
        'trace'       => <PERSON>\SecurityLayer\Http\Middleware\TraceMiddleware::class,
        'https'       => <PERSON>\SecurityLayer\Http\Middleware\HttpsMiddleware::class,
        'cors'        => <PERSON>\SecurityLayer\Http\Middleware\CorsMiddleware::class,
        'nonce'       => <PERSON>\SecurityLayer\Http\Middleware\NonceMiddleware::class,
        'timestamp'   => <PERSON>\SecurityLayer\Http\Middleware\TimestampMiddleware::class,
        'signature'   => <PERSON>\SecurityLayer\Http\Middleware\SignatureMiddleware::class,
        'rate_limit'  => <PERSON>\SecurityLayer\Http\Middleware\RateLimitMiddleware::class,
        'idempotency' => Thorne\SecurityLayer\Http\Middleware\IdempotencyMiddleware::class,
        'session'     => Thorne\SecurityLayer\Http\Middleware\SessionMiddleware::class,
    ],
    'features' => [
        'https' => [
            'enabled'       => env('SECURITY_LAYER_HTTPS_ENABLED', true),
            'redirect_code' => env('SECURITY_LAYER_HTTPS_REDIRECT_CODE', 301),
        ],
        'cors' => [
            'enabled'                 => env('SECURITY_LAYER_CORS_ENABLED', true),
            'allowed_origins'         => array_filter(explode(',', env('SECURITY_LAYER_CORS_ALLOWED_ORIGINS', 'https://api.example.com'))),
            'default_allowed_methods' => array_filter(explode(',', env('SECURITY_LAYER_CORS_DEFAULT_ALLOWED_METHODS', 'GET,POST,PUT,DELETE,OPTIONS'))),
            'default_allowed_headers' => array_filter(explode(',', env('SECURITY_LAYER_CORS_DEFAULT_ALLOWED_HEADERS', 'Content-Type,X-Requested-With,Authorization,X-Nonce,X-Timestamp,X-Signature,X-Customer-Id,Idempotency-Key'))),
            'required_headers'        => env('SECURITY_LAYER_CORS_REQUIRED_HEADERS') ? array_filter(explode(',', env('SECURITY_LAYER_CORS_REQUIRED_HEADERS'))) : [],
            'default_max_age'         => env('SECURITY_LAYER_CORS_DEFAULT_MAX_AGE', 86400),
        ],
        'nonce' => [
            'enabled'      => env('SECURITY_LAYER_NONCE_ENABLED', true),
            'header_names' => [
                'nonce'       => env('SECURITY_LAYER_NONCE_HEADER', 'X-Nonce'),
                'customer_id' => env('SECURITY_LAYER_NONCE_CUSTOMER_ID_HEADER', 'X-Customer-Id'),
            ],
            'cache_ttl'             => env('SECURITY_LAYER_NONCE_CACHE_TTL', 300),
            'replay_protection_ttl' => env('SECURITY_LAYER_NONCE_REPLAY_PROTECTION_TTL', 3600),
        ],
        'timestamp' => [
            'enabled'     => env('SECURITY_LAYER_TIMESTAMP_ENABLED', true),
            'header_name' => env('SECURITY_LAYER_TIMESTAMP_HEADER', 'X-Timestamp'),
            'tolerance'   => env('SECURITY_LAYER_TIMESTAMP_TOLERANCE', 300),
        ],
        'signature' => [
            'enabled'        => env('SECURITY_LAYER_SIGNATURE_ENABLED', true),
            'header_name'    => env('SECURITY_LAYER_SIGNATURE_HEADER', 'X-Signature'),
            'secret'         => env('SECURITY_LAYER_SIGNATURE_SECRET'),
            'algorithm'      => env('SECURITY_LAYER_SIGNATURE_ALGORITHM', 'sha256'),
            'clock_skew'     => env('SECURITY_LAYER_SIGNATURE_CLOCK_SKEW', 300),
            'signed_headers' => [
                'X-Nonce',
                'X-Timestamp',
                'X-Customer-Id',
            ],
        ],
        'rate_limit' => [
            'enabled'        => env('SECURITY_LAYER_RATE_LIMIT_ENABLED', true),
            'secure_header'  => env('SECURITY_LAYER_RATE_LIMIT_SECURE_HEADER', 'X-Rate-Limit-Secure'),
            'global'         => [
                'limit'  => env('SECURITY_LAYER_RATE_LIMIT_GLOBAL_LIMIT', 200),
                'window' => env('SECURITY_LAYER_RATE_LIMIT_GLOBAL_WINDOW', 60),
            ],
            'customer' => [
                'limit'  => env('SECURITY_LAYER_RATE_LIMIT_CUSTOMER_LIMIT', 1000),
                'window' => env('SECURITY_LAYER_RATE_LIMIT_CUSTOMER_WINDOW', 60),
            ],
        ],
        'idempotency' => [
            'enabled'               => env('SECURITY_LAYER_IDEMPOTENCY_ENABLED', true),
            'header_name'           => env('SECURITY_LAYER_IDEMPOTENCY_HEADER', 'Idempotency-Key'),
            'cache_ttl'             => env('SECURITY_LAYER_IDEMPOTENCY_CACHE_TTL', 86400),
            'successful_http_codes' => env('SECURITY_LAYER_IDEMPOTENCY_SUCCESSFUL_HTTP_CODES') ? array_filter(explode(',', env('SECURITY_LAYER_IDEMPOTENCY_SUCCESSFUL_HTTP_CODES'))) : ['200', '201', '202'],
        ],
        'session' => [
            'enabled'    => env('SECURITY_LAYER_SESSION_ENABLED', true),
            'secret'     => env('SECURITY_LAYER_SESSION_SECRET', env('APP_KEY')),
            'ttl'        => env('SECURITY_LAYER_SESSION_TTL', 3600),
        ],
        'trace' => [
            'enabled'       => env('SECURITY_LAYER_TRACE_ENABLED', true),
            'methods'       => env('SECURITY_LAYER_TRACE_METHODS') ? array_filter(explode(',', env('SECURITY_LAYER_TRACE_METHODS'))) : ['GET', 'POST'],
            'content_types' => env('SECURITY_LAYER_TRACE_CONTENT_TYPES') ? array_filter(explode(',', env('SECURITY_LAYER_TRACE_CONTENT_TYPES'))) : ['application/json', 'text/html', 'text/plain', 'application/xml', 'application/x-www-form-urlencoded'],
            'sensitive'     => [
                'headers' => env('SECURITY_LAYER_TRACE_SENSITIVE_HEADERS') ? array_filter(explode(',', env('SECURITY_LAYER_TRACE_SENSITIVE_HEADERS'))) : ['cookie'],
                'cookies' => env('SECURITY_LAYER_TRACE_SENSITIVE_COOKIES') ? array_filter(explode(',', env('SECURITY_LAYER_TRACE_SENSITIVE_COOKIES'))) : [],
                'fields'  => env('SECURITY_LAYER_TRACE_SENSITIVE_FIELDS') ? array_filter(explode(',', env('SECURITY_LAYER_TRACE_SENSITIVE_FIELDS'))) : [],
            ],
        ],
    ],
    'modules' => [
        'test-module' => [
            'enabled'     => true,
            'features'    => [
                'cors' => [
                    'enabled'                 => env('SECURITY_LAYER_TEST_MODULE_CORS_ENABLED', true),
                    'allowed_origins'         => env('SECURITY_LAYER_TEST_MODULE_CORS_ALLOWED_ORIGINS') ? array_filter(explode(',', env('SECURITY_LAYER_TEST_MODULE_CORS_ALLOWED_ORIGINS'))) : ['https://test-module.example.com'],
                    'default_allowed_methods' => env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_METHODS') ? array_filter(explode(',', env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_METHODS'))) : ['GET', 'POST'],
                    'default_allowed_headers' => env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_HEADERS') ? array_filter(explode(',', env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_HEADERS'))) : ['Content-Type', 'X-Requested-With', 'Authorization', 'X-Customer-Id'],
                    'default_max_age'         => env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_MAX_AGE', 3600),
                ],
                'rate_limit' => [
                    'enabled'        => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_ENABLED', true),
                    'secure_header'  => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_SECURE_HEADER', 'X-Rate-Limit-Secure'),
                    'global'         => [
                        'limit'  => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_GLOBAL_LIMIT', 5),
                        'window' => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_GLOBAL_WINDOW', 1),
                    ],
                    'customer' => [
                        'limit'  => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_CUSTOMER_LIMIT', 1),
                        'window' => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_CUSTOMER_WINDOW', 1),
                    ],
                ],
            ],
        ],
    ],
];
