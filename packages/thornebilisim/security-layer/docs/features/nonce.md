# Özellik: Nonce <PERSON> (Nonce)

`thornebilisim/security-layer` pake<PERSON><PERSON> `NonceMiddleware` <PERSON><PERSON><PERSON><PERSON><PERSON>, "tek kullanımlık numara" anlamına gelen **nonce** kull<PERSON>rak tekrar oynatma (replay) saldırılarına karşı API'nizi korur. <PERSON><PERSON> mekan<PERSON><PERSON>, her isteğin benzersiz olmasını ve yetkisiz tekrarların engellenmesini sağlar.

## Tanıtım

Tekrar oynatma saldırısı, kötü niyetli bir aktörün geçerli bir isteği yakalayıp daha sonra aynı isteği tekrar göndererek yetkisiz bir işlem yapmaya çalışmasıdır. <PERSON><PERSON><PERSON><PERSON>, bir ödeme isteğinin birden çok kez gönderilmesi istenmeyen çift ödemelere yol açabilir. Non<PERSON> mekanizması, her iste<PERSON><PERSON> ben<PERSON><PERSON>, rastgele ve tek kullanımlık bir değer eklenmesini zorunlu kılar. <PERSON><PERSON><PERSON>, bu nonce'ın daha önce hiç kullanılmadığını doğrular ve her başarılı kullanımdan sonra onu bir süre için geçersiz kılar. Bu sayede, aynı nonce ile yapılan sonraki tüm istekler reddedilir.

`NonceMiddleware`, bu doğrulamayı otomatize ederek API'nize ek bir güvenlik katmanı sağlar.

## Çalışma Prensibi

`NonceMiddleware`, gelen her isteği işlerken nonce doğrulamalarını aşağıdaki adımlara göre uygular:

1.  **Konfigürasyon Çözümleme ve Etkinlik Kontrolü**: Middleware, `ResolvesModule` trait'ini kullanarak `nonce` özelliğine özel yapılandırmayı (global, modül veya inline) dinamik olarak belirler. Eğer nonce özelliği yapılandırmada etkin değilse, middleware işlemeyi atlar ve isteği bir sonraki adıma iletir.
2.  **Gerekli Başlıkları Kontrol Et**: İstekte yapılandırmada belirtilen nonce başlığının (varsayılan `X-Nonce`) ve müşteri ID başlığının (varsayılan `X-Customer-Id`) mevcut olup olmadığını kontrol eder. Bu başlıklar eksikse, bir hata yanıtı (400 Bad Request) döndürülür.
3.  **Önbellek Anahtarlarını Oluştur**: Müşteri ID ve nonce değerlerini kullanarak, önbellekte bu nonce'ın daha önce kullanılıp kullanılmadığını kontrol etmek için iki anahtar oluşturulur:
    *   **Nonce Anahtarı**: Nonce'ın aktif kullanım süresini (cache\_ttl) izler.
    *   **Replay Koruma Anahtarı**: Nonce'ın daha uzun bir süre (replay\_protection\_ttl) boyunca tekrar kullanılmasını engeller.
4.  **Tekrar Oynatma Saldırısı Kontrolü**: Replay koruma anahtarı önbellekte mevcutsa, bu, nonce'ın yakın zamanda kullanıldığını ve potansiyel bir tekrar oynatma saldırısı olduğunu gösterir. Bu durumda, bir hata yanıtı (409 Conflict) döndürülür.
5.  **Tekrarlayan Nonce Kontrolü**: Nonce anahtarı önbellekte mevcutsa, bu, nonce'ın `cache_ttl` süresi içinde zaten kullanıldığı anlamına gelir. Bu durumda, bir hata yanıtı (409 Conflict) döndürülür.
6.  **Nonce'ı Kaydet**: Tüm kontrollerden başarıyla geçilirse, nonce anahtarı ve replay koruma anahtarı önbelleğe kaydedilir. Nonce anahtarı, nonce'ın kullanıldığı zaman, IP adresi ve kullanıcı aracısı gibi detayları içerir.
7.  **İsteği İlerlet**: İstek, doğrulandıktan sonra bir sonraki middleware'e veya rota işleyicisine iletilir.

## Yapılandırma

`NonceMiddleware`'ın davranışı `config/security-layer.php` dosyasındaki `features.nonce` bölümünde yapılandırılır. Modüller aracılığıyla veya rota üzerinde inline olarak da özelleştirilebilir (öncelik sırası için [Rota Tanımlamaları ve Middleware Kullanımı](/docs/routing.md) bölümüne bakınız).

```php
// config/security-layer.php

'features' => [
    'nonce' => [
        'enabled'      => env('SECURITY_LAYER_NONCE_ENABLED', true),
        'header_names' => [
            'nonce'       => env('SECURITY_LAYER_NONCE_HEADER', 'X-Nonce'),
            'customer_id' => env('SECURITY_LAYER_NONCE_CUSTOMER_ID_HEADER', 'X-Customer-Id'),
        ],
        'cache_ttl'             => env('SECURITY_LAYER_NONCE_CACHE_TTL', 300),
        'replay_protection_ttl' => env('SECURITY_LAYER_NONCE_REPLAY_PROTECTION_TTL', 3600),
    ],
    // ... diğer özellikler
],
```

*   **`enabled`**: Nonce middleware'ını etkinleştirir veya devre dışı bırakır.
*   **`header_names`**:
    *   **`nonce`**: İstekte beklenecek nonce değerini içeren HTTP başlığının adı (varsayılan `X-Nonce`).
    *   **`customer_id`**: Nonce'ın hangi müşteriyle ilişkili olduğunu belirten müşteri ID'sini içeren HTTP başlığının adı (varsayılan `X-Customer-Id`). Nonce'lar genellikle müşteri bazında benzersiz olmalıdır.
*   **`cache_ttl`**: Bir nonce'ın önbellekte ne kadar süreyle geçerli kabul edileceğini belirleyen süre (saniye). Bu, nonce'ın "tazelik" süresidir.
*   **`replay_protection_ttl`**: Bir nonce'ın tekrar oynatma saldırılarına karşı korunacağı daha uzun süre (saniye). Bu süre içinde aynı nonce'ın yeniden kullanılması, nonce'ın `cache_ttl`'i dolmuş olsa bile bir saldırı olarak algılanır.

## Yaşam Döngüsü

Aşağıdaki şema, `NonceMiddleware`'ın bir HTTP isteğinin yaşam döngüsü içindeki yerini ve işleyişini göstermektedir. Diagram sözdiziminden kaynaklanan kutu ve karar düğümü parantezleri hariç, tüm metinler Türkçe karakterlerle ve doğru soru ekleri ile yazılmıştır.

```mermaid
graph TD
    A[Gelen HTTP İsteği] --> B[NonceMiddleware Handle Metodu];

    B --> C{Konfigürasyon: Nonce Etkin mi?};

    C -- Hayır --> D[İsteği Uygulama Akışına Aktar];
    D --> K[Yanıtı Döndür];

    C -- Evet --> E{X-Nonce ve X-Customer-Id Başlıkları Mevcut mu?};

    E -- Hayır --> F[Eksik Nonce Başlığı Hatası Oluştur];
    F --> K;

    E -- Evet --> G[Nonce ve Replay Koruma Cache Anahtarlarını Oluştur];

    G --> H{Replay Koruma Anahtarı Önbellekte Mevcut mu?};
    H -- Evet --> I[Replay Saldırısı Hatası Oluştur];
    I --> K;

    H -- Hayır --> J{Nonce Anahtarı Önbellekte Mevcut mu?};
    J -- Evet --> L[Tekrarlayan Nonce Hatası Oluştur];
    L --> K;

    J -- Hayır --> M[Nonce ve Replay Anahtarlarını Önbelleğe Kaydet];
    M --> D;
```

**Açıklama:**

1.  Gelen her HTTP isteği `NonceMiddleware`'ın `handle()` metoduna ulaşır.
2.  Middleware, `nonce` özelliğinin yapılandırmada etkin olup olmadığını kontrol eder.
3.  Eğer `nonce` `Hayır` (devre dışı) ise, istek doğrudan bir sonraki adıma (`İsteği Uygulama Akışına Aktar`) iletilir.
4.  Eğer `nonce` `Evet` (etkin) ise, `X-Nonce` ve `X-Customer-Id` başlıklarının istekte mevcut olup olmadığı kontrol edilir.
5.  Başlıklar `Hayır` (eksik) ise, bir `Eksik Nonce Başlığı Hatası Oluştur`ulur ve yanıt döndürülür.
6.  Başlıklar `Evet` (mevcut) ise, nonce ve replay koruma için önbellek anahtarları oluşturulur.
7.  Önbellekte `Replay Koruma Anahtarı Mevcut mu?` diye kontrol edilir. `Evet` ise, `Replay Saldırısı Hatası Oluştur`ulur ve yanıt döndürülür.
8.  `Replay Koruma Anahtarı` `Hayır` (mevcut değil) ise, `Nonce Anahtarı Önbellekte Mevcut mu?` diye kontrol edilir. `Evet` ise, `Tekrarlayan Nonce Hatası Oluştur`ulur ve yanıt döndürülür.
9.  `Nonce Anahtarı` `Hayır` (mevcut değil) ise, nonce ve replay anahtarları önbelleğe kaydedilir (`Nonce ve Replay Anahtarlarını Önbelleğe Kaydet`).
10. Nonce doğrulamasından başarıyla geçildikten sonra, istek bir sonraki middleware'e veya rota işleyicisine (`İsteği Uygulama Akışına Aktar`) iletilir.
11. Tüm senaryolarda, sonunda bir yanıt istemciye döndürülür.

## Faydaları

*   **Tekrar Oynatma Saldırısı Koruması**: API'nize yönelik en yaygın saldırı türlerinden biri olan tekrar oynatma saldırılarını etkili bir şekilde engeller.
*   **İşlemsel Bütünlük**: Özellikle ödeme işlemleri gibi kritik işlemlerde, aynı işlemin birden çok kez tetiklenmesini önleyerek veri tutarlılığını sağlar.
*   **Güvenilirlik**: API'nizin güvenilirliğini artırır ve beklenmedik davranışların önüne geçer.
*   **Esnek Konfigürasyon**: Nonce'ın geçerlilik sürelerini ve başlık isimlerini uygulama ihtiyaçlarınıza göre ayarlayabilirsiniz.

## Kullanım Örneği

`NonceMiddleware`'ı, özellikle POST, PUT, DELETE gibi veri değiştiren veya hassas işlemler içeren rotalarınıza atamalısınız.

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::post('/payments/process', function () {
    // Ödeme işlemi mantığı
    return response()->json(['status' => 'Ödeme başarıyla işlendi.']);
})->middleware([
    'security-layer.nonce', // Her ödeme isteği benzersiz bir nonce içermeli
    'security-layer.timestamp',
    'security-layer.signature',
]);

// Modül bazlı kullanım örneği (config/security-layer.php içinde tanımlanmış 'billing-module' varsayımıyla)
Route::post('/billing/invoice', function () {
    // Fatura oluşturma işlemi
    return response()->json(['status' => 'Fatura oluşturuldu.']);
})->middleware('security-layer.billing-module.nonce');
```

Bu örneklerde, `/payments/process` ve `/billing/invoice` rotaları, tekrar oynatma saldırılarına karşı `NonceMiddleware` tarafından korunmaktadır. İstemcilerin, her istekte benzersiz `X-Nonce` ve ilgili `X-Customer-Id` başlıklarını göndermeleri gerekecektir.