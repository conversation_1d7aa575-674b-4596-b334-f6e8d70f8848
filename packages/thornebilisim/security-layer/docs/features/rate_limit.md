# Özellik: <PERSON><PERSON><PERSON> (Rate Limit)

`thornebilisim/security-layer` paketinin `RateLimitMiddleware` <PERSON><PERSON><PERSON><PERSON><PERSON>, API'nize gelen istek trafiğini kontrol ederek, belirli bir zaman dilimi içinde izin verilen istek sayısını sınırlamanızı sağlar. Bu mekanizma, kötüye kullanımın, kaba kuvvet saldırılarının ve hizmet reddi (DoS) ataklarının önüne geçmek için hayati öneme sahiptir.

## Tanıtım

Hız sınırlama, bir API'nin istikrarını ve kullanılabilirliğini korumanın temel bir yöntemidir. Bir istemcinin kısa sürede aşırı sayıda istek göndermesini engelleyerek, sunucu kaynaklarının tükenmesini veya diğer meşru kullanıcılar için hizmet kalitesinin düşmesini önler. `RateLimitMiddleware`, hem genel (IP bazında) hem de müşteriye özel (müşteri ID bazında) hız sınırlamaları uygulayabilir, bu da API'nizin kullanımını daha granüler bir şekilde yönetmenize olanak tanır. Ayrıca, belirli güvenli istekler için hız sınırlamasını atlama seçeneği de sunar.

## Çalışma Prensibi

`RateLimitMiddleware`, gelen her isteği işlerken hız sınırlama kurallarını aşağıdaki adımlara göre uygular:

1.  **Konfigürasyon Çözümleme ve Etkinlik Kontrolü**: Middleware, `ResolvesModule` trait'ini kullanarak `rate_limit` özelliğine özel yapılandırmayı (global, modül veya inline) dinamik olarak belirler. Eğer hız sınırlama özelliği yapılandırmada etkin değilse, middleware işlemeyi atlar ve isteği bir sonraki adıma iletir.
2.  **Bypass Başlığı Kontrolü**: Eğer yapılandırmada belirtilen özel bir `secure_header` (varsayılan `X-Rate-Limit-Secure`) isteğin başlıklarında mevcutsa, bu istek hız sınırlamasını atlar ve doğrudan işleme devam eder. Bu, dahili servisler veya güvenilir kaynaklar için kullanışlıdır.
3.  **Hız Limiti Kontrolü**:
    *   İsteğin `X-Customer-Id` başlığına sahip olup olmadığına bakılır.
    *   **Global Limit**: Her zaman uygulanır ve isteğin IP adresi bazında sayım yapar.
    *   **Müşteri Limiti**: Eğer `X-Customer-Id` başlığı varsa, bu müşteri ID'si bazında ek bir hız sınırlaması uygulanır.
    *   Hem global hem de müşteri limitleri aynı anda kontrol edilir. Eğer ikisi de aşılmışsa, istemcinin daha uzun süre beklemesi gereken limit (daha yüksek `Retry-After` değeri) uygulanır.
4.  **Limit Aşımı Yanıtı**: Eğer herhangi bir hız limiti aşılmışsa, middleware isteği reddeder ve `429 Too Many Requests` HTTP durumuyla bir hata yanıtı döndürür. Bu yanıta, istemcinin tekrar denemeden önce beklemesi gereken süreyi belirten `Retry-After` başlığı da eklenir.
5.  **Limitleri Güncelleme**: Eğer istek hız limitini aşmamışsa, ilgili hız sınırlayıcılar (global ve/veya müşteri bazında) "hit" edilir (yani sayaçları artırılır).
6.  **Yanıt Başlıkları Ekleme**: İstek başarıyla işlenip bir yanıt döndürüldüğünde, middleware bu yanıta `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset` ve `X-RateLimit-Policy` gibi bilgilendirici başlıklar ekler. Bu başlıklar, istemcilerin mevcut hız sınırlama durumlarını izlemelerine olanak tanır.

## Yapılandırma

`RateLimitMiddleware`'ın davranışı `config/security-layer.php` dosyasındaki `features.rate_limit` bölümünde yapılandırılır. Modüller aracılığıyla veya rota üzerinde inline olarak da özelleştirilebilir (öncelik sırası için [Rota Tanımlamaları ve Middleware Kullanımı](/docs/routing.md) bölümüne bakınız).

```php
// config/security-layer.php

'features' => [
    'rate_limit' => [
        'enabled'        => env('SECURITY_LAYER_RATE_LIMIT_ENABLED', true),
        'secure_header'  => env('SECURITY_LAYER_RATE_LIMIT_SECURE_HEADER', 'X-Rate-Limit-Secure'),
        'global'         => [
            'limit'  => env('SECURITY_LAYER_RATE_LIMIT_GLOBAL_LIMIT', 200),
            'window' => env('SECURITY_LAYER_RATE_LIMIT_GLOBAL_WINDOW', 60),
        ],
        'customer' => [
            'limit'  => env('SECURITY_LAYER_RATE_LIMIT_CUSTOMER_LIMIT', 1000),
            'window' => env('SECURITY_LAYER_RATE_LIMIT_CUSTOMER_WINDOW', 60),
        ],
    ],
    // ... diğer özellikler
],
```

*   **`enabled`**: Hız sınırlama middleware'ını etkinleştirir veya devre dışı bırakır.
*   **`secure_header`**: Bu başlık, değerine bakılmaksızın varlığı halinde hız sınırlamasını atlamayı sağlar. İç servis çağrıları veya özel izinli sistemler için kullanılabilir.
*   **`global`**: Genel (IP bazında) hız sınırlama ayarları:
    *   **`limit`**: Belirli bir `window` süresi içinde izin verilen maksimum istek sayısı.
    *   **`window`**: Hız sınırlamasının uygulandığı pencere süresi (saniye).
*   **`customer`**: Müşteri bazında (X-Customer-Id başlığına göre) hız sınırlama ayarları:
    *   **`limit`**: Belirli bir `window` süresi içinde izin verilen maksimum istek sayısı.
    *   **`window`**: Hız sınırlamasının uygulandığı pencere süresi (saniye). Genellikle global limitten daha yüksek tutulur.

## Yaşam Döngüsü

Aşağıdaki şema, `RateLimitMiddleware`'ın bir HTTP isteğinin yaşam döngüsü içindeki yerini ve işleyişini göstermektedir. Diagram sözdiziminden kaynaklanan kutu ve karar düğümü parantezleri hariç, tüm metinler Türkçe karakterlerle ve doğru soru ekleri ile yazılmıştır.

```mermaid
graph TD
    A[Gelen HTTP İsteği] --> B[RateLimitMiddleware Handle Metodu];

    B --> C{Konfigürasyon: Rate Limit Etkin mi?};

    C -- Hayır --> D[İsteği Uygulama Akışına Aktar];
    D --> L[Yanıtı Döndür];

    C -- Evet --> E{Bypass Başlığı Mevcut mu?};

    E -- Evet --> D;

    E -- Hayır --> F{Müşteri ID Mevcut mu?};

    F -- Hayır --> G[Global Limit Kontrol Et];
    F -- Evet --> H[Global ve Müşteri Limitlerini Kontrol Et];

    G --> I{Limitler Aşıldı mı?};
    H --> I;

    I -- Evet --> J[Hız Sınırı Aşıldı Hatası Oluştur];
    J --> L;

    I -- Hayır --> K[Limitleri Güncelle ve İsteği Aktar];
    K --> M[Uygulamadan Yanıt Al];
    M --> N[Yanıt Başlıklarına Rate Limit Bilgilerini Ekle];
    N --> L;
```

**Açıklama:**

1.  Gelen her HTTP isteği `RateLimitMiddleware`'ın `handle()` metoduna ulaşır.
2.  Middleware, `rate_limit` özelliğinin yapılandırmada etkin olup olmadığını kontrol eder.
3.  Eğer `rate_limit` `Hayır` (devre dışı) ise, istek doğrudan bir sonraki adıma (`İsteği Uygulama Akışına Aktar`) iletilir.
4.  Eğer `rate_limit` `Evet` (etkin) ise, `secure_header` olarak tanımlanan `Bypass Başlığı Mevcut mu?` diye kontrol edilir.
5.  Eğer `Evet` (başlık mevcut) ise, hız sınırlaması atlanır ve istek doğrudan bir sonraki adıma (`İsteği Uygulama Akışına Aktar`) iletilir.
6.  Eğer `Hayır` (bypass başlığı yok) ise, isteğin `X-Customer-Id` başlığı ile `Müşteri ID Mevcut mu?` diye kontrol edilir.
7.  `Müşteri ID` `Hayır` ise sadece `Global Limit Kontrol Et` işlemi yapılır.
8.  `Müşteri ID` `Evet` ise `Global ve Müşteri Limitlerini Kontrol Et` işlemi yapılır.
9.  Her iki durumda da `Limitler Aşıldı mı?` diye kontrol edilir.
10. Eğer `Evet` (limitler aşılmış) ise, bir `Hız Sınırı Aşıldı Hatası Oluştur`ulur ve yanıt döndürülür.
11. Eğer `Hayır` (limitler aşılmamış) ise, ilgili `Limitleri Güncelle ve İsteği Aktar` işlemi yapılır.
12. Uygulamadan bir yanıt alındıktan sonra, bu yanıta `Yanıt Başlıklarına Rate Limit Bilgilerini Ekle` (Limit, Remaining, Reset vb.) eklenir.
13. Tüm senaryolarda, sonunda bir yanıt istemciye döndürülür.

## Faydaları

*   **DDoS/Kaba Kuvvet Koruması**: Aşırı istek bombardımanını engelleyerek API'nizi ve sunucu kaynaklarınızı korur.
*   **Kaynak Optimizasyonu**: Sunucu kaynaklarının meşru kullanıcılara ayrılmasını sağlayarak hizmet kalitesini artırır.
*   **API Kullanımını Yönetme**: API'nizin kullanımını ve suistimalini etkin bir şekilde kontrol etmenizi sağlar.
*   **İstemci Bilgilendirmesi**: `X-RateLimit-*` başlıkları aracılığıyla istemcilere hız sınırlaması durumları hakkında şeffaf bilgi sağlar, böylece kendi istek oranlarını ayarlayabilirler.
*   **Esnek Kontrol**: Hem genel hem de müşteri bazında limitler belirleyerek API'nizi farklı kullanıcı gruplarına göre farklı seviyelerde koruyabilirsiniz.

## Kullanım Örneği

`RateLimitMiddleware`'ı, API'nizdeki genel olarak veya belirli hassas/yoğun kullanılan rotalarınıza uygulayabilirsiniz.

**Global Rate Limit Uygulaması:**

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::middleware(['security-layer.rate_limit']) // Tüm API rotalarına global hız sınırlaması uygula
    ->prefix('v1')
    ->group(function () {
        Route::get('/public-data', function () {
            return response()->json(['data' => 'Herkese açık veri.']);
        });

        Route::post('/submit-form', function () {
            return response()->json(['status' => 'Form gönderildi.']);
        });
    });
```

**Modül Bazlı Rate Limit Konfigürasyonu (örneğin 'search-api' modülü için):**

Önce `config/security-layer.php` dosyanızda `search-api` modülünü tanımlayın, daha dar limitlerle:

```php
// config/security-layer.php
'modules' => [
    'search-api' => [
        'enabled' => true,
        'features' => [
            'rate_limit' => [
                'global' => [
                    'limit'  => 10,  // Örnek: Bu modül için global limit 10 istek/dk
                    'window' => 60,
                ],
                'customer' => [
                    'limit'  => 50, // Örnek: Müşteri bazında 50 istek/dk
                    'window' => 60,
                ],
            ],
        ],
    ],
],
```

Sonra rotanızda bu modülü kullanın:

```php
// routes/api.php
Route::middleware(['security-layer.search-api.rate_limit'])
    ->prefix('search')
    ->group(function () {
        Route::get('/query', function () {
            return response()->json(['results' => []]);
        });
    });
```

**Inline Rate Limit Konfigürasyonu (belirli bir rota için özel bir limit):**

```php
// routes/api.php
use Illuminate\Support\Facades\Route;
use function security_layer_inline_config;

Route::post('/high-cost-operation', function () {
    return response()->json(['status' => 'İşlem tamamlandı.']);
})->middleware('security-layer.rate_limit:'.security_layer_inline_config([
    'global' => [
        'limit'  => 1, // Bu rotaya dakikada sadece 1 istek
        'window' => 60,
    ],
]));
```

`RateLimitMiddleware` ile API'nizin performansını koruyabilir ve kötüye kullanım senaryolarına karşı güçlü bir savunma hattı oluşturabilirsiniz.