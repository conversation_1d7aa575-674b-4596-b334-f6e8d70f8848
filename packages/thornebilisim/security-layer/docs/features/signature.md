# Özellik: İstek İmza Doğrulaması (Signature)

`thornebilisim/security-layer` paketinin `SignatureMiddleware` özelliği, API isteklerinizin veri bütünlüğünü ve kimliğini HMAC (Hash-based Message Authentication Code) tabanlı dijital imzalar kullanarak doğrular. Bu, bir isteğin iletim sırasında değiştirilmediğinden ve yetkili bir istemci tarafından gönderildiğinden emin olmanızı sağlar.

## Tanıtım

Güvenli bir API iletişiminde, sadece verilerin şifrelenmesi (HTTPS ile sağlanan) yeterli değildir. Aynı zamanda verilerin değiştirilmediğinden (bütünlük) ve gönderenin gerçekten iddia ettiği kişi olduğundan (kimlik doğrulama) emin olmak gerekir. İstek imzalama, bu ihtiyaçları karşılayan güçlü bir mekanizmadır.

`SignatureMiddleware`, be<PERSON><PERSON><PERSON> başlıklarını (örneğin `X-<PERSON><PERSON>`, `X-Timestamp`, `X-Customer-Id`) ve isteğin gövde içeriğini (payload) gizli bir anahtar (`secret`) ile birleştirerek bir kriptografik karma (hash) oluşturur. Bu karma, `X-Signature` başlığı olarak istekle birlikte gönderilir. Sunucu tarafında middleware, aynı işlemi yaparak kendi imzasını hesaplar ve istemcinin gönderdiği imza ile karşılaştırır. Eşleşme varsa, isteğin güvenli olduğu kabul edilir.

Bu özellik, özellikle üçüncü taraf entegrasyonları ve hassas işlemler için inkar edilemezlik (non-repudiation) sağlar, yani bir istemcinin gönderdiği isteği daha sonra reddetmesi zorlaşır.

## Çalışma Prensibi

`SignatureMiddleware`, gelen her isteği işlerken imza doğrulamalarını aşağıdaki adımlara göre uygular:

1.  **Konfigürasyon Çözümleme ve Etkinlik Kontrolü**: Middleware, `ResolvesModule` trait'ini kullanarak `signature` özelliğine özel yapılandırmayı (global, modül veya inline) dinamik olarak belirler. Eğer imza özelliği yapılandırmada etkin değilse, middleware işlemeyi atlar ve isteği bir sonraki adıma iletir.
2.  **İmza Başlığı Kontrolü**: İstekte yapılandırmada belirtilen imza başlığının (varsayılan `X-Signature`) mevcut olup olmadığını kontrol eder. Bu başlık eksikse, bir hata yanıtı (401 Unauthorized) döndürülür.
3.  **Zaman Damgası Kontrolü**: İmza hesaplamasının önemli bir parçası olan `X-Timestamp` başlığının varlığını ve değerinin yapılandırılan `clock_skew` toleransı içinde olup olmadığını kontrol eder. Eğer zaman damgası eksik veya tolerans dışındaysa, geçersiz bir zaman damgası hatası (400 Bad Request) döndürülür. (Bu kontrol, `TimestampMiddleware` ile birleştirilmiş olsa da, imza doğrulaması için kritik olduğu için burada da temel bir kontrol yapılır.)
4.  **İmza Verisi Oluşturma**: Yapılandırmada `signed_headers` olarak belirtilen HTTP başlıklarının değerleri (alfabetik olarak sıralanmış) ve isteğin ham gövde içeriği (payload) alınır. Bu veriler, tek bir JSON dizesi halinde birleştirilerek imza hesaplaması için "veri" oluşturulur.
5.  **Sunucu İmzasını Hesapla**: Oluşturulan "veri" dizesi, yapılandırılan `algorithm` (örn. `sha256`) ve paketin gizli anahtarı (`secret`) kullanılarak HMAC algoritmasıyla hash'lenir. Bu, sunucu tarafında beklenen imzadır.
6.  **İmza Karşılaştırması**: Hesaplanan sunucu imzası (`serverSignature`), istemcinin `X-Signature` başlığı aracılığıyla gönderdiği imza (`clientSignature`) ile güvenli bir şekilde (`hash_equals()` fonksiyonu kullanılarak) karşılaştırılır. `hash_equals()` fonksiyonu, zamanlama saldırılarına karşı koruma sağlar.
7.  **Doğrulama Sonucu**:
    *   İmzalar eşleşmiyorsa, geçersiz imza hatası (401 Unauthorized) döndürülür.
    *   Tüm doğrulamalardan başarıyla geçilirse, istek bir sonraki middleware'e veya rota işleyicisine iletilir.

## Yapılandırma

`SignatureMiddleware`'ın davranışı `config/security-layer.php` dosyasındaki `features.signature` bölümünde yapılandırılır. Modüller aracılığıyla veya rota üzerinde inline olarak da özelleştirilebilir (öncelik sırası için [Rota Tanımlamaları ve Middleware Kullanımı](/docs/routing.md) bölümüne bakınız).

```php
// config/security-layer.php

'features' => [
    'signature' => [
        'enabled'        => env('SECURITY_LAYER_SIGNATURE_ENABLED', true),
        'header_name'    => env('SECURITY_LAYER_SIGNATURE_HEADER', 'X-Signature'),
        'secret'         => env('SECURITY_LAYER_SIGNATURE_SECRET'), // KRİTİK: Güvenli bir anahtar olmalı
        'algorithm'      => env('SECURITY_LAYER_SIGNATURE_ALGORITHM', 'sha256'),
        'clock_skew'     => env('SECURITY_LAYER_SIGNATURE_CLOCK_SKEW', 300),
        'signed_headers' => [ // İmza hesaplamasına dahil edilecek başlıklar
            'X-Nonce',
            'X-Timestamp',
            'X-Customer-Id',
        ],
    ],
    // ... diğer özellikler
],
```

*   **`enabled`**: İmza middleware'ını etkinleştirir veya devre dışı bırakır.
*   **`header_name`**: İstekte beklenecek dijital imza değerini içeren HTTP başlığının adı (varsayılan `X-Signature`).
*   **`secret`**: **ÇOK KRİTİK**: HMAC imzasını oluşturmak ve doğrulamak için kullanılacak gizli anahtar. Bu, yalnızca sunucu ve yetkili istemci arasında bilinen güçlü, rastgele bir dize olmalıdır. Kesinlikle `.env` dosyasında saklanmalı ve versiyon kontrolüne dahil edilmemelidir.
*   **`algorithm`**: İmza için kullanılacak hash algoritması (örn. `sha256`, `sha512`). İstemci ve sunucu aynı algoritmayı kullanmalıdır.
*   **`clock_skew`**: Zaman damgası doğrulaması için kabul edilebilir maksimum saat kayması (saniye). İstek zaman damgası ile sunucu saati arasındaki fark bu değeri aşmamalıdır.
*   **`signed_headers`**: İmza hesaplamasına dahil edilecek HTTP başlıklarının bir listesi. Bu başlıkların değerleri, isteğin gövdesi ile birlikte imzalanacak veri setini oluşturur. Bu başlıklar, istemci tarafından da aynı şekilde gönderilmeli ve değerleri tam olarak eşleşmelidir. Listeye eklenen başlıklar alfabetik olarak sıralanarak imzaya dahil edilir.

## Yaşam Döngüsü

Aşağıdaki şema, `SignatureMiddleware`'ın bir HTTP isteğinin yaşam döngüsü içindeki yerini ve işleyişini göstermektedir. Diagram sözdiziminden kaynaklanan kutu ve karar düğümü parantezleri hariç, tüm metinler Türkçe karakterlerle ve doğru soru ekleri ile yazılmıştır.

```mermaid
graph TD
    A[Gelen HTTP İsteği] --> B[SignatureMiddleware Handle Metodu];

    B --> C{Konfigürasyon İmza Etkin mi?};

    C -- Hayır --> D[İsteği Uygulama Akışına Aktar];
    D --> M[Yanıtı Döndür];

    C -- Evet --> E{X-Signature Başlığı Mevcut mu?};

    E -- Hayır --> F[Eksik İmza Hatası Oluştur];
    F --> M;

    E -- Evet --> G{X-Timestamp Başlığı ve Tolerans Geçerli mi?};

    G -- Hayır --> H[Geçersiz Zaman Damgası Hatası Oluştur];
    H --> M;

    G -- Evet --> I[İmza Verisi Oluştur];
    I --> J[HMAC İmza Hesapla];

    J --> K{İmzalar Eşleşiyor mu?};

    K -- Hayır --> L[Geçersiz İmza Hatası Oluştur];
    L --> M;

    K -- Evet --> N[İmza Geçerli Günlükle];
    N --> D;
```

**Açıklama:**

1.  Gelen her HTTP isteği `SignatureMiddleware`'ın `handle()` metoduna ulaşır.
2.  Middleware, `signature` özelliğinin yapılandırmada etkin olup olmadığını kontrol eder.
3.  Eğer `signature` `Hayır` (devre dışı) ise, istek doğrudan bir sonraki adıma (`İsteği Uygulama Akışına Aktar`) iletilir.
4.  Eğer `signature` `Evet` (etkin) ise, `X-Signature` başlığının istekte mevcut olup olmadığı kontrol edilir.
5.  Başlık `Hayır` (eksik) ise, bir `Eksik İmza Hatası Oluştur`ulur ve yanıt döndürülür.
6.  Başlık `Evet` (mevcut) ise, `X-Timestamp` başlığının varlığı ve yapılandırılan `clock_skew` toleransı içinde olup olmadığı kontrol edilir.
7.  Eğer `Hayır` (geçersiz zaman damgası) ise, bir `Geçersiz Zaman Damgası Hatası Oluştur`ulur ve yanıt döndürülür.
8.  Eğer `Evet` (geçerli zaman damgası) ise, `signed_headers` ve isteğin gövde içeriği kullanılarak imza hesaplaması için `İmza Verisi Oluştur`ulur.
9.  Oluşturulan veri ve gizli anahtar kullanılarak `HMAC İmza Hesapla`nır.
10. Hesaplanan sunucu imzası ile istemcinin gönderdiği imzanın `İmzalar Eşleşiyor mu?` diye karşılaştırılır.
11. İmzalar `Hayır` (eşleşmiyor) ise, bir `Geçersiz İmza Hatası Oluştur`ulur ve yanıt döndürülür.
12. İmzalar `Evet` (eşleşiyor) ise, `İmza Geçerli Günlükle` mesajı kaydedilir ve istek bir sonraki middleware'e veya rota işleyicisine (`İsteği Uygulama Akışına Aktar`) iletilir.
13. Tüm senaryolarda, sonunda bir yanıt istemciye döndürülür.

## Faydaları

*   **Veri Bütünlüğü**: İsteklerin iletim sırasında değiştirilmediğinden emin olunmasını sağlar.
*   **Kimlik Doğrulama**: İsteği gönderen istemcinin kimliğini gizli anahtar bilgisiyle doğrular.
*   **İnkâr Edilemezlik**: Bir istemcinin geçerli bir istek gönderdiğini daha sonra reddetmesini zorlaştırır.
*   **Tekrar Oynatma Saldırısı Koruması**: Zaman damgası ve nonce ile birleştirildiğinde, eski imzalı isteklerin tekrar oynatılmasını önler.
*   **Güçlü Güvenlik Katmanı**: API güvenliğinizi önemli ölçüde artıran kriptografik olarak sağlam bir yöntem sunar.
*   **Esnek Kapsam**: Hangi başlıkların imzaya dahil edileceğini belirleyerek imzanın kapsamını özelleştirebilirsiniz.

## Kullanım Örneği

`SignatureMiddleware`'ı, API'nizdeki kritik ve hassas işlemler (örneğin, ödeme işlemleri, kullanıcı kaydı, yetki değişiklikleri) içeren rotalarınıza atamalısınız. Genellikle `NonceMiddleware` ve `TimestampMiddleware` ile birlikte güçlü bir güvenlik zinciri oluşturur.

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::post('/api/secure-action', function () {
    // Çok önemli bir işlem
    return response()->json(['status' => 'Güvenli işlem başarıyla tamamlandı.']);
})->middleware([
    'security-layer.nonce',     // Tekrarlayan istekleri engelle
    'security-layer.timestamp', // Zaman damgası kontrolü
    'security-layer.signature', // İstek bütünlüğü ve kimlik doğrulaması
]);

// Modül bazlı kullanım örneği (config/security-layer.php içinde tanımlanmış 'finance-api' varsayımıyla)
Route::put('/finance/balance-update', function () {
    // Finansal bakiye güncelleme işlemi
    return response()->json(['status' => 'Bakiye güncellendi.']);
})->middleware([
    'security-layer.finance-api.nonce',
    'security-layer.finance-api.timestamp',
    'security-layer.finance-api.signature',
]);
```

İstemcilerin, imza oluşturma sürecini doğru bir şekilde uygulamaları ve gerekli tüm başlıkları (imza, nonce, zaman damgası, müşteri ID vb.) eksiksiz ve doğru değerlerle göndermeleri çok önemlidir. İmza anahtarının güvenliği, bu özelliğin etkinliği için en kritik faktördür.