# Özellik: HTTPS Zorunluluğu (HTTPS)

`thornebilisim/security-layer` paketinin `HttpsMiddleware` <PERSON>zelliği, tüm API isteklerinizin güvenli HTTPS bağlantıları üzerinden yapılmasını zorunlu kılar. Bu, verilerinizin aktarım sırasında şifrelenmesini sağlayarak "ortadaki adam" (Man-in-the-Middle) saldırıları gibi dinleme ve kurcalama risklerini azaltır.

## Tanıtım

Güvenli iletişim, modern API'lerin temel bir gereksinimidir. `HttpsMiddleware`, gelen her isteği kontrol ederek HTTPS protokolü üzerinden gelip gelmediğini doğrular. Eğer istek güvenli değilse ve HTTPS zorunluluğu etkinleştirilmişse, middleware yapılandırılan bir HTTP durum kodu (varsayılan olarak 301) ile bir hata yanıtı döndürerek istemciyi güvenli bağlantıya yönlendirmeye çalışır. Bu, özellikle hassas veri alışverişi yapan API'ler için kritik bir güvenlik önlemidir.

## Çalışma Prensibi

`HttpsMiddleware`, bir HTTP isteğini işlerken aşağıdaki adımları izler:

1.  **Konfigürasyon Kontrolü**: Middleware, `config/security-layer.php` dosyasındaki `features.https.enabled` ayarının etkin olup olmadığını kontrol eder.
2.  **Güvenli Bağlantı Kontrolü**: İstek Laravel'in `secure()` metodu aracılığıyla HTTPS üzerinden gelip gelmediğini doğrular.
3.  **Yönlendirme Kararı**:
    *   Eğer istek güvenli değilse ve HTTPS zorunluluğu etkinse, middleware bir güvenlik uyarısı günlükler ve yapılandırılan yönlendirme koduyla (varsayılan 301) bir hata yanıtı döndürür. Bu yanıt tipik olarak tarayıcıları veya HTTP istemcilerini isteğin HTTPS versiyonuna yeniden yönlendirmeye teşvik eder.
    *   Eğer istek zaten güvenliyse veya HTTPS zorunluluğu devre dışıysa, middleware bir bilgi mesajı günlükler ve isteği bir sonraki adıma iletir.

## Yapılandırma

`HttpsMiddleware`'ın davranışı `config/security-layer.php` dosyasındaki `features.https` bölümünde yapılandırılır. Bu özellik için modül bazlı veya inline konfigürasyon desteği bulunmaz, sadece global ayarlar geçerlidir.

```php
// config/security-layer.php

'features' => [
    'https' => [
        'enabled'       => env('SECURITY_LAYER_HTTPS_ENABLED', true),
        'redirect_code' => env('SECURITY_LAYER_HTTPS_REDIRECT_CODE', 301),
    ],
    // ... diğer özellikler
],
```

*   **`enabled`**: HTTPS zorunluluğunu etkinleştirir veya devre dışı bırakır. `false` olarak ayarlanırsa, HTTP istekleri güvenli bir bağlantı olmasa bile işlenmeye devam eder.
*   **`redirect_code`**: Güvenli olmayan bir bağlantı tespit edildiğinde döndürülecek HTTP durum kodunu belirler. Yaygın olarak 301 (Kalıcı Yönlendirme) veya 302 (Geçici Yönlendirme) kullanılır. 301, SEO ve kalıcılık açısından tercih edilir.

## Yaşam Döngüsü

Aşağıdaki şema, `HttpsMiddleware`'ın bir HTTP isteğinin yaşam döngüsü içindeki yerini ve işleyişini göstermektedir. Diagram sözdiziminden kaynaklanan kutu ve karar düğümü parantezleri hariç, tüm metinler Türkçe karakterlerle ve noktalama işareti olmadan yazılmıştır.

```mermaid
graph TD
    A[Gelen HTTP İsteği] --> B[HttpsMiddleware Handle Metodu];

    B --> C{HTTPS Etkin Mi Konfigürasyon};

    C -- Hayır --> D[İsteği Uygulama Akışına Aktar];
    D --> F[Yanıtı Döndür];

    C -- Evet --> E{İstek Güvenli Mi HTTPS};

    E -- Evet --> G[İsteği Uygulama Akışına Aktar];
    G --> F;

    E -- Hayır --> H[Güvenli Olmayan İsteği Günlükle];
    H --> I[HTTP Yönlendirme Yanıtı Oluştur];
    I --> F;
```

**Açıklama:**

1.  Gelen her HTTP isteği `HttpsMiddleware`'ın `handle()` metoduna ulaşır.
2.  Middleware, HTTPS zorunluluğunun yapılandırmada etkin olup olmadığını kontrol eder.
3.  Eğer HTTPS zorunluluğu `Hayır` (devre dışı) ise, istek doğrudan bir sonraki middleware'e veya rota işleyicisine (`İsteği Uygulama Akışına Aktar`) iletilir.
4.  Eğer HTTPS zorunluluğu `Evet` (etkin) ise, middleware isteğin HTTPS üzerinden gelip gelmediğini kontrol eder.
5.  Eğer istek `Evet` (güvenli) ise, istek yine bir sonraki adıma (`İsteği Uygulama Akışına Aktar`) iletilir.
6.  Eğer istek `Hayır` (güvenli değil) ise, middleware güvenli olmayan isteği günlükler (`Güvenli Olmayan İsteği Günlükle`) ve yapılandırılmış `redirect_code` ile bir hata yanıtı oluşturur (`HTTP Yönlendirme Yanıtı Oluştur`).
7.  Her senaryoda, sonunda bir yanıt istemciye döndürülür.

## Faydaları

*   **Veri Bütünlüğü ve Gizlilik**: Tüm iletişim trafiğinin şifrelenmesini sağlayarak hassas verilerin dinlenmesini veya değiştirilmesini önler.
*   **Saldırı Azaltma**: Ortadaki adam (Man-in-the-Middle) saldırılarına karşı koruma sağlar.
*   **SEO ve Güven İyileştirmesi**: Modern arama motorları ve tarayıcılar, HTTPS kullanan sitelere öncelik verir ve kullanıcılar için daha güvenli bir deneyim sunar.
*   **API Güvenliği Standardı**: Günümüz API geliştirme pratiklerinde HTTPS kullanımı bir standart haline gelmiştir.
*   **Kolay Yapılandırma**: Tek bir konfigürasyon bayrağı ile tüm API'niz için HTTPS zorunluluğunu kolayca etkinleştirebilirsiniz.

## Kullanım Örneği

`HttpsMiddleware`'ı genellikle tüm API rotalarını içeren bir rota grubuna atarsınız, böylece tüm API'nizin güvenli bağlantılar üzerinden çalışmasını sağlarsınız:

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::middleware(['security-layer.https']) // Tüm API rotalarına HTTPS zorunluluğu uygula
    ->prefix('api')
    ->group(function () {
        Route::get('/public', function () {
            return response()->json(['message' => 'Bu API endpointi HTTPS gerektirir.']);
        });

        Route::post('/private', function () {
            return response()->json(['message' => 'Bu endpoint HTTPS üzerinden hassas işlem yapar.']);
        });
    });
```

`security-layer` paketi, `HttpsMiddleware` ile API'nizin en temel güvenlik katmanlarından birini sağlamanıza yardımcı olur.