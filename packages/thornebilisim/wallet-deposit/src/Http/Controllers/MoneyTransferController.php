<?php

namespace Thorne\WalletDeposit\Http\Controllers;

use App\Services\OAuth\ApiClientAuth10Service;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Thorne\WalletDeposit\Services\WalletDepositService;

class MoneyTransferController extends WalletController
{
    public const SOURCE = 'MONEY-TRANSFER';

    public const PATH = 'money-transfer';

    private string $source = self::SOURCE;

    private string $path = self::PATH;

    private ApiClientAuth10Service $apiService;

    public function __construct()
    {
        parent::__construct();
    }

    public function accounts(): JsonResponse
    {
        $this->apiService = app(ApiClientAuth10Service::class);

        $accounts = cache()->remember('api_banks', now()->addHours(24), function () {
            $payload = [
                'userId'   => auth()->guard('customer')->user()?->id,
                'tenantId' => 50,
                'currency' => request('currency') ?? core()->getBaseCurrencyCode(),
            ];

            $response = $this->apiService->baseUrl('/account/get')
                ->baseMethod('post')
                ->baseClient($payload);

            return $response->json();
        });

        $normalizedAccounts = collect($accounts['bankDetail'] ?? [])->flatMap(function ($detail) {
            return collect($detail['bankDetailInfo'] ?? [])->map(function ($info) use ($detail) {
                return [
                    'account_holder' => $info['accountName'] ?? null,
                    'iban'           => $info['iban']        ?? null,
                    'bic'            => $info['swiftCode']   ?? null,
                    'bank_name'      => $info['bankName']    ?? '',
                    'currency'       => $detail['currency']  ?? '',
                    'status'         => 'active',
                    'main'           => true,
                ];
            });
        });

        return response()->json([
            'success' => true,
            'data'    => $normalizedAccounts->values(),
        ]);
    }

    public function history(): View|JsonResponse
    {
        $transactions = $this->customer->walletTransactions()
            ->where('source', $this->source)
            ->latest()
            ->get();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'data'    => $transactions ?? [],
            ]);
        }

        return view("wallet-deposit::{$this->path}.history", [
            'transactions' => $transactions,
        ]);
    }

    public function deposit(): View
    {
        $reference = $this->customer->getMeta('reference_code')
            ?? tap(app(WalletDepositService::class)->generateUniqueReferenceCode(), function ($code) {
                $this->customer->setMeta('reference_code', $code);
            });

        return view("wallet-deposit::{$this->path}.deposit", [
            'reference' => $reference,
        ]);
    }
}
