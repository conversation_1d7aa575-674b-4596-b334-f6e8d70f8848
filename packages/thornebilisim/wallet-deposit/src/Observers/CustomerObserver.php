<?php

namespace Thorne\WalletDeposit\Observers;

use Thorne\WalletDeposit\Services\WalletDepositService;
use Webkul\Customer\Models\Customer;

class CustomerObserver
{
    public function creating(Customer $customer): void
    {
        if (! $customer->hasMeta('reference_code')) {
            $walletDeposit = app(WalletDepositService::class);

            $customer->setMeta('reference_code', $walletDeposit->generateUniqueReferenceCode());
        }
    }
}
