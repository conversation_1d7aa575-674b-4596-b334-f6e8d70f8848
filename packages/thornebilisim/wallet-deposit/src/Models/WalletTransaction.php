<?php

namespace Thorne\WalletDeposit\Models;

use App\Traits\HasMeta;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Thorne\WalletDeposit\Enums\TransactionDirection;
use Thorne\WalletDeposit\Enums\TransactionStatus;
use Thorne\WalletDeposit\Enums\TransactionType;
use Webkul\Customer\Models\Customer;

class WalletTransaction extends Model
{
    use HasFactory, HasMeta;

    protected $table = 'wallet_transactions';

    protected $fillable = [
        'customer_id',
        'source',
        'source_id',
        'type',
        'direction',
        'status',
        'currency',
        'amount',
        'raw_data',
        'description',
        'parent_id',
    ];

    protected $casts = [
        'type'       => TransactionType::class,
        'direction'  => TransactionDirection::class,
        'status'     => TransactionStatus::class,
        'raw_data'   => 'array',
        'amount'     => 'decimal:8',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function getCreatedAtAttribute($value): array
    {
        return [
            'raw'      => $value,
            'datetime' => Carbon::parse($value)->format('d.m.Y H:i:s'),
            'readable' => Carbon::parse($value)->diffForHumans(),
        ];
    }

    public function getUpdatedAtAttribute($value): array
    {
        return [
            'raw'      => $value,
            'datetime' => Carbon::parse($value)->format('d.m.Y H:i:s'),
            'readable' => Carbon::parse($value)->diffForHumans(),
        ];
    }
}
