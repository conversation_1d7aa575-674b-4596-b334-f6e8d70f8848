<?php

use Illuminate\Support\Facades\Route;
use Thorne\WalletDeposit\Http\Controllers\MoneyTransferController;
use Thorne\WalletDeposit\Http\Controllers\WalletController;

Route::middleware(['web', 'customer', 'require.kyc'])
    ->prefix('customer/account/wallet')
    ->name('customer.account.wallet.')
    ->group(function () {
        Route::get('/balance', [WalletController::class, 'balance'])->name('balance');
        Route::get('/sources', [WalletController::class, 'sources'])->name('sources')->withoutMiddleware('require.kyc');
        Route::match(['get', 'post'], '/withdraw', [WalletController::class, 'withdraw'])->name('withdraw');
        Route::get('/transaction/{transactionId}', [WalletController::class, 'transaction'])->name('transaction');

        Route::prefix('money-transfer')->name('money-transfer.')->group(function () {
            Route::get('/deposit', [MoneyTransferController::class, 'deposit'])->name('deposit');
            Route::get('/history', [MoneyTransferController::class, 'history'])->name('history');
            Route::get('/accounts', [MoneyTransferController::class, 'accounts'])->name('accounts');
        });
    });
