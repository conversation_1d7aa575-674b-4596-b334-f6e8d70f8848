<header id="header">
    <div id="sticky-header" class="menu-area menu-style-two transparent-header">
        <div class="container custom-container-three">
            <div class="row">
                <div class="col-12">
                    <div class="mobile-nav-toggler my-4"><i class="fas fa-bars"></i></div>
                    <div class="menu-wrap">
                        <nav class="menu-nav">
                            <div class="logo">
                                <a href="{{ route('shop.home.index') }}"><img src="{{ asset('assets/img/logo/white-logo.png') }}" style="max-height: 85px;" alt=""></a>
                            </div>
                            @if (isset($showNavigation) && $showNavigation)
                                <div class="navbar-wrap main-menu d-none d-lg-flex">
                                    <ul class="navigation">
                                        <li><a href="{{ route('shop.home.index') }}">{{ __('velocity::app.header.home') }}</a></li>
                                        <li><a style="color: #12D176" href="{{ route('shop.productOrCategory.index', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}">{{ __('velocity::app.header.mirum') }}</a></li>
                                        <li><a href="{!! route( 'shop.home.faq', 'faq' ) !!}">{{ __('velocity::app.header.faq') }}</a></li>
                                        <li><a target="_blank" href="https://credipto.com">{{ __('velocity::app.header.credipto') }}</a></li>
                                        <li><a target="_blank" href="https://terramirum.com">{{ __('velocity::app.header.marketplace') }}</a></li>
                                        <li><a href="{{ route('shop.home.contact-us') }}">{{ __('velocity::app.header.contact') }}</a></li>
                                        @auth
                                            <li class="block d-md-none"><a href="{{ route('customer.profile.index') }}">{{ __('velocity::app.header.my-account') }}</a></li>
                                        @else
                                            <li class="block d-md-none"><a href="{{ route('customer.session.create', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}">{{ __('velocity::app.customer.login-form.title') }}</a></li>
                                        @endauth
                                    </ul>
                                </div>
                                <div class="header-action d-none d-lg-block position-relative">
                                    <ul>
                                        <li class="header-lang">
                                        <span class="selected-lang uppercase">
                                            {{ app('Webkul\Core\Repositories\LocaleRepository')->findOneByField('code', request()->get('locale') ?: app()->getLocale())->code }}
                                        </span>
                                            <ul class="lang-list">
                                                @foreach(app('Webkul\Core\Repositories\LocaleRepository')->all() as $locale)
                                                    <li>
                                                        <a href="{{ request()->fullUrlWithQuery(['locale' => $locale->code]) }}">
                                                            <span class="uppercase">{{ $locale->name }}</span>
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </li>
                                        <li class="header-btn me-2"><a href="{{ route('shop.productOrCategory.index', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}"
                                                                       class="btn">
                                                {{ __('velocity::app.header.buy-now') }}
                                            </a></li>
                                        @auth
                                            <li class="header-btn"><a href="{{ route('customer.profile.index') }}"
                                                                      class="btn2">
                                                    {{ __('velocity::app.header.my-account') }}
                                                </a></li>
                                        @else
                                            {{--                                            <li class="header-btn"><a href="{{ route('shop.productOrCategory.index', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}" class="btn2">--}}
                                            {{--                                                    {{ __('velocity::app.header.buy-now') }}--}}
                                            {{--                                                </a>--}}
                                            {{--                                            </li>--}}
                                            <li class="header-btn"><a href="{{ route('customer.session.create', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}" class="btn2">
                                                    {{ __('velocity::app.customer.login-form.title') }}
                                                </a>
                                            </li>
                                        @endauth
                                    </ul>
                                </div>
                            @endif
                        </nav>
                    </div>
                    <!-- Mobile Menu  -->
                    <div class="mobile-menu">
                        <nav class="menu-box">
                            <div class="close-btn"><i class="fas fa-times"></i></div>
                            <div class="nav-logo"><a href="index.php"><img src="doc/white-logo.png" alt=""
                                                                           title=""></a>
                            </div>
                            <div class="menu-outer">
                                <!--Here Menu Will Come Automatically Via Javascript / Same Menu as in Header-->
                            </div>
                            <!--                            <div class="social-links">-->
                            <!--                                <ul class="clearfix">-->
                            <!--                                    <li><a href="#"><i class="fab fa-facebook-f"></i></a></li>-->
                            <!--                                    <li><a href="#"><i class="fab fa-twitter"></i></a></li>-->
                            <!--                                    <li><a href="#"><i class="fab fa-instagram"></i></a></li>-->
                            <!--                                    <li><a href="#"><i class="fab fa-linkedin-in"></i></a></li>-->
                            <!--                                    <li><a href="#"><i class="fab fa-youtube"></i></a></li>-->
                            <!--                                </ul>-->
                            <!--                            </div>-->
                        </nav>
                    </div>
                    <div class="menu-backdrop"></div>
                    <!-- End Mobile Menu -->
                </div>
            </div>
        </div>
    </div>
</header>