<?php

namespace Thorne\QontoDeposit\Services;

use App\Models\MetaData;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Thorne\QontoDeposit\Models\QontoTransaction;
use Webkul\Customer\Models\Customer;

class QontoDepositService
{
    protected array $config;

    protected string $baseUrl;

    protected string $slug;

    protected string $secret;

    protected string $iban;

    protected string $referencePrefix;

    public function __construct()
    {
        $this->baseUrl         = config('qonto-deposit.base_url')               ?? '';
        $this->slug            = config('qonto-deposit.slug')                   ?? '';
        $this->secret          = config('qonto-deposit.secret')                 ?? '';
        $this->iban            = config('qonto-deposit.iban')                   ?? '8c81d4a-e6b1-4115-9acd-2b6b9603efa2';
        $this->referencePrefix = config('wallet-deposit.reference_code_prefix') ?? '';
    }

    protected function getHeaders(): array
    {
        return [
            'Authorization' => "$this->slug:$this->secret",
            'Content-Type'  => 'application/json',
            'Accept'        => 'application/json',
        ];
    }

    public function getOrganization(array $queryParams = ['include_external_accounts' => false])
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get("{$this->baseUrl}/organization", $queryParams);

            Log::channel('qonto-api')->info('QontoDepositService:getOrganization – Organization details retrieved: '.json_encode($response->json()));

            return $response->json();
        } catch (Exception $exception) {
            Log::channel('qonto-api')->error('QontoDepositService:getOrganization – Failed to retrieve organization details: '.$exception->getMessage());

            return null;
        }
    }

    public function getOrganizationSummary(): ?array
    {
        $organizationData = $this->getOrganization(['include_external_accounts' => true]);

        if (! $organizationData || ! isset($organizationData['organization'])) {
            return null;
        }

        $organization = $organizationData['organization'];

        $organizationSummary = [
            'id'                      => $organization['id']                      ?? null,
            'slug'                    => $organization['slug']                    ?? null,
            'legal_name'              => $organization['legal_name']              ?? null,
            'legal_number'            => $organization['legal_number']            ?? null,
            'legal_sector'            => $organization['legal_sector']            ?? null,
            'legal_registration_date' => $organization['legal_registration_date'] ?? null,
            'legal_address'           => $organization['legal_address']           ?? null,
            'legal_form'              => $organization['legal_form']              ?? null,
            'legal_country'           => $organization['legal_country']           ?? null,
            'legal_share_capital'     => $organization['legal_share_capital']     ?? null,
            'locale'                  => $organization['locale']                  ?? null,
            'contract_signed_at'      => $organization['contract_signed_at']      ?? null,
        ];

        $organizationSummary['bank_accounts'] = collect($organization['bank_accounts'] ?? [])
            ->map(fn (array $bankAccount) => [
                'id'                       => $bankAccount['id']                       ?? null,
                'slug'                     => $bankAccount['slug']                     ?? null,
                'name'                     => $bankAccount['name']                     ?? null,
                'iban'                     => $bankAccount['iban']                     ?? null,
                'bic'                      => $bankAccount['bic']                      ?? null,
                'currency'                 => $bankAccount['currency']                 ?? null,
                'balance'                  => $bankAccount['balance']                  ?? null,
                'balance_cents'            => $bankAccount['balance_cents']            ?? null,
                'authorized_balance'       => $bankAccount['authorized_balance']       ?? null,
                'authorized_balance_cents' => $bankAccount['authorized_balance_cents'] ?? null,
                'status'                   => $bankAccount['status']                   ?? null,
                'main'                     => $bankAccount['main']                     ?? null,
                'is_external_account'      => $bankAccount['is_external_account']      ?? null,
                'updated_at'               => $bankAccount['updated_at']               ?? null,
                'account_number'           => $bankAccount['account_number']           ?? null,
            ])
            ->all();

        return $organizationSummary;
    }

    public function getBankAccountDetails(): ?array
    {
        $organizationSummary = $this->getOrganizationSummary();
        if (! $organizationSummary) {
            return null;
        }

        return collect($organizationSummary['bank_accounts'])
            ->map(fn (array $bankAccount) => [
                'name'           => $bankAccount['name'],
                'iban'           => $bankAccount['iban'],
                'bic'            => $bankAccount['bic'],
                'currency'       => $bankAccount['currency'],
                'account_number' => $bankAccount['account_number'],
                'main'           => $bankAccount['main'],
            ])
            ->all();
    }

    public function getRequests()
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get("{$this->baseUrl}/requests");

            Log::channel('qonto-api')->info('QontoDepositService:getRequests – Requests retrieved: '.json_encode($response->json()));

            return $response->json();
        } catch (Exception $exception) {
            Log::channel('qonto-api')->error('QontoDepositService:getRequests – Failed to retrieve requests: '.$exception->getMessage());

            return null;
        }
    }

    public function getTransactions($bankAccountId = null)
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get("{$this->baseUrl}/transactions", [
                    'bank_account_id' => $bankAccountId,
                ]);

            Log::channel('qonto-api')->info('QontoDepositService:getTransactions – '.count($response->json('transactions')).' transactions listed.', $response->json());

            return $response->json();
        } catch (Exception $exception) {
            Log::channel('qonto-api')->error('QontoDepositService:getTransactions – Failed to retrieve transactions: '.$exception->getMessage());

            return null;
        }
    }

    public function checkAndProcessTransactions(): void
    {
        $transactions = $this->getTransactions('8c81d4a-e6b1-4115-9acd-2b6b9603efa2');

        if (! $transactions || empty($transactions['transactions'])) {
            Log::channel('qonto-deposit')->info('QontoDepositService:checkAndProcessTransactions – No transactions to process.');

            return;
        }

        foreach ($transactions['transactions'] as $transaction) {
            Log::channel('qonto-deposit')->info('QontoDepositService:checkAndProcessTransactions – Processing transaction: '.$transaction['transaction_id']);

            $this->processTransaction($transaction);

            Log::channel('qonto-deposit')->info('QontoDepositService:checkAndProcessTransactions – Transaction processed', $transaction);
        }
    }

    protected function processTransaction(array $transaction): void
    {
        $transactionIban = $this->extractTransactionIban($transaction);
        Log::channel('qonto-deposit')->info('QontoDepositService:processTransaction – Transaction IBAN: '.$transactionIban.', Expected IBAN: '.$this->iban);

        if ($transactionIban !== $this->iban) {
            Log::channel('qonto-deposit')->info('QontoDepositService:processTransaction – Transaction skipped due to IBAN mismatch.');

            return;
        }

        if ($this->isAlreadyProcessed($transaction['transaction_id'])) {
            Log::channel('qonto-deposit')->info('QontoDepositService:processTransaction – Transaction already processed: '.$transaction['transaction_id']);

            return;
        }

        $reference = $transaction['reference'] ?? null;
        if (! $reference) {
            Log::channel('qonto-deposit')->warning('QontoDepositService:processTransaction – Transaction has no reference: '.$transaction['transaction_id']);

            return;
        }

        if (str($reference)->startsWith($this->referencePrefix)) {
            Log::channel('qonto-deposit')->info('QontoDepositService:processTransaction – Processing prefixed reference: '.$reference);

            $this->processPrefixedReferenceTransaction($transaction, $reference);
        } else {
            Log::channel('qonto-deposit')->warning('QontoDepositService:processTransaction – Transaction has no prefixed reference: '.$reference);
        }
    }

    private function extractTransactionIban(array $transaction): ?string
    {
        return $transaction['bank_account_id'] ?? null;
    }

    private function isAlreadyProcessed(string $transactionId): bool
    {
        return QontoTransaction::where('transaction_id', $transactionId)->exists();
    }

    private function processPrefixedReferenceTransaction(array $transaction, string $reference): void
    {
        $meta = MetaData::where('key', 'reference_code')
            ->where('text_value', $reference)
            ->with('metaable')
            ->first();

        if (! $meta) {
            Log::channel('qonto-deposit')->warning('QontoDepositService:processPrefixedReferenceTransaction – No metadata found for reference='.$reference);

            return;
        }

        $customer = $meta->metaable;

        if (! $customer instanceof Customer) {
            Log::channel('qonto-deposit')->warning('QontoDepositService:processPrefixedReferenceTransaction – Metadata metaable is not a Customer: meta_id='.$meta->meta_id);

            return;
        }

        $this->createTransaction($customer, $transaction);
    }

    private function createTransaction(Customer $customer, array $transaction): void
    {
        $qontoTransaction = QontoTransaction::firstOrNew(
            ['transaction_id' => $transaction['transaction_id']]
        );
        $qontoTransaction->customer_id = $customer->id;
        $qontoTransaction->transaction = $transaction;
        $qontoTransaction->save();

        Log::channel('qonto-deposit')->info('QontoDepositService:createTransaction – Transaction stored for customer='.$customer->id);
    }
}
