<?php

namespace Thorne\QontoDeposit\Models;

use App\Traits\HasMeta;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Thorne\QontoDeposit\Enums\Deposit\StatusType;

class QontoDeposit extends Model
{
    use HasFactory, HasMeta;

    protected $table = 'qonto_deposits';

    protected $fillable = [
        'transaction_id',
        'status',
    ];

    protected $casts = [
        'status' => StatusType::class,
    ];

    protected $with = ['transaction.customer'];

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(QontoTransaction::class, 'transaction_id', 'transaction_id');
    }

    public function getAmount(): ?float
    {
        return $this->transaction->transaction['amount'] ?? null;
    }

    public function getCurrency(): ?string
    {
        return $this->transaction->transaction['currency'] ?? null;
    }
}
