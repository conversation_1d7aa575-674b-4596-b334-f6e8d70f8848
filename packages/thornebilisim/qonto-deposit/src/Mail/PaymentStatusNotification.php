<?php

namespace Thorne\QontoDeposit\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Thorne\WalletDeposit\Models\WalletTransaction;

class PaymentStatusNotification extends Mailable
{
    use Queueable, SerializesModels;

    private WalletTransaction $walletTransaction;

    private string $status;

    private string $recipientType;

    public function __construct(WalletTransaction $walletTransaction, ?string $status = null, string $recipientType = 'customer')
    {
        $this->walletTransaction  = $walletTransaction;
        $this->status             = $status;
        $this->recipientType      = $recipientType;
        $this->locale             = $this->walletTransaction->customer?->locale ?? app()->getLocale();
    }

    public function build()
    {
        $sender = core()->getSenderEmailDetails();
        $status = $this->status ?? $this->walletTransaction->status ?? 'failed';

        $fromEmail = data_get($sender, 'email');
        $fromName  = data_get($sender, 'name', 'Admin');
        $subject   = trans('mail.sales.deposit.'.$status.'.subject', ['transaction_id' => $this->walletTransaction->id]);
        $viewPath  = 'qonto-deposit::emails.sales.deposit-'.$status;

        $email = $this->from($fromEmail, $fromName)
            ->subject($subject)
            ->view($viewPath, ['walletTransaction' => $this->walletTransaction]);

        $toEmail = $this->walletTransaction->customer->email;
        $toName  = $this->walletTransaction->customer->full_name;

        if ($this->recipientType === 'admin') {
            $toEmail = data_get(core()->getSenderEmailDetails(), 'email', env('MAIL_FROM_ADDRESS'));
            $toName  = 'Admin';
        }

        return $email->to($toEmail, $toName);
    }
}
