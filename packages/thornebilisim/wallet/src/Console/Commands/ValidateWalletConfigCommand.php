<?php

namespace Thorne\Wallet\Console\Commands;

use Illuminate\Console\Command;
use Thorne\Wallet\Services\ConfigValidationService;

class ValidateWalletConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'wallet:validate-config {--fix : Attempt to fix common configuration issues}';

    /**
     * The console command description.
     */
    protected $description = 'Validate wallet package configuration';

    /**
     * Execute the console command.
     */
    public function handle(ConfigValidationService $configValidationService): int
    {
        $this->info('Validating wallet configuration...');

        try {
            $errors = $configValidationService->validateConfiguration();

            if (empty($errors)) {
                $this->info('✅ Wallet configuration is valid!');
                return self::SUCCESS;
            }

            $this->error('❌ Wallet configuration validation failed:');
            $this->newLine();

            foreach ($errors as $error) {
                $this->line("  • {$error}");
            }

            $this->newLine();

            if ($this->option('fix')) {
                $this->info('Attempting to fix configuration issues...');
                $this->fixCommonIssues($errors);
            } else {
                $this->info('Run with --fix option to attempt automatic fixes.');
            }

            return self::FAILURE;

        } catch (\Exception $e) {
            $this->error("Configuration validation failed with exception: {$e->getMessage()}");
            return self::FAILURE;
        }
    }

    /**
     * Attempt to fix common configuration issues.
     */
    protected function fixCommonIssues(array $errors): void
    {
        $fixed = 0;

        foreach ($errors as $error) {
            if (str_contains($error, 'No enabled currencies found')) {
                $this->warn('Cannot auto-fix: No enabled currencies found. Please check your wallet.php config file.');
                continue;
            }

            if (str_contains($error, 'Missing enabled flag')) {
                $this->warn('Cannot auto-fix: Missing enabled flags. Please check your wallet.php config file.');
                continue;
            }

            if (str_contains($error, 'WEB3 is enabled but')) {
                $this->warn('Cannot auto-fix: WEB3 configuration issues. Please check your .env file.');
                continue;
            }
        }

        if ($fixed > 0) {
            $this->info("✅ Fixed {$fixed} configuration issues.");
        } else {
            $this->warn('No issues could be automatically fixed. Manual intervention required.');
        }
    }
}
