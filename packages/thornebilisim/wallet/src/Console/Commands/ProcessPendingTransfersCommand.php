<?php

namespace Thorne\Wallet\Console\Commands;

use Illuminate\Console\Command;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\TransferService;

class ProcessPendingTransfersCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'wallet:process-transfers 
                            {--limit=100 : Maximum number of transfers to process}
                            {--timeout=24 : Timeout in hours for pending transfers}';

    /**
     * The console command description.
     */
    protected $description = 'Process pending wallet transfers';

    /**
     * Execute the console command.
     */
    public function handle(TransferService $transferService): int
    {
        $limit   = (int) $this->option('limit');
        $timeout = (int) $this->option('timeout');

        // Get pending transfers
        $pendingTransfers = WalletTransfer::pending()
            ->where('created_at', '>=', now()->subHours($timeout))
            ->limit($limit)
            ->get();

        if ($pendingTransfers->isEmpty()) {
            $this->info('No pending transfers to process');

            return self::SUCCESS;
        }

        $this->info("Processing {$pendingTransfers->count()} pending transfers...");

        $progressBar = $this->output->createProgressBar($pendingTransfers->count());
        $progressBar->start();

        $processed = 0;
        $failed    = 0;

        foreach ($pendingTransfers as $transfer) {
            try {
                // In a real implementation, you might want to add additional
                // validation or external service calls here
                $transferService->processTransfer($transfer);
                $processed++;
            } catch (\Exception $e) {
                $failed++;
                $this->newLine();
                $this->error("Failed to process transfer {$transfer->reference}: {$e->getMessage()}");

                // Mark transfer as failed
                try {
                    $transfer->fail("Auto-processing failed: {$e->getMessage()}");
                } catch (\Exception $failException) {
                    $this->error("Failed to mark transfer as failed: {$failException->getMessage()}");
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        // Handle expired transfers
        $expiredTransfers = WalletTransfer::pending()
            ->where('created_at', '<', now()->subHours($timeout))
            ->get();

        if ($expiredTransfers->isNotEmpty()) {
            $this->info("Found {$expiredTransfers->count()} expired transfers, cancelling...");

            foreach ($expiredTransfers as $transfer) {
                try {
                    $transferService->cancelTransfer($transfer, 'Transfer expired after '.$timeout.' hours');
                } catch (\Exception $e) {
                    $this->error("Failed to cancel expired transfer {$transfer->reference}: {$e->getMessage()}");
                }
            }
        }

        $this->info("Processing completed: {$processed} successful, {$failed} failed");

        return $failed > 0 ? self::FAILURE : self::SUCCESS;
    }
}
