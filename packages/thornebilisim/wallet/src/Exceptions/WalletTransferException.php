<?php

namespace Thorne\Wallet\Exceptions;

class WalletTransferException extends WalletException
{
    /**
     * Create a new invalid account exception.
     */
    public static function invalidAccount(string $accountNumber): self
    {
        return new self(
            "Invalid account number: {$accountNumber}",
            'INVALID_ACCOUNT',
            [
                'account_number' => $accountNumber,
            ]
        );
    }

    /**
     * Create a new account not found exception.
     */
    public static function accountNotFound(string $accountNumber): self
    {
        return new self(
            "Account not found: {$accountNumber}",
            'ACCOUNT_NOT_FOUND',
            [
                'account_number' => $accountNumber,
            ]
        );
    }

    /**
     * Create a new same account transfer exception.
     */
    public static function sameAccountTransfer(string $accountNumber): self
    {
        return new self(
            "Cannot transfer to the same account: {$accountNumber}",
            'SAME_ACCOUNT_TRANSFER',
            [
                'account_number' => $accountNumber,
            ]
        );
    }

    /**
     * Create a new transfer limit exceeded exception.
     */
    public static function limitExceeded(string $limitType, float $limit, float $usage, string $currency): self
    {
        $remaining = max(0, $limit - $usage);
        
        return new self(
            "{$limitType} transfer limit exceeded. Limit: {$limit} {$currency}, Usage: {$usage} {$currency}, Remaining: {$remaining} {$currency}",
            'TRANSFER_LIMIT_EXCEEDED',
            [
                'limit_type' => $limitType,
                'limit' => $limit,
                'usage' => $usage,
                'remaining' => $remaining,
                'currency' => $currency,
            ]
        );
    }

    /**
     * Create a new transfer disabled exception.
     */
    public static function transfersDisabled(): self
    {
        return new self(
            "Transfers are currently disabled",
            'TRANSFERS_DISABLED'
        );
    }
}
