<?php

namespace Thorne\Wallet\Exceptions;

class WalletBalanceException extends WalletException
{
    /**
     * Create a new insufficient balance exception.
     */
    public static function insufficientBalance(float $required, float $available, string $currency): self
    {
        return new self(
            "Insufficient balance. Required: {$required} {$currency}, Available: {$available} {$currency}",
            'INSUFFICIENT_BALANCE',
            [
                'required_amount' => $required,
                'available_amount' => $available,
                'currency' => $currency,
                'deficit' => $required - $available,
            ]
        );
    }

    /**
     * Create a new balance lock failed exception.
     */
    public static function lockFailed(float $amount, string $currency): self
    {
        return new self(
            "Failed to lock balance amount: {$amount} {$currency}",
            'BALANCE_LOCK_FAILED',
            [
                'amount' => $amount,
                'currency' => $currency,
            ]
        );
    }

    /**
     * Create a new balance unlock failed exception.
     */
    public static function unlockFailed(float $amount, string $currency): self
    {
        return new self(
            "Failed to unlock balance amount: {$amount} {$currency}",
            'BALANCE_UNLOCK_FAILED',
            [
                'amount' => $amount,
                'currency' => $currency,
            ]
        );
    }

    /**
     * Create a new balance not found exception.
     */
    public static function notFound(int $customerId, string $currency): self
    {
        return new self(
            "Balance not found for customer {$customerId} in currency {$currency}",
            'BALANCE_NOT_FOUND',
            [
                'customer_id' => $customerId,
                'currency' => $currency,
            ]
        );
    }
}
