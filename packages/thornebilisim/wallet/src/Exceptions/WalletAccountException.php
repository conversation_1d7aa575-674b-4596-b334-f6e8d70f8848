<?php

namespace Thorne\Wallet\Exceptions;

class WalletAccountException extends WalletException
{
    /**
     * Create a new account generation failed exception.
     */
    public static function generationFailed(string $currency, int $attempts): self
    {
        return new self(
            "Failed to generate unique account number for currency {$currency} after {$attempts} attempts",
            'ACCOUNT_GENERATION_FAILED',
            [
                'currency' => $currency,
                'attempts' => $attempts,
                'suggestion' => 'Please contact system administrator',
            ]
        );
    }

    /**
     * Create a new account not found exception.
     */
    public static function notFound(string $accountNumber): self
    {
        return new self(
            "Account not found: {$accountNumber}",
            'ACCOUNT_NOT_FOUND',
            [
                'account_number' => $accountNumber,
            ]
        );
    }

    /**
     * Create a new account not owned exception.
     */
    public static function notOwned(string $accountNumber, int $customerId): self
    {
        return new self(
            "Account {$accountNumber} is not owned by customer {$customerId}",
            'ACCOUNT_NOT_OWNED',
            [
                'account_number' => $accountNumber,
                'customer_id' => $customerId,
            ]
        );
    }

    /**
     * Create a new account inactive exception.
     */
    public static function inactive(string $accountNumber): self
    {
        return new self(
            "Account is inactive: {$accountNumber}",
            'ACCOUNT_INACTIVE',
            [
                'account_number' => $accountNumber,
            ]
        );
    }
}
