<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Events\WalletBalanceSynced;
use Thorne\Wallet\Exceptions\WalletBalanceException;
use Thorne\Wallet\Exceptions\Web3SyncException;
use Thorne\Wallet\Models\WalletBalance;

class BalanceService
{
    /**
     * Sync all balances with WEB3 service.
     */
    public function syncAllWithWeb3(int $customerId): Collection
    {
        if (! wallet_config('web3.enabled', true)) {
            throw new Web3SyncException('WEB3 service is disabled', $customerId, 'ALL');
        }

        try {
            $web3Data = $this->fetchWeb3Balances($customerId);
            $balances = collect();

            foreach ($web3Data['balances'] as $balanceData) {
                $currency = $balanceData['isoCurrency'];
                $balance  = $this->calculateActualBalance($balanceData);

                $walletBalance = WalletBalance::updateOrCreate(
                    ['customer_id' => $customerId, 'currency' => $currency],
                    [
                        'balance'        => $balance,
                        'web3_synced_at' => now(),
                        'web3_data'      => [
                            'address'         => $web3Data['address'],
                            'chain_id'        => $web3Data['chainId'],
                            'raw_balance'     => $balanceData['balance'],
                            'pending_balance' => $balanceData['pendingBalance'],
                            'exponent'        => $balanceData['exponent'],
                            'is_stable_coin'  => $balanceData['isStableCoin'],
                            'is_cbdc'         => $balanceData['isCbdc'],
                            'last_sync'       => now()->toISOString(),
                        ],
                    ]
                );

                $balances->push($walletBalance);
                event(new WalletBalanceSynced($walletBalance, $balanceData));
            }

            return $balances;

        } catch (\Exception $e) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('WEB3 balance sync failed', [
                    'customer_id' => $customerId,
                    'error'       => $e->getMessage(),
                ]);

            throw new Web3SyncException($e->getMessage(), $customerId, 'ALL');
        }
    }

    /**
     * Sync balance for specific currency with WEB3 service.
     */
    public function syncWithWeb3(int $customerId, string $currency): WalletBalance
    {
        $balances = $this->syncAllWithWeb3($customerId);

        $balance = $balances->firstWhere('currency', $currency);

        if (! $balance) {
            throw new Web3SyncException("Currency {$currency} not found in WEB3 response", $customerId, $currency);
        }

        return $balance;
    }

    /**
     * Get customer balance (with optional WEB3 sync).
     */
    public function getBalance(int $customerId, string $currency, bool $forceSync = false): WalletBalance
    {
        $balance = WalletBalance::getCustomerBalance($customerId, $currency);

        if (! $balance) {
            $balance = WalletBalance::getOrCreateForCustomer($customerId, $currency);
        }

        // Check if sync is needed
        if ($forceSync || $balance->needsWeb3Sync()) {
            $balance = $this->syncWithWeb3($customerId, $currency);
        }

        return $balance;
    }

    /**
     * Get all customer balances.
     */
    public function getAllBalances(int $customerId, bool $forceSync = false): Collection
    {
        if ($forceSync) {
            // Use syncAllWithWeb3 for efficiency - single API call for all currencies
            return $this->syncAllWithWeb3($customerId);
        }

        return WalletBalance::where('customer_id', $customerId)->get();
    }

    /**
     * Check if customer has sufficient balance.
     */
    public function hasSufficientBalance(int $customerId, string $currency, string|float $amount, bool $includeLockedBalance = false): bool
    {
        return DB::transaction(function () use ($customerId, $currency, $amount, $includeLockedBalance) {
            $balance = WalletBalance::where('customer_id', $customerId)
                ->where('currency', $currency)
                ->lockForUpdate()
                ->first();

            if (!$balance) {
                return false;
            }

            return $balance->hasSufficientBalance($amount, $includeLockedBalance);
        });
    }

    /**
     * Lock amount from customer balance.
     */
    public function lockAmount(int $customerId, string $currency, string|float $amount): bool
    {
        return DB::transaction(function () use ($customerId, $currency, $amount) {
            $balance = WalletBalance::where('customer_id', $customerId)
                ->where('currency', $currency)
                ->lockForUpdate()
                ->first();

            if (!$balance) {
                $balance = WalletBalance::getOrCreateForCustomer($customerId, $currency);
            }

            return $balance->lockAmount($amount);
        });
    }

    /**
     * Unlock amount from customer balance.
     */
    public function unlockAmount(int $customerId, string $currency, string|float $amount): bool
    {
        return DB::transaction(function () use ($customerId, $currency, $amount) {
            $balance = WalletBalance::where('customer_id', $customerId)
                ->where('currency', $currency)
                ->lockForUpdate()
                ->first();

            if (!$balance) {
                throw WalletBalanceException::notFound($customerId, $currency);
            }

            return $balance->unlockAmount($amount);
        });
    }

    /**
     * Compare database balance with WEB3 balance.
     */
    public function compareBalances(int $customerId, string $currency): array
    {
        $dbBalance = WalletBalance::getCustomerBalance($customerId, $currency);
        $dbAmount  = $dbBalance ? $dbBalance->balance : '0.00000000';

        try {
            // Fetch all balances from WEB3 and find the specific currency
            $web3Data        = $this->fetchWeb3Balances($customerId);
            $web3Amount      = '0.00000000';
            $web3BalanceData = null;

            // Find the specific currency in WEB3 response
            foreach ($web3Data['balances'] as $balanceData) {
                if ($balanceData['isoCurrency'] === $currency) {
                    $web3Amount      = $this->calculateActualBalance($balanceData);
                    $web3BalanceData = $balanceData;
                    break;
                }
            }

            $matches = bccomp($dbAmount, $web3Amount, 8) === 0;

            return [
                'matches'          => $matches,
                'database_balance' => $dbAmount,
                'web3_balance'     => $web3Amount,
                'difference'       => bcsub($web3Amount, $dbAmount, 8),
                'last_sync'        => $dbBalance?->web3_synced_at?->toISOString(),
                'web3_data'        => $web3BalanceData,
                'currency'         => $currency,
            ];

        } catch (\Exception $e) {
            return [
                'matches'          => null,
                'database_balance' => $dbAmount,
                'web3_balance'     => null,
                'difference'       => null,
                'error'            => $e->getMessage(),
                'last_sync'        => $dbBalance?->web3_synced_at?->toISOString(),
                'currency'         => $currency,
            ];
        }
    }

    /**
     * Fetch all balances from WEB3 service.
     *
     * @throws \Exception
     */
    protected function fetchWeb3Balances(int $customerId): array
    {
        try {
            // Prepare payload according to your specification
            $payload = [
                'userId'   => $customerId,
                'tenantId' => wallet_config('web3.tenant_id', 50),
                'chainId'  => wallet_config('web3.chain_id', 'mirum-testnet'),
            ];

            // Use the new Web3ApiClient
            $response = Web3ApiClient::make()
                ->endpoint('/wallet/balance')
                ->timeout(wallet_config('web3.timeout', 30))
                ->retries(wallet_config('web3.retry_attempts', 3))
                ->post($payload);

            $data = $response->json();

            // Validate response structure according to your specification
            if (! isset($data['balances']) || ! is_array($data['balances'])) {
                throw new \RuntimeException('Invalid WEB3 response structure: missing balances array');
            }

            if (! isset($data['chainId']) || ! isset($data['address'])) {
                throw new \RuntimeException('Invalid WEB3 response structure: missing chainId or address');
            }

            // Validate each balance entry
            foreach ($data['balances'] as $balance) {
                $requiredFields = ['isoCurrency', 'chainCurrency', 'balance', 'pendingBalance', 'isStableCoin', 'isCbdc', 'exponent'];
                foreach ($requiredFields as $field) {
                    if (! array_key_exists($field, $balance)) {
                        throw new \RuntimeException("Invalid balance entry: missing {$field} field");
                    }
                }
            }

            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->info('WEB3 balances fetched successfully', [
                    'customer_id'      => $customerId,
                    'currencies_count' => count($data['balances']),
                    'currencies'       => array_column($data['balances'], 'isoCurrency'),
                ]);

            return $data;

        } catch (\Exception $e) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Failed to fetch WEB3 balances', [
                    'customer_id' => $customerId,
                    'error'       => $e->getMessage(),
                ]);

            throw new \RuntimeException("Failed to fetch WEB3 balances: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Calculate actual balance from WEB3 balance data.
     */
    protected function calculateActualBalance(array $balanceData): string
    {
        $rawBalance = $balanceData['balance']  ?? 0;
        $exponent   = $balanceData['exponent'] ?? 0;

        if ($exponent === 0) {
            return (string) $rawBalance;
        }

        // Convert from minor units to major units
        $divisor       = pow(10, $exponent);
        $actualBalance = $rawBalance / $divisor;

        return number_format($actualBalance, $exponent, '.', '');
    }
}
