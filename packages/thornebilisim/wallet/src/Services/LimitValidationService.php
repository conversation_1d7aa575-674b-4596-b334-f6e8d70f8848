<?php

namespace Thorne\Wallet\Services;

use Thorne\Wallet\Exceptions\WalletException;

class LimitValidationService
{
    /**
     * Validate amount against both global and payment method limits.
     * 
     * This implements a two-tier limit system:
     * 1. Global/Operational limits (from permissions config)
     * 2. Payment method specific limits (from payment_methods config)
     * 
     * The most restrictive limit always takes precedence.
     */
    public function validateAmountLimits(
        string $operation, 
        string $paymentMethod, 
        float $amount, 
        string $currency
    ): void {
        // First check global limits (Primary limits)
        $this->validateGlobalLimits($operation, $amount, $currency);
        
        // Then check payment method specific limits (Secondary limits)
        $this->validatePaymentMethodLimits($paymentMethod, $operation, $amount, $currency);
    }

    /**
     * Validate global operational limits.
     * These are the primary limits that apply to all operations.
     */
    protected function validateGlobalLimits(string $operation, float $amount, string $currency): void
    {
        $permissions = wallet_config("permissions.{$operation}", []);
        
        if (!$permissions['enabled'] ?? true) {
            throw new WalletException(
                ucfirst($operation) . " operations are currently disabled",
                strtoupper($operation) . '_DISABLED'
            );
        }

        // Check global minimum amount
        $globalMin = (float) ($permissions['min_amount'] ?? 0);
        if ($amount < $globalMin) {
            throw new WalletException(
                "Amount {$amount} {$currency} is below global minimum of {$globalMin} {$currency}",
                'GLOBAL_MIN_AMOUNT_VIOLATION',
                [
                    'operation' => $operation,
                    'amount' => $amount,
                    'min_amount' => $globalMin,
                    'currency' => $currency,
                    'limit_type' => 'global_minimum'
                ]
            );
        }

        // Check global maximum amount
        $globalMax = (float) ($permissions['max_amount'] ?? PHP_FLOAT_MAX);
        if ($amount > $globalMax) {
            throw new WalletException(
                "Amount {$amount} {$currency} exceeds global maximum of {$globalMax} {$currency}",
                'GLOBAL_MAX_AMOUNT_VIOLATION',
                [
                    'operation' => $operation,
                    'amount' => $amount,
                    'max_amount' => $globalMax,
                    'currency' => $currency,
                    'limit_type' => 'global_maximum'
                ]
            );
        }
    }

    /**
     * Validate payment method specific limits.
     * These are secondary limits that apply to specific payment methods.
     */
    protected function validatePaymentMethodLimits(
        string $paymentMethod, 
        string $operation, 
        float $amount, 
        string $currency
    ): void {
        $methodConfig = wallet_payment_method_config($paymentMethod);
        
        if (empty($methodConfig)) {
            throw new WalletException(
                "Payment method {$paymentMethod} is not configured",
                'PAYMENT_METHOD_NOT_CONFIGURED',
                ['payment_method' => $paymentMethod]
            );
        }

        if (!$methodConfig['enabled']) {
            throw new WalletException(
                "Payment method {$paymentMethod} is disabled",
                'PAYMENT_METHOD_DISABLED',
                ['payment_method' => $paymentMethod]
            );
        }

        // Check if operation is allowed for this payment method
        $operationKey = "can_{$operation}";
        if (!($methodConfig[$operationKey] ?? false)) {
            throw new WalletException(
                "Payment method {$paymentMethod} does not support {$operation} operations",
                'PAYMENT_METHOD_OPERATION_NOT_SUPPORTED',
                [
                    'payment_method' => $paymentMethod,
                    'operation' => $operation
                ]
            );
        }

        // Check payment method minimum amount
        $methodMin = (float) ($methodConfig['min_amount'] ?? 0);
        if ($amount < $methodMin) {
            throw new WalletException(
                "Amount {$amount} {$currency} is below {$paymentMethod} minimum of {$methodMin} {$currency}",
                'PAYMENT_METHOD_MIN_AMOUNT_VIOLATION',
                [
                    'payment_method' => $paymentMethod,
                    'operation' => $operation,
                    'amount' => $amount,
                    'min_amount' => $methodMin,
                    'currency' => $currency,
                    'limit_type' => 'payment_method_minimum'
                ]
            );
        }

        // Check payment method maximum amount
        $methodMax = (float) ($methodConfig['max_amount'] ?? PHP_FLOAT_MAX);
        if ($amount > $methodMax) {
            throw new WalletException(
                "Amount {$amount} {$currency} exceeds {$paymentMethod} maximum of {$methodMax} {$currency}",
                'PAYMENT_METHOD_MAX_AMOUNT_VIOLATION',
                [
                    'payment_method' => $paymentMethod,
                    'operation' => $operation,
                    'amount' => $amount,
                    'max_amount' => $methodMax,
                    'currency' => $currency,
                    'limit_type' => 'payment_method_maximum'
                ]
            );
        }
    }

    /**
     * Get effective limits for a given operation and payment method.
     * Returns the most restrictive limits from both global and payment method configs.
     */
    public function getEffectiveLimits(string $operation, string $paymentMethod, string $currency): array
    {
        $globalPermissions = wallet_config("permissions.{$operation}", []);
        $methodConfig = wallet_payment_method_config($paymentMethod);

        $globalMin = (float) ($globalPermissions['min_amount'] ?? 0);
        $globalMax = (float) ($globalPermissions['max_amount'] ?? PHP_FLOAT_MAX);
        
        $methodMin = (float) ($methodConfig['min_amount'] ?? 0);
        $methodMax = (float) ($methodConfig['max_amount'] ?? PHP_FLOAT_MAX);

        // Most restrictive limits win
        $effectiveMin = max($globalMin, $methodMin);
        $effectiveMax = min($globalMax, $methodMax);

        return [
            'min_amount' => $effectiveMin,
            'max_amount' => $effectiveMax,
            'currency' => $currency,
            'global_limits' => [
                'min' => $globalMin,
                'max' => $globalMax,
            ],
            'payment_method_limits' => [
                'min' => $methodMin,
                'max' => $methodMax,
            ],
            'most_restrictive' => [
                'min_source' => $effectiveMin === $globalMin ? 'global' : 'payment_method',
                'max_source' => $effectiveMax === $globalMax ? 'global' : 'payment_method',
            ]
        ];
    }
}
