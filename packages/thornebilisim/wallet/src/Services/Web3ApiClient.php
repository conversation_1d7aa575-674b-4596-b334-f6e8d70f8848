<?php

namespace Thorne\Wallet\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Exceptions\Web3ApiException;

class Web3ApiClient
{
    protected string $baseUrl;

    protected string $method = 'GET';

    protected ?int $timeout = null;

    protected int $retryAttempts;

    protected array $headers = [];

    protected array $queryParams = [];

    protected bool $enableLogging;

    protected string $logChannel;

    public function __construct()
    {
        $this->baseUrl       = wallet_config('web3.base_url');
        $this->timeout       = wallet_config('web3.timeout', 30);
        $this->retryAttempts = wallet_config('web3.retry_attempts', 3);
        $this->enableLogging = wallet_config('logging.enabled', true);
        $this->logChannel    = wallet_config('logging.channel', 'wallet');

        $this->headers = [
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
        ];
    }

    /**
     * Set the endpoint path.
     */
    public function endpoint(string $path): self
    {
        $this->baseUrl = rtrim(wallet_config('web3.base_url'), '/').'/'.ltrim($path, '/');

        return $this;
    }

    /**
     * Set HTTP method.
     */
    public function method(string $method): self
    {
        $this->method = strtoupper($method);

        return $this;
    }

    /**
     * Set request timeout.
     */
    public function timeout(int $timeout): self
    {
        $this->timeout = $timeout;

        return $this;
    }

    /**
     * Set retry attempts.
     */
    public function retries(int $attempts): self
    {
        $this->retryAttempts = $attempts;

        return $this;
    }

    /**
     * Add custom headers.
     */
    public function withHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);

        return $this;
    }

    /**
     * Add query parameters.
     */
    public function withQuery(array $params): self
    {
        $this->queryParams = array_merge($this->queryParams, $params);

        return $this;
    }

    /**
     * Disable logging for this request.
     */
    public function withoutLogging(): self
    {
        $this->enableLogging = false;

        return $this;
    }

    /**
     * Make GET request.
     */
    public function get(array $query = []): Response
    {
        return $this->method('GET')->withQuery($query)->send();
    }

    /**
     * Make POST request.
     */
    public function post(array $data = []): Response
    {
        return $this->method('POST')->send($data);
    }

    /**
     * Make PUT request.
     */
    public function put(array $data = []): Response
    {
        return $this->method('PUT')->send($data);
    }

    /**
     * Make DELETE request.
     */
    public function delete(array $data = []): Response
    {
        return $this->method('DELETE')->send($data);
    }

    /**
     * Send the request with OAuth 1.0 authentication.
     */
    public function send(array $data = []): Response
    {
        $requestId = uniqid('web3_', true);
        $startTime = microtime(true);

        try {
            // Add OAuth 1.0 authorization header
            $this->headers['Authorization'] = $this->generateOAuthSignature($data);

            // Build full URL with query parameters
            $fullUrl = $this->baseUrl;
            if (! empty($this->queryParams)) {
                $fullUrl .= '?'.http_build_query($this->queryParams);
            }

            $this->logRequest($requestId, $fullUrl, $data);

            $response = $this->executeRequest($fullUrl, $data);

            $this->logResponse($requestId, $response, microtime(true) - $startTime);

            return $response;

        } catch (\Exception $e) {
            $this->logError($requestId, $e, microtime(true) - $startTime);
            throw new Web3ApiException(
                "Web3 API request failed: {$e->getMessage()}",
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Execute HTTP request with retry logic.
     */
    protected function executeRequest(string $url, array $data = []): Response
    {
        $lastException = null;

        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $httpClient = Http::withHeaders($this->headers);

                if ($this->timeout) {
                    $httpClient = $httpClient->timeout($this->timeout);
                }

                $response = match ($this->method) {
                    'GET'    => $httpClient->get($url),
                    'POST'   => $httpClient->post($url, $data),
                    'PUT'    => $httpClient->put($url, $data),
                    'DELETE' => $httpClient->delete($url, $data),
                    default  => throw new \InvalidArgumentException("Unsupported HTTP method: {$this->method}")
                };

                // Check if response is successful
                if ($response->successful()) {
                    return $response;
                }

                // Handle HTTP errors
                throw new \RuntimeException(
                    "HTTP {$response->status()}: {$response->body()}",
                    $response->status()
                );

            } catch (\Exception $e) {
                $lastException = $e;

                if ($attempt < $this->retryAttempts) {
                    $delay = pow(2, $attempt - 1); // Exponential backoff
                    sleep($delay);

                    if ($this->enableLogging) {
                        Log::channel($this->logChannel)->warning('Web3 API request retry', [
                            'attempt'       => $attempt,
                            'max_attempts'  => $this->retryAttempts,
                            'delay_seconds' => $delay,
                            'error'         => $e->getMessage(),
                            'url'           => $url,
                        ]);
                    }
                }
            }
        }

        throw $lastException;
    }

    /**
     * Generate OAuth 1.0 signature for authentication.
     */
    protected function generateOAuthSignature(array $bodyData = []): string
    {
        $endpointUrl   = explode('?', $this->baseUrl)[0];
        $endpointQuery = parse_url($this->baseUrl);
        $jsonData      = ! empty($bodyData) ? json_encode($bodyData) : '';

        // Generate body hash
        $hash       = hash('sha256', $jsonData, true);
        $base64Data = base64_encode($hash);

        // OAuth parameters
        $oauthParams = [
            'oauth_consumer_key'     => config('app.oauth_consumer_key'),
            'oauth_signature_method' => 'RSA-SHA256',
            'oauth_timestamp'        => (string) time(),
            'oauth_nonce'            => $this->generateNonce(11),
            'oauth_version'          => '1.0',
            'oauth_body_hash'        => $base64Data,
        ];

        // Build parameter array for signature
        $paramArray = [];
        foreach ($oauthParams as $key => $value) {
            $paramArray[] = urlencode($key).'='.urlencode($value);
        }

        // Add query parameters if present
        if (isset($endpointQuery['query'])) {
            parse_str($endpointQuery['query'], $queryParams);
            foreach ($queryParams as $key => $value) {
                $paramArray[] = urlencode($key).'='.urlencode($value);
            }
        }

        // Sort parameters
        sort($paramArray);
        $paramString = implode('&', $paramArray);

        // Create signature base string
        $baseString = sprintf(
            '%s&%s&%s',
            strtoupper($this->method),
            urlencode($endpointUrl),
            urlencode($paramString)
        );

        // Generate signature
        $signature = base64_encode($this->signWithPrivateKey($baseString));

        // Build authorization header
        return $this->buildAuthorizationHeader($oauthParams, $signature);
    }

    /**
     * Sign data with private key.
     */
    protected function signWithPrivateKey(string $data): string
    {
        $privateKeyPath = base_path('private-key.pem');

        if (! file_exists($privateKeyPath)) {
            throw new \RuntimeException('Private key file not found: '.$privateKeyPath);
        }

        $privateKey = openssl_pkey_get_private(file_get_contents($privateKeyPath));

        if (! $privateKey) {
            throw new \RuntimeException('Failed to load private key');
        }

        if (! openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            throw new \RuntimeException('Failed to sign data with private key');
        }

        return $signature;
    }

    /**
     * Generate random nonce.
     */
    protected function generateNonce(int $length): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce      = '';
        $max        = strlen($characters) - 1;

        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $max)];
        }

        return $nonce;
    }

    /**
     * Build OAuth authorization header.
     */
    protected function buildAuthorizationHeader(array $oauthParams, string $signature): string
    {
        $oauthParams['oauth_signature'] = $signature;

        $headerParts = ['OAuth '];
        $first       = true;

        foreach ($oauthParams as $key => $value) {
            if (! $first) {
                $headerParts[] = ', ';
            }
            $headerParts[] = $key.'="'.$value.'"';
            $first         = false;
        }

        return implode('', $headerParts);
    }

    /**
     * Log request details.
     */
    protected function logRequest(string $requestId, string $url, array $data): void
    {
        if (! $this->enableLogging) {
            return;
        }

        Log::channel($this->logChannel)->info('Web3 API Request', [
            'request_id'     => $requestId,
            'method'         => $this->method,
            'url'            => $url,
            'headers'        => $this->sanitizeHeaders($this->headers),
            'data'           => $data,
            'timeout'        => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
        ]);
    }

    /**
     * Log response details.
     */
    protected function logResponse(string $requestId, Response $response, float $duration): void
    {
        if (! $this->enableLogging) {
            return;
        }

        Log::channel($this->logChannel)->info('Web3 API Response', [
            'request_id'  => $requestId,
            'status'      => $response->status(),
            'headers'     => $response->headers(),
            'body'        => $response->json(),
            'duration_ms' => round($duration * 1000, 2),
            'successful'  => $response->successful(),
        ]);
    }

    /**
     * Log error details.
     */
    protected function logError(string $requestId, \Exception $exception, float $duration): void
    {
        if (! $this->enableLogging) {
            return;
        }

        Log::channel($this->logChannel)->error('Web3 API Error', [
            'request_id'  => $requestId,
            'error'       => $exception->getMessage(),
            'code'        => $exception->getCode(),
            'duration_ms' => round($duration * 1000, 2),
            'trace'       => $exception->getTraceAsString(),
        ]);
    }

    /**
     * Sanitize headers for logging (remove sensitive data).
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sanitized = $headers;

        if (isset($sanitized['Authorization'])) {
            $sanitized['Authorization'] = '[REDACTED]';
        }

        return $sanitized;
    }

    /**
     * Create a new instance for fluent interface.
     */
    public static function make(): self
    {
        return new static;
    }
}
