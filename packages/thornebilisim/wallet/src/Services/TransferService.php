<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Enums\TransferStatus;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\PermissionService;

class TransferService
{
    public function __construct(
        protected BalanceService $balanceService
    ) {}

    /**
     * Create an internal transfer between customers.
     */
    public function createTransfer(array $data): WalletTransfer
    {
        $this->validateTransferData($data);

        // Get accounts
        $fromAccount = WalletAccount::findByAccountNumber($data['from_account_number']);
        $toAccount   = WalletAccount::findByAccountNumber($data['to_account_number']);

        if (! $fromAccount || ! $toAccount) {
            throw new \InvalidArgumentException('Invalid account number(s)');
        }

        if ($fromAccount->currency !== $toAccount->currency) {
            throw new \InvalidArgumentException('Currency mismatch between accounts');
        }

        // Check permissions
        $this->checkTransferPermissions($fromAccount->customer_id, $fromAccount->currency, $data['amount']);

        // Check balance
        if (! $this->balanceService->hasSufficientBalance($fromAccount->customer_id, $fromAccount->currency, $data['amount'])) {
            throw new \InvalidArgumentException('Insufficient balance for transfer');
        }

        // Calculate fee and net amount
        $fee         = $this->calculateTransferFee($data['amount'], $fromAccount->currency);
        $totalAmount = bcadd($data['amount'], $fee, 8);

        if (! $this->balanceService->hasSufficientBalance($fromAccount->customer_id, $fromAccount->currency, $totalAmount)) {
            throw new \InvalidArgumentException('Insufficient balance for transfer including fees');
        }

        DB::beginTransaction();

        try {
            // Lock the amount (including fee) from sender's balance
            $this->balanceService->lockAmount($fromAccount->customer_id, $fromAccount->currency, $totalAmount);

            // Create transfer
            $transfer = WalletTransfer::createTransfer([
                'from_customer_id'    => $fromAccount->customer_id,
                'to_customer_id'      => $toAccount->customer_id,
                'from_account_number' => $data['from_account_number'],
                'to_account_number'   => $data['to_account_number'],
                'amount'              => $data['amount'],
                'fee'                 => $fee,
                'total_amount'        => $data['amount'], // Recipient gets full amount
                'description'         => $data['description'] ?? null,
                'metadata'            => array_merge($data['metadata'] ?? [], [
                    'fee_calculation' => [
                        'base_amount'    => $data['amount'],
                        'fee_amount'     => $fee,
                        'total_deducted' => $totalAmount,
                    ],
                ]),
            ]);

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer created', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'from_customer_id' => $fromAccount->customer_id,
                        'to_customer_id'   => $toAccount->customer_id,
                        'amount'           => $data['amount'],
                        'fee'              => $fee,
                        'currency'         => $fromAccount->currency,
                    ]);
            }

            return $transfer;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Process a transfer (complete it).
     */
    public function processTransfer(WalletTransfer $transfer, array $data = []): bool
    {
        if (! $transfer->isPending()) {
            throw new \InvalidArgumentException('Transfer is not in pending status');
        }

        DB::beginTransaction();

        try {
            // Complete the transfer
            $transfer->complete($data);

            // Unlock the locked amount from sender
            $totalAmount = bcadd($transfer->amount, $transfer->fee, 8);
            $this->balanceService->unlockAmount($transfer->from_customer_id, $transfer->currency, $totalAmount);

            // Sync balances with WEB3 if enabled
            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transfer->from_customer_id, $transfer->currency);
                $this->balanceService->syncWithWeb3($transfer->to_customer_id, $transfer->currency);
            }

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer completed', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'from_customer_id' => $transfer->from_customer_id,
                        'to_customer_id'   => $transfer->to_customer_id,
                        'amount'           => $transfer->amount,
                        'fee'              => $transfer->fee,
                        'currency'         => $transfer->currency,
                    ]);
            }

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel a transfer.
     */
    public function cancelTransfer(WalletTransfer $transfer, ?string $reason = null): bool
    {
        if (! $transfer->isPending()) {
            throw new \InvalidArgumentException('Transfer is not in pending status');
        }

        DB::beginTransaction();

        try {
            // Cancel the transfer
            $transfer->cancel($reason);

            // Unlock the locked amount from sender
            $totalAmount = bcadd($transfer->amount, $transfer->fee, 8);
            $this->balanceService->unlockAmount($transfer->from_customer_id, $transfer->currency, $totalAmount);

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer cancelled', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'reason'           => $reason,
                        'from_customer_id' => $transfer->from_customer_id,
                        'to_customer_id'   => $transfer->to_customer_id,
                        'amount'           => $transfer->amount,
                        'currency'         => $transfer->currency,
                    ]);
            }

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get transfer history for a customer.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getTransferHistory(int $customerId, array $filters = [])
    {
        $query = WalletTransfer::forCustomer($customerId)
            ->with(['fromCustomer', 'toCustomer', 'fromAccount', 'toAccount'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (isset($filters['currency'])) {
            $query->forCurrency($filters['currency']);
        }

        if (isset($filters['status'])) {
            $query->withStatus(TransferStatus::from($filters['status']));
        }

        if (isset($filters['direction'])) {
            if ($filters['direction'] === 'outgoing') {
                $query->outgoingForCustomer($customerId);
            } elseif ($filters['direction'] === 'incoming') {
                $query->incomingForCustomer($customerId);
            }
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Calculate transfer fee.
     */
    public function calculateTransferFee(string|float $amount, string $currency): string
    {
        // For now, return zero fee
        // This can be enhanced to include percentage-based fees, fixed fees, etc.
        return '0.********';
    }

    /**
     * Validate transfer data.
     *
     * @throws \InvalidArgumentException
     */
    protected function validateTransferData(array $data): void
    {
        $required = ['from_account_number', 'to_account_number', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        if (strlen($data['from_account_number']) !== 11 || ! ctype_digit($data['from_account_number'])) {
            throw new \InvalidArgumentException('Invalid from_account_number format');
        }

        if (strlen($data['to_account_number']) !== 11 || ! ctype_digit($data['to_account_number'])) {
            throw new \InvalidArgumentException('Invalid to_account_number format');
        }

        if ($data['from_account_number'] === $data['to_account_number']) {
            throw new \InvalidArgumentException('Cannot transfer to the same account');
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw new \InvalidArgumentException('Amount must be greater than zero');
        }

        // Validate account numbers using Luhn algorithm
        $luhnService = app(LuhnService::class);

        if (! $luhnService->validateAccountNumber($data['from_account_number'])) {
            throw new \InvalidArgumentException('Invalid from_account_number (Luhn validation failed)');
        }

        if (! $luhnService->validateAccountNumber($data['to_account_number'])) {
            throw new \InvalidArgumentException('Invalid to_account_number (Luhn validation failed)');
        }
    }

    /**
     * Check transfer permissions for a customer.
     *
     * @throws \InvalidArgumentException
     */
    protected function checkTransferPermissions(int $customerId, string $currency, string|float $amount): void
    {
        // Use PermissionService for comprehensive permission checks
        $permissionService = app(PermissionService::class);
        $permissionService->validateOperationOrFail('transfer', $customerId, (float) $amount, $currency);

        // Additional transfer-specific checks
        $permissions = wallet_config('permissions.transfer', []);

        if (! $permissions['enabled']) {
            throw new \InvalidArgumentException('Transfers are currently disabled');
        }

        // Check minimum amount
        $minAmount = $permissions['min_amount'] ?? '0.********';
        if (bccomp($amount, $minAmount, 8) < 0) {
            throw new \InvalidArgumentException("Transfer amount must be at least {$minAmount} {$currency}");
        }

        // Check maximum amount
        $maxAmount = $permissions['max_amount'] ?? null;
        if ($maxAmount && bccomp($amount, $maxAmount, 8) > 0) {
            throw new \InvalidArgumentException("Transfer amount cannot exceed {$maxAmount} {$currency}");
        }

        // Check transfer-specific daily/monthly limits
        $this->checkTransferLimits($customerId, $currency, $amount);
    }

    /**
     * Check transfer-specific limits.
     */
    protected function checkTransferLimits(int $customerId, string $currency, string|float $amount): void
    {
        $permissions = wallet_config('permissions.transfer', []);

        // Daily transfer limit
        if (isset($permissions['daily_limit']) && $permissions['daily_limit'] > 0) {
            $dailyUsage = $this->getDailyTransferUsage($customerId, $currency);
            if (($dailyUsage + (float) $amount) > $permissions['daily_limit']) {
                $remaining = max(0, $permissions['daily_limit'] - $dailyUsage);
                throw new \InvalidArgumentException("Daily transfer limit exceeded. Remaining: {$remaining} {$currency}");
            }
        }

        // Monthly transfer limit
        if (isset($permissions['monthly_limit']) && $permissions['monthly_limit'] > 0) {
            $monthlyUsage = $this->getMonthlyTransferUsage($customerId, $currency);
            if (($monthlyUsage + (float) $amount) > $permissions['monthly_limit']) {
                $remaining = max(0, $permissions['monthly_limit'] - $monthlyUsage);
                throw new \InvalidArgumentException("Monthly transfer limit exceeded. Remaining: {$remaining} {$currency}");
            }
        }
    }

    /**
     * Get daily transfer usage for customer.
     */
    protected function getDailyTransferUsage(int $customerId, string $currency): float
    {
        return (float) WalletTransfer::where('from_customer_id', $customerId)
            ->where('currency', $currency)
            ->where('status', TransferStatus::COMPLETED)
            ->whereDate('created_at', today())
            ->sum('amount');
    }

    /**
     * Get monthly transfer usage for customer.
     */
    protected function getMonthlyTransferUsage(int $customerId, string $currency): float
    {
        return (float) WalletTransfer::where('from_customer_id', $customerId)
            ->where('currency', $currency)
            ->where('status', TransferStatus::COMPLETED)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');
    }
}
