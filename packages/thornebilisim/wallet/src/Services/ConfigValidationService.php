<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Facades\Log;

class ConfigValidationService
{
    /**
     * Validate wallet configuration.
     */
    public function validateConfiguration(): array
    {
        $errors = [];

        // Validate currencies configuration
        $currencies = wallet_get_enabled_currencies();
        if (empty($currencies)) {
            $errors[] = 'No enabled currencies found in wallet configuration';
        }

        foreach ($currencies as $currency => $config) {
            if (empty($config['iso_code'])) {
                $errors[] = "Missing iso_code for currency: {$currency}";
            }

            if (empty($config['symbol'])) {
                $errors[] = "Missing symbol for currency: {$currency}";
            }

            if (!isset($config['exponent'])) {
                $errors[] = "Missing exponent for currency: {$currency}";
            }
        }

        // Validate payment methods configuration
        $paymentMethods = wallet_config('payment_methods', []);
        foreach ($paymentMethods as $method => $config) {
            if (!isset($config['enabled'])) {
                $errors[] = "Missing enabled flag for payment method: {$method}";
            }

            if (!isset($config['can_deposit']) || !isset($config['can_withdraw'])) {
                $errors[] = "Missing deposit/withdraw flags for payment method: {$method}";
            }

            if (empty($config['name'])) {
                $errors[] = "Missing name for payment method: {$method}";
            }
        }

        // Validate permissions configuration
        $operations = ['deposit', 'withdraw', 'transfer'];
        foreach ($operations as $operation) {
            $permissions = wallet_config("permissions.{$operation}", []);
            
            if (!isset($permissions['enabled'])) {
                $errors[] = "Missing enabled flag for operation: {$operation}";
            }

            if (isset($permissions['min_amount']) && isset($permissions['max_amount'])) {
                if (bccomp($permissions['min_amount'], $permissions['max_amount'], 8) > 0) {
                    $errors[] = "Min amount is greater than max amount for operation: {$operation}";
                }
            }

            if (isset($permissions['daily_limit']) && isset($permissions['monthly_limit'])) {
                if ($permissions['daily_limit'] > $permissions['monthly_limit']) {
                    $errors[] = "Daily limit is greater than monthly limit for operation: {$operation}";
                }
            }
        }

        // Validate WEB3 configuration
        if (wallet_config('web3.enabled', false)) {
            $web3Config = wallet_config('web3', []);
            
            if (empty($web3Config['base_url'])) {
                $errors[] = 'WEB3 is enabled but base_url is missing';
            }

            if (empty($web3Config['api_key'])) {
                $errors[] = 'WEB3 is enabled but api_key is missing';
            }
        }

        return $errors;
    }

    /**
     * Validate configuration and log errors.
     */
    public function validateAndLog(): bool
    {
        $errors = $this->validateConfiguration();

        if (!empty($errors)) {
            Log::error('Wallet configuration validation failed', [
                'errors' => $errors,
                'config_file' => 'wallet.php'
            ]);

            return false;
        }

        Log::info('Wallet configuration validation passed');
        return true;
    }

    /**
     * Validate configuration and throw exception if invalid.
     */
    public function validateOrFail(): void
    {
        $errors = $this->validateConfiguration();

        if (!empty($errors)) {
            throw new \InvalidArgumentException(
                'Wallet configuration is invalid: ' . implode(', ', $errors)
            );
        }
    }
}
