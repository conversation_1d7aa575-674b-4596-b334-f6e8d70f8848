<?php

namespace Thorne\Wallet\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Exceptions\WalletPermissionException;
use Thorne\Wallet\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;

class PermissionService
{
    /**
     * Check if operation is enabled.
     */
    public function isOperationEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enabled", true);
    }

    /**
     * Check if KYC is required for operation.
     */
    public function isKycRequired(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.require_kyc", true);
    }

    /**
     * Check if approval is required for operation.
     */
    public function isApprovalRequired(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.require_approval", false);
    }

    /**
     * Check if daily limit is enabled for operation.
     */
    public function isDailyLimitEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_daily_limit", true);
    }

    /**
     * Check if monthly limit is enabled for operation.
     */
    public function isMonthlyLimitEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_monthly_limit", true);
    }

    /**
     * Check if amount limits are enabled for operation.
     */
    public function areAmountLimitsEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_amount_limits", true);
    }

    /**
     * Get daily limit for operation.
     */
    public function getDailyLimit(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.daily_limit", 0);
    }

    /**
     * Get monthly limit for operation.
     */
    public function getMonthlyLimit(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.monthly_limit", 0);
    }

    /**
     * Get minimum amount for operation.
     */
    public function getMinAmount(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.min_amount", 0);
    }

    /**
     * Get maximum amount for operation.
     */
    public function getMaxAmount(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.max_amount", 0);
    }

    /**
     * Validate operation permissions.
     */
    public function validateOperation(string $operation, int $customerId, float $amount, string $currency = 'EUR'): array
    {
        $errors = [];

        // Check if operation is enabled
        if (! $this->isOperationEnabled($operation)) {
            $errors[] = "Operation '{$operation}' is currently disabled";
        }

        // Check KYC requirement
        if ($this->isKycRequired($operation)) {
            $customer = Customer::find($customerId);
            if (! $customer || ! $this->isCustomerKycVerified($customer)) {
                $errors[] = "KYC verification is required for {$operation} operations";
            }
        }

        // Check amount limits
        if ($this->areAmountLimitsEnabled($operation)) {
            $minAmount = $this->getMinAmount($operation);
            $maxAmount = $this->getMaxAmount($operation);

            if ($amount < $minAmount) {
                $errors[] = 'Amount must be at least '.number_format($minAmount, 2)." {$currency}";
            }

            if ($maxAmount > 0 && $amount > $maxAmount) {
                $errors[] = 'Amount cannot exceed '.number_format($maxAmount, 2)." {$currency}";
            }
        }

        // Check daily limit
        if ($this->isDailyLimitEnabled($operation)) {
            $dailyUsage = $this->getDailyUsage($customerId, $operation, $currency);
            $dailyLimit = $this->getDailyLimit($operation);

            if ($dailyLimit > 0 && ($dailyUsage + $amount) > $dailyLimit) {
                $remaining = max(0, $dailyLimit - $dailyUsage);
                $errors[]  = 'Daily limit exceeded. Remaining: '.number_format($remaining, 2)." {$currency}";
            }
        }

        // Check monthly limit
        if ($this->isMonthlyLimitEnabled($operation)) {
            $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);
            $monthlyLimit = $this->getMonthlyLimit($operation);

            if ($monthlyLimit > 0 && ($monthlyUsage + $amount) > $monthlyLimit) {
                $remaining = max(0, $monthlyLimit - $monthlyUsage);
                $errors[]  = 'Monthly limit exceeded. Remaining: '.number_format($remaining, 2)." {$currency}";
            }
        }

        return $errors;
    }

    /**
     * Check if operation is allowed.
     */
    public function isOperationAllowed(string $operation, int $customerId, float $amount, string $currency = 'EUR'): bool
    {
        $errors = $this->validateOperation($operation, $customerId, $amount, $currency);

        return empty($errors);
    }

    /**
     * Validate operation and throw exception if not allowed.
     */
    public function validateOperationOrFail(string $operation, int $customerId, float $amount, string $currency = 'EUR'): void
    {
        $errors = $this->validateOperation($operation, $customerId, $amount, $currency);

        if (! empty($errors)) {
            throw new WalletPermissionException(
                'Operation not allowed: '.implode(', ', $errors),
                0,
                null,
                [
                    'operation'   => $operation,
                    'customer_id' => $customerId,
                    'amount'      => $amount,
                    'currency'    => $currency,
                    'errors'      => $errors,
                ]
            );
        }
    }

    /**
     * Get daily usage for customer and operation.
     */
    public function getDailyUsage(int $customerId, string $operation, string $currency = 'EUR'): float
    {
        $transactionType = $this->getTransactionTypeForOperation($operation);
        $startOfDay      = Carbon::today();
        $endOfDay        = Carbon::tomorrow();

        return (float) WalletTransaction::where('customer_id', $customerId)
            ->where('type', $transactionType)
            ->where('currency', $currency)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startOfDay, $endOfDay])
            ->sum('amount');
    }

    /**
     * Get monthly usage for customer and operation.
     */
    public function getMonthlyUsage(int $customerId, string $operation, string $currency = 'EUR'): float
    {
        $transactionType = $this->getTransactionTypeForOperation($operation);
        $startOfMonth    = Carbon::now()->startOfMonth();
        $endOfMonth      = Carbon::now()->endOfMonth();

        return (float) WalletTransaction::where('customer_id', $customerId)
            ->where('type', $transactionType)
            ->where('currency', $currency)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->sum('amount');
    }

    /**
     * Get usage statistics for customer.
     */
    public function getUsageStatistics(int $customerId, string $currency = 'EUR'): array
    {
        $operations = ['deposit', 'withdraw', 'transfer'];
        $stats      = [];

        foreach ($operations as $operation) {
            $dailyUsage   = $this->getDailyUsage($customerId, $operation, $currency);
            $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);
            $dailyLimit   = $this->getDailyLimit($operation);
            $monthlyLimit = $this->getMonthlyLimit($operation);

            $stats[$operation] = [
                'daily' => [
                    'used'       => $dailyUsage,
                    'limit'      => $dailyLimit,
                    'remaining'  => max(0, $dailyLimit - $dailyUsage),
                    'percentage' => $dailyLimit > 0 ? ($dailyUsage / $dailyLimit) * 100 : 0,
                    'enabled'    => $this->isDailyLimitEnabled($operation),
                ],
                'monthly' => [
                    'used'       => $monthlyUsage,
                    'limit'      => $monthlyLimit,
                    'remaining'  => max(0, $monthlyLimit - $monthlyUsage),
                    'percentage' => $monthlyLimit > 0 ? ($monthlyUsage / $monthlyLimit) * 100 : 0,
                    'enabled'    => $this->isMonthlyLimitEnabled($operation),
                ],
                'amount_limits' => [
                    'min'     => $this->getMinAmount($operation),
                    'max'     => $this->getMaxAmount($operation),
                    'enabled' => $this->areAmountLimitsEnabled($operation),
                ],
                'settings' => [
                    'enabled'          => $this->isOperationEnabled($operation),
                    'require_kyc'      => $this->isKycRequired($operation),
                    'require_approval' => $this->isApprovalRequired($operation),
                ],
            ];
        }

        return $stats;
    }

    /**
     * Get usage statistics for all currencies.
     */
    public function getUsageStatisticsForAllCurrencies(int $customerId): array
    {
        $currencies = wallet_get_enabled_currencies();
        $stats      = [];

        foreach ($currencies as $currency => $config) {
            $stats[$currency] = $this->getUsageStatistics($customerId, $currency);
        }

        return $stats;
    }

    /**
     * Get operation limits for display.
     */
    public function getOperationLimits(string $operation): array
    {
        return [
            'enabled'          => $this->isOperationEnabled($operation),
            'require_kyc'      => $this->isKycRequired($operation),
            'require_approval' => $this->isApprovalRequired($operation),
            'daily_limit'      => [
                'enabled' => $this->isDailyLimitEnabled($operation),
                'amount'  => $this->getDailyLimit($operation),
            ],
            'monthly_limit' => [
                'enabled' => $this->isMonthlyLimitEnabled($operation),
                'amount'  => $this->getMonthlyLimit($operation),
            ],
            'amount_limits' => [
                'enabled' => $this->areAmountLimitsEnabled($operation),
                'min'     => $this->getMinAmount($operation),
                'max'     => $this->getMaxAmount($operation),
            ],
        ];
    }

    /**
     * Check if customer KYC is verified.
     */
    protected function isCustomerKycVerified(Customer $customer): bool
    {
        // This should integrate with your KYC system
        // For now, we'll check if customer has required fields
        return ! empty($customer->email)      &&
               ! empty($customer->first_name) &&
               ! empty($customer->last_name);
    }

    /**
     * Get transaction type for operation.
     */
    protected function getTransactionTypeForOperation(string $operation): string
    {
        return match ($operation) {
            'deposit'  => TransactionType::DEPOSIT->value,
            'withdraw' => TransactionType::WITHDRAWAL->value,
            'transfer' => TransactionType::TRANSFER->value,
            default    => $operation,
        };
    }

    /**
     * Log permission check.
     */
    public function logPermissionCheck(string $operation, int $customerId, float $amount, string $currency, bool $allowed, array $errors = []): void
    {
        if (! wallet_config('logging.enabled', true)) {
            return;
        }

        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->info('Permission check', [
                'operation'   => $operation,
                'customer_id' => $customerId,
                'amount'      => $amount,
                'currency'    => $currency,
                'allowed'     => $allowed,
                'errors'      => $errors,
                'timestamp'   => now()->toISOString(),
            ]);
    }

    /**
     * Get remaining limits for customer.
     */
    public function getRemainingLimits(int $customerId, string $operation, string $currency = 'EUR'): array
    {
        $dailyUsage   = $this->getDailyUsage($customerId, $operation, $currency);
        $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);
        $dailyLimit   = $this->getDailyLimit($operation);
        $monthlyLimit = $this->getMonthlyLimit($operation);

        return [
            'daily_remaining'    => max(0, $dailyLimit - $dailyUsage),
            'monthly_remaining'  => max(0, $monthlyLimit - $monthlyUsage),
            'max_allowed_amount' => min(
                $this->getMaxAmount($operation),
                max(0, $dailyLimit - $dailyUsage),
                max(0, $monthlyLimit - $monthlyUsage)
            ),
        ];
    }
}
