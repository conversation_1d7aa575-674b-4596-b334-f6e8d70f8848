<?php

namespace Thorne\Wallet\Services;

use InvalidArgumentException;

class LuhnService
{
    /**
     * Generate a check digit using the Luhn algorithm.
     */
    public function generateCheckDigit(string $number): int
    {
        if (! ctype_digit($number)) {
            throw new InvalidArgumentException('Number must contain only digits');
        }

        $sum    = 0;
        $length = strlen($number);
        $parity = $length % 2;

        for ($i = 0; $i < $length; $i++) {
            $digit = (int) $number[$i];

            if ($i % 2 === $parity) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }

            $sum += $digit;
        }

        return (10 - ($sum % 10)) % 10;
    }

    /**
     * Validate a number using the <PERSON>hn algorithm.
     */
    public function validate(string $number): bool
    {
        if (! ctype_digit($number) || strlen($number) < 2) {
            return false;
        }

        $checkDigit         = (int) substr($number, -1);
        $numberWithoutCheck = substr($number, 0, -1);

        return $this->generateCheckDigit($numberWithoutCheck) === $checkDigit;
    }

    /**
     * Generate a complete number with check digit.
     */
    public function generateWithCheckDigit(string $baseNumber): string
    {
        if (! ctype_digit($baseNumber)) {
            throw new InvalidArgumentException('Base number must contain only digits');
        }

        $checkDigit = $this->generateCheckDigit($baseNumber);

        return $baseNumber.$checkDigit;
    }

    /**
     * Generate a random number of specified length.
     */
    public function generateRandomNumber(int $length): string
    {
        if ($length <= 0) {
            throw new InvalidArgumentException('Length must be greater than 0');
        }

        $number = '';
        for ($i = 0; $i < $length; $i++) {
            // First digit cannot be 0 to avoid leading zeros
            $number .= $i === 0 ? random_int(1, 9) : random_int(0, 9);
        }

        return $number;
    }

    /**
     * Generate an account number with currency code and check digit.
     */
    public function generateAccountNumber(string $currencyIsoCode, int $randomLength = 7): string
    {
        if (strlen($currencyIsoCode) !== 3 || ! ctype_digit($currencyIsoCode)) {
            throw new InvalidArgumentException('Currency ISO code must be exactly 3 digits');
        }

        if ($randomLength <= 0) {
            throw new InvalidArgumentException('Random length must be greater than 0');
        }

        // Generate random part
        $randomPart = $this->generateRandomNumber($randomLength);

        // Combine currency code with random part
        $baseNumber = $currencyIsoCode.$randomPart;

        // Generate check digit for the base number
        $checkDigit = $this->generateCheckDigit($baseNumber);

        // Insert check digit after currency code
        // Format: [Currency ISO Code (3)] + [Check Digit (1)] + [Random (7)]
        return $currencyIsoCode.$checkDigit.$randomPart;
    }

    /**
     * Validate an account number format.
     */
    public function validateAccountNumber(string $accountNumber, ?string $expectedCurrency = null): bool
    {
        // Check basic format (11 digits)
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return false;
        }

        // Extract parts
        $currencyCode = substr($accountNumber, 0, 3);
        $checkDigit   = substr($accountNumber, 3, 1);
        $randomPart   = substr($accountNumber, 4, 7);

        // Validate currency if specified
        if ($expectedCurrency !== null && $currencyCode !== $expectedCurrency) {
            return false;
        }

        // Reconstruct base number for validation
        $baseNumber = $currencyCode.$randomPart;

        // Validate check digit
        return $this->generateCheckDigit($baseNumber) === (int) $checkDigit;
    }

    /**
     * Extract currency code from account number.
     */
    public function extractCurrencyCode(string $accountNumber): ?string
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return substr($accountNumber, 0, 3);
    }

    /**
     * Extract check digit from account number.
     */
    public function extractCheckDigit(string $accountNumber): ?int
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return (int) substr($accountNumber, 3, 1);
    }

    /**
     * Extract random part from account number.
     */
    public function extractRandomPart(string $accountNumber): ?string
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return substr($accountNumber, 4, 7);
    }
}
