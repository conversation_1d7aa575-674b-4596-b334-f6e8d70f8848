<?php

namespace Thorne\Wallet\Models\Traits;

use OwenIt\Auditing\Auditable;

trait HasWalletAudit
{
    use Auditable;

    /**
     * Audit kayıtlarında gizlenecek sütunlar
     */
    protected $auditExclude = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Audit kayıtlarında zorunlu olarak dahil edilecek sütunlar
     */
    protected $auditInclude = [];

    /**
     * Sadece değişiklik olduğunda audit kaydı oluştur
     */
    protected $auditStrict = true;

    /**
     * Audit kayıtlarında gösterilecek olaylar
     */
    protected $auditableEvents = [
        'created',
        'updated',
        'deleted',
        'restored'
    ];
} 