<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Webkul\Customer\Models\Customer;

class WalletBalance extends Model
{
    use HasFactory;

    protected $table = 'wallet_balances';

    protected $fillable = [
        'customer_id',
        'currency',
        'balance',
        'locked_balance',
        'last_web3_sync',
        'web3_synced_at',
        'web3_data',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'balance'        => 'decimal:8',
        'locked_balance' => 'decimal:8',
        'last_web3_sync' => 'datetime',
        'web3_synced_at' => 'datetime',
        'web3_data'      => 'array',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
    ];

    /**
     * Get the customer that owns this balance.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the wallet account for this balance.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'customer_id', 'customer_id')
            ->where('currency', $this->currency);
    }

    /**
     * Get available balance (total - locked).
     */
    public function getAvailableBalance(): string
    {
        return bcsub($this->balance, $this->locked_balance, 8);
    }

    /**
     * Get formatted balance for display.
     */
    public function getFormattedBalance(): string
    {
        return wallet_format_amount($this->balance, $this->currency);
    }

    /**
     * Get formatted available balance for display.
     */
    public function getFormattedAvailableBalance(): string
    {
        return wallet_format_amount($this->getAvailableBalance(), $this->currency);
    }

    /**
     * Get formatted locked balance for display.
     */
    public function getFormattedLockedBalance(): string
    {
        return wallet_format_amount($this->locked_balance, $this->currency);
    }

    /**
     * Check if balance is sufficient for amount.
     */
    public function hasSufficientBalance(string|float $amount, bool $includeLockedBalance = false): bool
    {
        $availableBalance = $includeLockedBalance ? $this->balance : $this->getAvailableBalance();

        return bccomp($availableBalance, (string) $amount, 8) >= 0;
    }

    /**
     * Lock amount from available balance.
     *
     * @throws \InvalidArgumentException
     */
    public function lockAmount(string|float $amount): bool
    {
        $amount = (string) $amount;

        if (bccomp($amount, '0', 8) <= 0) {
            throw new \InvalidArgumentException('Lock amount must be greater than zero');
        }

        if (! $this->hasSufficientBalance($amount)) {
            throw new \InvalidArgumentException('Insufficient available balance to lock');
        }

        $newLockedBalance = bcadd($this->locked_balance, $amount, 8);

        return $this->update(['locked_balance' => $newLockedBalance]);
    }

    /**
     * Unlock amount from locked balance.
     *
     * @throws \InvalidArgumentException
     */
    public function unlockAmount(string|float $amount): bool
    {
        $amount = (string) $amount;

        if (bccomp($amount, '0', 8) <= 0) {
            throw new \InvalidArgumentException('Unlock amount must be greater than zero');
        }

        if (bccomp($this->locked_balance, $amount, 8) < 0) {
            throw new \InvalidArgumentException('Insufficient locked balance to unlock');
        }

        $newLockedBalance = bcsub($this->locked_balance, $amount, 8);

        return $this->update(['locked_balance' => $newLockedBalance]);
    }

    /**
     * Update balance from WEB3 service data.
     */
    public function updateFromWeb3(array $web3Data): bool
    {
        $balance       = $web3Data['balance']        ?? '0';
        $lockedBalance = $web3Data['locked_balance'] ?? $this->locked_balance;

        return $this->update([
            'balance'        => $balance,
            'locked_balance' => $lockedBalance,
            'last_web3_sync' => now(),
            'web3_data'      => $web3Data,
        ]);
    }

    /**
     * Check if balance needs WEB3 sync.
     */
    public function needsWeb3Sync(int $maxAgeMinutes = 30): bool
    {
        if ($this->last_web3_sync === null) {
            return true;
        }

        return $this->last_web3_sync->diffInMinutes(now()) > $maxAgeMinutes;
    }

    /**
     * Get or create balance for customer and currency.
     */
    public static function getOrCreateForCustomer(int $customerId, string $currency): static
    {
        return static::firstOrCreate(
            [
                'customer_id' => $customerId,
                'currency'    => $currency,
            ],
            [
                'balance'        => '0.00000000',
                'locked_balance' => '0.00000000',
                'web3_data'      => [],
            ]
        );
    }

    /**
     * Get all balances for a customer.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getCustomerBalances(int $customerId)
    {
        return static::where('customer_id', $customerId)->get();
    }

    /**
     * Get customer balance for specific currency.
     */
    public static function getCustomerBalance(int $customerId, string $currency): ?static
    {
        return static::where('customer_id', $customerId)
            ->where('currency', $currency)
            ->first();
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter balances that need sync.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeedsSync($query, int $maxAgeMinutes = 30)
    {
        return $query->where(function ($q) use ($maxAgeMinutes) {
            $q->whereNull('last_web3_sync')
                ->orWhere('last_web3_sync', '<', now()->subMinutes($maxAgeMinutes));
        });
    }

    /**
     * Scope to filter balances with positive amounts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithBalance($query)
    {
        return $query->where('balance', '>', '0');
    }

    /**
     * Get WEB3 address from metadata.
     */
    public function getWeb3Address(): ?string
    {
        return $this->web3_data['address'] ?? null;
    }

    /**
     * Get WEB3 chain ID from metadata.
     */
    public function getWeb3ChainId(): ?string
    {
        return $this->web3_data['chain_id'] ?? null;
    }

    /**
     * Get raw WEB3 balance (before exponent conversion).
     */
    public function getRawWeb3Balance(): ?int
    {
        return $this->web3_data['raw_balance'] ?? null;
    }

    /**
     * Get WEB3 pending balance.
     */
    public function getWeb3PendingBalance(): ?int
    {
        return $this->web3_data['pending_balance'] ?? null;
    }

    /**
     * Get currency exponent from WEB3 data.
     */
    public function getCurrencyExponent(): int
    {
        return $this->web3_data['exponent'] ?? 0;
    }

    /**
     * Check if currency is a stable coin.
     */
    public function isStableCoin(): bool
    {
        return $this->web3_data['is_stable_coin'] ?? false;
    }

    /**
     * Check if currency is a CBDC.
     */
    public function isCbdc(): bool
    {
        return $this->web3_data['is_cbdc'] ?? false;
    }

    /**
     * Get formatted WEB3 sync status.
     */
    public function getWeb3SyncStatus(): array
    {
        return [
            'last_sync'        => $this->web3_synced_at?->toISOString(),
            'is_synced'        => $this->web3_synced_at !== null,
            'sync_age_minutes' => $this->web3_synced_at ? $this->web3_synced_at->diffInMinutes(now()) : null,
            'needs_sync'       => $this->web3_synced_at === null || $this->web3_synced_at->lt(now()->subMinutes(wallet_config('web3.cache_ttl', 30))),
        ];
    }
}
