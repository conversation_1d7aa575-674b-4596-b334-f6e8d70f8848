<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransactionType;
use Webkul\Customer\Models\Customer;

class WalletTransaction extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table = 'wallet_transactions';

    protected $fillable = [
        'customer_id',
        'account_number',
        'reference',
        'type',
        'direction',
        'status',
        'method',
        'currency',
        'amount',
        'fee',
        'total_amount',
        'exchange_rate',
        'description',
        'metadata',
        'external_reference',
        'external_transaction_id',
        'processed_at',
        'failed_at',
        'failure_reason',
        'parent_id',
        'transfer_id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'type'          => TransactionType::class,
        'direction'     => TransactionDirection::class,
        'status'        => TransactionStatus::class,
        'amount'        => 'decimal:8',
        'fee'           => 'decimal:8',
        'total_amount'  => 'decimal:8',
        'exchange_rate' => 'decimal:8',
        'metadata'      => 'array',
        'processed_at'  => 'datetime',
        'failed_at'     => 'datetime',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
    ];

    /**
     * Get the options for the activity log.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => "Transaction {$eventName}: {$this->reference}");
    }

    /**
     * Get the customer that owns this transaction.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the wallet account for this transaction.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'account_number', 'account_number');
    }

    /**
     * Get the parent transaction.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get child transactions.
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get related transfer if this is a transfer transaction.
     */
    public function transfer(): BelongsTo
    {
        return $this->belongsTo(WalletTransfer::class, 'transfer_id');
    }

    /**
     * Generate a unique reference for the transaction.
     */
    public static function generateReference(string $type = 'TXN'): string
    {
        return wallet_generate_reference($type);
    }

    /**
     * Create a deposit transaction.
     */
    public static function createDeposit(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('DEP');

        return static::create([
            'customer_id'             => $data['customer_id'],
            'account_number'          => $data['account_number'] ?? null,
            'reference'               => $reference,
            'type'                    => TransactionType::DEPOSIT,
            'direction'               => TransactionDirection::INBOUND,
            'status'                  => $data['status'] ?? TransactionStatus::PENDING,
            'method'                  => $data['method'],
            'currency'                => $data['currency'],
            'amount'                  => $data['amount'],
            'fee'                     => $data['fee']                     ?? '0.********',
            'total_amount'            => $data['total_amount']            ?? $data['amount'],
            'description'             => $data['description']             ?? null,
            'metadata'                => $data['metadata']                ?? [],
            'external_reference'      => $data['external_reference']      ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    /**
     * Create a withdrawal transaction.
     */
    public static function createWithdrawal(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('WTH');

        return static::create([
            'customer_id'             => $data['customer_id'],
            'account_number'          => $data['account_number'] ?? null,
            'reference'               => $reference,
            'type'                    => TransactionType::WITHDRAWAL,
            'direction'               => TransactionDirection::OUTBOUND,
            'status'                  => $data['status'] ?? TransactionStatus::PENDING,
            'method'                  => $data['method'],
            'currency'                => $data['currency'],
            'amount'                  => $data['amount'],
            'fee'                     => $data['fee']                     ?? '0.********',
            'total_amount'            => $data['total_amount']            ?? $data['amount'],
            'description'             => $data['description']             ?? null,
            'metadata'                => $data['metadata']                ?? [],
            'external_reference'      => $data['external_reference']      ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    /**
     * Create a transfer transaction.
     */
    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('TRF');

        return static::create([
            'customer_id'    => $data['customer_id'],
            'account_number' => $data['account_number'] ?? null,
            'reference'      => $reference,
            'type'           => TransactionType::TRANSFER,
            'direction'      => $data['direction'],
            'status'         => $data['status'] ?? TransactionStatus::PENDING,
            'method'         => 'internal_transfer',
            'currency'       => $data['currency'],
            'amount'         => $data['amount'],
            'fee'            => $data['fee']          ?? '0.********',
            'total_amount'   => $data['total_amount'] ?? $data['amount'],
            'description'    => $data['description']  ?? null,
            'metadata'       => $data['metadata']     ?? [],
            'parent_id'      => $data['parent_id']    ?? null,
            'transfer_id'    => $data['transfer_id']  ?? null,
        ]);
    }

    /**
     * Mark transaction as completed.
     */
    public function markAsCompleted(array $data = []): bool
    {
        return $this->update([
            'status'                  => TransactionStatus::COMPLETED,
            'processed_at'            => now(),
            'external_transaction_id' => $data['external_transaction_id'] ?? $this->external_transaction_id,
            'metadata'                => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    /**
     * Mark transaction as failed.
     */
    public function markAsFailed(string $reason, array $data = []): bool
    {
        return $this->update([
            'status'         => TransactionStatus::FAILED,
            'failed_at'      => now(),
            'failure_reason' => $reason,
            'metadata'       => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    /**
     * Mark transaction as cancelled.
     */
    public function markAsCancelled(?string $reason = null): bool
    {
        return $this->update([
            'status'         => TransactionStatus::CANCELLED,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Check if transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    /**
     * Check if transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === TransactionStatus::COMPLETED;
    }

    /**
     * Check if transaction is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === TransactionStatus::FAILED;
    }

    /**
     * Check if transaction is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === TransactionStatus::CANCELLED;
    }

    /**
     * Get formatted amount for display.
     */
    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    /**
     * Get formatted net amount for display.
     */
    public function getFormattedTotalAmount(): string
    {
        return wallet_format_amount($this->total_amount, $this->currency);
    }

    /**
     * Get formatted fee for display.
     */
    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    /**
     * Get transaction age in human readable format.
     */
    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope to filter by transaction type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, TransactionType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by transaction status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, TransactionStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter by method.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('method', $method);
    }

    /**
     * Scope to filter pending transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', TransactionStatus::PENDING);
    }

    /**
     * Scope to filter completed transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    /**
     * Scope to filter failed transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', TransactionStatus::FAILED);
    }

    /**
     * Scope to filter transactions within date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Carbon\Carbon  $from
     * @param  \Carbon\Carbon  $to
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInDateRange($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }
}
