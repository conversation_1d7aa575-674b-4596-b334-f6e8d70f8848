<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransferStatus;
use Thorne\Wallet\Models\Traits\HasWalletAudit;
use Webkul\Customer\Models\Customer;

class WalletTransfer extends Model
{
    use HasFactory, SoftDeletes, HasWalletAudit;

    protected $table = 'wallet_transfers';

    protected $fillable = [
        'reference',
        'from_customer_id',
        'to_customer_id',
        'from_account_number',
        'to_account_number',
        'currency',
        'amount',
        'fee',
        'total_amount',
        'status',
        'description',
        'metadata',
        'processed_at',
        'failed_at',
        'failure_reason',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'status'       => TransferStatus::class,
        'amount'       => 'decimal:8',
        'fee'          => 'decimal:8',
        'total_amount' => 'decimal:8',
        'metadata'     => 'array',
        'processed_at' => 'datetime',
        'failed_at'    => 'datetime',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
    ];



    /**
     * Get the sender customer.
     */
    public function fromCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'from_customer_id');
    }

    /**
     * Get the recipient customer.
     */
    public function toCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'to_customer_id');
    }

    /**
     * Get the sender account.
     */
    public function fromAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'from_account_number', 'account_number');
    }

    /**
     * Get the recipient account.
     */
    public function toAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'to_account_number', 'account_number');
    }

    /**
     * Get all related transactions.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'reference', 'reference');
    }

    /**
     * Get the outbound transaction (from sender).
     */
    public function outboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::OUTBOUND);
    }

    /**
     * Get the inbound transaction (to recipient).
     */
    public function inboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::INBOUND);
    }

    /**
     * Create a new transfer with transactions.
     *
     * @throws \Exception
     */
    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? WalletTransaction::generateReference('TRF');

        // Validate accounts exist and are active
        $fromAccount = WalletAccount::findByAccountNumber($data['from_account_number']);
        $toAccount   = WalletAccount::findByAccountNumber($data['to_account_number']);

        if (! $fromAccount || ! $toAccount) {
            throw new \InvalidArgumentException('Invalid account number(s)');
        }

        if ($fromAccount->currency !== $toAccount->currency) {
            throw new \InvalidArgumentException('Currency mismatch between accounts');
        }

        if ($data['from_customer_id'] === $data['to_customer_id']) {
            throw new \InvalidArgumentException('Cannot transfer to same customer');
        }

        \DB::beginTransaction();

        try {
            // Create transfer record
            $transfer = static::create([
                'reference'           => $reference,
                'from_customer_id'    => $data['from_customer_id'],
                'to_customer_id'      => $data['to_customer_id'],
                'from_account_number' => $data['from_account_number'],
                'to_account_number'   => $data['to_account_number'],
                'currency'            => $fromAccount->currency,
                'amount'              => $data['amount'], // Amount received by recipient
                'fee'                 => $data['fee']          ?? '0.********', // Transfer fee
                'total_amount'        => bcadd($data['amount'], $data['fee'] ?? '0.********', 8), // Total deducted from sender (amount + fee)
                'status'              => TransferStatus::PENDING,
                'description'         => $data['description'] ?? null,
                'metadata'            => $data['metadata']    ?? [],
            ]);

            // Generate separate references for outbound and inbound transactions
            $outboundReference = WalletTransaction::generateReference('TRF');
            $inboundReference  = WalletTransaction::generateReference('TRF');

            // Create outbound transaction (sender) - Deducts total_amount (amount + fee)
            WalletTransaction::createTransfer([
                'customer_id'    => $data['from_customer_id'],
                'account_number' => $data['from_account_number'],
                'reference'      => $outboundReference,
                'direction'      => TransactionDirection::OUTBOUND,
                'currency'       => $fromAccount->currency,
                'amount'         => $transfer->amount, // Base transfer amount (what recipient will receive)
                'fee'            => $transfer->fee, // Transfer fee charged to sender
                'total_amount'   => $transfer->total_amount, // Total deducted from sender (amount + fee)
                'description'    => $data['description']  ?? "Transfer to {$data['to_account_number']}",
                'metadata'       => array_merge($data['metadata'] ?? [], [
                    'transfer_type'     => 'outbound',
                    'recipient_account' => $data['to_account_number'],
                    'transfer_semantics' => 'sender_pays_fee',
                ]),
                'transfer_id' => $transfer->id,
            ]);

            // Create inbound transaction (recipient) - Receives amount (no fee deduction)
            WalletTransaction::createTransfer([
                'customer_id'    => $data['to_customer_id'],
                'account_number' => $data['to_account_number'],
                'reference'      => $inboundReference,
                'direction'      => TransactionDirection::INBOUND,
                'currency'       => $toAccount->currency,
                'amount'         => $transfer->amount, // Amount received by recipient (same as transfer amount)
                'fee'            => '0.********', // Recipient doesn't pay fee
                'total_amount'   => $transfer->amount, // Recipient receives the full amount (no fee deduction)
                'description'    => $data['description'] ?? "Transfer from {$data['from_account_number']}",
                'metadata'       => array_merge($data['metadata'] ?? [], [
                    'transfer_type'  => 'inbound',
                    'sender_account' => $data['from_account_number'],
                    'transfer_semantics' => 'recipient_receives_full_amount',
                ]),
                'transfer_id' => $transfer->id,
            ]);

            \DB::commit();

            return $transfer;

        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Complete the transfer.
     */
    public function complete(array $data = []): bool
    {
        \DB::beginTransaction();

        try {
            // Update transfer status
            $this->update([
                'status'       => TransferStatus::COMPLETED,
                'processed_at' => now(),
                'metadata'     => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status'       => TransactionStatus::COMPLETED,
                'processed_at' => now(),
            ]);

            \DB::commit();

            return true;

        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Fail the transfer.
     */
    public function fail(string $reason, array $data = []): bool
    {
        \DB::beginTransaction();

        try {
            // Update transfer status
            $this->update([
                'status'         => TransferStatus::FAILED,
                'failed_at'      => now(),
                'failure_reason' => $reason,
                'metadata'       => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status'         => TransactionStatus::FAILED,
                'failed_at'      => now(),
                'failure_reason' => $reason,
            ]);

            \DB::commit();

            return true;

        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel the transfer.
     */
    public function cancel(?string $reason = null): bool
    {
        \DB::beginTransaction();

        try {
            // Update transfer status
            $this->update([
                'status'         => TransferStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status'         => TransactionStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            \DB::commit();

            return true;

        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if transfer is pending.
     */
    public function isPending(): bool
    {
        return $this->status === TransferStatus::PENDING;
    }

    /**
     * Check if transfer is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === TransferStatus::COMPLETED;
    }

    /**
     * Check if transfer is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === TransferStatus::FAILED;
    }

    /**
     * Check if transfer is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === TransferStatus::CANCELLED;
    }

    /**
     * Get formatted amount for display.
     */
    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    /**
     * Get formatted net amount for display.
     */
    public function getFormattedTotalAmount(): string
    {
        return wallet_format_amount($this->total_amount, $this->currency);
    }

    /**
     * Get formatted fee for display.
     */
    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    /**
     * Get transfer age in human readable format.
     */
    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope to filter by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, TransferStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter pending transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', TransferStatus::PENDING);
    }

    /**
     * Scope to filter completed transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', TransferStatus::COMPLETED);
    }

    /**
     * Scope to filter failed transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', TransferStatus::FAILED);
    }

    /**
     * Scope to filter transfers involving a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, int $customerId)
    {
        return $query->where(function ($q) use ($customerId) {
            $q->where('from_customer_id', $customerId)
                ->orWhere('to_customer_id', $customerId);
        });
    }

    /**
     * Scope to filter outgoing transfers for a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOutgoingForCustomer($query, int $customerId)
    {
        return $query->where('from_customer_id', $customerId);
    }

    /**
     * Scope to filter incoming transfers for a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIncomingForCustomer($query, int $customerId)
    {
        return $query->where('to_customer_id', $customerId);
    }
}
