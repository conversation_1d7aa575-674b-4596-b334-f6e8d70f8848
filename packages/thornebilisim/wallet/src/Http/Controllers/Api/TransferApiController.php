<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Exceptions\WalletPermissionException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CreateTransferRequest;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Services\TransferService;
use Webkul\Customer\Models\Customer;

class TransferApiController extends Controller
{
    public function __construct(
        protected TransferService $transferService,
        protected BalanceService $balanceService,
        protected LuhnService $luhnService,
        protected PermissionService $permissionService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check() && ! request()->secureContext('customer_id')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }

            return $next($request);
        });
    }

    /**
     * Get customer transfers.
     */
    public function index(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $filters = [
            'currency'  => request()->get('currency'),
            'status'    => request()->get('status'),
            'direction' => request()->get('direction'), // incoming, outgoing
            'from_date' => request()->get('from_date'),
            'to_date'   => request()->get('to_date'),
            'per_page'  => request()->get('per_page', 15),
        ];

        $transfers = $this->transferService->getTransferHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $transfers->items(),
            'pagination' => [
                'current_page' => $transfers->currentPage(),
                'last_page'    => $transfers->lastPage(),
                'per_page'     => $transfers->perPage(),
                'total'        => $transfers->total(),
                'from'         => $transfers->firstItem(),
                'to'           => $transfers->lastItem(),
            ],
        ]);
    }

    /**
     * Create a new transfer.
     */
    public function store(CreateTransferRequest $request): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        try {
            // Check if from_account belongs to customer
            $fromAccount = WalletAccount::where('account_number', $request->from_account_number)
                ->where('customer_id', $customer->id)
                ->active()
                ->first();

            if (! $fromAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'From account not found or not owned by customer',
                ], 400);
            }

            // Check permissions
            $this->permissionService->validateOperationOrFail(
                'transfer',
                $customer->id,
                $request->amount,
                $fromAccount->currency
            );

            // Check if to_account exists
            $toAccount = WalletAccount::findByAccountNumber($request->to_account_number);
            if (! $toAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'To account not found',
                ], 400);
            }

            // Check if trying to transfer to same account
            if ($request->from_account_number === $request->to_account_number) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot transfer to the same account',
                ], 400);
            }

            // Check balance
            if (! $this->balanceService->hasSufficientBalance($customer->id, $fromAccount->currency, $request->amount)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ], 400);
            }

            $transfer = $this->transferService->createTransfer([
                'from_account_number' => $request->from_account_number,
                'to_account_number'   => $request->to_account_number,
                'amount'              => $request->amount,
                'description'         => $request->description,
                'metadata'            => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Transfer created successfully',
                'data'    => [
                    'id'                     => $transfer->id,
                    'reference'              => $transfer->reference,
                    'from_customer_id'       => $transfer->from_customer_id,
                    'to_customer_id'         => $transfer->to_customer_id,
                    'from_account_number'    => $transfer->from_account_number,
                    'to_account_number'      => $transfer->to_account_number,
                    'currency'               => $transfer->currency,
                    'amount'                 => $transfer->amount,
                    'fee'                    => $transfer->fee,
                    'total_amount'           => $transfer->total_amount,
                    'status'                 => $transfer->status->value,
                    'formatted_amount'       => $transfer->getFormattedAmount(),
                    'formatted_fee'          => $transfer->getFormattedFee(),
                    'formatted_total_amount' => $transfer->getFormattedTotalAmount(),
                    'description'            => $transfer->description,
                    'created_at'             => $transfer->created_at->toISOString(),
                ],
            ]);

            // Log permission check
            $this->permissionService->logPermissionCheck(
                'transfer',
                $customer->id,
                $request->amount,
                $fromAccount->currency,
                true
            );

        } catch (WalletPermissionException $e) {
            // Log permission denial
            $this->permissionService->logPermissionCheck(
                'transfer',
                $customer->id,
                $request->amount,
                $fromAccount->currency ?? 'EUR',
                false,
                $e->getErrors()
            );

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors'  => $e->getErrors(),
                'context' => $e->getContext(),
            ], 403);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Validate account number.
     */
    public function validateAccount(): JsonResponse
    {
        $accountNumber = request()->get('account_number');
        $customer      = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        if (! $accountNumber || strlen($accountNumber) !== 11) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number format',
            ], 400);
        }

        // Validate Luhn algorithm
        if (! $this->luhnService->validateAccountNumber($accountNumber)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number (Luhn validation failed)',
            ], 400);
        }

        // Check if account exists
        $account = WalletAccount::findByAccountNumber($accountNumber);
        if (! $account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found',
            ], 404);
        }

        // Check if it's customer's own account
        if ($account->customer_id === $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot transfer to your own account',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Account is valid',
            'data'    => [
                'account_number'           => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'currency'                 => $account->currency,
                'customer_name'            => $account->customer->first_name.' '.$account->customer->last_name,
                'is_active'                => $account->is_active,
            ],
        ]);
    }

    /**
     * Show transfer details.
     */
    public function show(WalletTransfer $transfer): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        // Check if customer is involved in this transfer
        if ($transfer->from_customer_id !== $customer->id && $transfer->to_customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Transfer not found',
            ], 404);
        }

        $transfer->load(['fromCustomer', 'toCustomer', 'fromAccount', 'toAccount']);

        return response()->json([
            'success' => true,
            'data'    => [
                'id'                     => $transfer->id,
                'reference'              => $transfer->reference,
                'from_customer_id'       => $transfer->from_customer_id,
                'to_customer_id'         => $transfer->to_customer_id,
                'from_account_number'    => $transfer->from_account_number,
                'to_account_number'      => $transfer->to_account_number,
                'currency'               => $transfer->currency,
                'amount'                 => $transfer->amount,
                'fee'                    => $transfer->fee,
                'total_amount'           => $transfer->total_amount,
                'status'                 => $transfer->status->value,
                'formatted_amount'       => $transfer->getFormattedAmount(),
                'formatted_fee'          => $transfer->getFormattedFee(),
                'formatted_total_amount' => $transfer->getFormattedTotalAmount(),
                'description'            => $transfer->description,
                'metadata'               => $transfer->metadata,
                'processed_at'           => $transfer->processed_at?->toISOString(),
                'failed_at'              => $transfer->failed_at?->toISOString(),
                'failure_reason'         => $transfer->failure_reason,
                'created_at'             => $transfer->created_at->toISOString(),
                'updated_at'             => $transfer->updated_at->toISOString(),
                'age'                    => $transfer->getAge(),
                'from_customer'          => [
                    'id'    => $transfer->fromCustomer->id,
                    'name'  => $transfer->fromCustomer->first_name.' '.$transfer->fromCustomer->last_name,
                    'email' => $transfer->fromCustomer->email,
                ],
                'to_customer' => [
                    'id'    => $transfer->toCustomer->id,
                    'name'  => $transfer->toCustomer->first_name.' '.$transfer->toCustomer->last_name,
                    'email' => $transfer->toCustomer->email,
                ],
            ],
        ]);
    }

    /**
     * Cancel a transfer.
     */
    public function cancel(WalletTransfer $transfer, Request $request): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        // Only the sender can cancel a transfer
        if ($transfer->from_customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'You can only cancel your own outgoing transfers',
            ], 403);
        }

        if (! $transfer->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Only pending transfers can be cancelled',
            ], 400);
        }

        try {
            $reason = $request->get('reason', 'Cancelled by customer');
            $this->transferService->cancelTransfer($transfer, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Transfer cancelled successfully',
                'data'    => [
                    'id'             => $transfer->id,
                    'reference'      => $transfer->reference,
                    'status'         => $transfer->fresh()->status->value,
                    'failure_reason' => $reason,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
