<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Exceptions\WalletPermissionException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Services\PermissionService;
use Webkul\Customer\Models\Customer;

class PermissionApiController extends Controller
{
    public function __construct(
        protected PermissionService $permissionService
    ) {}

    /**
     * Get operation limits and permissions.
     */
    public function showLimit(string $operation): JsonResponse
    {
        try {
            if (! in_array($operation, ['deposit', 'withdraw', 'transfer'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid operation. Must be one of: deposit, withdraw, transfer',
                ], 400);
            }

            $limits = $this->permissionService->getOperationLimits($operation);

            return response()->json([
                'success'   => true,
                'data'      => $limits,
                'operation' => $operation,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all operation limits.
     */
    public function limits(): JsonResponse
    {
        try {
            $operations = ['deposit', 'withdraw', 'transfer'];
            $allLimits  = [];

            foreach ($operations as $operation) {
                $allLimits[$operation] = $this->permissionService->getOperationLimits($operation);
            }

            return response()->json([
                'success'    => true,
                'data'       => $allLimits,
                'operations' => $operations,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get usage statistics for customer.
     */
    public function usageStatistics(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = request()->get('currency');

        try {
            if ($currency) {
                $stats = $this->permissionService->getUsageStatistics($customer->id, $currency);
            } else {
                $stats = $this->permissionService->getUsageStatisticsForAllCurrencies($customer->id);
            }

            return response()->json([
                'success'     => true,
                'data'        => $stats,
                'customer_id' => $customer->id,
                'currency'    => $currency ?? 'all',
                'meta'        => [
                    'timestamp' => now()->toISOString(),
                    'timezone'  => config('app.timezone'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve usage statistics: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if operation is allowed.
     */
    public function checkOperationPermission(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $request = request();
        $request->validate([
            'operation' => 'required|string|in:deposit,withdraw,transfer',
            'amount'    => 'required|numeric|min:0.01',
            'currency'  => 'required|string|in:EUR,USD,MLGR',
        ]);

        try {
            $errors = $this->permissionService->validateOperation(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency
            );

            $allowed         = empty($errors);
            $remainingLimits = $this->permissionService->getRemainingLimits(
                $customer->id,
                $request->operation,
                $request->currency
            );

            // Log permission check
            $this->permissionService->logPermissionCheck(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency,
                $allowed,
                $errors
            );

            return response()->json([
                'success' => true,
                'data'    => [
                    'allowed'          => $allowed,
                    'errors'           => $errors,
                    'remaining_limits' => $remainingLimits,
                    'operation_limits' => $this->permissionService->getOperationLimits($request->operation),
                    'usage_statistics' => $this->permissionService->getUsageStatistics($customer->id, $request->currency)[$request->operation] ?? null,
                ],
                'operation'   => $request->operation,
                'amount'      => $request->amount,
                'currency'    => $request->currency,
                'customer_id' => $customer->id,
                'meta'        => [
                    'timestamp'  => now()->toISOString(),
                    'check_type' => 'comprehensive',
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check operation permission: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate operation and return detailed response.
     */
    public function validateOperation(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $request = request();
        $request->validate([
            'operation' => 'required|string|in:deposit,withdraw,transfer',
            'amount'    => 'required|numeric|min:0.01',
            'currency'  => 'required|string|in:EUR,USD,MLGR',
        ]);

        try {
            $errors = $this->permissionService->validateOperation(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency
            );

            $allowed = empty($errors);

            if ($allowed) {
                return response()->json([
                    'success' => true,
                    'message' => 'Operation is allowed',
                    'data'    => [
                        'allowed'   => true,
                        'operation' => $request->operation,
                        'amount'    => $request->amount,
                        'currency'  => $request->currency,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Operation not allowed',
                    'data'    => [
                        'allowed'   => false,
                        'errors'    => $errors,
                        'operation' => $request->operation,
                        'amount'    => $request->amount,
                        'currency'  => $request->currency,
                    ],
                ], 403);
            }

        } catch (WalletPermissionException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'data'    => [
                    'allowed' => false,
                    'errors'  => $e->getErrors(),
                    'context' => $e->getContext(),
                ],
            ], 403);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate operation: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get permission configuration summary.
     */
    public function summary(): JsonResponse
    {
        try {
            $operations = ['deposit', 'withdraw', 'transfer'];
            $summary    = [];

            foreach ($operations as $operation) {
                $summary[$operation] = [
                    'enabled'          => $this->permissionService->isOperationEnabled($operation),
                    'require_kyc'      => $this->permissionService->isKycRequired($operation),
                    'require_approval' => $this->permissionService->isApprovalRequired($operation),
                    'limits_enabled'   => [
                        'daily'   => $this->permissionService->isDailyLimitEnabled($operation),
                        'monthly' => $this->permissionService->isMonthlyLimitEnabled($operation),
                        'amount'  => $this->permissionService->areAmountLimitsEnabled($operation),
                    ],
                    'limits' => [
                        'daily'      => $this->permissionService->getDailyLimit($operation),
                        'monthly'    => $this->permissionService->getMonthlyLimit($operation),
                        'min_amount' => $this->permissionService->getMinAmount($operation),
                        'max_amount' => $this->permissionService->getMaxAmount($operation),
                    ],
                ];
            }

            return response()->json([
                'success'    => true,
                'data'       => $summary,
                'operations' => $operations,
                'meta'       => [
                    'timestamp'     => now()->toISOString(),
                    'config_source' => 'wallet.permissions',
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve permission summary: '.$e->getMessage(),
            ], 500);
        }
    }
}
