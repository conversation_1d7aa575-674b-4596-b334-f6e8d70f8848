<?php

namespace Thorne\Wallet\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\WalletService;
use Webkul\Customer\Models\Customer;

class WalletController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return redirect()->route('customer.session.index');
            }

            return $next($request);
        });
    }

    /**
     * Display wallet overview.
     */
    public function index(): View
    {
        $customer = auth()->guard('customer')->user();
        $overview = $this->walletService->getWalletOverview($customer);
        $balances = $this->walletService->getCustomerBalances($customer->id);

        return view('wallet::wallet.index', compact('overview', 'balances'));
    }

    /**
     * Get wallet overview data.
     */
    public function overview(): JsonResponse
    {
        $customer     = auth()->guard('customer')->user();
        $syncWithWeb3 = request()->boolean('sync', false);

        $overview = $this->walletService->getWalletOverview($customer, $syncWithWeb3);
        $balances = $this->walletService->getCustomerBalances($customer->id, $syncWithWeb3);

        return response()->json([
            'success' => true,
            'data'    => [
                'overview' => $overview,
                'balances' => $balances,
            ],
        ]);
    }

    /**
     * Get customer balance.
     */
    public function balance(): JsonResponse
    {
        $customer  = auth()->guard('customer')->user();
        $currency  = request()->get('currency');
        $forceSync = request()->boolean('sync', false);

        if ($currency) {
            $balance = $this->balanceService->getBalance($customer->id, $currency, $forceSync);

            return response()->json([
                'success' => true,
                'data'    => [
                    'currency'                    => $balance->currency,
                    'balance'                     => $balance->balance,
                    'locked_balance'              => $balance->locked_balance,
                    'available_balance'           => $balance->getAvailableBalance(),
                    'formatted_balance'           => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync'                   => $balance->last_web3_sync?->toISOString(),
                ],
            ]);
        }

        $balances = $this->balanceService->getAllBalances($customer->id, $forceSync);

        return response()->json([
            'success' => true,
            'data'    => $balances->map(function ($balance) {
                return [
                    'currency'                    => $balance->currency,
                    'balance'                     => $balance->balance,
                    'locked_balance'              => $balance->locked_balance,
                    'available_balance'           => $balance->getAvailableBalance(),
                    'formatted_balance'           => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync'                   => $balance->last_web3_sync?->toISOString(),
                ];
            }),
        ]);
    }

    /**
     * Sync balance with WEB3 service.
     */
    public function syncBalance(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();
        $currency = request()->get('currency');

        if (! $currency) {
            return response()->json([
                'success' => false,
                'message' => 'Currency is required',
            ], 400);
        }

        if (! wallet_is_supported_currency($currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported currency',
            ], 400);
        }

        try {
            $balance = $this->balanceService->syncWithWeb3($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data'    => [
                    'currency'                    => $balance->currency,
                    'balance'                     => $balance->balance,
                    'locked_balance'              => $balance->locked_balance,
                    'available_balance'           => $balance->getAvailableBalance(),
                    'formatted_balance'           => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync'                   => $balance->last_web3_sync?->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync balance: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer accounts.
     */
    public function accounts(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();
        $accounts = $customer->walletAccounts()->active()->get();

        return response()->json([
            'success' => true,
            'data'    => $accounts->map(function ($account) {
                return [
                    'id'                       => $account->id,
                    'currency'                 => $account->currency,
                    'account_number'           => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active'                => $account->is_active,
                    'created_at'               => $account->created_at->toISOString(),
                ];
            }),
        ]);
    }

    /**
     * Create a new account for a currency.
     */
    public function createAccount(Request $request): JsonResponse
    {
        $request->validate([
            'currency' => 'required|string|size:3',
        ]);

        $customer = auth()->guard('customer')->user();
        $currency = strtoupper($request->currency);

        if (! wallet_is_supported_currency($currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported currency',
            ], 400);
        }

        // Check if account already exists
        $existingAccount = WalletAccount::where('customer_id', $customer->id)
            ->where('currency', $currency)
            ->first();

        if ($existingAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Account already exists for this currency',
            ], 400);
        }

        try {
            $account = WalletAccount::getOrCreateForCustomer($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data'    => [
                    'id'                       => $account->id,
                    'currency'                 => $account->currency,
                    'account_number'           => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active'                => $account->is_active,
                    'created_at'               => $account->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create account: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get transaction history.
     */
    public function transactions(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();

        $filters = [
            'currency'  => request()->get('currency'),
            'type'      => request()->get('type'),
            'status'    => request()->get('status'),
            'method'    => request()->get('method'),
            'from_date' => request()->get('from_date'),
            'to_date'   => request()->get('to_date'),
            'per_page'  => request()->get('per_page', 15),
        ];

        $transactions = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page'    => $transactions->lastPage(),
                'per_page'     => $transactions->perPage(),
                'total'        => $transactions->total(),
                'from'         => $transactions->firstItem(),
                'to'           => $transactions->lastItem(),
            ],
        ]);
    }

    /**
     * Show a specific transaction.
     */
    public function showTransaction(WalletTransaction $transaction): JsonResponse
    {
        $customer = auth()->guard('customer')->user();

        if ($transaction->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data'    => [
                'id'                      => $transaction->id,
                'reference'               => $transaction->reference,
                'type'                    => $transaction->type->value,
                'direction'               => $transaction->direction->value,
                'status'                  => $transaction->status->value,
                'method'                  => $transaction->method,
                'currency'                => $transaction->currency,
                'amount'                  => $transaction->amount,
                'fee'                     => $transaction->fee,
                'total_amount'            => $transaction->total_amount,
                'formatted_amount'        => $transaction->getFormattedAmount(),
                'formatted_fee'           => $transaction->getFormattedFee(),
                'formatted_total_amount'  => $transaction->getFormattedTotalAmount(),
                'description'             => $transaction->description,
                'metadata'                => $transaction->metadata,
                'external_reference'      => $transaction->external_reference,
                'external_transaction_id' => $transaction->external_transaction_id,
                'processed_at'            => $transaction->processed_at?->toISOString(),
                'failed_at'               => $transaction->failed_at?->toISOString(),
                'failure_reason'          => $transaction->failure_reason,
                'created_at'              => $transaction->created_at->toISOString(),
                'updated_at'              => $transaction->updated_at->toISOString(),
                'age'                     => $transaction->getAge(),
            ],
        ]);
    }
}
