<?php

namespace Thorne\Wallet\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\TransferService;

class TransferController extends Controller
{
    public function __construct(
        protected TransferService $transferService,
        protected BalanceService $balanceService,
        protected LuhnService $luhnService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return redirect()->route('customer.session.index');
            }

            return $next($request);
        });
    }

    /**
     * Display transfer page.
     */
    public function index(): View
    {
        $customer   = auth()->guard('customer')->user();
        $transfers  = $this->transferService->getTransferHistory($customer->id, ['per_page' => 10]);
        $currencies = wallet_get_enabled_currencies();
        $balances   = $this->balanceService->getAllBalances($customer->id);
        $accounts   = $customer->walletAccounts()->active()->get();

        return view('wallet::transfer.index', compact('transfers', 'currencies', 'balances', 'accounts'));
    }

    /**
     * Show create transfer form.
     */
    public function create(): View
    {
        $customer   = auth()->guard('customer')->user();
        $currencies = wallet_get_enabled_currencies();
        $balances   = $this->balanceService->getAllBalances($customer->id);
        $accounts   = $customer->walletAccounts()->active()->get();

        return view('wallet::transfer.create', compact('currencies', 'balances', 'accounts'));
    }

    /**
     * Store a new transfer.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'from_account_number' => 'required|string|size:11',
            'to_account_number'   => 'required|string|size:11',
            'amount'              => 'required|numeric|min:0.01',
            'description'         => 'nullable|string|max:255',
        ]);

        $customer = auth()->guard('customer')->user();

        // Validate account numbers
        if (! $this->luhnService->validateAccountNumber($request->from_account_number)) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.transfers.account_invalid'));
        }

        if (! $this->luhnService->validateAccountNumber($request->to_account_number)) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.transfers.account_invalid'));
        }

        // Check if from_account belongs to customer
        $fromAccount = WalletAccount::where('account_number', $request->from_account_number)
            ->where('customer_id', $customer->id)
            ->active()
            ->first();

        if (! $fromAccount) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.transfers.account_not_found'));
        }

        // Check if to_account exists
        $toAccount = WalletAccount::findByAccountNumber($request->to_account_number);
        if (! $toAccount) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.transfers.account_not_found'));
        }

        // Check if trying to transfer to same account
        if ($request->from_account_number === $request->to_account_number) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.transfers.same_account_error'));
        }

        // Check balance
        if (! $this->balanceService->hasSufficientBalance($customer->id, $fromAccount->currency, $request->amount)) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.withdrawals.insufficient_balance'));
        }

        try {
            $transfer = $this->transferService->createTransfer([
                'from_account_number' => $request->from_account_number,
                'to_account_number'   => $request->to_account_number,
                'amount'              => $request->amount,
                'description'         => $request->description,
                'metadata'            => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return redirect()
                ->route('customer.account.wallet.transfer.show', $transfer->id)
                ->with('success', __('wallet::app.transfers.success'));

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Show transfer details.
     */
    public function show(WalletTransfer $transfer): View
    {
        $customer = auth()->guard('customer')->user();

        // Check if customer is involved in this transfer
        if ($transfer->from_customer_id !== $customer->id && $transfer->to_customer_id !== $customer->id) {
            abort(404);
        }

        $transfer->load(['fromCustomer', 'toCustomer', 'fromAccount', 'toAccount']);

        return view('wallet::transfer.show', compact('transfer'));
    }

    /**
     * Validate account number via AJAX.
     */
    public function validateAccount(): JsonResponse
    {
        $accountNumber = request()->get('account_number');
        $customer      = auth()->guard('customer')->user();

        if (! $accountNumber || strlen($accountNumber) !== 11) {
            return response()->json([
                'success' => false,
                'message' => __('wallet::app.transfers.account_invalid'),
            ]);
        }

        // Validate Luhn algorithm
        if (! $this->luhnService->validateAccountNumber($accountNumber)) {
            return response()->json([
                'success' => false,
                'message' => __('wallet::app.transfers.account_invalid'),
            ]);
        }

        // Check if account exists
        $account = WalletAccount::findByAccountNumber($accountNumber);
        if (! $account) {
            return response()->json([
                'success' => false,
                'message' => __('wallet::app.transfers.account_not_found'),
            ]);
        }

        // Check if it's customer's own account
        if ($account->customer_id === $customer->id) {
            return response()->json([
                'success' => false,
                'message' => __('wallet::app.transfers.same_account_error'),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => __('wallet::app.transfers.account_valid'),
            'data'    => [
                'account_number'           => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'currency'                 => $account->currency,
                'customer_name'            => $account->customer->first_name.' '.$account->customer->last_name,
            ],
        ]);
    }
}
