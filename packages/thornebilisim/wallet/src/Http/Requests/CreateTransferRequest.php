<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LuhnService;

class CreateTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->guard('customer')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'from_account_number' => [
                'required',
                'string',
                'size:11',
                function ($attribute, $value, $fail) {
                    $customer = auth()->guard('customer')->user();

                    // Validate Luhn algorithm
                    $luhnService = app(LuhnService::class);
                    if (! $luhnService->validateAccountNumber($value)) {
                        $fail(__('wallet::app.errors.invalid_account_number'));

                        return;
                    }

                    // Check if account belongs to customer
                    $account = WalletAccount::where('account_number', $value)
                        ->where('customer_id', $customer->id)
                        ->active()
                        ->first();

                    if (! $account) {
                        $fail(__('wallet::app.errors.account_not_found'));
                    }
                },
            ],
            'to_account_number' => [
                'required',
                'string',
                'size:11',
                'different:from_account_number',
                function ($attribute, $value, $fail) {
                    // Validate Luhn algorithm
                    $luhnService = app(LuhnService::class);
                    if (! $luhnService->validateAccountNumber($value)) {
                        $fail(__('wallet::app.errors.invalid_account_number'));

                        return;
                    }

                    // Check if account exists
                    $account = WalletAccount::findByAccountNumber($value);
                    if (! $account) {
                        $fail(__('wallet::app.errors.account_not_found'));

                        return;
                    }

                    // Check currency compatibility
                    $fromAccountNumber = $this->input('from_account_number');
                    if ($fromAccountNumber) {
                        $fromAccount = WalletAccount::where('account_number', $fromAccountNumber)->first();
                        if ($fromAccount && $fromAccount->currency !== $account->currency) {
                            $fail(__('wallet::app.errors.currency_mismatch'));
                        }
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) {
                    $customer          = auth()->guard('customer')->user();
                    $fromAccountNumber = $this->input('from_account_number');

                    if ($fromAccountNumber) {
                        $fromAccount = WalletAccount::where('account_number', $fromAccountNumber)
                            ->where('customer_id', $customer->id)
                            ->first();

                        if ($fromAccount) {
                            // Check balance
                            $balanceService = app(BalanceService::class);
                            if (! $balanceService->hasSufficientBalance($customer->id, $fromAccount->currency, $value)) {
                                $fail(__('wallet::app.errors.insufficient_balance'));
                            }

                            // Check transfer limits
                            $permissions = wallet_config('permissions.transfer', []);

                            if (isset($permissions['min_amount']) && $value < $permissions['min_amount']) {
                                $fail(__('wallet::app.errors.min_amount_not_met'));
                            }

                            if (isset($permissions['max_amount']) && $value > $permissions['max_amount']) {
                                $fail(__('wallet::app.errors.max_amount_exceeded'));
                            }
                        }
                    }
                },
            ],
            'description' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'from_account_number.required' => __('wallet::app.transfers.from_account_required'),
            'from_account_number.size'     => __('wallet::app.errors.invalid_account_number'),
            'to_account_number.required'   => __('wallet::app.transfers.to_account_number'),
            'to_account_number.size'       => __('wallet::app.errors.invalid_account_number'),
            'to_account_number.different'  => __('wallet::app.errors.transfer_to_same_account'),
            'amount.required'              => __('wallet::app.transfers.enter_amount'),
            'amount.numeric'               => __('wallet::app.errors.invalid_amount'),
            'amount.min'                   => __('wallet::app.errors.invalid_amount'),
        ];
    }
}
