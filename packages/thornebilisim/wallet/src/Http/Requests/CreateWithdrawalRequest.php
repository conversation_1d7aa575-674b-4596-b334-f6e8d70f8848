<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Services\LimitValidationService;
use Thorne\Wallet\Services\BalanceService;

class CreateWithdrawalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->guard('customer')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'method' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $methodConfig = wallet_payment_method_config($value);
                    if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_withdraw']) {
                        $fail(__('wallet::app.errors.method_not_supported'));
                    }
                },
            ],
            'currency' => [
                'required',
                'string',
                'size:3',
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) {
                    $customer = auth()->guard('customer')->user();
                    $currency = strtoupper($this->input('currency'));

                    // Check balance first
                    $balanceService = app(BalanceService::class);
                    if (! $balanceService->hasSufficientBalance($customer->id, $currency, $value)) {
                        $fail(__('wallet::app.errors.insufficient_balance'));
                        return;
                    }

                    // Validate two-tier limits: Global + Payment Method
                    try {
                        $limitService = app(LimitValidationService::class);
                        $limitService->validateAmountLimits(
                            'withdraw',
                            $this->input('method'),
                            (float) $value,
                            $currency
                        );
                    } catch (\Exception $e) {
                        $fail($e->getMessage());
                    }
                },
            ],
            'account_details'                => 'required|array',
            'account_details.account_number' => 'required_if:method,bank_transfer|string',
            'account_details.bank_name'      => 'required_if:method,bank_transfer|string',
            'account_details.account_holder' => 'required_if:method,bank_transfer|string',
            'description'                    => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'method.required'          => __('wallet::app.withdrawals.select_method'),
            'currency.required'        => __('wallet::app.withdrawals.select_currency'),
            'currency.size'            => __('wallet::app.errors.unsupported_currency'),
            'amount.required'          => __('wallet::app.withdrawals.enter_amount'),
            'amount.numeric'           => __('wallet::app.errors.invalid_amount'),
            'amount.min'               => __('wallet::app.errors.invalid_amount'),
            'account_details.required' => __('wallet::app.withdrawals.enter_account_details'),
        ];
    }
}
