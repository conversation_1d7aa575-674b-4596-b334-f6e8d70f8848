<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SyncBalanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->guard('customer')->check() || request()->secureContext('customer_id');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'currency' => [
                'required',
                'string',
                'size:3',
                function ($attribute, $value, $fail) {
                    if (!wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'currency.required' => __('wallet::app.errors.currency_required'),
            'currency.string' => __('wallet::app.errors.currency_string'),
            'currency.size' => __('wallet::app.errors.currency_size'),
        ];
    }
}
