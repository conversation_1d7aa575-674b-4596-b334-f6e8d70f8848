<?php

namespace Thorne\Wallet\Enums;

enum TransactionStatus: string
{
    case PENDING    = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED  = 'completed';
    case FAILED     = 'failed';
    case CANCELLED  = 'cancelled';
    case EXPIRED    = 'expired';

    /**
     * Get all transaction statuses.
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get transaction status label.
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING    => 'Pending',
            self::PROCESSING => 'Processing',
            self::COMPLETED  => 'Completed',
            self::FAILED     => 'Failed',
            self::CANCELLED  => 'Cancelled',
            self::EXPIRED    => 'Expired',
        };
    }

    /**
     * Get transaction status description.
     */
    public function description(): string
    {
        return match ($this) {
            self::PENDING    => 'Transaction is waiting to be processed',
            self::PROCESSING => 'Transaction is being processed',
            self::COMPLETED  => 'Transaction has been completed successfully',
            self::FAILED     => 'Transaction has failed',
            self::CANCELLED  => 'Transaction has been cancelled',
            self::EXPIRED    => 'Transaction has expired',
        };
    }

    /**
     * Check if status is final (cannot be changed).
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED, self::EXPIRED]);
    }

    /**
     * Check if status is active (can be processed).
     */
    public function isActive(): bool
    {
        return in_array($this, [self::PENDING, self::PROCESSING]);
    }

    /**
     * Check if status is successful.
     */
    public function isSuccessful(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * Check if status indicates failure.
     */
    public function isFailure(): bool
    {
        return in_array($this, [self::FAILED, self::CANCELLED, self::EXPIRED]);
    }
}
