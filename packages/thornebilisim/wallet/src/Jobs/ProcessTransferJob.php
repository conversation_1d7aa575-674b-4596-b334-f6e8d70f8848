<?php

namespace Thorne\Wallet\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\TransferService;

class ProcessTransferJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $backoff = 60; // seconds

    public function __construct(
        public int $transferId
    ) {}

    public function handle(TransferService $transferService): void
    {
        $transfer = WalletTransfer::find($this->transferId);

        if (! $transfer) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Transfer not found for processing', [
                    'transfer_id' => $this->transferId,
                ]);

            return;
        }

        if (! $transfer->isPending()) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->info('Transfer is not pending, skipping processing', [
                    'transfer_id' => $this->transferId,
                    'status'      => $transfer->status->value,
                ]);

            return;
        }

        try {
            $transferService->processTransfer($transfer);

            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->info('Transfer processing job completed', [
                    'transfer_id' => $this->transferId,
                    'reference'   => $transfer->reference,
                ]);

        } catch (\Exception $e) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Transfer processing job failed', [
                    'transfer_id' => $this->transferId,
                    'reference'   => $transfer->reference,
                    'error'       => $e->getMessage(),
                ]);

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        $transfer = WalletTransfer::find($this->transferId);

        if ($transfer && $transfer->isPending()) {
            try {
                $transfer->fail("Job processing failed: {$exception->getMessage()}");
            } catch (\Exception $e) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->error('Failed to mark transfer as failed', [
                        'transfer_id' => $this->transferId,
                        'error'       => $e->getMessage(),
                    ]);
            }
        }

        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->error('Transfer processing job failed permanently', [
                'transfer_id' => $this->transferId,
                'error'       => $exception->getMessage(),
            ]);
    }
}
