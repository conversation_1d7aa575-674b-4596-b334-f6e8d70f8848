<?php

namespace Thorne\Wallet\Auditing\Resolvers;

use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use OwenIt\Auditing\Contracts\UserResolver;
use Webkul\Customer\Models\Customer;

class CustomerIdResolver implements UserResolver
{
    public static function resolve()
    {
        $guards = Config::get('audit.user.guards', [
            Config::get('auth.defaults.guard'),
        ]);

        if (request()->secureContext('customer_id')) {
            return Customer::find(request()->secureContext('customer_id')) ?? null;
        }

        foreach ($guards as $guard) {
            try {
                $authenticated = Auth::guard($guard)->check();
            } catch (Exception $exception) {
                continue;
            }

            if ($authenticated === true) {
                return Auth::guard($guard)->user();
            }
        }

        return null;
    }
}