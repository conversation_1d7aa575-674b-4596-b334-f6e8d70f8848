<?php

use Illuminate\Support\Facades\Route;
use Thorne\Wallet\Http\Controllers\Api\DepositApiController;
use <PERSON>\Wallet\Http\Controllers\Api\PermissionApiController;
use <PERSON>\Wallet\Http\Controllers\Api\TransferApiController;
use <PERSON>\Wallet\Http\Controllers\Api\WalletApiController;
use Thorne\Wallet\Http\Controllers\Api\WithdrawApiController;

/*
|--------------------------------------------------------------------------
| Wallet API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the wallet package. These routes are loaded
| by the WalletServiceProvider and will be assigned to the "api" middleware group.
|
*/

Route::middleware(['api', 'wallet.exceptions'])
    ->prefix('api/wallet')
    ->name('api.wallet.')
    ->group(function () {
        Route::get('/currencies', [WalletApiController::class, 'currencies'])->name('currencies');
        Route::get('/methods', [WalletApiController::class, 'methods'])->name('methods');
        Route::post('/validate-account', [WalletApiController::class, 'validateAccount'])->name('validate-account');

        Route::middleware(['security-layer.session'])->group(function () {
            Route::get('/summary', [WalletApiController::class, 'summary'])->name('summary.show');

            Route::get('/balances', [WalletApiController::class, 'showBalance'])->name('balances.show');
            Route::post('/balances/sync', [WalletApiController::class, 'syncBalance'])->name('balances.sync');
            Route::post('/balances/sync-all', [WalletApiController::class, 'syncAllBalances'])->name('balances.sync-all');
            Route::get('/balances/compare', [WalletApiController::class, 'compareBalance'])->name('balances.compare');

            Route::get('/accounts', [WalletApiController::class, 'indexAccounts'])->name('accounts.index');
            Route::post('/accounts', [WalletApiController::class, 'storeAccount'])->name('accounts.store');

            Route::get('/transactions', [WalletApiController::class, 'indexTransactions'])->name('transactions.index');
            Route::get('/transactions/{transaction}', [WalletApiController::class, 'showTransaction'])->name('transactions.show');

            Route::prefix('deposits')->name('deposits.')->group(function () {
                Route::get('/', [DepositApiController::class, 'index'])->name('index');
                Route::post('/', [DepositApiController::class, 'store'])->name('store');
                Route::get('/methods', [DepositApiController::class, 'methods'])->name('methods.index');
                Route::prefix('company-accounts')->name('company-accounts.')->group(function () {
                    Route::get('/', [DepositApiController::class, 'companyAccounts'])->name('index');
                    Route::get('/{account}', [DepositApiController::class, 'showAccount'])->name('show');
                    Route::get('/method/{method}', [DepositApiController::class, 'accountsByMethod'])->name('by-method');
                    Route::get('/method/{method}/currency/{currency_code}', [DepositApiController::class, 'accountsByMethodCurrency'])->name('by-method-currency');
                });
                Route::get('/{transaction}', [DepositApiController::class, 'show'])->name('show');
            });

            Route::prefix('withdrawals')->name('withdrawals.')->group(function () {
                Route::get('/', [WithdrawApiController::class, 'index'])->name('index');
                Route::post('/', [WithdrawApiController::class, 'store'])->name('store');
                Route::get('/methods', [WithdrawApiController::class, 'methods'])->name('methods.index');
                Route::get('/{transaction}', [WithdrawApiController::class, 'show'])->name('show');
            });

            Route::prefix('transfers')->name('transfers.')->group(function () {
                Route::get('/', [TransferApiController::class, 'index'])->name('index');
                Route::post('/', [TransferApiController::class, 'store'])->name('store');
                Route::get('/validate-account', [TransferApiController::class, 'validateAccount'])->name('validate-account');
                Route::get('/{transfer}', [TransferApiController::class, 'show'])->name('show');
                Route::post('/{transfer}/cancel', [TransferApiController::class, 'cancel'])->name('cancel');
            });

            Route::prefix('permissions')->name('permissions.')->group(function () {
                Route::get('/summary', [PermissionApiController::class, 'summary'])->name('summary');
                Route::get('/limits', [PermissionApiController::class, 'limits'])->name('limits.index');
                Route::get('/limits/{operation}', [PermissionApiController::class, 'showLimit'])->name('limits.show');
                Route::get('/usage-statistics', [PermissionApiController::class, 'usageStatistics'])->name('usage-statistics');
                Route::post('/validate', [PermissionApiController::class, 'validateOperation'])->name('validate');
            });
        });
    });
