<?php

return [
    'wallet' => [
        'title'                    => 'Wallet',
        'balances'                 => 'Balances',
        'balance'                  => 'Balance',
        'available'                => 'Available',
        'locked'                   => 'Locked',
        'account'                  => 'Account',
        'sync'                     => 'Sync',
        'syncing'                  => 'Syncing',
        'last_sync'                => 'Last Sync',
        'sync_balances'            => 'Sync All Balances',
        'balance_synced'           => 'Balance synced successfully',
        'all_balances_synced'      => 'All balances synced successfully',
        'sync_failed'              => 'Failed to sync balance',
        'loading'                  => 'Loading...',
        'quick_actions'            => 'Quick Actions',
        'deposit'                  => 'Deposit',
        'withdraw'                 => 'Withdraw',
        'transfer'                 => 'Transfer',
        'history'                  => 'Transaction History',
        'account_number'           => 'Account Number',
        'formatted_account_number' => 'Account Number',
        'currency'                 => 'Currency',
        'amount'                   => 'Amount',
        'fee'                      => 'Fee',
        'total_amount'             => 'Total Amount',
        'status'                   => 'Status',
        'type'                     => 'Type',
        'method'                   => 'Method',
        'description'              => 'Description',
        'reference'                => 'Reference',
        'created_at'               => 'Created At',
        'processed_at'             => 'Processed At',
    ],

    'transactions' => [
        'title'           => 'Transaction History',
        'no_transactions' => 'No transactions found',
        'filter'          => 'Filter',
        'all_currencies'  => 'All Currencies',
        'all_types'       => 'All Types',
        'all_statuses'    => 'All Statuses',
        'all_methods'     => 'All Methods',
        'from_date'       => 'From Date',
        'to_date'         => 'To Date',
        'apply_filter'    => 'Apply Filter',
        'clear_filter'    => 'Clear Filter',
        'export'          => 'Export',
    ],

    'deposits' => [
        'title'            => 'Deposits',
        'create'           => 'Create Deposit',
        'methods'          => 'Deposit Methods',
        'company_accounts' => 'Company Accounts',
        'select_method'    => 'Select Deposit Method',
        'select_currency'  => 'Select Currency',
        'enter_amount'     => 'Enter Amount',
        'min_amount'       => 'Minimum Amount',
        'max_amount'       => 'Maximum Amount',
        'processing_time'  => 'Processing Time',
        'submit'           => 'Submit Deposit Request',
        'success'          => 'Deposit request created successfully',
        'failed'           => 'Failed to create deposit request',
    ],

    'withdrawals' => [
        'title'                 => 'Withdrawals',
        'create'                => 'Create Withdrawal',
        'methods'               => 'Withdrawal Methods',
        'select_method'         => 'Select Withdrawal Method',
        'select_currency'       => 'Select Currency',
        'enter_amount'          => 'Enter Amount',
        'enter_account_details' => 'Enter Account Details',
        'insufficient_balance'  => 'Insufficient balance',
        'min_amount'            => 'Minimum Amount',
        'max_amount'            => 'Maximum Amount',
        'processing_time'       => 'Processing Time',
        'submit'                => 'Submit Withdrawal Request',
        'success'               => 'Withdrawal request created successfully',
        'failed'                => 'Failed to create withdrawal request',
    ],

    'transfers' => [
        'title'                => 'Internal Transfers',
        'create'               => 'Create Transfer',
        'to_account_number'    => 'To Account Number',
        'enter_account_number' => 'Enter 11-digit account number',
        'validate_account'     => 'Validate Account',
        'account_valid'        => 'Account is valid',
        'account_invalid'      => 'Account is invalid',
        'account_not_found'    => 'Account not found',
        'same_account_error'   => 'Cannot transfer to your own account',
        'currency_mismatch'    => 'Currency mismatch between accounts',
        'enter_amount'         => 'Enter Amount',
        'transfer_fee'         => 'Transfer Fee',
        'total_amount'         => 'Total Amount',
        'recipient_receives'   => 'Recipient Receives',
        'submit'               => 'Submit Transfer',
        'success'              => 'Transfer created successfully',
        'failed'               => 'Failed to create transfer',
        'confirm_transfer'     => 'Confirm Transfer',
        'transfer_details'     => 'Transfer Details',
        'from_account'         => 'From Account',
        'to_account'           => 'To Account',
    ],

    'statuses' => [
        'pending'    => 'Pending',
        'processing' => 'Processing',
        'completed'  => 'Completed',
        'failed'     => 'Failed',
        'cancelled'  => 'Cancelled',
        'expired'    => 'Expired',
    ],

    'types' => [
        'deposit'    => 'Deposit',
        'withdrawal' => 'Withdrawal',
        'transfer'   => 'Transfer',
        'fee'        => 'Fee',
        'refund'     => 'Refund',
        'adjustment' => 'Adjustment',
    ],

    'directions' => [
        'inbound'  => 'Inbound',
        'outbound' => 'Outbound',
    ],

    'currencies' => [
        'EUR'  => 'Euro',
        'USD'  => 'US Dollar',
        'MLGR' => 'Miligram',
    ],

    'payment_methods' => [
        'cash'          => 'Cash',
        'bank_transfer' => 'Bank Transfer',
        'qonto'         => 'Qonto',
        'paypal'        => 'PayPal',
    ],

    'errors' => [
        'wallet_disabled'          => 'Wallet system is currently disabled',
        'unsupported_currency'     => 'Unsupported currency',
        'invalid_amount'           => 'Invalid amount',
        'insufficient_balance'     => 'Insufficient balance',
        'invalid_account_number'   => 'Invalid account number',
        'account_not_found'        => 'Account not found',
        'transfer_to_same_account' => 'Cannot transfer to the same account',
        'currency_mismatch'        => 'Currency mismatch between accounts',
        'method_disabled'          => 'Payment method is disabled',
        'method_not_supported'     => 'Payment method not supported for this operation',
        'daily_limit_exceeded'     => 'Daily limit exceeded',
        'monthly_limit_exceeded'   => 'Monthly limit exceeded',
        'min_amount_not_met'       => 'Minimum amount not met',
        'max_amount_exceeded'      => 'Maximum amount exceeded',
        'kyc_required'             => 'KYC verification required',
        'web3_sync_failed'         => 'Failed to sync with WEB3 service',
        'balance_mismatch'         => 'Balance mismatch detected',
    ],

    'success' => [
        'account_created'    => 'Account created successfully',
        'balance_synced'     => 'Balance synced successfully',
        'deposit_created'    => 'Deposit request created successfully',
        'withdrawal_created' => 'Withdrawal request created successfully',
        'transfer_created'   => 'Transfer created successfully',
        'transfer_completed' => 'Transfer completed successfully',
        'transfer_cancelled' => 'Transfer cancelled successfully',
    ],

    // Additional translations
    'back'                  => 'Back',
    'view'                  => 'View',
    'actions'               => 'Actions',
    'optional'              => 'Optional',
    'important'             => 'Important',
    'failure_reason'        => 'Failure Reason',
    'recent'                => 'Recent',
    'details'               => 'Details',
    'supported_currencies'  => 'Supported Currencies',
    'transfer_to'           => 'Transfer To',
    'transfer_instructions' => 'Please transfer the exact amount to the account details shown above and use the reference number provided.',
    'payment_instructions'  => 'Payment Instructions',
    'use_reference'         => 'Use this reference',
    'exact_amount'          => 'Transfer exact amount',
    'processing_time_note'  => 'Processing time may vary depending on your bank',
];
