<?php

namespace Thorne\FastOrder\Jobs\Selling;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\FastOrder\Jobs\MaxRetryJob;
use Thorne\FastOrder\Jobs\ProcessStatusJob;
use Thorne\FastOrder\Payment\FastOrderPayment;
use Throwable;
use Webkul\Customer\Models\Customer;
use Webkul\Sales\Models\Order;

class DepositToWalletJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [3, 6, 9, 12, 15];

    public int $timeout = 60;

    private Order $order;

    private Customer $customer;

    private ApiClientAuth10Service $apiService;

    private FastOrderPayment $paymentMethod;

    public function __construct(Order $order, ApiClientAuth10Service $apiService)
    {
        $this->order         = $order;
        $this->apiService    = $apiService;
        $this->customer      = $order->customer;
        $this->paymentMethod = app(FastOrderPayment::class);
    }

    public function handle()
    {
        $meta = $this->order->meta()->where('meta_key', 'orderData')->first();

        if (! $meta || ! ($orderData = json_decode($meta->meta_value, true))) {
            throw new Exception('orderData bulunamadı veya geçersiz.');
        }

        $payload = [
            'tenantId'   => $this->paymentMethod->getConfigData('tenant_id'),
            'userId'     => $this->customer->id,
            'payeeType'  => 'CRYPTO',
            'amount'     => (float) $orderData['receivedAmountFormatted'] ?? null,
            'exponent'   => $this->paymentMethod->getConfigData('sell.received_currency.precision'),
            'currency'   => $this->paymentMethod->getConfigData('sell.received_currency.crypto_currency'),
            'chainId'    => $this->paymentMethod->getConfigData('chain_id'),
        ];

        $response = $this->apiService
            ->baseUrl2('/wallet/deposit')
            ->baseMethod('post')
            ->baseClient($payload);

        if (! $response->json('isSuccessful')) {
            throw new Exception('SellingProcess:DepositToWallet:handle failed - retrying - '.($response->json('errorMessage') ?? $response->body()));
        }

        $this->succeeded($response, $payload);
    }

    protected function succeeded($response, array $payload): void
    {
        Log::channel('fast-order')->info('SellingProcess:DepositToWallet:handle successful', [
            'order_id'  => $this->order->id,
            'response'  => $response->json(),
            'payload'   => $payload,
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        DB::transaction(function () use ($response) {
            $this->order->meta()->create([
                'meta_key'   => 'walletDepositData',
                'meta_value' => json_encode($response->json()),
            ]);

            $this->order->update(['status' => 'completed']);

            dispatch(new ProcessStatusJob($this->order, 'completed', 'customer'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-sell');
        });
    }

    public function failed(Throwable $exception)
    {
        Log::channel('fast-order')->critical('SellingProcess:DepositToWallet:failed - all attempts exhausted', [
            'order_id'  => $this->order->id,
            'exception' => $exception->getMessage(),
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        $this->order->update(['status' => 'failed']);

        dispatch(new MaxRetryJob($this->order, 'deposit'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-sell');
    }
}
