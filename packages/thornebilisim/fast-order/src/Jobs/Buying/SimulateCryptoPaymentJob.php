<?php

namespace Thorne\FastOrder\Jobs\Buying;

use App\Services\CurrencyConverter;
use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\FastOrder\Jobs\MaxRetryJob;
use Thorne\FastOrder\Jobs\ProcessStatusJob;
use Thorne\FastOrder\Payment\FastOrderPayment;
use Throwable;
use Webkul\Customer\Models\Customer;
use Webkul\Sales\Models\Order;

class SimulateCryptoPaymentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [3, 6, 9, 12, 15];

    public int $timeout = 60;

    private ApiClientAuth10Service $apiService;

    private Order $order;

    private Customer $customer;

    private CurrencyConverter $converter;

    private FastOrderPayment $paymentMethod;

    public function __construct(Order $order, ApiClientAuth10Service $apiService)
    {
        $this->order         = $order;
        $this->apiService    = $apiService;
        $this->customer      = $order->customer;
        $this->converter     = app(CurrencyConverter::class);
        $this->paymentMethod = app(FastOrderPayment::class);
    }

    public function handle()
    {
        $meta = $this->order->meta()->where('meta_key', 'orderData')->first();

        if (! $meta || ! ($orderData = json_decode($meta->meta_value, true))) {
            throw new Exception('orderData bulunamadı veya geçersiz.');
        }

        $payload = [
            'tenantId'          => $this->paymentMethod->getConfigData('tenant_id'),
            'userId'            => $this->customer->id,
            'amount'            => (float) $orderData['paymentAmountFormatted'] ?? null,
            'exponent'          => $this->paymentMethod->getConfigData('buy.payment_currency.precision'),
            'currency'          => $this->paymentMethod->getConfigData('buy.payment_currency.currency'),
            'cryptoCurrency'    => $this->paymentMethod->getConfigData('buy.payment_currency.crypto_currency'),
            'chainId'           => $this->paymentMethod->getConfigData('chain_id'),
            'gasCoinCredit'     => 1,
        ];

        $response = $this->apiService
            ->baseUrl2('/tx/cryptopayment')
            ->baseMethod('post')
            ->baseClient($payload);

        if (
            ! $response->successful() ||
            ! $response->json('hasAvailableBalance')
        ) {
            throw new Exception('BuyingProcess:SimulateCryptoPayment:handle failed - retrying - '.($response->json('errorMessage') ?? $response->body()));
        }

        $this->succeeded($response, $payload);
    }

    protected function succeeded($response, array $payload): void
    {
        Log::channel('fast-order')->info('BuyingProcess:SimulateCryptoPayment:handle successful', [
            'order_id'  => $this->order->id,
            'response'  => $response->json(),
            'payload'   => $payload,
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        DB::transaction(function () use ($response) {
            $this->order->meta()->create([
                'meta_key'   => 'cryptoPaymentData',
                'meta_value' => json_encode($response->json()),
            ]);

            dispatch(new ProcessStatusJob($this->order, 'pending', 'customer'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-buy');

            dispatch(new WithdrawEuroPaymentJob($this->order, $this->apiService))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-buy');
        });
    }

    public function failed(Throwable $exception)
    {
        Log::channel('fast-order')->critical('BuyingProcess:SimulateCryptoPayment:failed - all attempts exhausted', [
            'order_id'  => $this->order->id,
            'exception' => $exception->getMessage(),
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        DB::transaction(function () {
            $this->order->update(['status' => 'failed']);

            dispatch(new MaxRetryJob($this->order, 'payment'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-buy');
        });
    }
}
