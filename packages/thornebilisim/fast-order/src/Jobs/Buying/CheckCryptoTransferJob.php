<?php

namespace Thorne\FastOrder\Jobs\Buying;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\FastOrder\Jobs\MaxRetryJob;
use Throwable;
use Webkul\Customer\Models\Customer;
use Webkul\Sales\Models\Order;

class CheckCryptoTransferJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [3, 6, 9, 12, 15];

    public int $timeout = 60;

    private Order $order;

    private Customer $customer;

    private ApiClientAuth10Service $apiService;

    public function __construct(Order $order, ApiClientAuth10Service $apiService)
    {
        $this->order      = $order;
        $this->apiService = $apiService;
        $this->customer   = $order->customer;
    }

    public function handle()
    {
        $meta = $this->order->meta()->where('meta_key', 'cryptoWithdrawalData')->first();

        if (! $meta || ! ($withdrawalData = json_decode($meta->meta_value, true))) {
            throw new Exception('cryptoWithdrawalData bulunamadı veya geçersiz.');
        }

        $response = $this->apiService
            ->baseUrl2('/tx/check')
            ->baseMethod('post')
            ->baseClient($withdrawalData);

        if (! $response->successful() || $response->json('status') != 1) {
            throw new Exception('BuyingProcess:CheckCryptoTransfer:handle failed - retrying - '.($response->json('errorMessage') ?? $response->body()));
        }

        $this->succeeded($response);
    }

    protected function succeeded($response): void
    {
        Log::channel('fast-order')->info('BuyingProcess:CheckCryptoTransfer:handle successful', [
            'order_id'  => $this->order->id,
            'response'  => $response->json(),
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        DB::transaction(function () use ($response) {
            $this->order->meta()->create([
                'meta_key'   => 'cryptoCheckData',
                'meta_value' => json_encode($response->json()),
            ]);

            dispatch(new DepositToWalletJob($this->order, $this->apiService))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-buy');
        });
    }

    public function failed(Throwable $exception)
    {
        Log::channel('fast-order')->critical('BuyingProcess:CheckCryptoTransfer:failed - all attempts exhausted', [
            'order_id'  => $this->order->id,
            'exception' => $exception->getMessage(),
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        DB::transaction(function () {
            $this->order->update(['status' => 'failed']);

            dispatch(new MaxRetryJob($this->order, 'payment'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-buy');
        });
    }
}
