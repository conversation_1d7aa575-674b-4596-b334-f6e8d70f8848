<?php

namespace Thorne\FastOrder\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Thorne\FastOrder\DTO\FastOrderData;
use Thorne\FastOrder\Services\FastOrderService;

class WebhookController extends Controller
{
    protected FastOrderService $service;

    public function __construct(FastOrderService $service)
    {
        $this->service = $service;
    }

    public function handle(): JsonResponse
    {
        Log::channel('fast-order')->info('FastOrder webhook received', [
            'headers' => request()->headers->all(),
            'body'    => request()->all(),
        ]);

        $hookToken = request()->header('X-Webhook-Token');

        if ($hookToken !== config('fast-order.webhook_token')) {
            Log::channel('fast-order')->warning('FastOrder webhook received with invalid hook token', [
                'received' => $hookToken,
                'expected' => config('fast-order.webhook_token'),
            ]);

            return response()->json([
                'status'  => 'unauthorized',
                'message' => 'Invalid or missing hook token.',
            ], 401);
        }

        $rules = [
            'type'        => 'required|in:buy,sell',
            'product_id'  => 'required|numeric',
            'quantity'    => 'required|numeric|min:1',
            'price'       => 'required|numeric|min:0',
            'price_key'   => 'required|string',
            'timestamp'   => 'required|numeric',
            'nonce'       => 'required|string|size:32',
            'hmac'        => 'required|string',
            'customer_id' => 'required|numeric',
            'idempotency' => 'required|string',
        ];

        if (App::environment('production')) {
            $rules['customer_id'] .= '|exists:customers,id';
            $rules['product_id']  .= '|exists:products,id';
        }

        $validator = Validator::make(request()->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'status'  => 'error',
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $idempotency = request('idempotency');
        $ttl         = config('fast-order.idempotency.ttl_seconds');

        if (cache()->has($idempotency)) {
            $cached = cache()->get($idempotency);

            return response()->json($cached);
        }

        try {
            $result = $this->service->handleTransaction(
                new FastOrderData(
                    type: request('type'),
                    product_id: request('product_id'),
                    quantity: request('quantity'),
                    price: request('price'),
                    price_key: request('price_key'),
                    timestamp: request('timestamp'),
                    nonce: request('nonce'),
                    hmac: request('hmac'),
                    customer_id: request('customer_id'),
                )
            );

            cache()->put($idempotency, $result, $ttl);

            return response()->json($result);
        } catch (Exception $e) {
            Log::channel('fast-order')->error('FastOrder webhook failed', [
                'headers' => request()->headers->all(),
                'body'    => request()->all(),
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString(),
            ]);

            cache()->forget($idempotency);

            return response()->json([
                'status'  => 'error',
                'message' => 'FastOrderService: '.$e->getMessage(),
            ], 422);
        }
    }
}
