@if(request()->route()->getName() != 'customer.forgot-password.create' && request()->route()->getName() != 'customer.reset-password.create' && request()->route()->getName() != 'customer.register.success')

<!-- <header class="sticky-header">
    <div class="row remove-padding-margin velocity-divide-page">
        <a class="left navbar-brand" href="{{ route('shop.home.index') }}" aria-label="Logo">
            <img class="logo" src="{{ core()->getCurrentChannel()->logo_url ?? asset('themes/velocity/assets/images/logo-text.png') }}" alt="" />
        </a>

        <div class="right searchbar">
            <div class="row">
                <div class="col-lg-5 col-md-12">
                    {{-- @include('velocity::shop.layouts.particals.search-bar') --}}
                </div>

                <div class="col-lg-7 col-md-12 vc-full-screen">
                    <div class="left-wrapper">

                        {{-- {!! view_render_event('bagisto.shop.layout.header.wishlist.before') !!}

                            @include('velocity::shop.layouts.particals.wishlist', ['isText' => true])

                        {!! view_render_event('bagisto.shop.layout.header.wishlist.after') !!}

                        {!! view_render_event('bagisto.shop.layout.header.compare.before') !!}

                            @include('velocity::shop.layouts.particals.compare', ['isText' => true])

                        {!! view_render_event('bagisto.shop.layout.header.compare.after') !!}

                        {!! view_render_event('bagisto.shop.layout.header.cart-item.before') !!}

                            @include('shop::checkout.cart.mini-cart')

                        {!! view_render_event('bagisto.shop.layout.header.cart-item.after') !!}

                        --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</header> -->

{{-- @push('scripts')
    <script type="text/javascript">
        (() => {
            document.addEventListener('scroll', e => {
                scrollPosition = Math.round(window.scrollY);

                if (scrollPosition > 50) {
                    document.querySelector('header').classList.add('header-shadow');
                } else {
                    document.querySelector('header').classList.remove('header-shadow');
                }
            });
        })();
    </script>
@endpush  --}}



<header class="w-full bg-white shadow-institutional z-9999" id="masterheader">
    <div class="container mx-auto relative">

        <div class="flex justify-between items-center py-3 lg:px-5">
            <div class="vc-small-screen">
                @include('fast-order::layouts.header.mobile')
            </div>
            <div class="flex items-center space-x-2 group">
{{--                transition-all duration-300 group-hover:drop-shadow-lg group-hover:opacity-90 group-hover:drop-shadow-text-terra-light-green--}}
                <a href="{{ route('shop.home.index') }}"><img class="w-full max-w-[35px] lg:max-w-70p " src="/deneme/svg/logo.svg" /></a>
                <div class="flex">
{{--                    group-hover:text-terra-light-green group-hover:drop-shadow-lg group-hover:opacity-90 group-hover:drop-shadow-text-terra-light-green--}}
                    <a href="<?php echo e(route('shop.home.index')); ?>" class="transition-all duration-300	 unset">
                        <span class="font-sans text-lg lg:text-2xl font-semibold text-terra-green tracking-wider">T</span><span class="font-sans text-lg lg:text-2xl font-semibold tracking-wider">erra</span><span class="font-sans text-lg lg:text-2xl font-semibold text-terra-light-green tracking-wider">M</span><span class="font-sans text-lg lg:text-2xl font-semibold tracking-wider ">irum</span>
                    </a>
                </div>
            </div>
            <div class="block lg:hidden">
                <a href="{{ route('shop.checkout.cart.index') }}" class="unset">
                    <img src="/deneme/svg/cart-icon.svg" style="width: 20px;">
                </a>
            </div>
            <div class="dropbtn relative menu-item text-base text-iconcolor hidden lg:flex justify-around w-2/5">
                {{-- <div><a class="font-sans text-xl font-semibold">Pazar yeri</a></div> --}}
                    <sidebar-header heading= "{{ __('shop::app.menu.marketplace') }}" route="{{route('shop.productOrCategory.index', 'all')}}">
                        <div class="main-category fs16 unselectable fw6 left ">
                            <a href="{{route('shop.productOrCategory.index', 'all')}}" class="font-sans text-xl font-semibold transition-all duration-300 hover:text-terra-light-green text-decoration-none">
                                {{ __('shop::app.menu.marketplace') }}
                                {{-- Pazar Yeri --}}
                            </a>
                        </div>

                    </sidebar-header>

                    @include('fast-order::layouts.top-nav.index')

            </div>

            <div class="hidden lg:flex space-x-4 items-center">


                {{-- Search Bar --}}
                @include ('fast-order::layouts.particals.search-bar')

                {{-- <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="34" viewBox="0 0 39.577 35">
                    <g id="Group_1629" data-name="Group 1629">
                        <path id="Path_1574" data-name="Path 1574"
                              d="M11.106,29.04h9.662a4.473,4.473,0,0,0,.5,4.132,4.229,4.229,0,0,0,2.409,1.675,4.316,4.316,0,0,0,3.335-7.865c.783-3.256,1.562-6.482,2.333-9.709Q30.981,10.439,32.6,3.6a1.081,1.081,0,0,1,1.175-.946c1.443-.006,2.887,0,4.33,0A1.305,1.305,0,0,0,39.576,1.33,1.32,1.32,0,0,0,38.1.006C36.677,0,35.25,0,33.822,0A3.725,3.725,0,0,0,30.03,3.03c-.176.739-.391,1.47-.528,2.216-.069.376-.233.442-.571.441q-8.541-.013-17.083-.006-5.187,0-10.373.007A1.326,1.326,0,0,0,.064,7.5Q1.833,14.935,3.6,22.372c.24,1.009.631,1.32,1.656,1.32H25.1c-.155.626-.267,1.2-.442,1.753a1.372,1.372,0,0,1-1.36.926c-.936.01-1.871,0-2.807,0q-6.614,0-13.229,0A4.312,4.312,0,1,0,11.29,31.72a4.637,4.637,0,0,0-.183-2.68M19.3,8.34c-.137,1.693-.27,3.341-.4,4.989H12.842c-.132-1.675-.262-3.313-.394-4.989ZM2.986,8.353A1.519,1.519,0,0,1,3.2,8.308c2.077,0,4.154.006,6.231-.012.392,0,.39.239.41.488q.161,2.014.307,4.028c.012.168,0,.337,0,.523H4.173L2.986,8.353m18.961-.016h6.781c-.029.174-.044.312-.076.446-.326,1.37-.67,2.736-.972,4.111-.08.366-.216.485-.59.48-1.681-.02-3.362-.009-5.043-.011-.153,0-.307-.013-.5-.022.134-1.681.265-3.321.4-5M13.06,16.031h5.619c-.122,1.6-.247,3.164-.358,4.727-.022.311-.218.29-.431.29q-1.665,0-3.331,0h-1.1c-.135-1.7-.264-3.342-.4-5.016m7.883,5.015c.123-1.622.245-3.168.353-4.715.021-.309.187-.344.441-.341.935.009,1.87,0,2.8,0h2.393c-.4,1.671-.782,3.282-1.178,4.889a.31.31,0,0,1-.24.157c-1.5.009-3,.007-4.573.007m-10.138,0c-1.56,0-3.076,0-4.592-.011-.088,0-.224-.15-.253-.255-.132-.455-.236-.918-.345-1.38-.265-1.118-.528-2.236-.794-3.364h5.586l.4,5.009m15.63,9.639a1.641,1.641,0,0,1-1.648,1.667A1.683,1.683,0,0,1,23.113,30.7a1.7,1.7,0,0,1,1.665-1.671,1.652,1.652,0,0,1,1.658,1.66m-20.97-.008a1.647,1.647,0,0,1,1.663-1.652A1.662,1.662,0,1,1,7.1,32.35a1.631,1.631,0,0,1-1.635-1.673"
                              transform="translate(0 0)" />
                    </g>
                </svg> --}}

                {{-- {!! view_render_event('bagisto.shop.layout.header.cart-item.before') !!} --}}

                {{-- {!! view_render_event('bagisto.shop.layout.header.cart-item.after') !!}  --}}

                @include('fast-order::layouts.top-nav.login-section')

                {{-- <a href="{{ route('customer.session.index') }}">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="28.125" height="30" viewBox="0 0 28.125 30">
                    <g id="Group_4" data-name="Group 4">
                        <path id="Path_19" data-name="Path 19"
                              d="M0,299.658c.058-.55.1-1.1.175-1.651a10.236,10.236,0,0,1,7.091-8.76,15.874,15.874,0,0,1,4.537-.924,33.989,33.989,0,0,1,3.712-.055,15.407,15.407,0,0,1,7.7,2.178,9.834,9.834,0,0,1,4.438,6.529c.17.805.217,1.636.322,2.455.01.076.028.152.042.227v.643c-.275.748-.561.993-1.184.993q-12.825,0-25.649,0a1.465,1.465,0,0,1-.373-.048A1.165,1.165,0,0,1,0,300.3v-.643m25.741-.574a10.229,10.229,0,0,0-.54-2.741,7.652,7.652,0,0,0-2.939-3.9,12.233,12.233,0,0,0-5.735-1.907,22.729,22.729,0,0,0-4.183-.071,15.388,15.388,0,0,0-4.863,1.045,7.9,7.9,0,0,0-4.933,5.777c-.136.582-.2,1.179-.308,1.8Z"
                              transform="translate(0 -271.297)" />
                        <path id="Path_20" data-name="Path 20" d="M122.874,0c.284.057.57.1.851.173a6.9,6.9,0,0,1,5.5,5.7,7.932,7.932,0,0,1-3.578,8.385,6.516,6.516,0,0,1-7.147-.048,7.506,7.506,0,0,1-3.51-5.261,7.531,7.531,0,0,1,1.369-6.169A6.844,6.844,0,0,1,120.814.113c.18-.031.358-.075.536-.114Zm-5.826,7.165a6.224,6.224,0,0,0,1.43,4.185,4.683,4.683,0,0,0,6.294.875,5.943,5.943,0,0,0,2.234-6.213,5.056,5.056,0,0,0-7.4-3.19,4.827,4.827,0,0,0-2.558,4.344" transform="translate(-108.103 0.047)" />
                    </g>
                </svg>
                </a> --}}
            </div>
        </div>
    </div>
</header>
    @section('couponscript')
        <script>
            document.addEventListener("DOMContentLoaded", function(){
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 50) {
                        document.getElementById('masterheader').classList.add('fixed-top');
                        // add padding top to show content behind navbar
                        navbar_height = document.querySelector('header').offsetHeight;
                        document.body.style.paddingTop = navbar_height + 'px';
                    } else {
                        document.getElementById('masterheader').classList.remove('fixed-top');
                        // remove padding top from body
                        document.body.style.paddingTop = '0';
                    }
                });
            });
        </script>
        <style>
            .fixed-top {
                top: -40px;
                transform: translateY(40px);
                transition: transform .3s;
            }
        </style>
    @endsection
@endif