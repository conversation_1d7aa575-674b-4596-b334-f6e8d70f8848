@extends('fast-order::emails.layouts.miligold-mail')

@section('content')
    <div style="margin-bottom: 20px; display: flex; align-items: center; gap: 20px; padding: 0 20px;">
        <img src="{{ asset('miligold/img/icons/order_failed.svg') }}" width="48">
        <h2 style="margin: 0; font-weight: bold; color: #D09800; font-size: 24px;">{!! __('mail.sales.order.failed.greeting') !!}</h2>
    </div>

    <div style="background-color: #ffffff; border-radius: 35px; box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1); padding: 20px; background-image: url('{{ asset('miligold/img/pattern.png') }}'); background-repeat: repeat; background-position: center;">
        <div>
            <p style="font-size: 14px; color: #333; margin-bottom: 30px;">{!! __('mail.sales.order.failed.summary', ['customer_name' => $order->customer->name]) !!}</p>
            <p style="font-size: 14px; color: #555;">{!! __('mail.sales.order.failed.dear') !!}</p>
        </div>
    </div>

    <div style="margin-top: 40px;">
        <h3 style="font-weight: normal; color: #D09800; font-size: 20px; margin: 0 0 20px;">{!! __('mail.order.summary') !!}</h3>
        <div style="width: 100%; background: black; color: white; border-radius: 20px; overflow: hidden; display: flex;">
            <div style="flex: 4; padding: 25px;">{{ __('mail.order.product-name') }}</div>
            <div style="flex: 1; padding: 25px;">{{ __('mail.order.price') }}</div>
            <div style="flex: 1; padding: 25px;">{{ __('mail.order.quantity') }}</div>
        </div>

        @foreach ($order->items as $item)
            <div style="width: 100%; border-bottom: 1px solid rgba(0, 0, 0, 0.1); display: flex;">
                <div style="flex: 4; padding: 25px;">
                    {{ $item->name }}
                    @if (isset($item->additional['attributes']))
                        <div class="item-options">

                            @foreach ($item->additional['attributes'] as $attribute)
                                <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>
                            @endforeach
                        </div>
                    @endif
                </div>
                <div style="flex: 1; padding: 25px;">
                    {{ core()->formatPrice($item->price, $order->order_currency_code, 4) }}
                </div>
                <div style="flex: 1; padding: 25px;">
                    {{ $item->qty_ordered }}
                </div>
            </div>
        @endforeach

        <div style="display: flex; justify-content: space-between; align-items: flex-start; flex-wrap: wrap;">
            <div style="width: 50%; font-size: 14px; padding: 20px; background-color: #ffffff; border-radius: 35px; box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1); margin-left: auto; margin-top: 20px; align-self: flex-start;">
                <div style="display: flex; justify-content: space-between; padding: 12px 10px;">
                    <span>{{ __('mail.order.subtotal') }}</span>
                    <span>{{ core()->formatPrice($order->sub_total, $order->order_currency_code) }}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 12px 10px;">
                    <span>{{ __('mail.order.tax') }}</span>
                    @foreach (Webkul\Tax\Helpers\Tax::getTaxRatesWithAmount($order, false) as $taxRate => $taxAmount )
                        <span id="taxamount-{{ core()->taxRateAsIdentifier($taxRate) }}">
                                {{ core()->formatPrice($taxAmount, $order->order_currency_code) }}
                            </span>
                    @endforeach
                </div>
                <hr style="border: none; border-top: 1px solid #ccc; margin: 8px 0;">
                <div style="display: flex; justify-content: space-between; padding: 12px 10px; font-weight: bold;">
                    <span>{{ __('mail.order.grand-total') }}</span>
                    <span>{{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}</span>
                </div>
            </div>
        </div>
    </div>
@endsection

