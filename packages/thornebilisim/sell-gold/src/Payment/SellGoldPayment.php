<?php

namespace Thorne\SellGold\Payment;

use Webkul\Payment\Payment\Payment;

class SellGoldPayment extends Payment
{
    /**
     * Payment method code
     *
     * @var string
     */
    protected $code  = 'sellgold';

    public function getRedirectUrl()
    {
        return route('sellgold.summary');
    }

    public function getPaymentCode()
    {
        return $this->code;
    }

    public function isPaymentMethod()
    {
        return false;
    }
}
