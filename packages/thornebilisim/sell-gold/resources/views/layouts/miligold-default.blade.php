<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" class="no-js">
<head>
    <title>@yield('page_title')</title>

    {{-- meta data --}}
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="content-language" content="{{ app()->getLocale() }}" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="base-url" content="{{ url()->to('/') }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    {!! view_render_event('bagisto.shop.layout.head') !!}

    {{-- for extra head data --}}
    @yield('head')

    {{-- seo meta data --}}
    @yield('seo')

    {{-- fav icon --}}
    @if ($favicon = core()->getCurrentChannel()->favicon_url)
        <link rel="shortcut icon" href="/miligold/img/mili-ikon.png"/>
    @else
        <link rel="shortcut icon" href="/miligold/img/mili-ikon.png"/>
    @endif

    @include('sellgold::layouts.partials._noindex')


    <link rel="stylesheet" href="{{ asset('miligold/css/style.css') }}?v=2" />
    <link rel="stylesheet" href="{{ asset('assets/css/modal.css') }}?v=2" />
    <link rel="stylesheet" href="{{ mix('/css/app.css') }}" />
    <script src='https://www.google.com/recaptcha/api.js'></script>

    @yield('styles')
    <style>
        .alert {
            border: 1px solid transparent;
            border-radius: 20px;
            box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
            z-index: 99999;
            color: #fff;
        }
        #alert-container {
            position: fixed;
            top: 0;
            right: 0;
            z-index: 99999;
            padding: 24px;
        }
        #alert-container-session {
            position: fixed;
            top: 0;
            right: 0;
            z-index: 99999;
            padding: 24px;
        }
        .alert strong{
            display: block;
        }
        .alert-close {
            color: rgba(255, 255, 255, 0.25);
        }
        .alert-success{
            background-color: #232a32;
            border-color: rgba(0, 214, 143, 0.2);
            box-shadow: 0 0 20px 0 rgba(0, 214, 143, 0.1);
            color: #00D68F;
        }
        .alert-warning{
            background-color: #232a32;
            border-color: rgba(255, 193, 7, 0.2);
            box-shadow: 0 0 20px 0 rgba(255, 193, 7, 0.1);
            color: #FFC107;
        }
        .alert-error {
            background-color: #232a32;
            border-color: rgba(255, 61, 61, 0.2);
            box-shadow: 0 0 20px 0 rgba(255, 61, 61, 0.1);
            color: #FF3D3D;
        }
        .alert-info{
            background-color: #232a32;
            border-color: rgba(0, 184, 255, 0.2);
            box-shadow: 0 0 20px 0 rgba(0, 184, 255, 0.1);
            color: #00B8FF;
        }
        .alert-dismissible{
            padding-right: 2rem;
        }
        .glowAnimation{
            animation: glowAnimation 3s infinite ease-in-out;
            background: linear-gradient(135deg, #f8951e, #ffcc33, #f8951e);
            background-size:200% 200%;
        }
        @keyframes glowAnimation {
            0% {
                background-position: -200% -200%;
            }
            50% {
                background-position: 100% 100%;
            }
            100% {
                background-position: -200% -200%;
            }
        }
    </style>
    <script>
        window.Laravel = {!! json_encode([
                'csrfToken' => csrf_token(),
            ]) !!};
    </script>
</head>
<body data-layout="horizontal" class="home-01 {{ isset($bodyClass) ? $bodyClass : '' }}">
{!! view_render_event('bagisto.shop.layout.body.before') !!}
@include('sellgold::layouts.partials._loading')

{{-- main app --}}
<div id="app">
    @include('sellgold::layouts.partials.miligold-header', ['showNavigation' => isset($showNavigation) ? $showNavigation : true])
    @yield('body')
    @include('sellgold::layouts.partials.miligold-footer')
</div>

<div id="alert-container"></div>
<div id="alert-container-session">
    @php
        $alerts = [ 'success', 'warning', 'error', 'info' ];
    @endphp
    @foreach ($alerts as $key)
        @if (session()->has($key))
            <div class="alert alert-{{ $key }} alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center justify-content-between">
                    <strong>{{ __('velocity::app-static.alert.' . $key) }}</strong>
                    <button type="button" data-dismiss="alert" aria-label="Close" class="close alert-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <p class="text-white">{!! session()->get($key)  !!}</p>
            </div>
        @endif
    @endforeach
</div>

@if(Route::current()->uri() !== '/' &&
    Route::current()->uri() !== 'customer/login' &&
    Route::current()->uri() !== 'customer/account/profile' &&
    Route::current()->uri() !== 'customer/account/password' &&
    Route::current()->uri() !== 'customer/account/wallet' &&
    Route::current()->uri() !== 'customer/account/locked-coins' &&
    Route::current()->uri() !== 'customer/account/orders' &&
    Route::current()->uri() !== 'customer/account/transfer-details' &&
    Route::current()->uri() !== 'customer/account/institutional')
    @include('sellgold::layouts.scripts')
@endif
@yield('couponscript')
<script src='https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.5/d3.min.js'></script>
<script src="{{ asset('miligold/js/app.bundle.js') }}"></script>

@yield('scripts')
@stack('scripts')
<script>
    setTimeout(function(){
        $('#alert-container-session').fadeOut();
    }, 5000);
    $(document).on('click', '.alert-close', function(){
        $(this).parent().fadeOut();
    });
</script>

<script type="text/javascript">
    var _iub = _iub || [];
    _iub.csConfiguration = {"askConsentAtCookiePolicyUpdate":true,"cookiePolicyInOtherWindow":true,"emailMarketing":{"theme":"dark"},"floatingPreferencesButtonDisplay":"bottom-right","lang":"en","perPurposeConsent":true,"preferenceCookie":{"expireAfter":180},"siteId":3772496,"whitelabel":false,"cookiePolicyId":********,"cookiePolicyUrl":"/page/cookies","privacyPolicyUrl":"/page/terms-and-conditions","banner":{"acceptButtonDisplay":true,"closeButtonDisplay":false,"customizeButtonDisplay":true,"explicitWithdrawal":true,"listPurposes":true,"ownerName":"terramirum.com","position":"bottom","rejectButtonDisplay":true,"showTitle":false,"showTotalNumberOfProviders":true}};
</script>
<script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3772496.js"></script>
<script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>

<style>
    #iubenda-cs-banner .iubenda-cs-brand-badge{
        display:none;
    }
    #iubenda-cs-banner .iubenda-cs-content{
        box-shadow: -2px -2px 8px 2px rgb(184 184 184) !important;
    }
    @media (min-width: 992px) {
        #iubenda-cs-banner.iubenda-cs-default .iubenda-cs-rationale {
            width: 90% !important;
            margin: 0px auto!important;
        }
    }
    #iubenda-cs-banner #iubenda-cs-paragraph.iubenda-cs-small-margin-top{
        margin-top: 0px!important;
    }
    #iubenda-cs-banner .iubenda-cs-opt-group button, #iubenda-cs-banner .iubenda-banner-content:not(.iubenda-custom-content) *, #iubenda-cs-banner [class*=" iub"], #iubenda-cs-banner [claass^=iub]{
        font-size: 12px!important;
    }
    #iubenda-iframe .iubenda-iframe-badge-container .iubenda-cs-brand-badge{
        display: none!important;
    }
    #iubenda-iframe .purposes-header-right .iub-iframe-brand-button{
        display: none!important;
    }
    .iub__us-widget{
        display: none!important;
    }
    .iubenda-tp-alert-btn[data-tp-icon], .iubenda-tp-btn[data-tp-icon], .iubenda-uspr-btn[data-tp-icon]{
        background-image:url("/miligold/img/mili-ikon.png")!important ;
    }
</style>
<style>
    /* Scroll behavior */
    .scroll-container {
        scroll-behavior: smooth;
    }
</style>
@if(View::exists('sellgold::layouts.global-modal'))
    @include ('sellgold::layouts.global-modal')
@endif
</body>
</html>
