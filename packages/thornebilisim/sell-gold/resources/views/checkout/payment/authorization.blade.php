@extends('sellgold::layouts.miligold-default')

@section('page_title')
    {{ __('velocity::app-static.sell-gold.authorization.page-title') }}
@stop

@section('body')
    <main class="pt-12">
        <section class="relative bg-contain bg-right-top bg-no-repeat pt-32 pb-16">
            <div class="container relative z-10">
                <h1 class="text-center font-display text-4xl lg:text-6xl font-medium text-jacarta-700">
                    {{ __('velocity::app-static.sell-gold.authorization.heading') }}
                </h1>
            </div>
        </section>

        <!-- Contact -->
        <section class="relative pb-12 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="mx-auto max-w-5xl">
                <crypto_authorization></crypto_authorization>
            </div>
        </section>
    </main>
@endsection

@push('scripts')
    <style>
        select {
            background: #0B1D33;
            border: 1px solid rgba(255, 255, 255, 0.07);
            border-radius: 5px;
            font-size: 16px;
            color: #fff;
            font-weight: 400;
            padding: 6px 20px;
            height: 60px;
            width: 220px!important;
            margin-left: 20px;
        }
        select:first-child {
            margin-left: 0!important;
        }
    </style>
    <script type="text/x-template" id="crypto_authorization_template">
        <div class="mx-auto max-w-5xl">
            <form action="{{ route('sellgold.payment.confirmation') }}" method="POST" class="gap-2 flex flex-wrap w-full justify-between my-5 pb-2" @submit.prevent="onSubmit"
                  id="payment-authorization">
                @csrf
                @method('POST')
                    <div class="w-full md:w-6/12">
                        <section class="contact-aera">
                            <div class="container custom-container-four p-0">
                                <div class="rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700 shadow-md">
                                    <div class="row">
                                        <div class="col-md-12 mb-4">
                                            <div class="wallet-list-item transfer-details flex-column">
                                                <div class="row select-btns w-100">
                                                    <div class="w-full"><h4 class="mb-4 font-display text-3xl text-jacarta-700 dark:text-white text-center border-b-2 border-[#D0AA49] pb-4">{{ __('velocity::app-static.sell-gold.authorization.show-payment-details') }}</h4></div>

                                                    <input type="hidden" name="txCryptoPayment" value="{{ base64_encode(json_encode($txCryptoPayment)) }}">
                                                    <input type="hidden" name="transactionStatus" v-model="transactionStatus">
                                                    <input type="hidden" name="transactionTxId" v-model="transactionTxId">
                                                    <input type="hidden" name="transactionMessage" v-model="transactionMessage">

                                                    <div v-if="placeholderStatus">
                                                        <div class="w-full mb-3">
                                                            <label for="chain" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.sell-gold.authorization.chain') }}</label>
                                                            <div class="relative">
                                                                <input v-model="selectedChainValue" type="text" disabled id="chain"
                                                                       class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700">
                                                            </div>
                                                        </div>

                                                        <div class="w-full mb-3">
                                                            <label for="walletBalance" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.sell-gold.authorization.wallet-balance') }}</label>
                                                            <div class="relative">
                                                                <input v-model="transaction.walletBalance" type="text" disabled id="walletBalance"
                                                                       class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700">
                                                                <div class="absolute inset-0 w-full h-full flex items-center justify-end pr-2">
                                                                    <h5 class="mb-0" v-text="transaction.currency"></h5>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="w-full mb-3">
                                                            <label for="requiredAmount" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.sell-gold.authorization.processing-amount') }}</label>
                                                            <div class="relative">
                                                                <input v-model="transaction.requiredAmount" type="text" disabled id="requiredAmount"
                                                                       class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700">
                                                                <div class="absolute inset-0 w-full h-full flex items-center justify-end pr-2">
                                                                    <h5 class="mb-0" v-text="transaction.currency"></h5>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="w-full mb-3">
                                                            <label for="totalRequiredAmount" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.sell-gold.authorization.transaction-amount') }}</label>
                                                            <div class="relative">
                                                                <input v-model="transaction.totalRequiredAmount" type="text" disabled id="totalRequiredAmount"
                                                                       class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-[#D0AA49] focus:border-2 focus:border-transparent active:outline-none active:ring-2 active:ring-[#D0AA49] active:border-2 active:border-transparent bg-white text-gray-700">
                                                                <div class="absolute inset-0 w-full h-full flex items-center justify-end pr-2">
                                                                    <h5 class="mb-0" v-text="transaction.currency"></h5>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-if="!placeholderStatus">
                                                        <div class="w-100 mb-3">
                                                            <div class="cart-content d-flex checkbox-wrapper-6">
                                                                <div class="checkbox-wrapper-14 d-flex align-items-center">
                                                                    <input id="s1-14" type="checkbox" class="switch me-2" required v-model="acceptRules1" @change="updateAcceptRules1">
                                                                    <label for="s1-14">
                                                                        {!! __('velocity::app-static.moneytransfer.authorization.risk-clauses') !!}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="w-100 mb-3">
                                                            <div class="cart-content d-flex checkbox-wrapper-6">
                                                                <div class="checkbox-wrapper-14 d-flex align-items-center">
                                                                    <input id="s1-15" type="checkbox" class="switch me-2" required v-model="acceptRules2" @change="updateAcceptRules2">
                                                                    <label for="s1-15">
                                                                        {!! __('velocity::app-static.moneytransfer.authorization.terms-conditions') !!}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="w-100 mb-3">
                                                            <div class="cart-content d-flex checkbox-wrapper-6">
                                                                <div class="checkbox-wrapper-14 d-flex align-items-center">
                                                                    <input id="s1-16" type="checkbox" class="switch me-2" required v-model="acceptRules3" @change="updateAcceptRules3">
                                                                    <label for="s1-16">
                                                                        {!! __('velocity::app-static.moneytransfer.authorization.data-protect') !!}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="w-100 mb-5">
                                                            <div class="cart-content d-flex checkbox-wrapper-6">
                                                                <div class="checkbox-wrapper-14 d-flex align-items-center">
                                                                    <input id="s1-17" type="checkbox" class="switch me-2" required v-model="acceptRules4" @change="updateAcceptRules4">
                                                                    <label for="s1-17">
                                                                        {!! __('velocity::app-static.moneytransfer.authorization.email-confirmation') !!}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>



                                                <div v-if="isModalRiskClauses" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
                                                    <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;">
                                                        <header class="modal__header" style="height: 20px;">
                                                            <h2 class="modal__title" id="modal-1-title"></h2>
                                                            <button @click="functionCloseRiskClauses()" class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                                                        </header>
                                                        <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                                                            <div class="popup-info-item">
                                                                <div class="d-flex align-items-center mb-5">
                                                                    <div class="icon">
                                                                        <span class="icon-background"></span>
                                                                        <i class="fas fa-exclamation-triangle"></i>
                                                                    </div>
                                                                    <div class="content">
                                                                        <h4>Risk Warnings</h4>
                                                                    </div>
                                                                </div>
                                                                <div class="content">
                                                                    <div style="border-bottom: 1px solid #212a32;padding: 10px;"></div>
                                                                    <div class="position-relative mt-4 text-left text-white">
                                                                        <ol>
                                                                            <li>General Information</li>
                                                                        </ol>
                                                                        <p><strong>&nbsp;</strong></p>
                                                                        <p>The following general risk information outlines the risks that may be present when investing in financial instruments, in particular investments or securities, offered on the <a href="https://www.credipto.com/">https://www.credipto.com/</a> platform. These general risk warnings are supplemented by project-specific risk warnings for individual financing projects. These project-specific risk warnings may differ from and take precedence over the disclosures below. Project-specific risk information is provided to investors in the context of the relevant investment.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>The financial instruments offered are associated with economic, legal and tax risks. Investors should therefore read the risk information below and the relevant project-specific risk information carefully and take it into account when making their decisions. In particular, the investor's investment should be appropriate to its own financial circumstances and its investment in the financial instruments offered should only represent a small portion of its total assets.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Some of the legal and actual risks associated with the financial instruments offered and important for the valuation of financial instruments are presented below. Also presented are risk factors that may impair the ability of issuers (&ldquo;issuers&rdquo;) to produce expected results.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Not all risks associated with investments can be described below. The risks listed below cannot be exhaustively described here for all investments offered on the platform. The chronological order of the risks listed does not allow any conclusion to be drawn about the probability of their possible realization or the extent of a potential impairment.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol start="2">
                                                                            <li>general risks</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Maximum risk - risk of total loss</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>For many of the financial instruments offered, there is a risk of total loss of the investment amount and interest receivable. The occurrence of individual risks or the cumulative interaction of various risks may have a significant negative impact on the expected results of an issuer, which may lead to its bankruptcy. The individual investor may be exposed to additional financial disadvantages. This may be the case, for example, if the investor finances the acquisition of financial instruments with a loan, strictly schedules payments from the financial instrument to cover other obligations despite the existing risk of loss, or due to costs for back tax payments. In the worst case scenario, such additional financial disadvantages can even lead to the personal bankruptcy of the investor. Investors should therefore examine all risks taking into account their personal situation and circumstances and, if necessary, seek individual professional advice. External financing of investments (e.g. through a bank loan) is strongly discouraged.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Financial instruments are only suitable as an addition to an investment portfolio. Investment is only suitable for investors who can accept a loss up to the total loss of their capital investment. As a rule, there is no legal or other deposit protection. Furthermore, the financial instruments offered are usually not suitable for pension provision.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>On the other hand, there is usually no obligation to make additional contributions or any other risk of liability exceeding the amount of invested capital. In particular, asset investments offered on the platform under the Asset Investment Act (e.g. qualified subordinated loans) never provide for an investor's obligation to make additional contributions.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Subordination risk</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Some of the financial instruments offered on the platform are equipped with loss participation (i.e. a reduction in investors' repayment claims in case the issuer incurs losses) and/or qualified subordination and pre-bankruptcy enforcement block. Such financial instruments are entrepreneurial financing with a corresponding entrepreneurial risk of loss (liability function similar to equity). However, the investor does not obtain any participation or control rights under company law and therefore does not have the opportunity to influence the realization of the entrepreneurial risk (in particular, the investor does not have the opportunity to terminate loss-making business activities before the invested capital is used). From the investor's point of view, this type of contractual arrangement combines the disadvantages of debt capital (in particular, the investor's participation in the asset, the investor's influence over the management of the issuer, and the investor has no other participation and information rights) with the disadvantages of equity capital (the investor's participation in entrepreneurial risk, no obligation to file for bankruptcy of the issuer if there is no possibility of repayment). For the investor, this means that the risk assumed by the investor may in certain respects be even higher than the entrepreneurial risk of a shareholder.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>If a qualified subordination and pre-insolvency bar is recognized, all claims of the investor, in particular claims for repayment and claims for interest or profit participation (&ldquo;subordination claims&rdquo;), cannot be asserted against the relevant issuer if they would give rise to a binding reason for the issuer to file for insolvency proceedings or if such a reason for insolvency already exists (pre-insolvency bar). The corresponding grounds for insolvency under German law are insolvency and over-indebtedness (see below for further details). For issuers domiciled abroad, these are replaced by the applicable grounds for insolvency under the relevant national insolvency law.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>A pre-insolvency bar to enforcement means that claims arising from financial instruments are no longer enforceable if the issuer is insolvent or over-indebted at the time of the payment request or if another ground for insolvency exists under the law applicable to the issuer or is threatened to materialize as a result of the payment. The investor's claims will be permanently barred from enforcement unless and to the extent that the issuer's crisis is resolved. This may result in the investor's claims being permanently unenforceable even outside of insolvency proceedings.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Insolvency occurs if the issuer is unable to fulfill its payment obligations as they fall due (Section 17 (2) of the German Insolvency Act).</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Over-indebtedness exists if the issuer's assets can no longer cover its current liabilities, if it is predominantly unlikely under these circumstances that the issuer will be able to continue its activities (Section 19 (2) of the German Insolvency Act).</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>In the case of foreign issuers, the valid insolvency grounds of the issuer under the relevant national insolvency law are specified in the project documentation.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>These legal provisions may change in the future. This will also change the requirements and conditions under which the pre-insolvency enforcement bar applies.&nbsp;</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Qualified subordination, including the pre-insolvency enforcement bar, may have the following effects: The issuer would have to suspend interest and redemption payments or dividend payments for as long as it is obliged to do so, if the pre-bankruptcy enforcement bar is triggered. The investor will not be able to claim its claims when due.</p>
                                                                        <p>The investor will be obliged to repay, upon demand, an interest or redemption payment or a dividend payment that it has wrongfully received despite qualified loyalty to the issuer.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>There is also the possibility that the investor may not receive, or may not receive in a timely manner, interest payments or redemption payments or a profit participation payment as a result of the subordination. In addition, the investor may have to pay tax on interest or dividends already paid, even if the investor is obliged to repay the amounts received.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>The investor's subordinated claims are also subject to the following claims in the event of liquidation proceedings and bankruptcy of the issuer: Qualified subordination applies to all present and future claims of all non-subordinated creditors of the issuer and - in the case of issuers domiciled in Germany - to all subordinated claims referred to in Section 39(1) of the German Insolvency Act.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Consequently, the investor's claims will only be taken into account after all other creditors of the issuer have been fully and finally satisfied.&nbsp; In the case of foreign issuers, the applicable ranking of subordinated claims in the event of the issuer's bankruptcy and/or liquidation is disclosed in the project-related documentation.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Risks arising from lack of collateralization</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>If the financial instruments are unsecured, the investor will not be able to satisfy, in the event of the issuer's insolvency, any claim for repayment of the invested capital or any claim for payment of interest or distribution of profits arising from the collateral. Particularly in the case of insolvency, this may mean that the claims of individual investors cannot be satisfied or can only be satisfied to a lesser extent. This may result in payments not being made or not being made on time or in partial or total loss of the investment amount.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Risks in case of final redemption</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Depending on the financial instrument, it may be agreed that the issuer must repay all or part of the capital provided at maturity on a specific date (bullet repayment). If the issuer is unable to raise the capital required for redemption from its business activities and/or obtain the necessary follow-up financing by that date, there is a risk that the final redemption cannot be made or cannot be made on schedule. An investment in a bullet repayment financial instrument involves a higher risk than, other things being equal, for example, an annuity or installment repayment financial instrument.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Marketability (fungibility), availability of invested capital, long-term commitment</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Financial instruments brokered on the platform usually have a fixed contractual term. In these cases, early ordinary cancellation by the investor is not foreseen.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Investments are not securities and are not comparable to securities. There is no liquid secondary market for brokered investments. Even if the sale of financial instruments by the investor is in principle legally possible, there will usually be no opportunity to sell financial instruments due to small market sizes and trading volumes. As a result, if the investor wishes to sell, it may not be possible to find a buyer or the sale may only be possible at a lower price than desired. The investment amount may remain tied until the end of the contract term.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol start="3">
                                                                            <li>relevant issuer-level risks</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>In many cases, the financial instruments intermediated here are corporate financings. In these cases, the investor bears the risk of an unfavorable business development of the issuer. There is a risk that the issuer may not have the necessary funds in the future to fulfill the demands of investors and/or to distribute profits. Neither the economic success of the issuer's future business activities nor the success of the projects pursued by the issuer can be predicted with certainty. Issuers can neither guarantee nor assure the amount and timing of inflows.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Issuer default risk (issuer risk)</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Issuers may become insolvent or over-indebted. This may be particularly the case if an issuer has lower revenues and/or higher expenses than expected or is unable to provide the necessary follow-up financing. The insolvency of an issuer can lead to the loss of investors' investments and interest, as issuers are not part of any deposit protection scheme.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Special purpose vehicle</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The Issuer may be a special purpose vehicle that has no other business other than the realization of the planned project (e.g. in the fields of wind turbines or real estate) to absorb potential losses and overcome payment difficulties. Whether and when investors' claims can be satisfied and/or profit distributions can be made depends in these cases to a large extent on the progress and economic success of the relevant project.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Early company stage</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The issuer may also be a company at an early corporate stage that has not yet generated a positive operating cash flow (i.e. the outflow of liquid funds due to business activities initially exceeds the inflow of liquid funds). Financing such young companies is associated with certain risks. If a business idea does not succeed on the market or the planned business expansion does not materialize as hoped, there is a risk of total losses for investors. The success of a company depends on various factors such as the team, specific key people, experts and advisors, market environment, supplier relations, technological developments, property rights, legal framework conditions, competitors and other components. Investors who invest in an early-stage company are much more likely to lose their invested capital than to realize a return on their invested capital.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Risks arising from business activities and the realization of the project pursued by the relevant issuer</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Various risk factors may adversely affect the ability of operating issuers to fulfill their obligations arising from financial instruments. On the one hand, these are risks arising from the realization of the project carried out by the relevant issuer. On the other hand, the general business activities of the relevant issuer may also be associated with risks. These and/or other risks may have a negative impact on the issuer's asset, financial and earnings position. As a result, issuers may not have the necessary funds to fulfill the demands of investors in the future and/or to distribute profits and repay the debt on the financial instrument issued.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Key person risk</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The loss of key personnel of an entrepreneurial issuer entails the risk that specialized knowledge is no longer available and therefore qualified business development and risk management can no longer be guaranteed as before. The loss of such key personnel can have a detrimental impact on the economic development of the issuer concerned.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Forecast risk</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Estimates of the course of the project, the costs of carrying out the project and the revenue that may be generated, among other things, may prove to be inaccurate.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Previous market or business developments are not a basis or indication of future developments.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Legal change risk</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The presentation of the legal consequences of an investment in a financial instrument is based on the state of the law in force at the time of the offer, court decisions and administrative practice applied to date. Changes or reforms in the application of existing legal norms by the competent authorities and courts, as well as future changes in legal norms may have negative consequences for the platform, the issuer and the investor. There is no guarantee that the laws and regulations, judicial and administrative practice in force at the time of the Offer will not change. On the contrary, the investor bears the risk of legal changes.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol start="4">
                                                                            <li>Investor level risks</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Debt financing risk</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Depending on the individual circumstances, the investor may suffer further financial disadvantages in individual cases, for example due to additional tax payments. If the investor finances the purchase of the financial instrument externally, for example by taking out a private loan from a bank, the investor's other assets may be jeopardized in addition to the loss of the invested capital. In this case, the investor's maximum risk is over-indebtedness, which in the worst case can lead to the investor's personal bankruptcy. This can happen if the investor is unable to cover the interest and amortization expenses arising from debt financing due to little or no return on the financial instrument. We therefore strongly advise against borrowing against the financial instruments offered.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Note on risk diversification and avoidance of risk concentration</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>Due to its risk nature, an investment in one of the financial instruments brokered on the platform should be considered as only one component of a diversified (risk-mixed) investment portfolio. As a general rule, the higher the return or gain, the greater the risk of loss. Better risk diversification and avoidance of &ldquo;cluster risks&rdquo; can be achieved by spreading invested capital across various asset classes and financial instruments.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Risk of changes in the legal and tax framework</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>It cannot be ruled out that brokered financial instruments may be affected by future tax, corporate or other legislative changes in a way that requires a corresponding deduction to be applied to payments owed, so that the expected results for the investor may not be achieved (or may no longer be achieved). There is also the risk that the acquisition, sale or redemption of financial instruments may be taxed, resulting in additional costs for the investor. These costs may also have to be borne by the investor in the event of a total loss of the investment amount. The assumption of these costs may lead to the personal bankruptcy of the investor.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol start="5">
                                                                            <li>information from the platform operator</li>
                                                                            <li>Scope of the offer review by the Platform Operator</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The Platform Operator, acting as a contractual agent on behalf of, for the account and under the responsibility of Effecta GmbH (liability umbrella), will only carry out a suitability check prior to the publication of a financial instrument or financing project on the Platform. Publishing on the Platform does not constitute a recommendation for an investment. The Platform Operator does not assess the creditworthiness of the relevant issuer and does not check the accuracy, completeness or timeliness of the information provided by the issuer.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Activity profile of the platform operator</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>The platform operator does not have an advisory function, does not provide advisory services and does not provide any consulting services. In particular, it does not provide financial and/or investment advice or tax and/or legal advice. The platform operator does not provide investors with personal recommendations for the acquisition of financial instruments based on an examination of the personal circumstances of the respective investor. Personal circumstances are inquired into only to the extent required by law in the context of investment brokerage and only for the purpose of providing information required by law, but not for the purpose of making a personal recommendation to an investor to purchase a particular financial instrument.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Information content of documents</li>
                                                                        </ol>
                                                                        <p>&nbsp;</p>
                                                                        <p>In the case of offerings for which a formal prospectus has not been prepared (prospectus-exempt offerings), the project description and other documents of an offered financial instrument on the platform do not purport to contain all the information necessary to evaluate an offered financial instrument. Investors should take the opportunity to ask questions of issuers on the platform before making an investment decision. Investors should also seek information from independent sources or seek expert advice if they are unsure whether they should subscribe to the financial instruments offered. As each investor may be pursuing personal objectives with their investments, the information provided and assumptions made by the issuer should be carefully scrutinized in light of individual circumstances.</p>
                                                                        <p>&nbsp;</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </main>
                                                        <footer class="modal__footer text-center">
                                                        </footer>
                                                    </div>
                                                </div>
                                                <div v-if="isModalTermsConditions" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
                                                    <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;">
                                                        <header class="modal__header" style="height: 20px;">
                                                            <h2 class="modal__title" id="modal-1-title"></h2>
                                                            <button @click="functionCloseTermsConditions()" class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                                                        </header>
                                                        <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                                                            <div class="popup-info-item">
                                                                <div class="d-flex align-items-center mb-5">
                                                                    <div class="icon">
                                                                        <span class="icon-background"></span>
                                                                        <i class="fas fa-exclamation-triangle"></i>
                                                                    </div>
                                                                    <div class="content">
                                                                        <h4>Terms and Conditions</h4>
                                                                    </div>
                                                                </div>
                                                                <div class="content">
                                                                    <div style="border-bottom: 1px solid #212a32;padding: 10px;"></div>
                                                                    <p>Terms of use of https://www.credipto.com/ platform</p>
                                                                    <p>From the following date: November 2023</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>The transparency and user-friendliness of Credipto's https://www.credipto.com/ platform is important to us. These general terms and conditions govern the use of this website (https https://www.credipto.com/).</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>For reasons of better readability, male, female and various (m/f/d) language forms are not used simultaneously. All personal names apply equally to all genders.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Credipto GmbH, Speditionstra&szlig;e 15a, D-40221 D&uuml;sseldorf (hereinafter &ldquo;Credipto&rdquo;) operates an internet platform (hereinafter &ldquo;platform&rdquo;) under the website https://www.credipto.com/. Through this platform, Credipto offers potential investors the opportunity to invest in companies and projects carried out by companies and other institutions.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Here, project owners and companies (hereinafter collectively referred to as &ldquo;issuers&rdquo;) can introduce themselves to potential investors (hereinafter referred to as &ldquo;investors&rdquo;) and digitally share with them information about the planned financing (hereinafter referred to as &ldquo;financing projects&rdquo;).</p>
                                                                    <p>Based on this information, investors have the opportunity to participate in the respective financing project through financing (&ldquo;swarm financing&rdquo;, &ldquo;crowdfunding&rdquo; or &ldquo;funding&rdquo;) by placing investments or securities (&ldquo;financial instruments&rdquo;) on the platform of the online investment brokerage framework.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Access to financing projects and potential subscription to financial instruments requires the investor to register on the platform.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Credipto is a contractual agent within the meaning of Section 3 Paragraph 2 of the WpIG and, when brokering investments pursuant to Section 2 Paragraph 2 No. 3 of the WpIG, is entered into solely in its name, on its behalf and under its responsibility. The registered office of Effecta GmbH, which is listed in the commercial register of the Friedberg district court under HRB 8830, is located at Am Sportplatz 13, D-61197 Florstadt (liability umbrella). Effacta GmbH is a licensed securities institution with a license to provide investment advice pursuant to Section 2 Paragraph 2 No. 4 WpIG and a license to provide investment brokerage pursuant to Section 2 Paragraph 2 No. 3 WpIG, each of which is not authorized to take ownership and/or possession. Sale of securities or raising funds from clients.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>The following general terms and conditions (&ldquo;Terms and Conditions&rdquo;) apply exclusively to any use of the platform by (potential) investors (hereinafter referred to as &ldquo;users&rdquo;).</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- Scope</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- By using the platform, a user agreement is concluded between users and Credipto, which is exclusively subject to the following terms and conditions. This platform user agreement sets out the conditions for the use of the platform for information purposes and for the brokerage of individual financial instruments between investors and issuers via the platform. Both the informational use and the brokerage of financial instruments are offered to users free of charge. The General Terms and Conditions apply to all content, functionalities and other services offered on the platform.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- At the same time, a separate brokerage agreement on financial instruments (&ldquo;brokerage agreement&rdquo;) is concluded between the investor and the liability umbrella (Effecta GmbH), represented by Credipto as a contractual agent, as soon as the platform operator meets the necessary conditions. The interested party offers certain financial instruments from project owners (issuers) on the platform.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>The content of the brokerage agreement is determined by the &ldquo;Effecta GmbH General Terms and Conditions for brokerage through affiliated agents&rdquo; and the additionally applicable &ldquo;Effecta GmbH General Terms and Conditions for brokerage through affiliated agents&rdquo;. These documents are available on the platform and are provided to the investor prior to the contract. Further information about Effecta GmbH and its business profile can be found in the documents also available on the platform.</p>
                                                                    <p>-</p>
                                                                    <p>- The legal relationship between Credipto and the issuer is not the subject of these general terms and conditions. Separate cooperation agreements (project agreements) establish the legal relationship between Credipto and the issuer. Likewise, the legal relationship between the issuer and the user is not the subject of these General Terms and Conditions. These legal relationships are subject to separate legal arrangements (e.g. loan conditions, lending conditions). Credipto is not a party to these legal agreements.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- It has no right to register and use the Platform. Credipto has the right to refuse a user at any time without giving reasons. Examples include: registrations made by providing false data or registrations created with disposable e-mail addresses (so-called &ldquo;disposable e-mail addresses&rdquo;).</p>
                                                                    <p>-</p>
                                                                    <p>- Registration</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- Registration is required for full use of the platform. Natural persons who have reached 18 years of age, have full legal capacity under civil law, reside in Germany and are not citizens of Canada, Japan or Australia and who are not US citizens in the following sense are allowed to register as private persons. A U.S. person is anyone who is a U.S. citizen, a holder of permanent residence and work authorization for the U.S. (green card) or otherwise subject to unlimited income taxation in the U.S., who has a place of residence or a second home in the U.S. or its territories, or who is a U.S. corporation or other entity, estate or trust organized under U.S. law that is subject to U.S. federal taxation, or who acts on behalf of such entity. Legal entities must have their registered office in Germany. Investors must act on their own account. It is not permitted to register a person more than once.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- It is essential that the registration is carried out with complete and correct information regarding the requested data.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- In order to accept an issuer's contract offer as a user, it is also necessary to provide complete and truthful information about the investor-specific data to be requested during or after registration.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- After the registration is completed, the user will receive a confirmation e-mail from Credipto. The registration process is completed by clicking on the link in the e-mail. Once the contract is concluded, the user has the possibility to view and change his/her data at any time in the &ldquo;Login - My Account&rdquo; section.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- Registration with false data or false investor-specific information is not allowed, this will result in the user being removed from the platform.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- The User undertakes to ensure that all information provided while using the platform is always accurate and up to date.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- The User undertakes to ensure that the data accessing the platform, in particular his/her password, are not accessible to third parties. The user is solely responsible for the transactions carried out through the user account. If there is any indication that the user account has been misused</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Cancellation policy</p>
                                                                    <p>Part 1</p>
                                                                    <p>Right of withdrawal</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>With a clear declaration, you can cancel your contract declaration within 14 days without giving reasons. This period begins after the conclusion of the contract and after receipt on a durable medium (e.g. letter, fax, e-mail) of the contractual provisions including the general terms and conditions as well as all information listed in Section 2 below. In order for the revocation period to be met, it is sufficient to send the revocation in good time if the notification is made on a durable medium. The revocation must be sent to the following address:</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Credipto GmbH</p>
                                                                    <p>Speditionstra&szlig;e 15a, D-40221 D&uuml;sseldorf</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>E-mail: <EMAIL></p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Part 2</p>
                                                                    <p>Information required for the start of the cancellation period</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>The information within the meaning of Section 1 Sentence 2 includes the following information:</p>
                                                                    <p>- the identity of the entrepreneur; the public company register in which the legal entity is registered and the relevant registration number or equivalent identifier must also be indicated;</p>
                                                                    <p>- the main business activity of the entrepreneur and the supervisory authority responsible for its approval;</p>
                                                                    <p>- the address of the entrepreneur and other addresses relevant to the business relationship between the entrepreneur and the consumer; the name of the authorized representative in the case of legal entities, associations of persons or groups of persons;</p>
                                                                    <p>- the main characteristics of the financial service and information on how the contract was concluded;</p>
                                                                    <p>- the total price of the financial service, including all relevant price components as well as all taxes paid through the entrepreneur, or, if a precise price cannot be determined, the calculation basis that enables the consumer to verify the price;</p>
                                                                    <p>- an indication of any additional costs that may arise, as well as any possible additional taxes or costs not paid or invoiced by the entrepreneur;</p>
                                                                    <p>- indication that the financial service refers to financial instruments which, due to their specific characteristics or the transactions to be carried out, are subject to special risks or whose price is subject to fluctuations on the financial market over which the entrepreneur has no influence, and that the income obtained in the past is not an indicator of future returns;</p>
                                                                    <p>- a limitation on the period of validity of the information provided; for example, the period of validity of limited offers, in particular in relation to price;</p>
                                                                    <p>- details on payment and performance;</p>
                                                                    <p>- the existence or non-existence of the right of withdrawal, as well as the conditions of its exercise, its details, in particular the name and address of the person to whom the withdrawal is to be declared, and the legal consequences of the withdrawal, including: information on the amount that the consumer must pay for the services rendered in the event of withdrawal; in the event that he/she is obliged to pay compensation for the service that must be paid (basic regulation: Article 357b of the Civil Code);</p>
                                                                    <p>- the minimum duration of the contract, if it involves a permanent or regularly recurring service;</p>
                                                                    <p>- the conditions for termination of the contract, including contractual penalties;</p>
                                                                    <p>- the Member States of the European Union whose laws the entrepreneur relies on when entering into the relationship with the consumer before concluding the contract;</p>
                                                                    <p>- a contractual clause on the law applicable to the contract or the competent court;</p>
                                                                    <p>- the languages in which the contractual conditions and the preliminary information referred to in this cancellation policy are communicated, as well as the languages in which the entrepreneur undertakes, with the consent of the consumer, to communicate during the term of this contract;</p>
                                                                    <p>- an indication as to whether the consumer can use the out-of-court complaints and redress procedure to which the entrepreneur is subject and the access requirements, if any.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Chapter 3</p>
                                                                    <p>Consequences of the annulment</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>In the event of an effective cancellation, the services received by both parties must be returned. If you were aware of this legal consequence before submitting your contractual declaration and expressly agreed that the evaluation can start before the end of the cancellation period, you are obliged to pay compensation for the service provided up to the moment of cancellation. If you are obliged to pay compensation, this may mean that you still have to fulfill the contractual payment obligations for the period until the cancellation. Your right of withdrawal expires prematurely if, before you exercise your right of withdrawal, the contract has been fully performed by both parties at your express request. Obligations regarding the return of payments must be fulfilled within 30 days. For you, this period begins when you send us your notice of withdrawal, for us it begins when we receive it.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>Credipto GmbH</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>End of cancellation policy</p>
                                                                    <p>- Services of the platform operator and use of the platform</p>
                                                                    <p>- Credipto offers users the opportunity to contact issuers via the platform.</p>
                                                                    <p>- Issuers have the possibility to publicize information and documents to potential investors via the platform. Investors can also subscribe to financial instruments online via the platform. In this context, Credipto is limited to providing the technical requirements for organizing the signing of the contract and the sending of documents, the transmission of declarations of intent as a messenger and the provision of certain other services to issuers in connection with the initiation and processing of the purchase of financial instruments (in particular. Contract management and payment monitoring). Credipto does not provide any additional services. In particular, Credipto has no discretion regarding the conclusion of contracts between issuers and investors and/or the management of the funding process. Credipto is not a party to the contract in this respect. Credipto also does not act as an authorized representative of either party in the conclusion of these contracts and does not accept any payment within the framework of the brokered contracts. Credipto does not provide any services that are subject to authorization under the Banking Act, the Capital Investment Act or the Payment Services Supervision Act. As a rule, payments are made through a licensed payment service provider or a so-called payment intermediary.</p>
                                                                    <p>- Credipto offers the user free use of the platform.</p>
                                                                    <p>- Credipto has no advisory function and does not provide any services in this regard. No financing and/or investment advice or tax and/or legal advice is explicitly provided. Credipto only performs a check according to official criteria before publishing an issuer's financing project on the platform. The placement of a financing project on the platform does not constitute investment advice. Credipto does not assess the creditworthiness of the issuer and does not check the information provided by the issuer for accuracy, completeness or timeliness. The platform operator does not provide investors with personal advice on the purchase of financial instruments on the basis of an examination of the personal situation of the respective investor. Unless the user has given separate consent to data collection, the user's personal terms and conditions will only be requested to the extent necessary for the execution of the contractual relationship or - in the context of investment brokerage - where required by law. In the context of investment brokerage, this is done for the purpose of providing legally required information, but not with the intention of giving the investor a personal recommendation to purchase a specific financial instrument.</p>
                                                                    <p>- The documents offered on the platform do not explicitly claim to contain all the information necessary to evaluate a financial instrument offered. They do not constitute a prospectus in the legal sense. Users should take advantage of the possibility to ask questions to the issuer via the platform before making an investment decision. If users are undecided whether to subscribe to the financial instruments offered, they should seek information from independent sources. The documentation provided on the platform cannot replace expert advice. Users are strongly advised to obtain comprehensive information on the legal, economic and tax implications of such an investment before subscribing to the financial instruments offered. When subscribing to qualified subordinated loans, users as lenders bear a higher entrepreneurial risk than the risk of a normal lender. Loan capital, including interest claims, cannot be reclaimed due to qualified subordination if it would cause the borrower to become insolvent. This could lead to a complete loss of the invested capital and interest. Users should note detailed risk information.</p>
                                                                    <p>- The respective subscription amount can be freely chosen within the framework set by the user. For investment, the user may only use his/her own liquid funds, which are exempt from third-party rights.</p>
                                                                    <p>- Comments made by users on the platform or in related blogs, etc., which are deemed to be inappropriate or in violation of applicable laws, are not permitted and will result in immediate deletion. They may also lead to the immediate banning of the user from using the platform. Credipto reserves the right to claim compensation.</p>
                                                                    <p>- The information provided on the Platform is not intended for distribution to or in the United States, Canada, Australia, Japan or any jurisdiction where such offer or invitation to make such an offer is not permitted. Any breach of this sharing restriction may constitute a violation of the laws of such jurisdictions.</p>
                                                                    <p>- Implementation of an investment</p>
                                                                    <p>An investment through the platform works like this:</p>
                                                                    <p>- A potential investor registers as a user on the platform and becomes aware of financing projects. For this purpose, they use the information and documents provided by the relevant issuer. The user will be able to ask questions to the issuer about the financing project via the platform.</p>
                                                                    <p>- To purchase a financial instrument, the investor and the issuer sign a subscription agreement (&ldquo;Subscription Agreement&rdquo;) for the amount chosen by the investor (&ldquo;Deposit Amount&rdquo;) using the digital process provided on the platform. Depending on the process, Credipto transmits the contractual declarations of the contracting parties to the counterparty as a messenger.</p>
                                                                    <p>- The acquisition of the financial instrument becomes effective when the counterparty (investor or issuer) accepts the subscription offer (&ldquo;conclusion of the contract&rdquo;). The issuer will ask the investor to pay the deposit amount. Depending on the financial instrument, the conclusion of the contract on an individual basis may, within the meaning of Section 158 Paragraph 2 of the German Civil Code (BGB), be enforceable, in particular if the deposit obligation is not fulfilled and/or the minimum subscription volume is not reached within a certain period of time.</p>
                                                                    <p>- In the event of effective cancellation of the conclusion of the contract, the issuer will ensure that the amount deposited by the user is transferred back to the deposit account without any deductions or costs; this does not affect any obligation to pay compensation or other rights in the event of cancellation by the user.</p>
                                                                    <p>- Duration and termination</p>
                                                                    <p>- The current user agreement according to these General Terms and Conditions is concluded indefinitely. It may be canceled at any time by the user or the platform operator with one week's notice before the end of the month. This does not affect the right of extraordinary termination.</p>
                                                                    <p>- Cancellations by the user must be sent by e-<NAME_EMAIL>. The user will be informed of the termination by Credipto by e-mail to the e-mail address last stored on the platform. The user is responsible for keeping this address updated at all times.</p>
                                                                    <p>- It must be clearly stated that existing contractual relationships between users and issuers will not be affected by the termination of the user agreement.</p>
                                                                    <p>- Availability</p>
                                                                    <p>Credipto endeavors to provide comprehensive availability of the platform to the extent technically possible and economically reasonable, but does not guarantee this. In particular, maintenance work, security and capacity reasons, technical conditions and events beyond Credipto's control may result in the platform being temporarily or permanently inaccessible. Credipto reserves the right to restrict access to the platform at any time and to the extent necessary, for example to carry out maintenance work.</p>
                                                                    <p>- Documentation</p>
                                                                    <p>The user has no right to transfer or reproduce any documents, information and documents downloaded from the platform. Publicly available information and documents are exempt from this obligation. This obligation applies indefinitely beyond the period of use of the platform and even after termination of this user agreement. In the event that a user breaches this obligation, Credipto reserves the right to claim possible damages. The same applies to the affected rights of the issuers.</p>
                                                                    <p>- Data protection</p>
                                                                    <p>The collection and use of the user's personal data is carried out exclusively within the framework of legal provisions, in particular taking into account the applicable data protection law. Further information in this regard can be found in the separate data protection declaration of the platform operator Credipto at https://www.credipto.com/.</p>
                                                                    <p>- Liability</p>
                                                                    <p>- Credipto's liability for breach of contractual obligations and tort liability is also limited to intent and gross negligence.</p>
                                                                    <p>- In addition, Credipto is only liable for simple negligence in the event of breach of essential contractual obligations, the fulfillment of which alone ensures the proper execution of the contract and on which the user can rely on regular compliance (&ldquo;substantial negligent obligations&rdquo;). A particularly important basic obligation is the receipt and transmission of subscription declarations via the platform. Liability for the main obligations is limited to typical damages and/or typical damage dimensions that were foreseeable at the time the contract was concluded.</p>
                                                                    <p>- The above restrictions also apply to legal representatives, directors, employees or vicarious agents of Credipto.</p>
                                                                    <p>- The above limitations do not apply to liability under the Product Liability Act arising from injury to life, body or health, nor to liability under the Product Liability Act, nor to cases where Credipto provides express warranties.</p>
                                                                    <p>- Credipto is not responsible for the information that issuers provide about themselves on the platform or for the legal validity of contracts concluded between the user and the issuer. It should be made clear that the information that issuers provide about themselves on the platform is based entirely on the statements and documents of the issuers. Therefore, it is understood that the responsibility for this information lies solely with the issuer. This applies in particular to the timeliness, accuracy and completeness of the information. Credipto does not check the information provided.</p>
                                                                    <p>- Availability</p>
                                                                    <p>Credipto endeavors to provide comprehensive availability of the platform to the extent technically possible and economically reasonable, but does not guarantee this. In particular, maintenance work, security and capacity reasons, technical conditions and events beyond Credipto 's control may result in the platform being temporarily or permanently inaccessible. Credipto reserves the right to restrict access to the platform at any time and to the extent necessary, for example to carry out maintenance work.</p>
                                                                    <p>- Documentation</p>
                                                                    <p>The user has no right to transfer or reproduce any documents, information and documents downloaded from the platform. Publicly available information and documents are exempt from this obligation. This obligation applies indefinitely beyond the period of use of the platform and even after termination of this user agreement. In the event that a user breaches this obligation, Credipto reserves the right to claim possible damages. The same applies to the affected rights of the issuers.</p>
                                                                    <p>- Data protection</p>
                                                                    <p>The collection and use of the user's personal data is carried out exclusively within the framework of legal provisions, in particular taking into account the applicable data protection law. Further information in this regard can be found in the separate data protection declaration of the platform operator Credipto at https://www.credipto.com/.</p>
                                                                    <p>- Liability</p>
                                                                    <p>- Credipto 's liability for breach of contractual obligations and tort liability is also limited to intent and gross negligence.</p>
                                                                    <p>- In addition, Credipto is only liable for simple negligence in the event of breach of essential contractual obligations, the fulfillment of which alone ensures the proper execution of the contract and on which the user can rely on regular compliance (&ldquo;substantial negligent obligations&rdquo;). A particularly important basic obligation is the receipt and transmission of subscription declarations via the platform. Liability for the main obligations is limited to typical damages and/or typical damage dimensions that were foreseeable at the time the contract was concluded.</p>
                                                                    <p>- The above restrictions also apply to legal representatives, directors, employees or vicarious agents of Credipto.</p>
                                                                    <p>- The above limitations do not apply to liability under the Product Liability Act arising from injury to life, body or health, nor to liability under the Product Liability Act, nor to cases where Credipto provides express warranties.</p>
                                                                    <p>- Credipto is not responsible for the information that issuers provide about themselves on the platform or for the legal validity of contracts concluded between the user and the issuer. It should be made clear that the information that issuers provide about themselves on the platform is based entirely on the statements and documents of the issuers. Therefore, it is understood that the responsibility for this information lies solely with the issuer. This applies in particular to the timeliness, accuracy and completeness of the information. Credipto does not check the information provided.</p>
                                                                    <p>- The https://www.credipto.com/ website of Credipto contains internet links to external third-party websites. Credipto has no influence directly or indirectly on the content of these linked websites. The respective provider or operator of the website is always responsible for the correctness of the content, Credipto therefore accepts no liability in this regard. Credipto checked external websites (internet links) for possible legal violations at the time of linking. No legal violations were found at the time the link was established. Credipto cannot continuously check the entire content of the pages linked to https://www.credipto.com/ without actual proof of a legal violation. If legal violations become known, Credipto will remove the relevant links immediately.</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- Final provisions</p>
                                                                    <p>&nbsp;</p>
                                                                    <p>- The general terms and conditions may be amended by Credipto if necessary</p>
                                                                    <p>- Credipto is obliged to ensure that the General Terms and Conditions comply with applicable law or governmental requirements;</p>
                                                                    <p>- Credipto complies with a court order or an order of an authority or a request of an authority directed against it or one of its sub-service providers, or to prevent or resolve complaints from an authority</p>
                                                                    <p>- Changes in the legal framework relevant to the contractual relationship between the parties, case law, administrative practice of a responsible supervisory authority and/or changes in other contractual circumstances beyond the control of both parties make it necessary to adapt the contract. General Terms and Conditions (e.g. because the relevant clauses in the General Terms and Conditions are now deemed ineffective due to such changes or because the continued application of the General Terms and Conditions without appropriate adjustments leads to a breach that may be sanctioned by supervisory law)</p>
                                                                    <p>and that the changes made by Credipto will not have unreasonable consequences for the user, unless they are unavoidable in accordance with the aforementioned standard, and no changes will be made to the contractual services that are interrelated.</p>
                                                                    <p>Prior to the intended entry into force of the updated General Terms and Conditions - Credipto will notify the user in text form and inform him/her separately about the new regulations and the planned entry into force date. At the same time, Credipto will give the user a reasonable period of at least two months to declare whether he/she accepts the updated terms and conditions for further use of the services. If no declaration is made within this period starting from the receipt of the message in text form, the changed conditions shall be deemed to have been accepted by implied declaration of intent. At the beginning of the deadline, Credipto will inform the user separately about this legal consequence, i.e. the right to object, the objection period and the legal consequences of silence.</p>
                                                                    <ul>
                                                                        <li>These terms of use are subject to the laws of the Federal Republic of Germany only. The contractual language and the relevant language of communication between the user and Credipto are German.</li>
                                                                    </ul>
                                                                    <p>&nbsp;</p>
                                                                    <ul>
                                                                        <li>For users who are traders or who do not have a general jurisdiction in Germany or another EU member state, the place of jurisdiction for all legal disputes arising from this contractual relationship is the registered office of Credipto. In all other cases, the legal place of jurisdiction applies.</li>
                                                                    </ul>
                                                                    <p>&nbsp;</p>
                                                                    <ul>
                                                                        <li>If individual provisions of these General Terms and Conditions are or become invalid, ineffective or unenforceable, the validity of these General Terms and Conditions shall not be affected. In this case, the parties are obliged to replace the invalid, ineffective or unenforceable provisions by provisions that are legally permissible and come closest to the intended purpose. The same applies in the event of gaps in the legislation.</li>
                                                                    </ul>
                                                                    <p>&nbsp;</p>
                                                                    <ul>
                                                                        <li>The European Commission has set up a European online dispute resolution platform at http://ec.europa.eu/consumers/odr/. The consumer can use this platform to resolve a dispute arising from an online contract with a company established in the EU without going to court. To do this, he/she must fill out the online complaint form, which can be accessed at the given address.</li>
                                                                    </ul>
                                                                    <p>&nbsp;</p>
                                                                    <ul>
                                                                        <li>We would like to point out that the following institution is responsible as the consumer arbitration board: Deutsche Bundesbank Arbitration Board, PO Box 10 06 02, 60006 Frankfurt am Main, www.bundesbank.de/schlichtungsstelle. We are obliged to take part in a dispute resolution procedure before this consumer arbitration board.</li>
                                                                    </ul>
                                                                    <p>&nbsp;</p>

                                                                </div>
                                                            </div>
                                                        </main>
                                                        <footer class="modal__footer text-center">
                                                        </footer>
                                                    </div>
                                                </div>
                                                <div v-if="isModalDataProtect" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
                                                    <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;">
                                                        <header class="modal__header" style="height: 20px;">
                                                            <h2 class="modal__title" id="modal-1-title"></h2>
                                                            <button @click="functionCloseDataProtect()" class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                                                        </header>
                                                        <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                                                            <div class="popup-info-item">
                                                                <div class="d-flex align-items-center mb-5">
                                                                    <div class="icon">
                                                                        <span class="icon-background"></span>
                                                                        <i class="fas fa-exclamation-triangle"></i>
                                                                    </div>
                                                                    <div class="content">
                                                                        <h4>Data protection</h4>
                                                                    </div>
                                                                </div>
                                                                <div class="content">
                                                                    <div style="border-bottom: 1px solid #212a32;padding: 10px;">
                                                                        <p>We are very pleased that you have shown interest in our business.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>Data protection is of a particularly high priority for the management of FeWo Finanz GmbH. It is usually possible to use the FeWo Finanz GmbH website without providing any personal data. However, if a data subject wishes to make use of special services of our company via our website, processing of personal data may be necessary. If the processing of personal data is necessary and there is no legal basis for such processing, we usually obtain the consent of the data subject.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>The processing of personal data, such as the name, address, e-mail address, or telephone number of a data subject shall always be in line with the General Data Protection Regulation (GDPR), and in accordance with the country-specific data protection regulations applicable to FeWo Finanz GmbH. By means of this data protection declaration, our company would like to inform the public about the type, scope and purpose of the personal data we collect, use and process. Furthermore, data subjects are informed of their rights through this data protection declaration.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>As the controller, FeWo Finanz GmbH has implemented numerous technical and organizational measures to ensure the most complete protection of personal data processed through this website. However, Internet-based data transmissions can often have security gaps, so absolute protection cannot be guaranteed. Therefore, every data subject is free to transmit his or her personal data to us by alternative means, for example by telephone.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>Definitions</li>
                                                                        </ol>
                                                                        <p>The data protection declaration of FeWo Finanz GmbH is based on the terms used by the European legislator for the adoption of the General Data Protection Regulation (GDPR). Our privacy policy should be easy to read and understand for our customers and business partners as well as for the general public. To ensure this, we would like to explain the terminology used in advance.</p>
                                                                        <p>&nbsp;</p>
                                                                        <p>In this privacy policy we use the following terms, among others</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>a) personal data</li>
                                                                        </ol>
                                                                        <p>Personal data is any information relating to an identified or identifiable natural person (hereinafter referred to as the &ldquo;data subject&rdquo;). An identifiable natural person is one who can be identified, directly or indirectly, in particular by reference to an identifier such as a name, an identification number, location data, an online identifier or to one or more factors specific to the physical, physiological, genetic, mental, economic, cultural or social identity of that natural person.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>b) Data subject</li>
                                                                        </ol>
                                                                        <p>The data subject is an identified or identifiable natural person whose personal data is processed by the controller responsible for processing.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>c) Processing</li>
                                                                        </ol>
                                                                        <p>Processing means any operation or set of operations which is performed on personal data or sets of personal data, whether or not by automated means, such as collection, recording, organization, structuring, storage, adaptation or alteration, retrieval, consultation, use, disclosure by transmission, dissemination or otherwise making available, alignment or combination, restriction, erasure or destruction.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>d) Restriction of processing</li>
                                                                        </ol>
                                                                        <p>Restriction of processing is the marking of stored personal data in order to restrict its processing in the future.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>e) Profile Creation</li>
                                                                        </ol>
                                                                        <p>Profiling means any automated processing of personal data consisting in the use of personal data to evaluate certain personal aspects relating to a natural person, in particular to analyze or predict aspects relating to that natural person's performance at work, economic situation, health, personal preferences, interests, reliability, behavior, location or movements.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>f) Nicknaming</li>
                                                                        </ol>
                                                                        <p>Pseudonymization is the processing of personal data in such a way that the personal data can no longer be associated with a specific data subject without the use of additional information, provided that such additional information is kept separate and subject to technical and organizational measures to ensure that the personal data are not attributed to an identified or identifiable natural person.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>g) Controller or supervisor responsible for processing</li>
                                                                        </ol>
                                                                        <p>The controller or controller responsible for processing is the natural or legal person, public authority, agency or other body which, alone or jointly with others, determines the purposes and means of the processing of personal data. Where the purposes and means of such processing are determined by Union or Member State law, specific criteria for the appointment of the controller or controller may be provided for by Union or Member State law.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>h) Processor</li>
                                                                        </ol>
                                                                        <p>Data processor is a natural or legal person, public authority, institution or other body that processes personal data on behalf of the data controller.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>i) Buyer</li>
                                                                        </ol>
                                                                        <p>The recipient is the natural or legal person, public authority, agency or other body to which the personal data are disclosed, whether or not a third party. However, public authorities which may receive personal data in the framework of a specific investigation under Union or Member State law shall not be considered recipients.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>j) Third party</li>
                                                                        </ol>
                                                                        <p>Third party is a natural or legal person, public authority, institution or body other than the data subject, controller, processor and persons authorized to process personal data under the direct authority of the controller or processor.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol>
                                                                            <li>k) Reza</li>
                                                                        </ol>
                                                                        <p>Consent is a freely given, specific, informed and unambiguous indication of wishes by which the data subject, by a declaration or by a clear affirmative action, indicates that he or she agrees to the processing of personal data concerning him or her.</p>
                                                                        <p>&nbsp;</p>
                                                                        <ol start="2">
                                                                            <li>name and address of the controller responsible for processing</li>
                                                                        </ol>
                                                                        <p>Controller within the meaning of the General Data Protection Regulation, other data protection laws applicable in the Member States of the European Union and other provisions of a data protection nature</p>
                                                                        <p>&nbsp;</p>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </main>
                                                        <footer class="modal__footer text-center">
                                                        </footer>
                                                    </div>
                                                </div>

                                                <div v-if="isModalStatus" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
                                                    <div class="modal__container cart-item" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;max-width:550px;">
                                                        <header class="modal__header" style="height: 20px;">
                                                            <h2 class="modal__title" id="modal-1-title">
                                                            </h2>
                                                        </header>
                                                        <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                                                            <div class="popup-info-item">
                                                                <div class="d-flex align-items-center mb-5">
                                                                    <div class="content" style="width: 100%;text-align: center;color: black;">
                                                                        <h6>{!! __('velocity::app-static.payment.summary.check-order-question') !!}</h6>
                                                                    </div>
                                                                </div>
                                                                <div class="content flex justify-center space-x-4">
                                                                    <button class="rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]" @click="placeOrder">{{ __('velocity::app-static.payment.summary.check-order') }}</button>
                                                                    <button class="rounded-full py-3 px-8 text-center text-jacarta cursor-pointer group rounded-2.5xl border border-jacarta-200 bg-white transition-shadow dark:border-transparent dark:bg-jacarta-700" @click="getCloseModal">{{ __('velocity::app-static.payment.summary.cancel') }}</button>
                                                                </div>
                                                            </div>
                                                        </main>
                                                        <footer class="modal__footer text-center">
                                                        </footer>
                                                    </div>
                                                </div>

                                                <div v-if="!isLoading" class="row justify-content-center">
                                                    <button
                                                        type="button"
                                                        v-if="placeholderStatus"
                                                        @click="functionPlaceholderStatus"
                                                        class="rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]"
                                                    >
                                                        {{ __('velocity::app-static.sell-gold.authorization.continue') }}
                                                    </button>
                                                    <button
                                                        v-if="!placeholderStatus"
                                                        class="@if(session()->has('error')) disabled-btn @endif rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]"
                                                        @click="checkPlaceOrder"
                                                        type="button"
                                                    >
                                                        {{ __('velocity::app-static.sell-gold.authorization.pay-now') }}
                                                    </button>

                                                    @if($txCryptoPayment['hasAvailableBalance'] === false)
                                                        <a class="font-terramirum bg-terra-soft-khaki-green text-white rounded-2xl py-2 px-10 self-center text-xl tracking ml-4" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                                           href="#">
                                                            {{ __('velocity::app-static.sell-gold.authorization.add-balance') }}
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>

                <div class="w-full md:w-5/12">
                    <div class="rounded-2.5xl border border-[#D0AA49] bg-white p-8 dark:border-jacarta-600 dark:bg-jacarta-700 shadow-md">
                        <h6 class="mb-6 font-display text-3xl text-jacarta-700 dark:text-white text-center border-b-2 border-[#D0AA49] pb-4">
                            {{ __('velocity::app-static.sell-gold.authorization.my-cart') }}
                        </h6>
                        <div class="flex flex-col items-center space-y-6">
                            <picture>
                                <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                                <img src="/miligold/img/mili-ikon.png" alt="miliGRAM Coin" class="w-24 h-24">
                            </picture>
                            <div class="flex flex-col items-center space-y-4 w-full">
                                <div class="flex items-center w-full max-w-md border border-gray-100 bg-white dark:bg-jacarta-800 p-4 rounded-lg">
                                <span class="text-lg text-jacarta-700 dark:text-white font-bold">
                                    {!! __('velocity::app-gold.profile.buy') !!}
                                </span>
                                    <span class="ml-auto text-lg font-bold text-jacarta-700 dark:text-white" v-text="cartTotal"></span>
                                    <span class="ml-2 text-lg text-jacarta-700 dark:text-white" v-text="cartCurrency"></span>
                                </div>
                                <div class="flex justify-between items-center w-full max-w-md border border-gray-100 bg-white dark:bg-jacarta-800 p-4 rounded-lg">
                                <span class="text-lg text-jacarta-700 dark:text-white font-bold">
                                    {!! __('velocity::app-gold.profile.sell') !!}
                                </span>
                                    <div class="flex items-center w-auto border border-gray-300 rounded-full overflow-hidden bg-white dark:bg-jacarta-700 px-2 py-1">
                                        <button
                                            disabled
                                            type="button"
                                            class="w-8 h-8 flex items-center justify-center text-white bg-gray-300 rounded-full transition hover:opacity-80 cursor-not-allowed">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
                                            </svg>
                                        </button>
                                        <div class="flex items-center">
                                            <input type="number" id="quantity" class="w-12 px-2 text-center text-lg font-semibold text-jacarta-700 dark:text-white bg-transparent border-none outline-none"
                                                   v-model="cartQuantity"
                                                   value="{{ $cartQuantity }}" min="1" max="{{ $cartExchangeLimit }}" readonly>
                                            <span class="pr-4">{{ $cart->items->first()->product->name }}</span>
                                        </div>
                                        <button
                                            disabled
                                            type="button"
                                            class="w-8 h-8 flex items-center justify-center text-white bg-gray-300 rounded-full transition hover:opacity-80 cursor-not-allowed">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex flex-col items-center space-y-2">
                                    <span class="text-sm text-jacarta-500 dark:text-jacarta-300">
                                        {!! __('velocity::app-static.sell-gold.authorization.exchange-note', ['url' => route('sellgold.payment.summary')]) !!}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </script>
    <script>
        Vue.component('crypto_authorization', {
            template: '#crypto_authorization_template',
            data: function() {
                return {
                    chains: [],
                    selectedChain: '{{ $txCryptoPayment['chainId'] }}',
                    selectedChainValue: '',
                    coins: [],
                    selectedCoin: '{{ $txCryptoPayment['paymentCurrency'] }}',
                    balance: '{{ $txCryptoPayment['walletBalance'] }}',
                    transaction: {
                        requiredAmount: '{{ $txCryptoPayment['requiredAmount'] }}',
                        paymentCurrency: '{{ $txCryptoPayment['paymentCurrency'] }}',
                        currency: '{{ $txCryptoPayment['currency'] }}',
                        gasCoin: '{{ $txCryptoPayment['gasCoin'] }}',
                        chainId: '{{ $txCryptoPayment['chainId'] }}',
                        minRequiredGasFee: '{{ $txCryptoPayment['minRequiredGasFee'] }}',
                        totalRequiredAmount: '{{ $txCryptoPayment['totalRequiredAmount'] }}',
                        hasAvailableBalance: '{{ $txCryptoPayment['hasAvailableBalance'] }}',
                        walletBalance: '{{ $txCryptoPayment['walletBalance'] }}',
                        pendingBalance: '{{ $txCryptoPayment['pendingBalance'] }}',
                        message: '{{ $txCryptoPayment['message'] }}',
                    },
                    acceptRules1: false,
                    acceptRules2: false,
                    acceptRules3: false,
                    acceptRules4: false,
                    isLoading: false,
                    responseMessage: '',
                    stepMessage: '',
                    timeoutId: null,
                    retryCount: 1,
                    maxRetries: 2,
                    retryInterval: 15 * 1000,
                    transactionStatus: 'pending',
                    transactionTxId: '{{ $txCryptoPayment['default']['txId'] }}',
                    transactionMessage: '',
                    isModalRiskClauses: false,
                    isModalTermsConditions: false,
                    isModalDataProtect: false,
                    placeholderStatus: true,
                    isModalStatus: false,
                    cartCurrency: '{{ $cart->cart_currency_code }}',
                    cartQuantity: {{ $cartQuantity }},
                    cartItemPrice: {{ $cartItemPrice }},
                    cartTotal: '{{ $cartTotal }}',
                    cartExchangeLimit: {{ $cartExchangeLimit }},
                }
            },
            mounted() {
                try {
                    this.getChains();
                    console.log(this.transaction);
                } catch (error) {
                    console.error('Error in mounted hook:', error.message);
                }
            },
            methods: {
                getOpenModal() {
                    this.isModalStatus = true;
                },
                getCloseModal() {
                    this.isModalStatus = false;
                },

                checkPlaceOrder: function () {
                    this.getOpenModal();
                },

                placeOrder: function () {
                    this.getCloseModal();

                    if (!this.acceptRules1 && !this.acceptRules2 && !this.acceptRules3) {
                        alert('Please accept the risk clauses');
                        return;
                    }
                    this.isLoading = true;
                    document.querySelector('form#payment-authorization').submit();
                },
                getChains() {
                    axios.get('{{ route('crypto.chains.list') }}')
                        .then(response => {
                            this.chains = response.data;
                            this.selectedChainValue = this.chains.find(chain => chain.chainId == this.selectedChain).chainName;
                        })
                        .catch(error => {
                            console.error('Error fetching chains', error);
                        });
                },
                getCoins() {
                    axios.get(`{{ route('crypto.chains.coins') }}?chainid=${this.selectedChain}`)
                        .then(response => {
                            this.coins = response.data;
                        })
                        .catch(error => {
                            console.error('Error fetching coins', error);
                        });
                },
                onSubmit(retryCount = 0) {
                    if (!this.acceptRules1 && !this.acceptRules2 && !this.acceptRules3 && !this.acceptRules4) {
                        alert('Please accept the risk clauses');
                        return;
                    }
                    this.isLoading = true;
                    document.querySelector('form#payment-authorization').submit();
                    {{--this.isLoading = true;--}}
                    {{--this.stepMessage = 'Please wait submitting transaction...';--}}
                    {{--this.responseMessage = '';--}}
                    {{--this.transactionStatus = '';--}}

                    {{--// STEP 2: Launchpad--}}
                    {{--axios.post('{{ route('crypto.payment.withdrawal') }}', {--}}
                    {{--    txCryptoPayment: '{{ base64_encode(json_encode($txCryptoPayment)) }}',--}}
                    {{--})--}}
                    {{--    .then(response => {--}}
                    {{--        if (response.data.isSuccessful) {--}}
                    {{--            this.responseMessage = '';--}}
                    {{--            this.transactionStatus = 'pending';--}}
                    {{--            this.transactionTxId = response.data.txId;--}}
                    {{--            this.stepMessage = 'Transaction is successful. Please wait...';--}}

                    {{--            console.warn('STEP 2: Transaction submitted successfully. Request:', JSON.parse(response.config.data));--}}
                    {{--            console.warn('STEP 2: Transaction submitted successfully. Response:', response.data);--}}

                    {{--            console.warn('STEP 2: Step 2 completed successfully. Proceeding to step 3...');--}}

                    {{--            clearTimeout(this.timeoutId);--}}
                    {{--            let timeOut = setTimeout(() => {--}}
                    {{--                this.checkWithdrawal(response.data);--}}
                    {{--            }, this.retryInterval);--}}
                    {{--            this.timeoutId = timeOut;--}}
                    {{--        } else {--}}
                    {{--            this.transactionStatus = 'failed';--}}
                    {{--            this.responseMessage = response.data.message || response.data;--}}
                    {{--            this.stepMessage = 'Transaction failed. Please wait. Retrying transaction...';--}}

                    {{--            console.error('STEP 2: Error submitting transaction. Request:', JSON.parse(response.config.data));--}}
                    {{--            console.error('STEP 2: Error submitting transaction. Response:', response.data);--}}

                    {{--            console.warn(`Retry count: ${this.retryCount}, Max retries: ${this.maxRetries}`);--}}

                    {{--            if (this.retryCount < this.maxRetries) {--}}
                    {{--                this.retryCount++;--}}
                    {{--                let timeOut = setTimeout(() => {--}}
                    {{--                    console.warn('STEP 2: Running step 2 retry count:', this.retryCount);--}}

                    {{--                    this.onSubmit(this.retryCount);--}}
                    {{--                }, this.retryInterval);--}}
                    {{--                this.timeoutId = timeOut;--}}
                    {{--            } else {--}}
                    {{--                this.responseMessage = 'Maximum retries reached for submitting transaction. Please try again later.';--}}
                    {{--                this.isLoading = false;--}}

                    {{--                clearTimeout(this.timeoutId);--}}

                    {{--                console.warn('STEP 2: Maximum retries reached for submitting transaction. Please try again later.');--}}
                    {{--            }--}}
                    {{--        }--}}
                    {{--    })--}}
                    {{--    .catch(error => {--}}
                    {{--        this.isLoading = false;--}}
                    {{--        console.error('STEP 2: Error submitting transaction', error);--}}
                    {{--    });--}}
                },
                checkWithdrawal(responseCheck) {
                    this.isLoading = true;
                    this.stepMessage = 'Please wait checking withdrawal...';
                    this.responseMessage = '';
                    this.transactionStatus = 'processing';

                    console.warn('STEP 3: Checking withdrawal. Request:', responseCheck);

                    // STEP 3: Launchpad
                    axios.post('{{ route('crypto.payment.check-withdrawal') }}', {
                        isSuccessful: responseCheck ? responseCheck.isSuccessful : false,
                        txId: responseCheck ? responseCheck.txId : '',
                        errorMessage: responseCheck ? responseCheck.errorMessage : '',
                        txnDate: responseCheck ? responseCheck.txnDate : '',
                        userId:  responseCheck ? responseCheck.userId : '',
                        tenantId: responseCheck ? responseCheck.tenantId : '',
                    })
                        .then(response => {
                            if (response.data.isSuccessful) {
                                this.responseMessage = '';
                                this.transactionStatus = 'processing';
                                this.transactionTxId = response.data.txId;
                                this.stepMessage = 'Transaction is successful. Please wait...';

                                console.warn('STEP 3: Transaction checked successfully. Request:', JSON.parse(response.config.data));
                                console.warn('STEP 3: Transaction checked successfully. Response:', response.data);

                                console.warn('STEP 3: Step 3 completed successfully. Proceeding to step 4...');

                                clearTimeout(this.timeoutId);

                                setTimeout(() => {
                                    document.querySelector('form#payment-authorization').submit();
                                }, 2500);
                            } else {
                                this.transactionStatus = 'pending';
                                this.responseMessage = response.data.errorMessage || response.data;
                                this.stepMessage = 'Transaction check failed. Please wait. Retrying check...';

                                console.error('STEP 3: Error checking withdrawal. Request:', JSON.parse(response.config.data));
                                console.error('STEP 3: Error checking withdrawal. Response:', response.data);

                                console.warn(`Retry count: ${this.retryCount}, Max retries: ${this.maxRetries}`);

                                if (this.retryCount < this.maxRetries) {
                                    this.retryCount++;
                                    let timeOut = setTimeout(() => {
                                        console.warn('STEP 3: Running step 3 retry count:', this.retryCount);

                                        this.checkWithdrawal(response.data);
                                    }, this.retryInterval);
                                    this.timeoutId = timeOut;
                                } else {
                                    this.transactionStatus = 'pending';
                                    this.responseMessage = 'Maximum retries reached for submitting transaction. Please try again later.';
                                    this.isLoading = false;

                                    clearTimeout(this.timeoutId);

                                    setTimeout(() => {
                                        document.querySelector('form#payment-authorization').submit();
                                    }, 2500);

                                    console.warn('STEP 3: Maximum retries reached for checking withdrawal. Please try again later.');
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error checking withdrawal', error);
                        });
                },
                updateAcceptRules1() {
                    this.acceptRules1 = !!this.acceptRules1;
                },
                updateAcceptRules2() {
                    this.acceptRules2 = !!this.acceptRules2;
                },
                updateAcceptRules3() {
                    this.acceptRules3 = !!this.acceptRules3;
                },
                updateAcceptRules4() {
                    this.acceptRules4 = !!this.acceptRules4;
                },
                functionShowRiskClauses() {
                    this.isModalRiskClauses = true;
                },
                functionCloseRiskClauses() {
                    this.isModalRiskClauses = false;
                },
                functionShowTermsConditions() {
                    this.isModalTermsConditions = true;
                },
                functionCloseTermsConditions() {
                    this.isModalTermsConditions = false;
                },
                functionShowDataProtect() {
                    this.isModalDataProtect = true;
                },
                functionCloseDataProtect() {
                    this.isModalDataProtect = false;
                },
                functionPlaceholderStatus: function() {
                    this.placeholderStatus = !this.placeholderStatus;
                }
            }
        });
    </script>
@endpush
