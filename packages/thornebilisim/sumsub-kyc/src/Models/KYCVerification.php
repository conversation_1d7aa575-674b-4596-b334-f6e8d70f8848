<?php

namespace Thorne\SumsubKyc\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Webkul\Customer\Models\Customer;

class KYCVerification extends Model
{
    use HasFactory;

    protected $table = 'k_y_c_verifications';

    protected $fillable = [
        'external_user_id',
        'applicant_id',
        'action',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function getReviewStatusAttribute(): string
    {
        return $this?->data['reviewStatus'] ?? 'undefined';
    }
}
