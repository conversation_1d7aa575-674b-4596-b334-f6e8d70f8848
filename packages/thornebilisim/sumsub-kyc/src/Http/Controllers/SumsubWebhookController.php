<?php

namespace Thorne\SumsubKyc\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Thorne\SumsubKyc\Services\SumsubWebhookService;

class SumsubWebhookController extends Controller
{
    protected $webhookService;

    public function __construct(SumsubWebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    public function handle(Request $request)
    {
        if (! $this->webhookService->validateSignature($request)) {
            Log::channel('sumsub-webhook')->warning('Sumsub webhook signature validation failed:', $request->headers->all());

            return response('Invalid signature', 403);
        }

        try {
            $payload        = $request->all();
            $externalUserId = data_get($payload, 'externalUserId');

            Log::channel('sumsub-webhook')->info("Sumsub webhook processed started for externalUserId: $externalUserId", $payload);

            $this->webhookService->processWebhook($payload);

            return response('Webhook processed', 200);
        } catch (\Exception $e) {
            return response('Error processing webhook', 500);
        }
    }
}
