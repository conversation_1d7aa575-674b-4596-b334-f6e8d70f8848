{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "cross-env npm run watch -- --watch-poll --progress", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.21.0", "cross-env": "^6.0.3", "jquery": "^3.4.1", "laravel-mix": "^5.0.0", "laravel-mix-merge-manifest": "^0.1.2", "sass": "^1.24.4", "sass-loader": "^8.0.0", "vue": "^2.6.11", "vue-template-compiler": "^2.6.11"}, "dependencies": {"@inotom/vue-go-top": "^1.3.0", "accounting": "^0.4.1", "ez-plus": "^1.2.1", "lazysizes": "^5.2.2", "vee-validate": "^2.2.15", "vue-flatpickr": "^2.3.0", "vue-slider-component": "^3.1.0"}}