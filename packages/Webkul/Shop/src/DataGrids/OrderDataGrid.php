<?php

namespace Webkul\Shop\DataGrids;

use Illuminate\Support\Facades\DB;
use Webkul\Ui\DataGrid\DataGrid;

class OrderDataGrid extends DataGrid
{
    /**
     * Index.
     *
     * @var string
     */
    protected $index = 'id';

    /**
     * Sort order.
     *
     * @var string
     */
    protected $sortOrder = 'desc';

    /**
     * Prepare query builder.
     *
     * @return void
     */
    public function prepareQueryBuilder()
    {
        $queryBuilder = DB::table('orders as order')
            ->addSelect(
                'order.id',
                'order.order_id',
                'order.status',
                'order.created_at',
                'order.grand_total',
                'order.total_qty_ordered',
                'order.order_currency_code',
                'order_payment.method',
            )
            ->leftJoin('order_payment', 'order.id', '=', 'order_payment.order_id')
            ->where('customer_id', auth()->guard('customer')->user()->id);

        $this->setQueryBuilder($queryBuilder);
    }

    /**
     * Add columns.
     *
     * @return void
     */
    public function addColumns()
    {
        $this->addColumn([
            'index'      => 'order_id',
            'label'      => trans('shop::app.customer.account.order.index.order_id'),
            'type'       => 'string',
            'searchable' => false,
            'sortable'   => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index'      => 'created_at',
            'label'      => trans('shop::app.customer.account.order.view.order-date'),
            'type'       => 'datetime',
            'searchable' => true,
            'sortable'   => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index'      => 'grand_total',
            'label'      => trans('shop::app.customer.account.order.index.total'),
            'type'       => 'number',
            'searchable' => true,
            'sortable'   => true,
            'filterable' => true,
            'closure'    => function ($value) {
                return core()->formatPrice($value->grand_total, $value->order_currency_code);
            },
        ]);
        $this->addColumn([
            'index'      => 'total_qty_ordered',
            'label'      => trans('shop::app.customer.account.order.index.total').' Ordered Quantity',
            'type'       => 'number',
            'searchable' => true,
            'sortable'   => true,
            'filterable' => true,

        ]);
        $this->addColumn([
            'index'      => 'status',
            'label'      => trans('shop::app.customer.account.order.index.status'),
            'type'       => 'checkbox',
            'options'    => [
                'processing'      => trans('shop::app.customer.account.order.index.processing'),
                'completed'       => trans('shop::app.customer.account.order.index.completed'),
                'canceled'        => trans('shop::app.customer.account.order.index.canceled'),
                'closed'          => trans('shop::app.customer.account.order.index.closed'),
                'pending'         => trans('shop::app.customer.account.order.index.pending'),
                'pending_payment' => trans('shop::app.customer.account.order.index.pending-payment'),
                'fraud'           => trans('shop::app.customer.account.order.index.fraud'),
            ],
            'searchable' => false,
            'sortable'   => true,
            'closure'    => function ($value) {
                if ($value->status == 'processing') {
                    return [
                        'text' => 'processing',
                        'html' => '<span class="status processing">'.trans('shop::app.customer.account.order.index.processing').'</span>',
                    ];
                } elseif ($value->status == 'completed') {
                    return [
                        'text' => 'completed',
                        'html' => '<span class="status completed">'.trans('shop::app.customer.account.order.index.completed').'</span>',
                    ];
                } elseif ($value->status == 'canceled') {
                    return [
                        'text' => 'canceled',
                        'html' => '<span class="status failed">'.trans('shop::app.customer.account.order.index.canceled').'</span>',
                    ];
                } elseif ($value->status == 'closed') {
                    return [
                        'text' => 'closed',
                        'html' => '<span class="badge badge-md badge-info">'.trans('shop::app.customer.account.order.index.closed').'</span>',
                    ];
                } elseif ($value->status == 'pending') {
                    return [
                        'text' => 'pending',
                        'html' => '<span class="status pending">'.trans('shop::app.customer.account.order.index.pending').'</span>',
                    ];
                } elseif ($value->status == 'pending_payment') {
                    return [
                        'text' => 'pending_payment',
                        'html' => '<span class="badge badge-md badge-warning">'.trans('shop::app.customer.account.order.index.pending-payment').'</span>',
                    ];
                } elseif ($value->status == 'fraud') {
                    return [
                        'text' => 'fraud',
                        'html' => '<span class="badge badge-md badge-danger">'.trans('shop::app.customer.account.order.index.fraud').'</span>',
                    ];
                }
            },
            'filterable' => true,
        ]);
    }

    /**
     * Prepare actions.
     *
     * @return void
     */
    public function prepareActions()
    {
        $this->addAction([
            'title'  => trans('ui::app.datagrid.view'),
            'type'   => 'View',
            'method' => 'GET',
            'route'  => 'customer.orders.view',
            'icon'   => 'icon eye-icon',
        ], true);
    }
}
