@extends('shop::layouts.miligold-default')

@section('page_title')
    {{ __('shop::app.checkout.success.title') }}
@stop

@section('body')
    <section class="relative bg-contain bg-right-top bg-no-repeat pt-40 pb-0">
        <div class="container">
            <h1 class="text-center font-display text-3xl md:text-5xl lg:text-6xl font-medium text-jacarta-700">
            {{ __('shop::app.checkout.success.title') }}
            </h1>
        </div>
    </section>

    <section class="pb-12 dark:bg-jacarta-800 relative">
        <div class="relative z-10 my-20 dark:bg-jacarta-900">
            <div class="container">
                <div class="relative overflow-hidden rounded-2.5xl px-16 py-8 shadow-md lg:px-24">
                    <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" alt="gradient" class="h-full w-full">
                    </picture>
                    <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" alt="gradient dark" class="h-full w-full">
                    </picture>
                    <div class="items-center justify-between md:flex">
                        <div class="mb-6 md:mb-0">
                            <div class="flex flex-wrap w-full mb-8">
                                <div class="md:w-2/3">
                                    <h2 class="mb-4 font-display text-2xl text-jacarta-700 dark:text-white sm:text-3xl text-center">
                                        {{ __('velocity::app-static.orders.success') }}
                                    </h2>
                                    <p class="mb-8 text-lg dark:text-jacarta-300">
                                        {!! __('velocity::app-static.orders.thanks') !!}
                                    </p>
                                </div>
                                <div class="hidden md:block w-1/3 px-3">
                                    <picture>
    {{--                                    <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/iletisim-new.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_iletisim.png" type="image/png">
                                        <img src="/miligold/img/mili_iletisim.png" class="max-w-[350px]" alt="">
                                    </picture>
                                </div>
                            </div>

                            <div class="grid gap-7 md:grid-cols-2 mb-8 ">
                                <div class="rounded-2.5xl bg-white p-4 text-center transition-shadow hover:shadow-xl dark:bg-jacarta-800">
                                    <p class="mx-auto font-display text-lg text-jacarta-700 dark:text-white">
                                        {{ __('velocity::app-static.orders.order-no') }} : #{{ $order->order_id }}
                                    </p>
                                </div>
                                <div class="rounded-2.5xl bg-white p-4 text-center transition-shadow hover:shadow-xl dark:bg-jacarta-800">
                                    <p class="mx-auto font-display text-lg text-jacarta-700 dark:text-white">
                                        {{ __('velocity::app-static.orders.amount') }} : {{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}
                                    </p>
                                </div>
                            </div>
                            <div class="rounded-2.5xl bg-white p-4 text-center transition-shadow hover:shadow-xl dark:bg-jacarta-800">
                                @if ($order->payment->method == "moneytransfer")
                                    <p class="mx-auto mb-4 font-display text-xl text-jacarta-700 dark:text-white">
                                        {{ __('velocity::app-static.orders.bank-info') }}
                                    </p>
                                    <bank-info-navigation>
                                    </bank-info-navigation>
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script type="text/x-template" id="bank-info-navigation-template">
        <div v-if="banks.length > 0">
            <div v-for="(bank, index) in banks" :key="index">
                <bank-info-navigation-item :bank="bank"></bank-info-navigation-item>
            </div>
        </div>
        <div v-else>

            <div class="block flex-wrap items-center">
                <img class="w-full md:w-[300px]" src="{{ asset('assets/img/icon/choose_icon01.svg') }}" alt="">
                <div>
                    <h2 class="font-display text-2xl text-jacarta-700 md:w-[calc(100% - 300px)] w-full">{{ __('velocity::app-static.moneytransfer.success.bank-info-not-exist.heading') }}</h2>
                    <p class="mb-4 font-display text-base text-jacarta-700">
                        {{ __('velocity::app-static.moneytransfer.success.bank-info-not-exist.contact-us') }}
                    </p>
                </div>
            </div>

        </div>

    </script>

    <script type="text/x-template" id="bank-info-navigation-item-template">
        <div class="flex flex-wrap">
            <bank-info-navigation-item-input label="{{ __('velocity::app-static.orders.bank-name') }}" :value="bank.bank_name"></bank-info-navigation-item-input>

            <bank-info-navigation-item-input label="{{ __('velocity::app-static.orders.account-name') }}" :value="bank.account_name"></bank-info-navigation-item-input>

            <bank-info-navigation-item-input label="{{ __('velocity::app-static.orders.iban') }}" :value="bank.account_number"></bank-info-navigation-item-input>

            <bank-info-navigation-item-input label="{{ __('velocity::app-static.orders.branch-code') }}" :value="bank.account_branch_code"></bank-info-navigation-item-input>
        </div>
    </script>

    <script type="text/x-template" id="bank-info-navigation-item-input-template">
        <div class="mb-6 w-full md:w-1/2 px-4 lg:px-6 relative">
            <label :for="value" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                @{{ label }}
            </label>
            <div class="relative">
                <input :name="value"
                       type="text"
                       class="w-full m-0 p-2 fs-6"
                       :value="value"
                       readonly
                       class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                <div
                    v-if="value"
                    @click="copyToClipboard(value)"
                    class="absolute"
                    style="top:10px;right: 10px;cursor: pointer;">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="27px" width="27px" version="1.1" id="Layer_1" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve" fill="#FF9700">
                        <g id="Text-files">
                            <path d="M53.9791489,9.1429005H50.010849c-0.0826988,0-0.1562004,0.0283995-0.2331009,0.0469999V5.0228   C49.7777481,2.253,47.4731483,0,44.6398468,0h-34.422596C7.3839517,0,5.0793519,2.253,5.0793519,5.0228v46.8432999   c0,2.7697983,2.3045998,5.0228004,5.1378999,5.0228004h6.0367002v2.2678986C16.253952,61.8274002,18.4702511,64,21.1954517,64   h32.783699c2.7252007,0,4.9414978-2.1725998,4.9414978-4.8432007V13.9861002   C58.9206467,11.3155003,56.7043495,9.1429005,53.9791489,9.1429005z M7.1110516,51.8661003V5.0228   c0-1.6487999,1.3938999-2.9909999,3.1062002-2.9909999h34.422596c1.7123032,0,3.1062012,1.3422,3.1062012,2.9909999v46.8432999   c0,1.6487999-1.393898,2.9911003-3.1062012,2.9911003h-34.422596C8.5049515,54.8572006,7.1110516,53.5149002,7.1110516,51.8661003z    M56.8888474,59.1567993c0,1.550602-1.3055,2.8115005-2.9096985,2.8115005h-32.783699   c-1.6042004,0-2.9097996-1.2608986-2.9097996-2.8115005v-2.2678986h26.3541946   c2.8333015,0,5.1379013-2.2530022,5.1379013-5.0228004V11.1275997c0.0769005,0.0186005,0.1504021,0.0469999,0.2331009,0.0469999   h3.9682999c1.6041985,0,2.9096985,1.2609005,2.9096985,2.8115005V59.1567993z"></path>
                            <path d="M38.6031494,13.2063999H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0158005   c0,0.5615997,0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4542999,1.0158997-1.0158997   C39.6190491,13.6606998,39.16465,13.2063999,38.6031494,13.2063999z"></path>
                            <path d="M38.6031494,21.3334007H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0157986   c0,0.5615005,0.4544001,1.0159016,1.0159006,1.0159016h22.3491974c0.5615005,0,1.0158997-0.454401,1.0158997-1.0159016   C39.6190491,21.7877007,39.16465,21.3334007,38.6031494,21.3334007z"></path>
                            <path d="M38.6031494,29.4603004H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4543991,1.0158997-1.0158997   S39.16465,29.4603004,38.6031494,29.4603004z"></path>
                            <path d="M28.4444485,37.5872993H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h12.1904964c0.5615025,0,1.0158005-0.4543991,1.0158005-1.0158997   S29.0059509,37.5872993,28.4444485,37.5872993z"></path>
                        </g>
                    </svg>
                </div>
                <div style="top: 50px;right: 15px;z-index: 99999;position: fixed;display: block;width: 430px;height: 100px;font-size: 16px;" v-if="copied">
                    <div class="alert alert-success alert-dismissible" id="504">
                        <strong>{{ __('shop::app.checkout.success.copied') }}</strong>
                    </div>
                </div>
            </div>

        </div>
    </script>

    <script>
        Vue.component('bank-info-navigation', {
            template: '#bank-info-navigation-template',
            data: function () {
                return {
                    banks: [],
                    currency: '{{ $order?->order_currency_code }}'
                }
            },
            created() {
                this.getBankInfo();
            },
            methods: {
                async getBankInfo() {
                    try {
                        const url = '{{ route('shop.checkout.bank-info') }}';
                        const currencyParam = `?currency=${this.currency}`;
                        const response = await axios.get(url + currencyParam);
                        const bankDetail = response.data.bankDetail;
                        this.banks = bankDetail.flatMap((detail, index) =>
                            detail.bankDetailInfo.map(info => ({
                                bank_id: index + 1,
                                bank_name: info.bankName,
                                account_name: info.accountName,
                                account_number: info.iban,
                                account_branch_code: info.bankCode
                            }))
                        );
                    } catch (error) {
                        console.error('Error:', error);
                    }
                }
            }
        });

        Vue.component('bank-info-navigation-item', {
            template: '#bank-info-navigation-item-template',
            props: ['bank']
        });

        Vue.component('bank-info-navigation-item-input', {
            template: '#bank-info-navigation-item-input-template',
            data: function () {
                return {
                    copied: false
                }
            },
            props: [
                'label',
                'value',
            ],
            methods: {
                copyToClipboard(value) {
                    navigator.clipboard.writeText(value).then(function() {
                        this.copied = true;

                        setTimeout(() => {
                            this.copied = false;
                        }, 3000);
                    }, function(error) {
                        console.error('Error:', error);
                    });
                }
            },
        });
    </script>
@endpush
