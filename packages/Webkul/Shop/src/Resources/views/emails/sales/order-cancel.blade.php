@component('shop::emails.layouts.master')
    <div style="text-align: center;">
        <a href="{{ config('app.url') }}">
            @include ('shop::emails.layouts.logo')
        </a>
    </div>

    <div style="padding: 30px;">
        <div style="font-size: 20px;color: #242424;line-height: 30px;margin-bottom: 34px;">
            <span style="font-weight: bold;">
                {{ __('mail.order.cancel.heading') }}
            </span> <br>

            <p style="font-size: 16px;color: #5E5E5E;line-height: 24px;">
                {{ __('mail.order.cancel.dear', ['customer_name' => $order->customer_full_name]) }},
            </p>

            <p style="font-size: 16px;color: #5E5E5E;line-height: 24px;">
                {!! __('mail.order.cancel.greeting', [
                    'order_id' => '<a href="' . route('customer.orders.view', $order->id) . '" style="color: #0041FF; font-weight: bold;">#' . $order->increment_id . '</a>',
                    'created_at' => $order->created_at
                    ])
                !!}
            </p>
        </div>

        <div style="font-weight: bold;font-size: 20px;color: #242424;line-height: 30px;margin-bottom: 20px !important;">
            {{ __('mail.order.cancel.summary') }}
        </div>

        <div style="display: flex;flex-direction: row;margin-top: 20px;justify-content: space-between;margin-bottom: 40px;">
            @if ($order->shipping_address)
                <div style="line-height: 25px;">
                    <div style="font-weight: bold;font-size: 16px;color: #242424;">
                        {{ __('mail.order.cancel.shipping-address') }}
                    </div>

                    <div>
                        {{ $order->shipping_address->company_name ?? '' }}
                    </div>

                    <div>
                        {{ $order->shipping_address->name }}
                    </div>

                    <div>
                        {{ $order->shipping_address->address1 }}
                    </div>

                    <div>
                        {{ $order->shipping_address->postcode . " " . $order->shipping_address->city }}
                    </div>

                    <div>
                        {{ $order->shipping_address->state }}
                    </div>

                    <div>
                        {{ core()->country_name($order->shipping_address->country) }}
                    </div>

                    <div>---</div>

                    <div style="margin-bottom: 40px;">
                        {{ __('mail.order.cancel.contact') }} : {{ $order->shipping_address->phone }}
                    </div>

                    <div style="font-size: 16px;color: #242424; font-weight: bold">
                        {{ __('mail.order.cancel.shipping') }}
                    </div>

                    <div style="font-size: 16px;color: #242424;">
                        {{ $order->shipping_title }}
                    </div>
                </div>
            @endif

            @if ($order->billing_address)
                <div style="line-height: 25px;">
                    <div style="font-weight: bold;font-size: 16px;color: #242424;">
                        {{ __('mail.order.cancel.billing-address') }}
                    </div>

                    <div>
                        {{ $order->billing_address->company_name ?? '' }}
                    </div>

                    <div>
                        {{ $order->billing_address->address1 }}
                    </div>

                    <div>
                        {{ $order->billing_address->postcode . " " . $order->billing_address->city }}
                    </div>

                    <div>
                        {{ $order->billing_address->state }}
                    </div>

                    <div>
                        {{ core()->country_name($order->billing_address->country) }}
                    </div>

                    <div>---</div>

                    <div style="margin-bottom: 40px;">
                        {{ __('mail.order.cancel.contact') }} : {{ $order->billing_address->phone }}
                    </div>

                    <div style="font-size: 16px; color: #242424; font-weight: bold">
                        {{ __('mail.order.cancel.payment') }}
                    </div>

                    <div style="font-size: 16px; color: #242424;">
                        {{ core()->getConfigData('sales.paymentmethods.' . $order->payment->method . '.title') }}
                    </div>
                </div>
            @endif
        </div>

        <div class="section-content">
            <div class="table mb-20">
                <table style="overflow-x: auto; border-collapse: collapse;
                border-spacing: 0;width: 100%">
                    <thead>
                    <tr style="background-color: #f2f2f2">
                        <th style="text-align: left;padding: 8px">{{ __('mail.customer.account.order.view.SKU') }}</th>
                        <th style="text-align: left;padding: 8px">{{ __('mail.customer.account.order.view.product-name') }}</th>
                        <th style="text-align: left;padding: 8px">{{ __('mail.customer.account.order.view.price') }}</th>
                        <th style="text-align: left;padding: 8px">{{ __('mail.customer.account.order.view.qty') }}</th>
                    </tr>
                    </thead>

                    <tbody>
                    @foreach ($order->items as $item)
                        <tr>
                            <td data-value="{{ __('mail.customer.account.order.view.SKU') }}" style="text-align: left;padding: 8px">
                                {{ $item->child ? $item->child->sku : $item->sku }}
                            </td>

                            <td data-value="{{ __('mail.customer.account.order.view.product-name') }}" style="text-align: left;padding: 8px">
                                {{ $item->name }}

                                @if (isset($item->additional['attributes']))
                                    <div class="item-options">

                                        @foreach ($item->additional['attributes'] as $attribute)
                                            <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>
                                        @endforeach

                                    </div>
                                @endif
                            </td>

                            <td data-value="{{ __('mail.customer.account.order.view.price') }}" style="text-align: left;padding: 8px">
                                {{ core()->formatPrice($item->price, $order->order_currency_code) }}
                            </td>

                            <td data-value="{{ __('mail.customer.account.order.view.qty') }}" style="text-align: left;padding: 8px">
                                {{ $item->qty_canceled }}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <div style="font-size: 16px;color: #242424;line-height: 30px;float: right;width: 40%;margin-top: 20px;">
            <div>
                <span>{{ __('mail.order.cancel.subtotal') }}</span>
                <span style="float: right;">
                    {{ core()->formatPrice($order->sub_total, $order->order_currency_code) }}
                </span>
            </div>

            <div>
                <span>{{ __('mail.order.cancel.shipping-handling') }}</span>
                <span style="float: right;">
                    {{ core()->formatPrice($order->shipping_amount, $order->order_currency_code) }}
                </span>
            </div>

            @foreach (Webkul\Tax\Helpers\Tax::getTaxRatesWithAmount($order, false) as $taxRate => $taxAmount )
                <div>
                    <span id="taxrate-{{ core()->taxRateAsIdentifier($taxRate) }}">{{ __('mail.order.cancel.tax') }} {{ $taxRate }} %</span>
                    <span id="taxamount-{{ core()->taxRateAsIdentifier($taxRate) }}" style="float: right;">
                    {{ core()->formatPrice($taxAmount, $order->order_currency_code) }}
                </span>
                </div>
            @endforeach

            @if ($order->discount_amount > 0)
                <div>
                    <span>{{ __('mail.order.cancel.discount') }}</span>
                    <span style="float: right;">
                        {{ core()->formatPrice($order->discount_amount, $order->order_currency_code) }}
                    </span>
                </div>
            @endif

            <div style="font-weight: bold">
                <span>{{ __('mail.order.cancel.grand-total') }}</span>
                <span style="float: right;">
                    {{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}
                </span>
            </div>
        </div>

        <div style="margin-top: 65px;font-size: 16px;color: #5E5E5E;line-height: 24px;display: inline-block">
            <p style="font-size: 16px;color: #5E5E5E;line-height: 24px;">
                {{ __('mail.order.cancel.final-summary') }}
            </p>

            <p style="font-size: 16px;color: #5E5E5E;line-height: 24px;">
                {!!
                    __('mail.order.cancel.help', [
                        'support_email' => '<a style="color:#0041FF" href="mailto:' . config('mail.from.address') . '">' . config('mail.from.address'). '</a>'
                        ])
                !!}
            </p>

            <p style="font-size: 16px;color: #5E5E5E;line-height: 24px;">
                {{ __('mail.order.cancel.thanks') }}
            </p>
        </div>
    </div>
@endcomponent
