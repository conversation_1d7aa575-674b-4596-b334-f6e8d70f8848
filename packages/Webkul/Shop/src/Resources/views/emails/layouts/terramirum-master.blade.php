<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
</head>
<body style="background-color: #030C15; font-family: 'Poppins', sans-serif; color: #ffffff; padding: 0; margin: 0;">
<div style="width: 100%;max-width: 800px; margin: auto; padding: 25px">
    <div style="display:flex; justify-content: center; align-items: center;width: 100%;">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 100 102.72" style="width: 50px; height: 50px;">
            <g id="Group_1621" data-name="Group 1621" transform="translate(0 0)">
                <g id="Group_1620" data-name="Group 1620" transform="translate(0 0)">
                    <path id="Path_1558" data-name="Path 1558"
                          d="M44.665,67.22a50.435,50.435,0,0,0-3.155-7.078c-.011-.018-.061-.109-.123-.217a39.577,39.577,0,0,0-5.169-7.442c-2.768-3.108-6.377-5.983-11.971-9.288a48.7,48.7,0,0,0-7.466-3.579c-2.152-.816-3.129-.665-3.138,1.969-.039,10.072,4.312,16.323,4.346,16.376A29.367,29.367,0,0,0,24.335,65.9a29.847,29.847,0,0,0,6.927,4.551,9.283,9.283,0,0,0,4.655,1.106,1.659,1.659,0,0,0,1.174-.425,1.142,1.142,0,0,0,.332-.747c.216-2.251-5.853-9.146-5.853-9.146-2.575-2.926-2.98-3.465-5.222-5.93-1.252-1.369-2.3-2.5-3.042-3.281.578.339,1.2.727,1.868,1.156A46.025,46.025,0,0,1,30.8,57.512a45.093,45.093,0,0,1,4.684,4.958A47.931,47.931,0,0,1,39.535,68,55.975,55.975,0,0,1,43.16,75.09a44.627,44.627,0,0,1,2.6,6.676,25.491,25.491,0,0,1,.808,3.9,23.1,23.1,0,0,1,.152,3.934,14.966,14.966,0,0,1-1.272,5.856,7.6,7.6,0,0,1-1.285,2.025,5.665,5.665,0,0,1-3.233,1.883c-.778.137-1.59.031-4.409-1.294-1.2-.566-1.826-.91-3.825-1.94-1.6-.823-2.394-1.234-3.311-1.69-.767-.38-1.457-.714-4.082-1.954-3.184-1.5-2.889-1.353-3.969-1.867-1.652-.787-3.673-1.749-6.1-3.013-1.492-.778-3.035-1.46-4.511-2.266a6.492,6.492,0,0,0-.715-.337h0A13.335,13.335,0,0,1,6.161,82.6a6.354,6.354,0,0,1-1.926-3.372A23.271,23.271,0,0,1,4.066,74.7l.049-4.943C4.3,59.712,4.225,42.864,4.591,33a5.957,5.957,0,0,1,3.051-4.349L11.926,26.2l8.708-5L37.486,11.53a3.842,3.842,0,0,0,.962.237,3.9,3.9,0,0,0,.457.024,3.966,3.966,0,0,0,2.435-.831A3.827,3.827,0,0,0,42.8,8.382a3.351,3.351,0,0,0,.024-.449,3.793,3.793,0,0,0-.845-2.389,3.994,3.994,0,0,0-5.523-.624,3.837,3.837,0,0,0-1.313,1.907c-5.8,3.486-20.13,12.065-25.579,15.346L5.2,24.79A10.822,10.822,0,0,0,.033,35.154c.024,3.1.12,11.639.144,14.822.034,3.584.212,21.517.241,24.709A23.429,23.429,0,0,0,.774,80c.9,4.032,4.1,6.28,7.672,7.919,6.051,3,12.14,5.774,18.253,8.546l9.184,4.1c.546.264,1.366.642,2.394,1.048.565.223.849.335,1.027.384a6.068,6.068,0,0,0,4.293-.752,8.006,8.006,0,0,0,2.474-2.219,10.724,10.724,0,0,0,1.366-2.213c1.626-3.467,1.556-7.388,1.482-10.207-.009-.335-.047-1.623-.214-3.327a55.739,55.739,0,0,0-1.725-9.035c-.161-.584-.294-1.034-.377-1.32-.335-1.128-.952-3.123-1.937-5.7m-7.757-59.5A1.949,1.949,0,0,1,37.653,6.4a2.032,2.032,0,0,1,2.813.317,1.932,1.932,0,0,1-.323,2.758,2.041,2.041,0,0,1-1.473.416,2.012,2.012,0,0,1-1.339-.732,1.941,1.941,0,0,1-.422-1.445"
                          transform="translate(0 0.568)" fill="#256130"/>
                    <path id="Path_1559" data-name="Path 1559"
                          d="M81,37.179c.391,10.064-3.788,16.467-3.82,16.519a29.01,29.01,0,0,1-2.284,3.766,29.333,29.333,0,0,1-3.85,4.393,30.236,30.236,0,0,1-6.84,4.8,9.475,9.475,0,0,1-4.665,1.271,1.683,1.683,0,0,1-1.2-.384,1.127,1.127,0,0,1-.361-.733c-.3-2.243,5.6-9.349,5.6-9.349,2.5-3.018,2.889-3.57,5.07-6.113,1.216-1.413,2.238-2.578,2.959-3.388-.572.36-1.19.769-1.847,1.222a46.831,46.831,0,0,0-5.54,4.528,45.314,45.314,0,0,0-4.559,5.122,54.907,54.907,0,0,0-3.9,5.674c-2.309,3.861-5.386,9.04-6.455,16.126-.128.851-.187,1.426-.2,1.585-.275,2.921-.769,8.216,2.263,11.264A7.757,7.757,0,0,0,55.1,95.5q.308,1.194.617,2.39A9.86,9.86,0,0,1,51.1,96.222a9.384,9.384,0,0,1-2.729-2.867,9.7,9.7,0,0,1-.993-2.495,17.673,17.673,0,0,1-.558-4.152,44.3,44.3,0,0,1,.136-6.558c.068-.722.143-1.321.186-1.651.2-1.483.44-2.752.682-3.893C49.194,68.093,50.336,62.7,53.6,56.5a39.819,39.819,0,0,1,4.965-7.621c2.688-3.2,6.236-6.206,11.773-9.708a49.328,49.328,0,0,1,7.419-3.843C79.9,34.43,80.9,34.547,81,37.179"
                          transform="translate(6.301 4.831)" fill="#84c348"/>
                    <path id="Path_1560" data-name="Path 1560" d="M45.765,34.615a74.776,74.776,0,0,1,.6,10.655c-.044,3.027-.165,11.291-2.28,11.7-.6.116-1.238-.42-2.454-1.459a11.708,11.708,0,0,1-2.426-2.88A21.094,21.094,0,0,1,36.63,45.2c-1.041-8.937,3.432-16.026,8.015-23.028.735-1.122,1.744-.908,2.432.066,5.8,8.207,10.111,16.814,7.041,27.325a14.43,14.43,0,0,1-3.677,5.949c-.841.8-1.464,1.164-1.946.987-.883-.323-.7-2.437-.681-6.845a57.446,57.446,0,0,0-.374-8.9,27.346,27.346,0,0,0-1.675-6.13"
                          transform="translate(4.916 2.97)" fill="#437d3b"/>
                    <path id="Path_1561" data-name="Path 1561"
                          d="M94.8,29.843c-.524-3.88-3.135-6.529-6.654-8.3-9.931-5.5-26.288-14.6-36.2-20.077A11.421,11.421,0,0,0,43.128.5c-2.055.756-4.422,1.783-4.422,1.783l-1,.5,2.529,4.43c.613-.29,1.525-.706,2.655-1.163a10.3,10.3,0,0,1,3.38-1.039,6.864,6.864,0,0,1,3.175.834l3.975,2.146L86.529,25.9c4.1,2.189,3.744,4.7,3.748,8.638.03,8.019.109,22.972.162,30.941l.03,4.419a21.406,21.406,0,0,1-.148,3.982,9.924,9.924,0,0,1-4.294,6.063c-2.369,1.812-4.843,3.59-7.307,5.351Q71.471,90.457,64.073,95.45a4.194,4.194,0,0,0-4.118.493,3.721,3.721,0,0,0-1.487,2.543,3.644,3.644,0,0,0,.84,2.794,4.035,4.035,0,0,0,2.679,1.415c.157.015.309.024.466.024a4.092,4.092,0,0,0,2.478-.824,3.725,3.725,0,0,0,1.491-2.538,3.561,3.561,0,0,0-.034-1.094q7.4-4.9,14.692-9.952c3.171-2.281,6.414-4.353,9.417-6.925a12.678,12.678,0,0,0,4.176-9.309c.015-1.337.01-5.268.034-6.618.035-9.015.148-24.043.211-33.068a15.751,15.751,0,0,0-.123-2.547M64.47,99.143a1.887,1.887,0,0,1-.756,1.295,2.1,2.1,0,0,1-1.5.405,2.053,2.053,0,0,1-1.37-.717,1.854,1.854,0,0,1-.427-1.426,1.914,1.914,0,0,1,.76-1.3,2.085,2.085,0,0,1,1.262-.419,1.856,1.856,0,0,1,.241.015,2.067,2.067,0,0,1,1.364.722,1.845,1.845,0,0,1,.427,1.425"
                          transform="translate(5.081 0)" fill="#84c348"/>
                </g>
            </g>
        </svg>
        <div style="font-weight: 600;color: #ffffff;letter-spacing: 1.3px;font-size: 28px;margin-left: 12px;">
            <span style="color: #286331">T</span>erra<span style="color:#83C245;">M</span>irum
        </div>
    </div>

    <div style="padding-top: 8px; padding-bottom: 8px;">
        @yield('content')
    </div>

    <div style="margin-top: 20px; padding-top: 20px">
        <div style="font-weight: 600;color: #DF9441;font-size: 26px;margin-bottom: 6px; text-align: center;">FOLLOW US!</div>
        <div style="display: flex;justify-content: center;">
            <a href="" target="_blank" style="margin-right: 15px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="30.617" height="30.617" viewBox="0 0 30.617 30.6 17">
                    <g id="Group_4695" data-name="Group 4695" transform="translate(0 0)">
                        <path id="Path_1986" data-name="Path 1986" d="M30.617,15.308A15.308,15.308,0,1,1,15.308,0,15.308,15.308,0,0,1,30.617,15.308" transform="translate(0 0)" fill="#ffffff"/>
                        <path id="Path_1987" data-name="Path 1987" d="M15.392,8.355c.556-.019,1.113,0,1.671,0h.23v-2.9c-.3-.03-.609-.073-.92-.087-.571-.026-1.143-.054-1.714-.041a4.194,4.194,0,0,0-2.421.748,3.616,3.616,0,0,0-1.454,2.417,8.335,8.335,0,0,0-.1,1.243c-.016.65,0,1.3,0,1.949v.244H7.909v3.235h2.756v8.132h3.366V15.176h2.747c.141-1.076.277-2.141.422-3.251h-.617c-.781,0-2.575,0-2.575,0s.009-1.6.027-2.3c.027-.954.592-1.245,1.356-1.272" transform="translate(1.886 1.269)" fill="#DF9441" fill-rule="evenodd"/>
                    </g>
                </svg>
            </a>
            <a href="https://twitter.com/ThorneBilisim" target="_blank" style="margin-right: 15px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="30.617" height="30.617" viewBox="0 0 30.617 30.617">
                    <g id="Group_4696" data-name="Group 4696" transform="translate(-47.155 0)">
                        <path id="Path_1988" data-name="Path 1988" d="M68.693,15.308A15.308,15.308,0,1,1,53.384,0,15.308,15.308,0,0,1,68.693,15.308" transform="translate(9.079 0)" fill="#ffffff"/>
                        <path id="Path_1989" data-name="Path 1989" d="M49.206,18.17a3.61,3.61,0,0,1-3.349-2.5,3.564,3.564,0,0,0,1.543-.047.265.265,0,0,0,.051-.024,3.6,3.6,0,0,1-2.337-1.651,3.5,3.5,0,0,1-.528-1.915,3.549,3.549,0,0,0,1.6.437,3.621,3.621,0,0,1-1.481-2.183,3.566,3.566,0,0,1,.386-2.607,10.3,10.3,0,0,0,7.423,3.761c-.021-.142-.043-.269-.058-.4A3.5,3.5,0,0,1,52.984,8.7a3.467,3.467,0,0,1,2.441-1.627,3.509,3.509,0,0,1,3.151,1,.183.183,0,0,0,.192.058,7.26,7.26,0,0,0,2.094-.8.245.245,0,0,1,.047-.025s.01,0,.025,0a3.687,3.687,0,0,1-1.542,1.959,6.894,6.894,0,0,0,2-.54l.015.016c-.137.181-.27.365-.414.54a7,7,0,0,1-1.309,1.243.12.12,0,0,0-.058.113,9.849,9.849,0,0,1-.068,1.6,10.529,10.529,0,0,1-.928,3.205,10.345,10.345,0,0,1-1.946,2.837,9.608,9.608,0,0,1-4.945,2.756,10.813,10.813,0,0,1-2.005.239,10.174,10.174,0,0,1-5.76-1.546c-.022-.014-.045-.03-.084-.056a7.265,7.265,0,0,0,3.6-.5,7.146,7.146,0,0,0,1.712-1" transform="translate(10.466 1.673)" fill="#DF9441" fill-rule="evenodd"/>
                    </g>
                </svg>
            </a>
            <a href="https://www.instagram.com/thornebilisim/" target="_blank" style="margin-right: 15px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="30.617" height="30.617" viewBox="0 0 30.617 30.617">
                    <g id="Group_4698" data-name="Group 4698" transform="translate(-94.311 0)">
                        <path id="Path_1990" data-name="Path 1990" d="M106.77,15.308A15.308,15.308,0,1,1,91.461,0,15.308,15.308,0,0,1,106.77,15.308" transform="translate(18.158 0)" fill="#ffffff"/>
                        <path id="Path_1991" data-name="Path 1991" d="M98.182,9.908A4.3,4.3,0,0,0,96.87,7.14,4.491,4.491,0,0,0,93.9,5.933a58.567,58.567,0,0,0-8.021.089,4.094,4.094,0,0,0-3.508,3.149c-.385,1.38-.31,7.739-.083,9.1a4.1,4.1,0,0,0,3.257,3.537,48.008,48.008,0,0,0,8.976.059,4.11,4.11,0,0,0,3.552-3.242c.372-1.417.241-7.443.111-8.72m-1.492,8.1a2.744,2.744,0,0,1-2.581,2.45,48.242,48.242,0,0,1-8.363-.116,2.657,2.657,0,0,1-2.016-2.291,54.129,54.129,0,0,1,0-8.184A2.739,2.739,0,0,1,86.3,7.43a57.208,57.208,0,0,1,7.983.032,2.747,2.747,0,0,1,2.445,2.586,58.166,58.166,0,0,1-.033,7.962M90.207,9.789a4.158,4.158,0,1,0,4.155,4.16,4.158,4.158,0,0,0-4.155-4.16m-.03,6.844a2.689,2.689,0,1,1,2.713-2.664,2.688,2.688,0,0,1-2.713,2.664m5.323-7a.973.973,0,1,1-.971-.976.974.974,0,0,1,.971.976" transform="translate(19.575 1.393)" fill="#DF9441"/>
                        <path id="Path_1992" data-name="Path 1992" d="M98.182,9.908A4.3,4.3,0,0,0,96.87,7.14,4.491,4.491,0,0,0,93.9,5.933a58.567,58.567,0,0,0-8.021.089,4.094,4.094,0,0,0-3.508,3.149c-.385,1.38-.31,7.739-.083,9.1a4.1,4.1,0,0,0,3.257,3.537,48.008,48.008,0,0,0,8.976.059,4.11,4.11,0,0,0,3.552-3.242c.372-1.417.241-7.443.111-8.72m-1.492,8.1a2.744,2.744,0,0,1-2.581,2.45,48.242,48.242,0,0,1-8.363-.116,2.657,2.657,0,0,1-2.016-2.291,54.129,54.129,0,0,1,0-8.184A2.739,2.739,0,0,1,86.3,7.43a57.208,57.208,0,0,1,7.983.032,2.747,2.747,0,0,1,2.445,2.586,58.166,58.166,0,0,1-.033,7.962M90.207,9.789a4.158,4.158,0,1,0,4.155,4.16,4.158,4.158,0,0,0-4.155-4.16m-.03,6.844a2.689,2.689,0,1,1,2.713-2.664,2.688,2.688,0,0,1-2.713,2.664m5.323-7a.973.973,0,1,1-.971-.976.974.974,0,0,1,.971.976" transform="translate(19.575 1.393)" fill="#DF9441"/>
                    </g>
                </svg>

            </a>
            <a href="https://www.linkedin.com/company/thorne-bili%C5%9Fim/" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="30.617" height="30.617" viewBox="0 0 30.617 30.617">
                    <g id="Group_4697" data-name="Group 4697" transform="translate(-141.466 0)">
                        <path id="Path_1993" data-name="Path 1993" d="M144.846,15.308A15.308,15.308,0,1,1,129.537,0a15.308,15.308,0,0,1,15.308,15.308" transform="translate(27.237 0)" fill="#ffffff"/>
                        <path id="Path_1994" data-name="Path 1994" d="M121.351,10.73h3.107v9.981h-3.107Zm1.554-4.96a1.8,1.8,0,1,1-1.8,1.8,1.8,1.8,0,0,1,1.8-1.8" transform="translate(28.876 1.376)" fill="#DF9441"/>
                        <path id="Path_1995" data-name="Path 1995" d="M125.385,9.822h2.976v1.365h.041a3.262,3.262,0,0,1,2.938-1.612c3.141,0,3.72,2.066,3.72,4.754V19.8h-3.1V14.95c0-1.158-.022-2.647-1.612-2.647-1.615,0-1.86,1.261-1.86,2.562V19.8h-3.1Z" transform="translate(29.897 2.283)" fill="#DF9441"/>
                    </g>
                </svg>
            </a>
        </div>
        <div style="font-weight: 600;color: #ffffff;font-size: 14px;margin-left: 6px; text-align: center; margin-top: 15px;">
            If you need any kind of help please contact us at <a href="mailto:<EMAIL>" style="color: #DF9441"><EMAIL></a>
            <br>Thanks!
        </div>
    </div>
</div>
</body>
</html>
