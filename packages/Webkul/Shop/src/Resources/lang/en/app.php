<?php

return [
    'invalid_vat_format' => 'The given vat id has a wrong format',
    'security-warning'   => 'Suspicious activity found!!!',
    'nothing-to-delete'  => 'Nothing to delete',

    'layouts' => [
        'my-account'            => 'My Account',
        'profile'               => 'Profile',
        'address'               => 'Address',
        'reviews'               => 'Reviews',
        'wishlist'              => 'Wishlist',
        'orders'                => 'Orders',
        'downloadable-products' => 'Downloadable Products',
    ],

    'common' => [
        'error'              => 'Something went wrong, please try again later.',
        'image-upload-limit' => 'Image max upload size is 2MB',
        'no-result-found'    => 'We could not find any records.',
    ],

    'home' => [
        'page-title'          => config('app.name').' - Home',
        'featured-products'   => 'Featured Products',
        'new-products'        => 'New Products',
        'verify-email'        => 'Verify your email account',
        'resend-verify-email' => 'Resend Verification Email',
    ],

    'header' => [
        'home'        => 'Home',
        'about'       => 'About Us',
        'faq'         => 'FAQ',
        'credipto'    => 'Credipto',
        'marketplace' => 'Marketplace',
        'contact'     => 'Contact',
        'whitepaper'  => 'Whitepaper',
        'buy-now'     => 'Buy Now',
        'my-account'  => 'My Account',

        'auth' => [
            'register' => [
                'subject'  => 'Account Created Confirmation',
                'greeting' => 'Account Created Confirmation',
                'dear'     => 'Hello :name, <br><br> Your account has been successfully created! You have now officially joined the :company_name family. Here are your account details:<br><br> Registered Email: :email<br> To access your account, please verify your email address using the link below:',
                'summary'  => 'If you do not want to click on the link, you can verify your account using the confirmation code below:<br><br>Confirmation Code: :code<br><br> Remember, for the security of your account, it is important that you do not share your password with anyone. If you did not create this account or if you encounter any problems, please let us know.<br><br>Have a nice day and enjoy your time at :company_name!',
                'verify'   => 'Verify Email',
                'thanks'   => 'Best regards, <br><br> Thorne IT Inc. <br> Customer Support Team',
            ],
            'forgot_password' => [
                'subject'        => 'Password Change Process',
                'greeting'       => 'Password Change Process',
                'dear'           => 'Hello, :name, <br><br> The security of your account is important to us. A process has been initiated to change your password. If you did not initiate this change, please contact our customer support team immediately. <br><br> If you have initiated this process, please follow the instructions below to change your password:',
                'summary'        => 'Clicking this link will take you to our password change page. <br><br> Set Your New Password <br> Set your new password on the page that opens and make sure to use a strong password. <br><br> Confirm Password <br> Confirm by re-entering the new password you have set. <br><br> Remember, passwords should be changed regularly to ensure the security of your personal information. If you encounter any problems, please let us know.',
                'reset-password' => 'Reset Password',
                'thanks'         => 'We wish you a good day. <br><br>Thorne IT Inc. <br> Customer Support Team',
            ],
            'updated_password' => [
                'subject'  => 'Your password has been changed',
                'greeting' => 'Your password has been changed',
                'dear'     => 'Hello, :name, <br><br> The security of your account is important to us. Your password has been changed.',
                'summary'  => 'If you did not initiate this change, please contact our customer support team immediately. <br><br> If you have initiated this process, please follow the instructions below to change your password:',
                'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
            ],
            'verification' => [
                'email' => [
                    'subject'           => 'Account Verification',
                    'greeting'          => 'Account Verification',
                    'dear'              => 'Hello, :name, <br><br> Thank you for creating your account. For the security of your account, please verify your e-mail address by clicking the link below:',
                    'alternate_summary' => 'If you cannot click on the link, you can verify your account using the confirmation code below:',
                    'verify'            => 'Verify Email',
                    'verification_code' => 'Verification Code: :code',
                    'summary'           => 'Note: Before verifying your account, make sure you received this email to the email address you used to create your account. If you did not request this email, please ignore this message.',
                    'thanks'            => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
                'customer' => [
                    'subject'  => 'Customer Verification',
                    'greeting' => 'Customer Verification',
                    'dear'     => 'Hello, :name, <br><br> For the security of your account, please verify your customer information. Please contact our customer support team to verify your customer information.',
                    'summary'  => 'Note: Before verifying your customer information, make sure you received this email to the email address you used to create your account. If you did not request this email, please ignore this message.',
                    'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
            ],
            'verified' => [
                'general' => [
                    'next_step'             => 'Next Step',
                    'identity_verification' => 'Next, you will need to verify your identity. Click the button below to verify your identity.',
                    'verify_identity'       => 'Verify Identity',
                    'address_verification'  => 'Next, you will need to verify your address. Click the button below to verify your address.',
                    'verify_address'        => 'Verify Address',
                    'customer_verification' => 'Next, you will need to verify your customer information. Please contact our customer support team to verify your customer information.',
                    'verify_customer'       => 'Contact Customer Support',
                ],
                'email' => [
                    'subject'  => 'Email Verified',
                    'greeting' => 'Email Verified',
                    'dear'     => 'Hello, :name, <br><br> Your email has been verified.',
                    'summary'  => 'Note: If you did not request this email, please contact our customer support team immediately.',
                    'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
                'identity' => [
                    'subject'  => 'Identity Verified',
                    'greeting' => 'Identity Verified',
                    'dear'     => 'Hello, :name, <br><br> Your identity has been verified.',
                    'summary'  => 'Note: If you did not request this email, please contact our customer support team immediately.',
                    'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
                'address' => [
                    'subject'  => 'Address Verified',
                    'greeting' => 'Address Verified',
                    'dear'     => 'Hello, :name, <br><br> Your address has been verified.',
                    'summary'  => 'Note: If you did not request this email, please contact our customer support team immediately.',
                    'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
                'customer' => [
                    'subject'  => 'Customer Information Verified',
                    'greeting' => 'Customer Information Verified',
                    'dear'     => 'Hello, :name, <br><br> Your customer information has been verified.',
                    'summary'  => 'Note: If you did not request this email, please contact our customer support team immediately.',
                    'thanks'   => 'We wish you a good day. <br><br> Thorne IT Inc. <br> Customer Support Team',
                ],
            ],
        ],
        'newsletter' => [
            'subscribe' => [
                'subject'     => 'Your Newsletter Subscription is Created!',
                'greeting'    => 'Your Newsletter Subscription is Created!',
                'dear'        => 'Hello :email,',
                'summary'     => 'You have successfully subscribed to the :company_name newsletter. We are very happy to bring you the latest news, updates and special offers! <br><br> Please click on the link below to confirm your subscription and get the most out of our newsletter:',
                'contact'     => 'If you have not initiated this subscription, please ignore this email or let us know. You can use the information below to contact us:<br><br>Email: :support_email<br>Telephone: :support_phone <br><br> Thank you again for joining the :company_name family. We strive to provide you with the best experience.',
                'confirm'     => 'Confirm Subscription',
                'unsubscribe' => 'Unsubscribe',
                'thanks'      => 'We wish you a good day. <br><br>Sincerely yours,<br><br> Thorne IT Inc. <br> Customer Service',
            ],
            'completed' => [
                'subject'     => 'Your Newsletter Subscription is Confirmed!',
                'greeting'    => 'Your Newsletter Subscription is Confirmed!',
                'dear'        => 'Hello :email,',
                'summary'     => 'You have successfully confirmed your subscription to the :company_name newsletter. We are very happy to bring you the latest news, updates and special offers! <br><br> If you have any questions or concerns, please let us know. You can use the information below to contact us:<br><br>Email: :support_email<br>Telephone: :support_phone <br><br> Thank you again for joining the :company_name family. We strive to provide you with the best experience.',
                'unsubscribe' => 'Unsubscribe',
                'thanks'      => 'We wish you a good day. <br><br>Sincerely yours,<br><br> Thorne IT Inc. <br> Customer Service',
            ],
            'unsubscribe' => [
                'subject'     => 'Your Newsletter Subscription has been Canceled',
                'greeting'    => 'Your Newsletter Subscription has been Canceled',
                'dear'        => 'Hello :email,',
                'contact'     => 'If you have not initiated this subscription, please ignore this email or let us know. You can use the information below to contact us:<br><br>Email: :support_email<br>Telephone: :support_phone <br><br> Thank you again for joining the :company_name family. We strive to provide you with the best experience.',
                'summary'     => 'You have successfully unsubscribed from the :company_name newsletter. We are very sorry to see you go. <br><br> If you have any questions or concerns, please let us know. You can use the information below to contact us:<br><br>Email: :support_email<br>Telephone: :support_phone <br><br> Thank you again for joining the :company_name family. We strive to provide you with the best experience.',
                'confirm'     => 'Confirm Subscription',
                'unsubscribe' => 'Unsubscribe',
                'thanks'      => 'We wish you a good day. <br><br>Sincerely yours,<br><br> Thorne IT Inc. <br> Customer Service',
            ],
        ],
        'deactivate' => [
            'subject'  => ':app_name - Your account has been deactivated',
            'greeting' => 'Your account has been deactivated',
            'dear'     => 'Hello :customer_name, your account has been deactivated.',
            'summary'  => 'Your account has been deactivated. If you think there is an error, please contact our customer service.',
            'thanks'   => 'Thanks!',
        ],
        'title'         => 'Account',
        'dropdown-text' => 'Manage Cart, Orders & Wishlist',
        'sign-in'       => 'Sign In',
        'sign-up'       => 'Sign Up',
        'account'       => 'Account',
        'profile'       => 'Profile',
        'wishlist'      => 'Wishlist',
        'cart'          => 'Cart',
        'logout'        => 'Logout',
        'search-text'   => 'Search products here',
    ],
    'menu' => [
        'marketplace' => 'Marketplace',
        'nft'         => 'NFT',
        'discover'    => 'Discover',
        'about-us'    => 'About Us',
        'career'      => 'Career',
        'blog'        => 'Blog',
    ],

    'minicart' => [
        'view-cart' => 'View Shopping Cart',
        'checkout'  => 'Checkout',
        'cart'      => 'Cart',
        'zero'      => '0',
    ],

    'footer' => [
        'subscribe-newsletter' => 'Subscribe Newsletter',
        'subscribe'            => 'Subscribe',
        'locale'               => 'Locale',
        'currency'             => 'Currency',
    ],

    'subscription' => [
        'unsubscribe'    => 'Unsubcribe',
        'subscribe'      => 'Subscribe',
        'subscribed'     => 'You are now subscribed to subscription emails.',
        'already'        => 'You are already subscribed to our subscription list.',
        'unsubscribed'   => 'You are unsubscribed from subscription mails.',
        'already-unsub'  => 'You are already unsubscribed.',
        'not-subscribed' => 'Error! Mail can not be sent currently, please try again later.',
    ],

    'search' => [
        'no-results'          => 'No Results Found',
        'page-title'          => config('app.name').' - Search',
        'found-results'       => 'Search Results Found',
        'found-result'        => 'Search Result Found',
        'analysed-keywords'   => 'Analysed Keywords',
        'image-search-option' => 'Image Search Option',
    ],

    'reviews' => [
        'title'                     => 'Title',
        'add-review-page-title'     => 'Add Review',
        'write-review'              => 'Write a review',
        'review-title'              => 'Give your review a title',
        'product-review-page-title' => 'Product Review',
        'rating-reviews'            => 'Rating & Reviews',
        'submit'                    => 'SUBMIT',
        'delete-all'                => 'All Reviews has deleted Succesfully',
        'ratingreviews'             => ':rating Ratings & :review Reviews',
        'star'                      => 'Star',
        'percentage'                => ':percentage %',
        'id-star'                   => 'star',
        'name'                      => 'Name',
    ],

    'customer' => [
        'compare' => [
            'text'                  => 'Compare',
            'compare_similar_items' => 'Compare Similar Items',
            'add-tooltip'           => 'Add product to compare list',
            'added'                 => 'Item successfully added to compare list',
            'already_added'         => 'Item already added to compare list',
            'removed'               => 'Item successfully removed from compare list',
            'removed-all'           => 'All items successfully removed from compare list',
            'confirm-remove-all'    => 'Are you sure you want to delete all compare items?',
            'empty-text'            => "You don't have any items in your compare list",
            'product_image'         => 'Product Image',
            'actions'               => 'Actions',
        ],

        'signup-text' => [
            'account_exists' => 'Already have an account',
            'title'          => 'Sign In',
        ],

        'signup-form' => [
            'page-title'                  => 'Create New Customer Account',
            'title'                       => 'Sign Up',
            'firstname'                   => 'First Name',
            'lastname'                    => 'Last Name',
            'email'                       => 'Email',
            'password'                    => 'Password',
            'confirm_pass'                => 'Confirm Password',
            'button_title'                => 'Register',
            'agree'                       => 'Agree',
            'terms'                       => 'Terms',
            'conditions'                  => 'Conditions',
            'using'                       => 'by using this website',
            'agreement'                   => 'Agreement',
            'subscribe-to-newsletter'     => 'Subscribe to newsletter',
            'success'                     => 'Account created successfully.',
            'success-verify'              => 'Account created successfully, an e-mail has been sent for verification.',
            'success-verify-email-unsent' => 'Account created successfully, but verification e-mail unsent.',
            'failed'                      => 'Error! Can not create your account, pleae try again later.',
            'already-verified'            => 'Your account is already verified Or please try sending a new verification email again.',
            'verification-not-sent'       => 'Error! Problem in sending verification email, please try again later.',
            'verification-sent'           => 'Verification email sent',
            'verified'                    => 'Your account has been verified, try to login now.',
            'verify-failed'               => 'We cannot verify your mail account.',
            'dont-have-account'           => 'You do not have account with us.',
            'customer-registration'       => 'Customer Registered Successfully',
        ],

        'login-text' => [
            'no_account' => 'Do not have account',
            'title'      => 'Sign Up',
        ],

        'login-form' => [
            'page-title'          => 'Customer Login',
            'title'               => 'Sign In',
            'email'               => 'Email',
            'password'            => 'Password',
            'forgot_pass'         => 'Forgot Password?',
            'button_title'        => 'Sign In',
            'remember'            => 'Remember Me',
            'footer'              => '© Copyright :year Webkul Software, All rights reserved',
            'invalid-creds'       => 'Please check your credentials and try again.',
            'verify-first'        => 'Verify your email account first.',
            'not-activated'       => 'Your activation seeks admin approval',
            'resend-verification' => 'Resend verification mail again',
            'show-password'       => 'Show Password',
        ],

        'verification-form' => [
            'id-number-verified'  => 'ID Verified',
            'id-number-not-valid' => 'ID Verification Failed',
        ],

        'forgot-password' => [
            'title'      => 'Recover Password',
            'email'      => 'Email',
            'submit'     => 'Send Password Reset Email',
            'page_title' => 'Forgot your password ?',
        ],

        'reset-password' => [
            'title'            => 'Reset Password',
            'email'            => 'Registered Email',
            'password'         => 'Password',
            'confirm-password' => 'Confirm Password',
            'back-link-title'  => 'Back to Sign In',
            'submit-btn-title' => 'Reset Password',
        ],

        'account' => [
            'dashboard' => 'Edit Profile',
            'menu'      => 'Menu',
            'general'   => [
                'no'  => 'No',
                'yes' => 'Yes',
            ],
            'profile' => [
                'index' => [
                    'page-title' => 'Profile',
                    'title'      => 'Profile',
                    'edit'       => 'Edit',
                ],
                'edit-success' => 'Profile updated successfully.',
                'edit-fail'    => 'Error! Profile cannot be updated, please try again later.',
                'unmatch'      => 'The old password does not match.',
                'fname'        => 'First Name',
                'lname'        => 'Last Name',
                'gender'       => 'Gender',
                'other'        => 'Other',
                'male'         => 'Male',
                'female'       => 'Female',
                'dob'          => 'Date Of Birth',
                'phone'        => 'Phone',
                'email'        => 'Email',
                'opassword'    => 'Old Password',
                'password'     => 'Password',
                'cpassword'    => 'Confirm Password',
                'submit'       => 'Update Profile',
                'edit-profile' => [
                    'title'      => 'Edit Profile',
                    'page-title' => 'Edit Profile',
                ],
            ],
            'address' => [
                'index' => [
                    'page-title'     => 'Address',
                    'title'          => 'Address',
                    'add'            => 'Add Address',
                    'edit'           => 'Edit',
                    'empty'          => 'You do not have any saved addresses here, please try to create it by clicking the add button.',
                    'create'         => 'Create Address',
                    'delete'         => 'Delete',
                    'make-default'   => 'Make Default',
                    'default'        => 'Default',
                    'contact'        => 'Contact',
                    'confirm-delete' => 'Do you really want to delete this address?',
                    'default-delete' => 'Default address cannot be changed.',
                    'enter-password' => 'Enter Your Password.',
                ],
                'create' => [
                    'page-title'     => 'Add Address',
                    'company_name'   => 'Company name',
                    'first_name'     => 'First name',
                    'last_name'      => 'Last name',
                    'vat_id'         => 'Vat id',
                    'vat_help_note'  => '[Note: Use Country Code with VAT Id. Eg. INV01234567891]',
                    'title'          => 'Add Address',
                    'street-address' => 'Street Address',
                    'country'        => 'Country',
                    'state'          => 'State',
                    'select-state'   => 'Select a region, state or province',
                    'city'           => 'City',
                    'postcode'       => 'Postal Code',
                    'phone'          => 'Phone',
                    'submit'         => 'Save Address',
                    'success'        => 'Address have been successfully added.',
                    'error'          => 'Address cannot be added.',
                ],
                'edit' => [
                    'page-title'     => 'Edit Address',
                    'company_name'   => 'Company name',
                    'first_name'     => 'First name',
                    'last_name'      => 'Last name',
                    'vat_id'         => 'Vat id',
                    'title'          => 'Edit Address',
                    'street-address' => 'Street Address',
                    'submit'         => 'Save Address',
                    'success'        => 'Address updated successfully.',
                ],
                'delete' => [
                    'success'        => 'Address successfully deleted',
                    'failure'        => 'Address cannot be deleted',
                    'wrong-password' => 'Wrong Password !',
                ],
                'default-address' => 'Default Address',
            ],
            'order' => [
                'index' => [
                    'page-title'      => 'Orders',
                    'title'           => 'Orders',
                    'order_id'        => 'Order ID',
                    'date'            => 'Date',
                    'status'          => 'Status',
                    'total'           => 'Total',
                    'order_number'    => 'Order Number',
                    'processing'      => 'Processing',
                    'completed'       => 'Completed',
                    'canceled'        => 'Canceled',
                    'closed'          => 'Closed',
                    'pending'         => 'Pending',
                    'pending-payment' => 'Pending Payment',
                    'fraud'           => 'Fraud',
                ],
                'view' => [
                    'page-tile'           => 'Order #:order_id',
                    'info'                => 'Information',
                    'placed-on'           => 'Placed On',
                    'products-ordered'    => 'Products Ordered',
                    'invoices'            => 'Invoices',
                    'shipments'           => 'Shipments',
                    'SKU'                 => 'SKU',
                    'product-name'        => 'Name',
                    'qty'                 => 'Qty',
                    'item-status'         => 'Item Status',
                    'item-ordered'        => 'Ordered (:qty_ordered)',
                    'item-invoice'        => 'Invoiced (:qty_invoiced)',
                    'item-shipped'        => 'shipped (:qty_shipped)',
                    'item-minted'         => 'Minted (:qty_shipped)',
                    'item-canceled'       => 'Canceled (:qty_canceled)',
                    'item-refunded'       => 'Refunded (:qty_refunded)',
                    'price'               => 'Price',
                    'total'               => 'Total',
                    'subtotal'            => 'Subtotal',
                    'shipping-handling'   => 'Shipping & Handling',
                    'tax'                 => 'Tax',
                    'discount'            => 'Discount',
                    'tax-percent'         => 'Tax Percent',
                    'tax-amount'          => 'Tax Amount',
                    'discount-amount'     => 'Discount Amount',
                    'grand-total'         => 'Grand Total',
                    'total-paid'          => 'Total Paid',
                    'total-refunded'      => 'Total Refunded',
                    'total-due'           => 'Total Due',
                    'shipping-address'    => 'Shipping Address',
                    'billing-address'     => 'Billing Address',
                    'shipping-method'     => 'Shipping Method',
                    'payment-method'      => 'Payment Method',
                    'individual-invoice'  => 'Invoice #:invoice_id',
                    'individual-shipment' => 'Shipment #:shipment_id',
                    'print'               => 'Print',
                    'invoice-id'          => 'Invoice Id',
                    'order-id'            => 'Order Id',
                    'order-date'          => 'Order Date',
                    'invoice-date'        => 'Invoice Date',
                    'payment-terms'       => 'Payment Terms',
                    'bill-to'             => 'Bill to',
                    'ship-to'             => 'Ship to',
                    'contact'             => 'Contact',
                    'refunds'             => 'Refunds',
                    'individual-refund'   => 'Refund #:refund_id',
                    'adjustment-refund'   => 'Adjustment Refund',
                    'adjustment-fee'      => 'Adjustment Fee',
                    'cancel-btn-title'    => 'Cancel',
                    'tracking-number'     => 'Tracking Number',
                    'cancel-confirm-msg'  => 'Are you sure you want to cancel this order ?',
                ],
            ],
            'wishlist' => [
                'page-title'           => 'Wishlist',
                'title'                => 'Wishlist',
                'deleteall'            => 'Delete All',
                'confirm-delete-all'   => 'Are you sure you want to delete all wishlist?',
                'moveall'              => 'Move All Products To Cart',
                'move-to-cart'         => 'Move To Cart',
                'error'                => 'Cannot add product to wishlist due to unknown problems, please checkback later',
                'add'                  => 'Item successfully added to wishlist',
                'remove'               => 'Item successfully removed from wishlist',
                'add-wishlist-text'    => 'Add product to wishlist',
                'remove-wishlist-text' => 'Remove product from wishlist',
                'moved'                => 'Item successfully moved To cart',
                'option-missing'       => 'Product options are missing, so item can not be moved to the wishlist.',
                'move-error'           => 'Item cannot be moved to wishlist, Please try again later',
                'success'              => 'Item successfully added to wishlist',
                'failure'              => 'Item cannot be added to wishlist, Please try again later',
                'already'              => 'Item already present in your wishlist',
                'removed'              => 'Item successfully removed from wishlist',
                'remove-fail'          => 'Item cannot Be removed from wishlist, Please try again later',
                'empty'                => 'You do not have any items in your wishlist',
                'remove-all-success'   => 'All the items from your wishlist have been removed',
                'save'                 => 'Save',
                'share'                => 'Share',
                'share-wishlist'       => 'Share Wishlist',
                'wishlist-sharing'     => 'Wishlist Sharing',
                'shared-link'          => 'Shared Link',
                'copy'                 => 'Copy',
                'copy-link'            => 'Copy Link',
                'copied'               => 'Copied!',
                'visibility'           => 'Visibility',
                'public'               => 'Public',
                'private'              => 'Private',
                'enable'               => 'Enable',
                'disable'              => 'Disable',
                'customer-name'        => ':name\'s Shared Wishlist',
                'enable-wishlist-info' => 'Enable wishlist sharing to get the link.',
                'update-message'       => 'Shared wishlist settings updated successfully',
            ],
            'downloadable_products' => [
                'title'               => 'Downloadable Products',
                'order-id'            => 'Order Id',
                'date'                => 'Date',
                'name'                => 'Title',
                'status'              => 'Status',
                'pending'             => 'Pending',
                'available'           => 'Available',
                'expired'             => 'Expired',
                'remaining-downloads' => 'Remaining Downloads',
                'unlimited'           => 'Unlimited',
                'download-error'      => 'Download link has been expired.',
                'payment-error'       => 'Payment has not been done for this download.',
            ],
            'review' => [
                'index' => [
                    'title'      => 'Reviews',
                    'page-title' => 'Reviews',
                ],
                'view' => [
                    'page-tile' => 'Review #:id',
                ],
                'delete' => [
                    'confirmation-message' => 'Are you sure you want to delete this review?',
                ],
                'delete-all' => [
                    'title'                => 'Delete All',
                    'confirmation-message' => 'Are you sure you want to delete all the reviews?',
                ],
            ],
        ],
    ],

    'products' => [
        'layered-nav-title'        => 'Shop By',
        'price-label'              => 'As low as',
        'remove-filter-link-title' => 'Clear All',
        'filter-to'                => 'to',
        'sort-by'                  => 'Sort By',
        'from-a-z'                 => 'From A-Z',
        'from-z-a'                 => 'From Z-A',
        'newest-first'             => 'Newest First',
        'oldest-first'             => 'Oldest First',
        'cheapest-first'           => 'Cheapest First',
        'expensive-first'          => 'Expensive First',
        'show'                     => 'Show',
        'pager-info'               => 'Showing :showing of :total Items',
        'description'              => 'Description',
        'specification'            => 'Specification',
        'total-reviews'            => ':total Reviews',
        'total-rating'             => ':total_rating Ratings & :total_reviews Reviews',
        'by'                       => 'By :name',
        'up-sell-title'            => 'We found other products you might like!',
        'related-product-title'    => 'Related Products',
        'cross-sell-title'         => 'More choices',
        'reviews-title'            => 'Ratings & Reviews',
        'write-review-btn'         => 'Write Review',
        'choose-option'            => 'Choose an option',
        'sale'                     => 'Sale',
        'new'                      => 'New',
        'empty'                    => 'No products available in this category',
        'add-to-cart'              => 'Add To Cart',
        'book-now'                 => 'Book Now',
        'buy-now'                  => 'Buy Now',
        'whoops'                   => 'Whoops!',
        'quantity'                 => 'Quantity',
        'in-stock'                 => 'In Stock',
        'out-of-stock'             => 'Out Of Stock',
        'view-all'                 => 'View All',
        'select-above-options'     => 'Please select above options first.',
        'less-quantity'            => 'Quantity can not be less than one.',
        'samples'                  => 'Samples',
        'links'                    => 'Links',
        'sample'                   => 'Sample',
        'name'                     => 'Name',
        'qty'                      => 'Qty',
        'starting-at'              => 'Starting at',
        'customize-options'        => 'Customize Options',
        'choose-selection'         => 'Choose a selection',
        'your-customization'       => 'Your Customization',
        'total-amount'             => 'Total Amount',
        'none'                     => 'None',
        'available-for-order'      => 'Available for Order',
        'settings'                 => 'Settings',
        'compare_options'          => 'Compare Options',
        'wishlist-options'         => 'Wishlist Options',
        'offers'                   => 'Buy :qty for :price each and save :discount%',
        'tax-inclusive'            => 'Inclusive of all taxes',
    ],

    'buynow' => [
        'no-options' => 'Please select options before buying this product.',
    ],

    'checkout' => [
        'cart' => [
            'integrity' => [
                'missing_fields'         => 'Some required fields missing for this product.',
                'missing_options'        => 'Options are missing for this product.',
                'missing_links'          => 'Downloadable links are missing for this product.',
                'qty_missing'            => 'Atleast one product should have more than 1 quantity.',
                'qty_impossible'         => 'Cannot add more than one of these products to cart.',
                'select_hourly_duration' => 'Select a slot duration of one hour.',
            ],
            'create-error'             => 'Encountered some issue while making cart instance.',
            'title'                    => 'Shopping Cart',
            'empty'                    => 'Your shopping cart is empty',
            'update-cart'              => 'Update Cart',
            'continue-shopping'        => 'Continue Shopping',
            'remove-all-items'         => 'Remove all items',
            'confirm-action'           => 'Confirm this action?',
            'continue-registration'    => 'Continue Registration',
            'proceed-to-checkout'      => 'Proceed To Checkout',
            'remove'                   => 'Remove',
            'buy'                      => 'Buy',
            'remove-link'              => 'Remove',
            'move-to-wishlist'         => 'Move to Wishlist',
            'move-to-wishlist-success' => 'Item moved to wishlist successfully.',
            'move-to-wishlist-error'   => 'Cannot move item to wishlist, please try again later.',
            'add-config-warning'       => 'Please select option before adding to cart.',
            'quantity'                 => [
                'quantity'          => 'Quantity',
                'success'           => 'Cart Item(s) successfully updated.',
                'illegal'           => 'Quantity cannot be lesser than one.',
                'inventory_warning' => 'The requested quantity is not available, please try again later.',
                'error'             => 'Cannot update the item(s) at the moment, please try again later.',
            ],
            'item' => [
                'error_remove'       => 'No items to remove from the cart.',
                'success'            => 'Item is successfully added to cart.',
                'success-remove'     => 'Item is successfully removed from the cart.',
                'success-all-remove' => 'All items is successfully removed from the cart.',
                'error-add'          => 'Item cannot be added to cart, please try again later.',
                'inactive'           => 'An item is inactive and was removed from cart.',
                'inactive-add'       => 'Inactive item cannot be added to cart.',
            ],
            'quantity-error'      => 'Requested quantity is not available.',
            'cart-subtotal'       => 'Cart Subtotal',
            'cart-remove-action'  => 'Do you really want to do this ?',
            'partial-cart-update' => 'Only some of the product(s) were updated',
            'link-missing'        => '',
            'event'               => [
                'expired' => 'This event has been expired.',
            ],
            'minimum-order-message'     => 'Minimum order amount is :amount',
            'add-new-address'           => 'Please add a new address before placing the order.',
            'suspended-account-message' => 'Your account has been suspended.',
            'inactive-account-message'  => 'Your account has been inactive.',
            'check-shipping-address'    => 'Please check shipping address.',
            'check-billing-address'     => 'Please check billing address.',
            'specify-shipping-method'   => 'Please specify shipping method.',
            'specify-payment-method'    => 'Please specify payment method.',
            'rule-applied'              => 'Cart rule applied',
        ],

        'onepage' => [
            'title'               => 'Checkout',
            'information'         => 'Information',
            'shipping'            => 'Shipping',
            'payment'             => 'Payment',
            'complete'            => 'Complete',
            'review'              => 'Review',
            'sign-in'             => 'Sign In',
            'company-name'        => 'Company Name',
            'first-name'          => 'First Name',
            'last-name'           => 'Last Name',
            'email'               => 'Email',
            'address1'            => 'Street Address',
            'city'                => 'City',
            'state'               => 'State',
            'select-state'        => 'Select a region, state or province',
            'postcode'            => 'Zip/Postcode',
            'phone'               => 'Telephone',
            'country'             => 'Country',
            'order-summary'       => 'Order Summary',
            'use_for_shipping'    => 'Ship to this address',
            'continue'            => 'Continue',
            'shipping-method'     => 'Select Shipping Method',
            'payment-methods'     => 'Select Payment Method',
            'payment-method'      => 'Payment Method',
            'payment-method-name' => [
                'PayPal Smart Button' => 'PayPal Smart Button',
                'PayPal'              => 'PayPal',
                'Cash On Delivery'    => 'Cash On Delivery',
                'Money Transfer'      => 'Money Transfer',
                'Paypal Standard'     => 'PayPal Standard',
                'IyzicoPayment'       => 'Pay with İyzico',
                'moneytransfer'       => 'Money Transfer',
                'paywithcrypto'       => 'Pay with Cryptocurrency',
            ],
            'summary'             => 'Order Summary',
            'price'               => 'Price',
            'quantity'            => 'Quantity',
            'billing-address'     => 'Billing Address',
            'shipping-address'    => 'Shipping Address',
            'contact'             => 'Contact',
            'place-order'         => 'Place Order',
            'new-address'         => 'Add New Address',
            'save_as_address'     => 'Save this address',
            'apply-coupon'        => 'Apply Coupon',
            'amt-payable'         => 'Amount Payable',
            'got'                 => 'Got',
            'free'                => 'Free',
            'coupon-used'         => 'Coupon Used',
            'applied'             => 'Applied',
            'back'                => 'Back',
            'cash-desc'           => 'Cash On Delivery',
            'money-desc'          => 'Money Transfer',
            'paypal-desc'         => 'Paypal Standard',
            'free-desc'           => 'This is a free shipping',
            'flat-desc'           => 'This is a flat rate',
            'password'            => 'Password',
            'login-exist-message' => 'You already have an account with us, Sign in or continue as guest.',
            'enter-coupon-code'   => 'Enter Coupon Code',
        ],
        'total' => [
            'order-summary'          => 'Order Summary',
            'sub-total'              => 'Items',
            'grand-total'            => 'Grand Total',
            'delivery-charges'       => 'Delivery Charges',
            'tax'                    => 'Tax',
            'discount'               => 'Discount',
            'price'                  => 'price',
            'disc-amount'            => 'Amount discounted',
            'new-grand-total'        => 'New Grand Total',
            'coupon'                 => 'Coupon',
            'coupon-applied'         => 'Applied Coupon',
            'remove-coupon'          => 'Remove Coupon',
            'cannot-apply-coupon'    => 'Cannot Apply Coupon',
            'invalid-coupon'         => 'Coupon code is invalid.',
            'success-coupon'         => 'Coupon code applied successfully.',
            'coupon-apply-issue'     => 'Coupon code can\'t be applied.',
            'coupon-already-applied' => 'Coupon code already applied.',
        ],
        'success' => [
            'title'         => 'Order successfully placed',
            'thanks'        => 'Thank you for your order!',
            'order-id-info' => 'Your order id is #:order_id',
            'info'          => 'We will email you, your order details and tracking information',
        ],
    ],

    'webkul' => [
        'copy-right' => '© Copyright :year Webkul Software, All rights reserved',
    ],

    'response' => [
        'create-success' => ':name created successfully.',
        'update-success' => ':name updated successfully.',
        'delete-success' => ':name deleted successfully.',
        'submit-success' => ':name submitted successfully.',
    ],
];
