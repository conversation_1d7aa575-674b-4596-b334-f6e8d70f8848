<?php

namespace Webkul\Shop\Http\Controllers;

use App\ContactRequest;
use Webkul\CMS\Repositories\CmsRepository;
use Webkul\Core\Repositories\SliderRepository;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Product\Repositories\SearchRepository;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected SliderRepository $sliderRepository,
        protected SearchRepository $searchRepository,
        protected CmsRepository $cmsRepository,
        protected ProductRepository $productRepository,
    ) {
        parent::__construct();
    }

    /**
     * Loads the home page for the storefront.
     *
     * @return \Illuminate\View\View
     */
    public function index(): mixed
    {

        $sliderData        = $this->sliderRepository->getActiveSliders();
        $productRepository = $this->productRepository->find(config('app.app_product_id'))?->attribute_values?->mapWithKeys(function ($item) {
            return [$item->attribute()->where('id', $item->attribute_id)->first()->code => [
                'attribute' => $item,
                'price'     => $item->float_value,
                'value'     => $item->text_value,
            ]];
        });

        return view($this->_config['view'], compact('sliderData', 'productRepository'));

    }

    /** end index(): mixed **/
    public function detailPage($urlKey)
    {
        $page = $this->cmsRepository->findByUrlKeyOrFail($urlKey);

        return view($this->_config['view'], compact('page'));
    }

    public function whatsMilyem($urlKey)
    {
        return view($this->_config['view']);
    }

    public function aboutUs()
    {
        return view($this->_config['view']);
    }

    public function physicalDelivery()
    {
        return view($this->_config['view']);
    }

    public function ourVision()
    {
        return view($this->_config['view']);
    }

    public function faq()
    {
        return view($this->_config['view']);
    }

    public function contactUs()
    {
        return view($this->_config['view']);
    }

    public function mailingPage()
    {
        return view($this->_config['view']);
    }

    public function contactUsSubmit(\App\Http\Requests\ContactRequest $request)
    {
        $contact = ContactRequest::create($request->only((new ContactRequest)->getFillable()));

        session()->flash('success', __('velocity::app-static.homepage.contact.submit-success'));

        return redirect()->back();
    }

    /**
     * Loads the home page for the storefront if something wrong.
     *
     * @return \Exception
     */
    public function notFound()
    {
        abort(404);
    }

    /**
     * Upload image for product search with machine learning.
     *
     * @return \Illuminate\Http\Response
     */
    public function upload()
    {
        return $this->searchRepository->uploadSearchImage(request()->all());
    }
}
