<?php

namespace Webkul\Shop\Http\Controllers;

use Cart;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Webkul\CartRule\Models\CartRule;
use Webkul\CartRule\Models\CartRuleCoupon;
use Webkul\CartRule\Repositories\CartRuleCouponRepository;
use Webkul\CatalogRule\Models\CatalogRule;
use Webkul\CatalogRule\Models\CatalogRuleProduct;
use Webkul\Checkout\Models\CartItem;
use Webkul\Customer\Repositories\WishlistRepository;
use Webkul\Product\Repositories\ProductRepository;

class CartController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Customer\Repositories\CartItemRepository  $wishlistRepository
     * @return void
     */
    public function __construct(
        protected WishlistRepository $wishlistRepository,
        protected ProductRepository $productRepository,
        protected CartRuleCouponRepository $cartRuleCouponRepository
    ) {
        $this->middleware('throttle:5,1')->only('applyCoupon');

        $this->middleware('customer')->only('moveToWishlist');

        parent::__construct();
    }

    /**
     * Method to populate the cart page which will be populated before the checkout process.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        Cart::collectTotals();

        if (isset(Cart::getCart()->applied_cart_rule_ids)) {

            $cart_rule = CartRuleCoupon::where('id', Cart::getCart()->applied_cart_rule_ids)->first();

            if (! empty($cart_rule)) {
                $coupon      = CartRule::where('id', $cart_rule->cart_rule_id)->first();
                $coupon_name = $coupon->name;
                $coupon_desc = $coupon->description;
                // dd($coupon_desc);

                return view($this->_config['view'])->with([
                    'cart'        => Cart::getCart(),
                    'coupon_name' => $coupon_name,
                    'coupon_desc' => $coupon_desc,
                ]);

            }

        }

        if (Cart::getCart()) {

            $cart          = Cart::getCart();
            $items         = CartItem::where('cart_id', $cart->id)->get();
            $catalog_rules = [];
            $products      = [];
            $product_ids   = [];
            if ($items) {
                $today = new \DateTime;

                foreach ($items as $item) {
                    $product = CatalogRuleProduct::where('product_id', $item->product_id)->first();
                    $dizi[]  = $product;

                    $product_id    = $item->product_id;
                    $product_ids[] = $product_id;
                    $base_total    = $item->base_total;

                    if (! empty($product)) {
                        $ends_till      = $product->ends_till;
                        $ends_till_date = new \DateTime($ends_till);

                        if ($ends_till_date > $today) {
                            $catalog_rule_id = $product->catalog_rule_id;
                            $catalog_rule    = CatalogRule::where('id', $catalog_rule_id)->first();
                            $catalog_rules[] = $catalog_rule;
                            // return view($this->_config['view'])->with([
                            //     'cart' => Cart::getCart(),
                            //     'catalog_rule' => $catalog_rule,
                            //     'base_total' => $item->base_total,
                            //     'product_id' => $item->product_id,
                            //     'catalog_rule_id' => $catalog_rule_id,
                            //     // 'customer_group_id' => $customer_group_id,
                            // ]);
                        }
                    }

                }

                // dd($catalog_rules);
                // dd($product_ids);
                // dd($dizi);
                return view($this->_config['view'])->with([
                    'cart'          => Cart::getCart(),
                    'catalog_rules' => $catalog_rules,
                    'base_total'    => $base_total,
                    'product_id'    => $product_id,
                    'product_ids'   => $product_ids,
                    'dizi'          => $dizi,
                    // 'catalog_rule_id' => $catalog_rule_id,
                    // 'customer_group_id' => $customer_group_id,
                ]);

            }

        }

        // if(isset(Cart::getCart()->applied_cart_rule_ids)){

        //     $cart_rule = CartRuleCoupon::where('id', Cart::getCart()->applied_cart_rule_ids)->first();

        //     if(!empty($cart_rule)){
        //         $coupon = CartRule::where('id', $cart_rule->cart_rule_id)->first();

        //         $coupon_name = $coupon->name;
        //         $coupon_desc = $coupon->description;

        //         return view($this->_config['view'])->with([
        //             'cart' => Cart::getCart(),
        //             'coupon_name' => $coupon_name,
        //             'coupon_desc' => $coupon_desc,
        //         ]);

        //     }

        // }

        return view($this->_config['view'])->with('cart', Cart::getCart());
    }

    /**
     * Function for guests user to add the product in the cart.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function add($id)
    {
        try {
            Cart::deactivateCurrentCartIfBuyNowIsActive();

            $result = Cart::addProduct($id, request()->all());

            if (request()->input('redirect_url')) {
                return redirect(request()->input('redirect_url'));
            }

            if ($this->onFailureAddingToCart($result)) {
                return redirect()->back();
            }

            session()->flash('success', __('shop::app.checkout.cart.item.success'));

            if ($customer = auth()->guard('customer')->user()) {
                $this->wishlistRepository->deleteWhere([
                    'product_id'  => $id,
                    'customer_id' => $customer->id,
                ]);
            }

            if (request()->get('is_buy_now')) {
                Event::dispatch('shop.item.buy-now', $id);

                return redirect()->route('shop.checkout.onepage.index');
            }
        } catch (\Exception $e) {
            session()->flash('warning', __($e->getMessage()));

            $product = $this->productRepository->findOrFail($id);

            Log::error(
                'Shop CartController: '.$e->getMessage(),
                [
                    'product_id' => $id,
                    'cart_id'    => cart()->getCart() ?? 0,
                ]
            );

            return redirect()->route('shop.productOrCategory.index', $product->url_key);
        }

        return redirect()->back();
    }

    /**
     * Removes the item from the cart if it exists.
     *
     * @param  int  $itemId
     * @return \Illuminate\Http\Response
     */
    public function remove($itemId)
    {
        $result = Cart::removeItem($itemId);

        if ($result) {
            session()->flash('success', trans('shop::app.checkout.cart.item.success-remove'));
        }

        return redirect()->back();
    }

    /**
     * Removes the item from the cart if it exists.
     *
     * @return \Illuminate\Http\Response
     */
    public function removeAllItems()
    {
        $result = Cart::removeAllItems();

        if ($result) {
            session()->flash('success', trans('shop::app.checkout.cart.item.success-all-remove'));
        }

        return redirect()->back();
    }

    /**
     * Updates the quantity of the items present in the cart.
     *
     * @return \Illuminate\Http\Response
     */
    public function updateBeforeCheckout()
    {
        try {
            $cart   = Cart::getCart();
            $result = Cart::updateItems([
                'qty' => [
                    $cart->items->first()->id => request()->input('count'),
                ],
            ]);

            if ($result) {
                session()->flash('success', trans('shop::app.checkout.cart.quantity.success'));
            }
        } catch (\Exception $e) {
            session()->flash('error', trans($e->getMessage()));
        }

        return redirect()->back();
    }

    /**
     * Function to move a already added product to wishlist will run only on customer authentication.
     *
     * @param  int  $id
     * @return mixed
     */
    public function moveToWishlist($id)
    {
        $result = Cart::moveToWishlist($id);

        if ($result) {
            session()->flash('success', trans('shop::app.checkout.cart.move-to-wishlist-success'));
        } else {
            session()->flash('warning', trans('shop::app.checkout.cart.move-to-wishlist-error'));
        }

        return redirect()->back();
    }

    /**
     * Apply coupon to the cart.
     *
     * @return \Illuminate\Http\Response
     */
    public function applyCoupon()
    {
        $couponCode = request()->get('code');

        try {
            if (strlen($couponCode)) {
                $coupon = $this->cartRuleCouponRepository->findOneByField('code', $couponCode);

                if ($coupon->cart_rule->status) {
                    if (Cart::getCart()->coupon_code == $couponCode) {
                        return response()->json([
                            'success' => false,
                            'message' => trans('shop::app.checkout.total.coupon-already-applied'),
                        ]);
                    }

                    Cart::setCouponCode($couponCode)->collectTotals();

                    if (Cart::getCart()->coupon_code == $couponCode) {
                        return response()->json([
                            'success' => true,
                            'message' => trans('shop::app.checkout.total.success-coupon'),
                        ]);
                    }
                }
            }

            return response()->json([
                'success' => false,
                'message' => trans('shop::app.checkout.total.invalid-coupon'),
            ]);
        } catch (\Exception $e) {
            report($e);

            return response()->json([
                'success' => false,
                'message' => trans('shop::app.checkout.total.coupon-apply-issue'),
            ]);
        }
    }

    /**
     * Remove applied coupon from the cart.
     *
     * @return \Illuminate\Http\Response
     */
    public function removeCoupon()
    {
        Cart::removeCouponCode()->collectTotals();

        return response()->json([
            'success' => true,
            'message' => trans('shop::app.checkout.total.remove-coupon'),
        ]);
    }

    /**
     * Returns true, if result of adding product to cart
     * is an array and contains a key "warning" or "info".
     *
     * @param  array  $result
     */
    private function onFailureAddingToCart($result): bool
    {
        if (! is_array($result)) {
            return false;
        }

        if (isset($result['warning'])) {
            session()->flash('warning', $result['warning']);
        } elseif (isset($result['info'])) {
            session()->flash('info', $result['info']);
        } else {
            return false;
        }

        return true;
    }
}
