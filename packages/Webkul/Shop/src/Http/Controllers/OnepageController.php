<?php

namespace Webkul\Shop\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Webkul\CartRule\Models\CartRule;
use Webkul\CartRule\Models\CartRuleCoupon;
use Webkul\CatalogRule\Models\CatalogRule;
use Webkul\CatalogRule\Models\CatalogRuleProduct;
use Webkul\Checkout\Facades\Cart;
use Webkul\Checkout\Models\CartItem;
use Webkul\Customer\Models\Customer;
use Webkul\Customer\Notifications\CustomerOrderNotification;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Payment\Facades\Payment;
use Webkul\Sales\Models\Order;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Shipping\Facades\Shipping;

class OnepageController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Attribute\Repositories\OrderRepository  $orderRepository
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected CustomerRepository $customerRepository
    ) {
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        Event::dispatch('checkout.load.index');

        if (
            ! auth()->guard('customer')->check()
        ) {
            return redirect()->route('customer.session.index');
        }

        if (
            auth()->guard('customer')->check()
            && auth()->guard('customer')->user()->is_suspended
        ) {
            session()->flash('warning', trans('shop::app.checkout.cart.suspended-account-message'));

            return redirect()->route('shop.checkout.cart.index');
        }

        if (Cart::hasError()) {
            return redirect()->route('shop.checkout.cart.index');
        }

        $cart = Cart::getCart();

        if (
            (
                ! auth()->guard('customer')->check()
                && $cart->hasDownloadableItems()
            )
            || (
                ! auth()->guard('customer')->check()
                && ! $cart->hasGuestCheckoutItems()
            )
        ) {
            return redirect()->route('customer.session.index');
        }

        $minimumOrderAmount = (float) core()->getConfigData('sales.orderSettings.minimum-order.minimum_order_amount') ?? 0;

        if (! $cart->checkMinimumOrder()) {
            session()->flash('warning', trans('shop::app.checkout.cart.minimum-order-message', ['amount' => core()->currency($minimumOrderAmount)]));

            return redirect()->back();
        }

        if (
            auth()->guard('customer')->user()->addresses->count() == 0
        ) {
            session()->flash('error', trans('shop::app.checkout.cart.add-new-address'));

            return redirect()->route('customer.profile.index');
        }

        Cart::collectTotals();

        $cart          = Cart::getCart();
        $cartTotal     = 0;
        $cartCurrency  = $cart->cart_currency_code;
        $cartQuantity  = (float) $cart->items_qty ?? 0;
        $walletAddress = auth()->guard('customer')->user()->wallet;

        foreach ($cart['items'] as $item) {
            $cartTotal += $item['total'];
        }

        if ($cart) {

            // $cart_id = CartItem::where('cart_id', $cart->id)->get();
            $items = CartItem::where('cart_id', $cart->id)->get();
            // $products = CatalogRuleProduct::where('product_id', $cart->product_id)->get();
            if ($items) {
                $today = new \DateTime;

                foreach ($items as $item) {
                    $product = CatalogRuleProduct::where('product_id', $item->product_id)->first();

                    if (! empty($product)) {
                        $ends_till      = $product->ends_till;
                        $ends_till_date = new \DateTime($ends_till);

                        $product_id = $item->product_id;
                        $base_total = $item->base_total;

                        if ($ends_till_date > $today) {
                            $catalog_rule_id = $product->catalog_rule_id;
                            $catalog_rule    = CatalogRule::where('id', $catalog_rule_id)->first();

                            return view($this->_config['view'])->with([
                                'cart'         => Cart::getCart(),
                                'catalog_rule' => $catalog_rule,
                                'base_total'   => $base_total,
                            ]);
                            // return response()->json([
                            //     'html' => view('shop::checkout.total.summary', compact('cart','catalog_rule','base_total'))->render(),
                            // ]);

                        }

                    }

                }

                // foreach ($products as $product) {

                //     $ends_till = $product->ends_till;
                //     $ends_till_date = new \DateTime($ends_till);

                //     if($ends_till_date > $today){
                //         $catalog_rule_id = $product->catalog_rule_id;
                //         $catalog_rule = CatalogRule::where('id', $catalog_rule_id)->first();
                //         return view($this->_config['view'])->with([
                //             'cart' => Cart::getCart(),
                //             'catalog_rule' => $catalog_rule,
                //             'base_total' => $base_total,
                //         ]);
                //         // return response()->json([
                //         //     'html' => view('shop::checkout.total.summary', compact('cart','catalog_rule','base_total'))->render(),
                //         // ]);

                //     }
                // }
            }

        }

        return view($this->_config['view'], compact('cart', 'cartTotal', 'cartCurrency', 'cartQuantity', 'walletAddress'));
    }

    public function validation()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        if ($cart->grand_total < config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount')) {
            session()->flash('error', 'Minimum order amount is '.config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount').' '.$cart->cart_currency_code);

            return redirect()->route('shop.checkout.onepage.index');
        }

        return redirect()->route('shop.checkout.summary');
    }

    /**
     * Return order short summary.
     *
     * @return \Illuminate\Http\Response
     */
    public function summary()
    {
        $cart = Cart::getCart();

        $cart_rule = CartRuleCoupon::where('id', $cart->applied_cart_rule_ids)->first();

        if ($cart_rule) {
            $coupon = CartRule::where('id', $cart_rule->cart_rule_id)->first();

            $coupon_name = $coupon->name;
            $coupon_desc = $coupon->description;

            return response()->json([
                'html' => view('shop::checkout.total.summary', compact('cart', 'coupon_name', 'coupon_desc'))->render(),
            ]);
        }

        if ($cart) {
            $items = CartItem::where('cart_id', $cart->id)->get();
            // $products = CatalogRuleProduct::where('product_id', $cart->product_id)->get();
            if ($items) {
                $today = new \DateTime;

                foreach ($items as $item) {
                    $product = CatalogRuleProduct::where('product_id', $item->product_id)->first();

                    $product_id = $item->product_id;
                    $base_total = $item->base_total;

                    if (! empty($product)) {
                        $ends_till      = $product->ends_till;
                        $ends_till_date = new \DateTime($ends_till);

                        $product_id = $item->product_id;
                        $base_total = $item->base_total;

                        if ($ends_till_date > $today) {
                            $catalog_rule_id = $product->catalog_rule_id;
                            $catalog_rule    = CatalogRule::where('id', $catalog_rule_id)->first();

                            return response()->json([
                                'html' => view('shop::checkout.total.summary', compact('cart', 'catalog_rule', 'base_total'))->render(),
                            ]);
                        }
                    }
                }

            }
        }

        return response()->json([
            'html' => view('shop::checkout.total.summary', compact('cart'))->render(),
        ]);
    }

    /**
     * Saves customer address.
     *
     * @return \Illuminate\Http\Response
     */
    public function saveAddress(\Illuminate\Support\Facades\Request $request)
    {
        $data = \request()->all();

        if (
            ! auth()->guard('customer')->check()
            && ! Cart::getCart()->hasGuestCheckoutItems()
        ) {
            return response()->json(['redirect_url' => route('customer.session.index')], 403);
        }

        $data['billing']['address1']  = implode(PHP_EOL, array_filter($data['billing']['address1']));
        $data['shipping']['address1'] = implode(PHP_EOL, array_filter($data['shipping']['address1']));

        if (
            Cart::hasError()
            || ! Cart::saveCustomerAddress($data)
        ) {
            return response()->json(['redirect_url' => route('shop.checkout.cart.index')], 403);
        }

        $cart = Cart::getCart();

        Cart::collectTotals();

        if ($cart->haveStockableItems()) {
            if (! $rates = Shipping::collectRates()) {
                return response()->json(['redirect_url' => route('shop.checkout.cart.index')], 403);
            }
        }

        return response()->json(Payment::getSupportedPaymentMethods());
    }

    /**
     * Saves shipping method.
     *
     * @return \Illuminate\Http\Response
     */
    public function saveShipping()
    {
        $shippingMethod = request()->get('shipping_method');

        if (
            Cart::hasError()
            || ! $shippingMethod
            || ! Cart::saveShippingMethod($shippingMethod)
        ) {
            return response()->json(['redirect_url' => route('shop.checkout.cart.index')], 403);
        }

        Cart::collectTotals();

        return response()->json(Payment::getSupportedPaymentMethods());
    }

    /**
     * Saves payment method.
     *
     * @return \Illuminate\Http\Response
     */
    public function savePayment()
    {
        $payment = request()->get('payment')['method'];

        if (
            Cart::hasError()
            || ! $payment
            || ! Cart::savePaymentMethod($payment)
        ) {
            return response()->json(['redirect_url' => route('shop.checkout.cart.index')], 403);
        }

        Cart::collectTotals();

        $cart = Cart::getCart();

        return response()->json([
            'jump_to_section' => 'review',
            'html'            => view('shop::checkout.onepage.review', compact('cart'))->render(),
        ]);
    }

    /**
     * Saves order.
     *
     * @return \Illuminate\Http\Response
     */
    public function saveOrder(Request $request): mixed
    {

        if (Cart::hasError()) {
            return response()->json([
                'redirect_url' => route('shop.checkout.cart.index'),
            ], 403);
        }
        Cart::collectTotals();
        $this->validateOrder();
        $cart = Cart::getCart();

        if ($cart->grand_total < config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount')) {
            return response()->json([
                'error'        => 'Minimum order amount is '.config('app.'.mb_strtolower($cart->items()->first()->product->product_number ?? 'SEED').'_minimum_order_amount').' '.$cart->cart_currency_code,
                'redirect_url' => route('shop.checkout.onepage.index'),
                'timeout'      => null,
            ]);
        }

        if ($redirectUrl = Payment::getRedirectUrl($cart)) {
            return response()->json([
                'success'      => true,
                'redirect_url' => $redirectUrl,
            ]);
        } else {
            session()->flash('error', 'Invalid payment method');

            return redirect()->route('shop.checkout.onepage.index');
        }

    }

    /**
     * Order success page.
     *
     * @return \Illuminate\Http\Response
     */
    public function success()
    {
        if (! $order = session('order')) {
            return redirect()->route('shop.checkout.cart.index');
        }

        return view($this->_config['view'], compact('order'));
    }

    /**
     * Validate order before creation.
     *
     * @return void|\Exception
     */
    public function validateOrder()
    {
        $cart = Cart::getCart();

        $minimumOrderAmount = core()->getConfigData('sales.orderSettings.minimum-order.minimum_order_amount') ?? 0;

        if (
            auth()->guard('customer')->check()
            && auth()->guard('customer')->user()->is_suspended
        ) {
            throw new \Exception(trans('shop::app.checkout.cart.suspended-account-message'));
        }

        if (
            auth()->guard('customer')->user()
            && ! auth()->guard('customer')->user()->status
        ) {
            throw new \Exception(trans('shop::app.checkout.cart.inactive-account-message'));
        }

        if (! $cart->checkMinimumOrder()) {
            throw new \Exception(trans('shop::app.checkout.cart.minimum-order-message', ['amount' => core()->currency($minimumOrderAmount)]));
        }

        if ($cart->haveStockableItems() && ! $cart->shipping_address) {
            throw new \Exception(trans('shop::app.checkout.cart.check-shipping-address'));
        }

        if (! $cart->billing_address) {
            throw new \Exception(trans('shop::app.checkout.cart.check-billing-address'));
        }

        //        if (
        //            $cart->haveStockableItems()
        //            && ! $cart->selected_shipping_rate
        //        ) {
        //            throw new \Exception(trans('shop::app.checkout.cart.specify-shipping-method'));
        //        }

        if (! $cart->payment) {
            throw new \Exception(trans('shop::app.checkout.cart.specify-payment-method'));
        }
    }

    /**
     * Check customer is exist or not.
     *
     * @return \Illuminate\Http\Response
     */
    public function checkExistCustomer()
    {
        $customer = $this->customerRepository->findOneWhere([
            'email' => request()->email,
        ]);

        if (! is_null($customer)) {
            return 'true';
        }

        return 'false';
    }

    /**
     * Login for checkout.
     *
     * @return \Illuminate\Http\Response
     */
    public function loginForCheckout()
    {
        $this->validate(request(), [
            'email' => 'required|email',
        ]);

        if (! auth()->guard('customer')->attempt(request(['email', 'password']))) {
            return response()->json(['error' => trans('shop::app.customer.login-form.invalid-creds')]);
        }

        Cart::mergeCart();

        return response()->json(['success' => 'Login successfully']);
    }

    /**
     * To apply couponable rule requested.
     *
     * @return \Illuminate\Http\Response
     */
    public function applyCoupon()
    {
        $this->validate(request(), [
            'code' => 'string|required',
        ]);

        $code = request()->input('code');

        $result = $this->coupon->apply($code);

        if ($result) {
            Cart::collectTotals();

            return response()->json([
                'success' => true,
                'message' => trans('shop::app.checkout.total.coupon-applied'),
                'result'  => $result,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => trans('shop::app.checkout.total.cannot-apply-coupon'),
            'result'  => null,
        ], 422);
    }

    /**
     * Initiates the removal of couponable cart rule.
     *
     * @return array
     */
    public function removeCoupon()
    {
        $result = $this->coupon->remove();

        if ($result) {
            Cart::collectTotals();

            return response()->json([
                'success' => true,
                'message' => trans('admin::app.promotion.status.coupon-removed'),
                'data'    => [
                    'grand_total' => core()->currency(Cart::getCart()->grand_total),
                ],
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => trans('admin::app.promotion.status.coupon-remove-failed'),
            'data'    => null,
        ], 422);
    }

    /**
     * Check for minimum order.
     *
     * @return \Illuminate\Http\Response
     */
    public function checkMinimumOrder()
    {
        $minimumOrderAmount = (float) core()->getConfigData('sales.orderSettings.minimum-order.minimum_order_amount') ?? 0;

        $status = Cart::checkMinimumOrder();

        return response()->json([
            'status'  => ! $status ? false : true,
            'message' => ! $status ? trans('shop::app.checkout.cart.minimum-order-message', ['amount' => core()->currency($minimumOrderAmount)]) : 'Success',
        ]);
    }

    public function getPaymentMethods()
    {
        return response()->json(Payment::getSupportedPaymentMethods());
    }

    public function orderSuccess($userId, $txId)
    {
        $customer = Customer::where('id', $userId)->first();

        if ($customer) {
            $order = $this->orderRepository->findOneWhere([
                // 'tx_id' => $txId,
                // 'customer_id' => $userId,
            ]);

            Mail::queue(new CustomerOrderNotification($customer->email, $order));

            if ($order) {
                return response()->json([
                    'success' => true,
                    'message' => 'Order placed successfully',
                ]);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Invalid wallet address',
            ]);
        }
    }
}
