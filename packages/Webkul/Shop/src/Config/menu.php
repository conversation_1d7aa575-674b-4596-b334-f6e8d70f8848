<?php

return [
    [
        'key'   => 'account',
        'name'  => 'shop::app.layouts.my-account',
        'route' => 'customer.profile.index',
        'sort'  => 1,
    ], [
        'key'   => 'account.profile',
        'name'  => 'shop::app.layouts.profile',
        'route' => 'customer.profile.index',
        'sort'  => 1,
    ],
    // [
    //     'key'   => 'account.address',
    //     'name'  => 'shop::app.layouts.address',
    //     'route' =>'customer.address.index',
    //     'sort'  => 2,
    // ],
    // [
    //     'key'   => 'account.logout',
    //     'name'  => 'Çıkış',
    //     'route' =>'customer.reviews.index',
    //     'sort'  => 3,
    // ],
    [
        'key'   => 'account.logout',
        'name'  => 'velocity::app-static.user-profile.logout',
        'route' => 'customer.session.destroy',
        'sort'  => 3,
    ], [
        'key'   => 'account.myaddress',
        'name'  => 'velocity::app-static.user-profile.address',
        'route' => 'customer.address.index',
        'sort'  => 4,
    ], [
        'key'   => 'account.notifications',
        'name'  => 'velocity::app-static.user-profile.notification',
        'route' => 'velocity.customer.product.compare',
        'sort'  => 5,
    ], [
        'key'   => 'account.wallet',
        'name'  => 'velocity::app-static.user-profile.wallet',
        'route' => 'customer.orders.index',
        'sort'  => 6,
    ], [
        'key'   => 'account.advantages',
        'name'  => 'velocity::app-static.user-profile.advantage',
        'route' => 'customer.advantages.index',
        'sort'  => 7,
    ], [
        'key'   => 'account.wishlist',
        'name'  => 'velocity::app-static.user-profile.wishlist',
        'route' => 'customer.wishlist.index',
        'sort'  => 8,
    ],
];
