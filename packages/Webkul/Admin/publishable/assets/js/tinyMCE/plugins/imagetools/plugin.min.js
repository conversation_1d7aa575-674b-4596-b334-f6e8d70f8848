!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};g("o",[],function(){function a(a,b){return function(){a.apply(b,arguments)}}function b(b){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof b)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],h(b,a(d,this),a(e,this))}function c(a){var b=this;return null===this._state?void this._deferreds.push(a):void i(function(){var c=b._state?a.onFulfilled:a.onRejected;if(null===c)return void(b._state?a.resolve:a.reject)(b._value);var d;try{d=c(b._value)}catch(b){return void a.reject(b)}a.resolve(d)})}function d(b){try{if(b===this)throw new TypeError("A promise cannot be resolved with itself.");if(b&&("object"==typeof b||"function"==typeof b)){var c=b.then;if("function"==typeof c)return void h(a(c,b),a(d,this),a(e,this))}this._state=!0,this._value=b,f.call(this)}catch(a){e.call(this,a)}}function e(a){this._state=!1,this._value=a,f.call(this)}function f(){for(var a=0,b=this._deferreds.length;a<b;a++)c.call(this,this._deferreds[a]);this._deferreds=null}function g(a,b,c,d){this.onFulfilled="function"==typeof a?a:null,this.onRejected="function"==typeof b?b:null,this.resolve=c,this.reject=d}function h(a,b,c){var d=!1;try{a(function(a){d||(d=!0,b(a))},function(a){d||(d=!0,c(a))})}catch(a){if(d)return;d=!0,c(a)}}if(window.Promise)return window.Promise;var i=b.immediateFn||"function"==typeof setImmediate&&setImmediate||function(a){setTimeout(a,1)},j=Array.isArray||function(a){return"[object Array]"===Object.prototype.toString.call(a)};return b.prototype["catch"]=function(a){return this.then(null,a)},b.prototype.then=function(a,d){var e=this;return new b(function(b,f){c.call(e,new g(a,d,b,f))})},b.all=function(){var a=Array.prototype.slice.call(1===arguments.length&&j(arguments[0])?arguments[0]:arguments);return new b(function(b,c){function d(f,g){try{if(g&&("object"==typeof g||"function"==typeof g)){var h=g.then;if("function"==typeof h)return void h.call(g,function(a){d(f,a)},c)}a[f]=g,0===--e&&b(a)}catch(a){c(a)}}if(0===a.length)return b([]);for(var e=a.length,f=0;f<a.length;f++)d(f,a[f])})},b.resolve=function(a){return a&&"object"==typeof a&&a.constructor===b?a:new b(function(b){b(a)})},b.reject=function(a){return new b(function(b,c){c(a)})},b.race=function(a){return new b(function(b,c){for(var d=0,e=a.length;d<e;d++)a[d].then(b,c)})},b}),g("p",[],function(){function a(a,b){return e(document.createElement("canvas"),a,b)}function b(b){var d,e;return d=a(b.width,b.height),e=c(d),e.drawImage(b,0,0),d}function c(a){return a.getContext("2d")}function d(a){var b=null;try{b=a.getContext("webgl")||a.getContext("experimental-webgl")}catch(a){}return b||(b=null),b}function e(a,b,c){return a.width=b,a.height=c,a}return{create:a,clone:b,resize:e,get2dContext:c,get3dContext:d}}),g("q",[],function(){function a(a){var b=document.createElement("a");return b.href=a,b.pathname}function b(b){var c,d,e,f;return 0===b.indexOf("data:")?(b=b.split(","),f=/data:([^;]+)/.exec(b[0]),f?f[1]:""):(e={jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"},c=a(b).split("."),d=c[c.length-1],d&&(d=d.toLowerCase()),e[d])}return{guessMimeType:b}}),g("r",[],function(){function a(a){return a.naturalWidth||a.width}function b(a){return a.naturalHeight||a.height}return{getWidth:a,getHeight:b}}),g("c",["o","p","q","r"],function(a,b,c,d){function e(b){return new a(function(a){function c(){b.removeEventListener("load",c),a(b)}b.complete?a(b):b.addEventListener("load",c)})}function f(a){return e(a).then(function(a){var c,e;return e=b.create(d.getWidth(a),d.getHeight(a)),c=b.get2dContext(e),c.drawImage(a,0,0),e})}function g(a){return e(a).then(function(a){var b=a.src;return 0===b.indexOf("blob:")?i(b):0===b.indexOf("data:")?k(b):f(a).then(function(a){return k(a.toDataURL(c.guessMimeType(b)))})})}function h(b){return new a(function(a){function c(){d.removeEventListener("load",c),a(d)}var d=new Image;d.addEventListener("load",c),d.src=URL.createObjectURL(b),d.complete&&c()})}function i(b){return new a(function(a){var c=new XMLHttpRequest;c.open("GET",b,!0),c.responseType="blob",c.onload=function(){200==this.status&&a(this.response)},c.send()})}function j(a){var b,c,d,e,f,g;if(a=a.split(","),e=/data:([^;]+)/.exec(a[0]),e&&(f=e[1]),b=atob(a[1]),window.WebKitBlobBuilder){for(g=new WebKitBlobBuilder,c=new ArrayBuffer(b.length),d=0;d<c.length;d++)c[d]=b.charCodeAt(d);return g.append(c),g.getBlob(f)}for(c=new Uint8Array(b.length),d=0;d<c.length;d++)c[d]=b.charCodeAt(d);return new Blob([c],{type:f})}function k(b){return new a(function(a){a(j(b))})}function l(a){return 0===a.indexOf("blob:")?i(a):0===a.indexOf("data:")?k(a):null}function m(b,c,d){return c=c||"image/png",HTMLCanvasElement.prototype.toBlob?new a(function(a){b.toBlob(function(b){a(b)},c,d)}):k(b.toDataURL(c,d))}function n(b){return new a(function(a){var c=new FileReader;c.onloadend=function(){a(c.result)},c.readAsDataURL(b)})}function o(a){return n(a).then(function(a){return a.split(",")[1]})}function p(a){URL.revokeObjectURL(a.src)}return{blobToImage:h,imageToBlob:g,blobToDataUri:n,blobToBase64:o,imageToCanvas:f,canvasToBlob:m,revokeImageUrl:p,uriToBlob:l,dataUriToBlobSync:j}}),g("d",["o","c","q","p"],function(a,b,c,d){function e(a,c){function e(){return c}function f(d,e){return b.canvasToBlob(a,d||c,e)}function g(b,d){return a.toDataURL(b||c,d)}function h(a,b){return g(a,b).split(",")[1]}function i(){return d.clone(a)}return{getType:e,toBlob:f,toDataURL:g,toBase64:h,toCanvas:i}}function f(a){return b.blobToImage(a).then(function(a){var c=b.imageToCanvas(a);return b.revokeImageUrl(a),c}).then(function(b){return e(b,a.type)})}function g(b,c){return new a(function(a){a(e(b,c))})}function h(a){var d=c.guessMimeType(a.src);return b.imageToCanvas(a).then(function(a){return e(a,d)})}return{fromBlob:f,fromCanvas:g,fromImage:h}}),g("1",["c","d"],function(a,b){var c=function(b){return a.blobToImage(b)},d=function(b){return a.imageToBlob(b)},e=function(b){return a.blobToDataUri(b)},f=function(b){return a.blobToBase64(b)},g=function(a){return b.fromBlob(a)},h=function(c){return a.uriToBlob(c).then(b.fromBlob)},i=function(a){return b.fromImage(a)},j=function(a,b,c){return a.toBlob(b,c)},k=function(b,c,d){return a.dataUriToBlobSync(b.toDataURL(c,d))};return{blobToImage:c,imageToBlob:d,blobToDataUri:e,blobToBase64:f,blobToImageResult:g,dataUriToImageResult:h,imageToImageResult:i,imageResultToBlob:j,imageResultToBlobSync:k}}),g("s",[],function(){function a(a,b,c){return a=parseFloat(a),a>c?a=c:a<b&&(a=b),a}function b(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function c(a,b){var c,d,e,f,g=[],h=new Array(10);for(c=0;c<5;c++){for(d=0;d<5;d++)g[d]=b[d+5*c];for(d=0;d<5;d++){for(f=0,e=0;e<5;e++)f+=a[d+5*e]*g[e];h[d+5*c]=f}}return h}function d(b,c){return c=a(c,0,1),b.map(function(b,d){return d%6===0?b=1-(1-b)*c:b*=c,a(b,0,1)})}function e(b,d){var e;return d=a(d,-1,1),d*=100,d<0?e=127+d/100*127:(e=d%1,e=0===e?l[d]:l[Math.floor(d)]*(1-e)+l[Math.floor(d)+1]*e,e=127*e+127),c(b,[e/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}function f(b,d){var e,f,g,h;return d=a(d,-1,1),e=1+(d>0?3*d:d),f=.3086,g=.6094,h=.082,c(b,[f*(1-e)+e,g*(1-e),h*(1-e),0,0,f*(1-e),g*(1-e)+e,h*(1-e),0,0,f*(1-e),g*(1-e),h*(1-e)+e,0,0,0,0,0,1,0,0,0,0,0,1])}function g(b,d){var e,f,g,h,i;return d=a(d,-180,180)/180*Math.PI,e=Math.cos(d),f=Math.sin(d),g=.213,h=.715,i=.072,c(b,[g+e*(1-g)+f*-g,h+e*-h+f*-h,i+e*-i+f*(1-i),0,0,g+e*-g+.143*f,h+e*(1-h)+.14*f,i+e*-i+f*-.283,0,0,g+e*-g+f*-(1-g),h+e*-h+f*h,i+e*(1-i)+f*i,0,0,0,0,0,1,0,0,0,0,0,1])}function h(b,d){return d=a(255*d,-255,255),c(b,[1,0,0,0,d,0,1,0,0,d,0,0,1,0,d,0,0,0,1,0,0,0,0,0,1])}function i(b,d,e,f){return d=a(d,0,2),e=a(e,0,2),f=a(f,0,2),c(b,[d,0,0,0,0,0,e,0,0,0,0,0,f,0,0,0,0,0,1,0,0,0,0,0,1])}function j(b,e){return e=a(e,0,1),c(b,d([.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0,0,0,0,0,1],e))}function k(b,e){return e=a(e,0,1),c(b,d([.33,.34,.33,0,0,.33,.34,.33,0,0,.33,.34,.33,0,0,0,0,0,1,0,0,0,0,0,1],e))}var l=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];return{identity:b,adjust:d,multiply:c,adjustContrast:e,adjustBrightness:h,adjustSaturation:f,adjustHue:g,adjustColors:i,adjustSepia:j,adjustGrayscale:k}}),g("e",["p","d","s"],function(a,b,c){function d(c,d){function e(a,b){var c,d,e,f,g,h=a.data,i=b[0],j=b[1],k=b[2],l=b[3],m=b[4],n=b[5],o=b[6],p=b[7],q=b[8],r=b[9],s=b[10],t=b[11],u=b[12],v=b[13],w=b[14],x=b[15],y=b[16],z=b[17],A=b[18],B=b[19];for(g=0;g<h.length;g+=4)c=h[g],d=h[g+1],e=h[g+2],f=h[g+3],h[g]=c*i+d*j+e*k+f*l+m,h[g+1]=c*n+d*o+e*p+f*q+r,h[g+2]=c*s+d*t+e*u+f*v+w,h[g+3]=c*x+d*y+e*z+f*A+B;return a}var f,g=c.toCanvas(),h=a.get2dContext(g);return f=e(h.getImageData(0,0,g.width,g.height),d),h.putImageData(f,0,0),b.fromCanvas(g,c.getType())}function e(c,d){function e(a,b,c){function d(a,b,c){return a>c?a=c:a<b&&(a=b),a}var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;for(g=Math.round(Math.sqrt(c.length)),h=Math.floor(g/2),e=a.data,f=b.data,t=a.width,u=a.height,j=0;j<u;j++)for(i=0;i<t;i++){for(k=l=m=0,o=0;o<g;o++)for(n=0;n<g;n++)p=d(i+n-h,0,t-1),q=d(j+o-h,0,u-1),r=4*(q*t+p),s=c[o*g+n],k+=e[r]*s,l+=e[r+1]*s,m+=e[r+2]*s;r=4*(j*t+i),f[r]=d(k,0,255),f[r+1]=d(l,0,255),f[r+2]=d(m,0,255)}return b}var f,g,h=c.toCanvas(),i=a.get2dContext(h);return f=i.getImageData(0,0,h.width,h.height),g=i.getImageData(0,0,h.width,h.height),g=e(f,g,d),i.putImageData(g,0,0),b.fromCanvas(h,c.getType())}function f(c){return function(d,e){function f(a,b){var c,d=a.data;for(c=0;c<d.length;c+=4)d[c]=b[d[c]],d[c+1]=b[d[c+1]],d[c+2]=b[d[c+2]];return a}var g,h,i=d.toCanvas(),j=a.get2dContext(i),k=new Array(256);for(h=0;h<k.length;h++)k[h]=c(h,e);return g=f(j.getImageData(0,0,i.width,i.height),k),j.putImageData(g,0,0),b.fromCanvas(i,d.getType())}}function g(a){return function(b,e){return d(b,a(c.identity(),e))}}function h(a){return function(b){return d(b,a)}}function i(a){return function(b){return e(b,a)}}return{invert:h([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0]),brightness:g(c.adjustBrightness),hue:g(c.adjustHue),saturate:g(c.adjustSaturation),contrast:g(c.adjustContrast),grayscale:g(c.adjustGrayscale),sepia:g(c.adjustSepia),colorize:function(a,b,e,f){return d(a,c.adjustColors(c.identity(),b,e,f))},sharpen:i([0,-1,0,-1,5,-1,0,-1,0]),emboss:i([-2,-1,0,-1,1,1,0,1,2]),gamma:f(function(a,b){return 255*Math.pow(a/255,1-b)}),exposure:f(function(a,b){return 255*(1-Math.exp(-(a/255)*b))}),colorFilter:d,convoluteFilter:e}}),g("t",["o","c","p","r"],function(a,b,c,d){function e(a,b,c){var g=d.getWidth(a),h=d.getHeight(a),i=b/g,j=c/h,k=!1;(i<.5||i>2)&&(i=i<.5?.5:2,k=!0),(j<.5||j>2)&&(j=j<.5?.5:2,k=!0);var l=f(a,i,j);return k?l.then(function(a){return e(a,b,c)}):l}function f(b,e,f){return new a(function(a){var g=d.getWidth(b),h=d.getHeight(b),i=Math.floor(g*e),j=Math.floor(h*f),k=c.create(i,j),l=c.get2dContext(k);l.drawImage(b,0,0,g,h,0,0,i,j),a(k)})}return{scale:e}}),g("f",["p","d","t"],function(a,b,c){function d(c,d){var e=c.toCanvas(),f=a.create(e.width,e.height),g=a.get2dContext(f),h=0,i=0;return d=d<0?360+d:d,90!=d&&270!=d||a.resize(f,f.height,f.width),90!=d&&180!=d||(h=f.width),270!=d&&180!=d||(i=f.height),g.translate(h,i),g.rotate(d*Math.PI/180),g.drawImage(e,0,0),b.fromCanvas(f,c.getType())}function e(c,d){var e=c.toCanvas(),f=a.create(e.width,e.height),g=a.get2dContext(f);return"v"==d?(g.scale(1,-1),g.drawImage(e,0,-f.height)):(g.scale(-1,1),g.drawImage(e,-f.width,0)),b.fromCanvas(f,c.getType())}function f(c,d,e,f,g){var h=c.toCanvas(),i=a.create(f,g),j=a.get2dContext(i);return j.drawImage(h,-d,-e),b.fromCanvas(i,c.getType())}function g(a,d,e){return c.scale(a.toCanvas(),d,e).then(function(c){return b.fromCanvas(c,a.getType())})}return{rotate:d,flip:e,crop:f,resize:g}}),g("2",["e","f"],function(a,b){var c=function(b){return a.invert(b)},d=function(b){return a.sharpen(b)},e=function(b){return a.emboss(b)},f=function(b,c){return a.gamma(b,c)},g=function(b,c){return a.exposure(b,c)},h=function(b,c,d,e){return a.colorize(b,c,d,e)},i=function(b,c){return a.brightness(b,c)},j=function(b,c){return a.hue(b,c)},k=function(b,c){return a.saturate(b,c)},l=function(b,c){return a.contrast(b,c)},m=function(b,c){return a.grayscale(b,c)},n=function(b,c){return a.sepia(b,c)},o=function(a,c){return b.flip(a,c)},p=function(a,c,d,e,f){return b.crop(a,c,d,e,f)},q=function(a,c,d){return b.resize(a,c,d)},r=function(a,c){return b.rotate(a,c)};return{invert:c,sharpen:d,emboss:e,brightness:i,hue:j,saturate:k,contrast:l,grayscale:m,sepia:n,colorize:h,gamma:f,exposure:g,flip:o,crop:p,resize:q,rotate:r}}),h("g",tinymce.util.Tools.resolve),g("3",["g"],function(a){return a("tinymce.Env")}),g("4",["g"],function(a){return a("tinymce.PluginManager")}),g("5",["g"],function(a){return a("tinymce.util.Delay")}),g("6",["g"],function(a){return a("tinymce.util.Promise")}),g("7",["g"],function(a){return a("tinymce.util.Tools")}),g("8",["g"],function(a){return a("tinymce.util.URI")}),g("9",[],function(){function a(a){function b(a){return/^[0-9\.]+px$/.test(a)}var c,d;return c=a.style.width,d=a.style.height,c||d?b(c)&&b(d)?{w:parseInt(c,10),h:parseInt(d,10)}:null:(c=a.width,d=a.height,c&&d?{w:parseInt(c,10),h:parseInt(d,10)}:null)}function b(a,b){var c,d;b&&(c=a.style.width,d=a.style.height,(c||d)&&(a.style.width=b.w+"px",a.style.height=b.h+"px",a.removeAttribute("data-mce-style")),c=a.width,d=a.height,(c||d)&&(a.setAttribute("width",b.w),a.setAttribute("height",b.h)))}function c(a){return{w:a.naturalWidth,h:a.naturalHeight}}return{getImageSize:a,setImageSize:b,getNaturalImageSize:c}}),g("h",["6","7"],function(a,b){var c=function(a){return null!==a&&void 0!==a},d=function(a,b){var d;return d=b.reduce(function(a,b){return c(a)?a[b]:void 0},a),c(d)?d:null},e=function(c,d){return new a(function(a){var e;e=new XMLHttpRequest,e.onreadystatechange=function(){4===e.readyState&&a({status:e.status,blob:this.response})},e.open("GET",c,!0),b.each(d,function(a,b){e.setRequestHeader(b,a)}),e.responseType="blob",e.send()})},f=function(b){return new a(function(a){var c=new FileReader;c.onload=function(b){var c=b.target;a(c.result)},c.readAsText(b)})},g=function(a){var b;try{b=JSON.parse(a)}catch(a){}return b};return{traverse:d,readBlob:f,requestUrlAsBlob:e,parseJson:g}}),g("a",["6","7","h"],function(a,b,c){function d(b){return c.requestUrlAsBlob(b,{}).then(function(b){return b.status>=400?g(b.status):a.resolve(b.blob)})}var e=function(a,b){var c=a.indexOf("?")===-1?"?":"&";return/[?&]apiKey=/.test(a)||!b?a:a+c+"apiKey="+encodeURIComponent(b)},f=function(a){return 400===a||403===a||500===a},g=function(b){return a.reject("ImageProxy HTTP error: "+b)},h=function(b){a.reject("ImageProxy Service error: "+b)},i=function(a,b){return c.readBlob(b).then(function(a){var b=c.parseJson(a),d=c.traverse(b,["error","type"]);return h(d?d:"Invalid JSON")})},j=function(a,b){return f(a)?i(a,b):g(a)},k=function(b,d){return c.requestUrlAsBlob(e(b,d),{"Content-Type":"application/json;charset=UTF-8","tiny-api-key":d}).then(function(b){return b.status>=400?j(b.status,b.blob):a.resolve(b.blob)})},l=function(a,b){return b?k(a,b):d(a)};return{getUrl:l}}),g("i",["g"],function(a){return a("tinymce.dom.DOMUtils")}),g("j",["g"],function(a){return a("tinymce.ui.Container")}),g("k",["g"],function(a){return a("tinymce.ui.Factory")}),g("l",["g"],function(a){return a("tinymce.ui.Form")}),g("u",["g"],function(a){return a("tinymce.geom.Rect")}),g("v",["g"],function(a){return a("tinymce.ui.Control")}),g("w",["g"],function(a){return a("tinymce.ui.DragHelper")}),g("y",["g"],function(a){return a("tinymce.dom.DomQuery")}),g("z",["g"],function(a){return a("tinymce.util.Observable")}),g("10",["g"],function(a){return a("tinymce.util.VK")}),g("x",["y","w","u","7","z","10"],function(a,b,c,d,e,f){var g=0;return function(h,i,j,k,l){function m(a,b){return{x:b.x+a.x,y:b.y+a.y,w:b.w,h:b.h}}function n(a,b){return{x:b.x-a.x,y:b.y-a.y,w:b.w,h:b.h}}function o(){return n(j,h)}function p(a,b,d,e){var f,g,i,k,l;f=b.x,g=b.y,i=b.w,k=b.h,f+=d*a.deltaX,g+=e*a.deltaY,i+=d*a.deltaW,k+=e*a.deltaH,i<20&&(i=20),k<20&&(k=20),l=h=c.clamp({x:f,y:g,w:i,h:k},j,"move"==a.name),l=n(j,l),y.fire("updateRect",{rect:l}),v(l)}function q(){function c(a){var c;return new b(D,{document:k.ownerDocument,handle:D+"-"+a.name,start:function(){c=h},drag:function(b){p(a,c,b.deltaX,b.deltaY)}})}a('<div id="'+D+'" class="'+C+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(k),d.each(B,function(b){a("#"+D,k).append('<div id="'+D+"-"+b+'"class="'+C+'croprect-block" style="display: none" data-mce-bogus="all">')}),d.each(z,function(b){a("#"+D,k).append('<div id="'+D+"-"+b.name+'" class="'+C+"croprect-handle "+C+"croprect-handle-"+b.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+b.label+'" aria-grabbed="false">')}),A=d.map(z,c),s(h),a(k).on("focusin focusout",function(b){a(b.target).attr("aria-grabbed","focus"===b.type)}),a(k).on("keydown",function(a){function b(a,b,d,e,f){a.stopPropagation(),a.preventDefault(),p(c,d,e,f)}var c;switch(d.each(z,function(b){if(a.target.id==D+"-"+b.name)return c=b,!1}),a.keyCode){case f.LEFT:b(a,c,h,-10,0);break;case f.RIGHT:b(a,c,h,10,0);break;case f.UP:b(a,c,h,0,-10);break;case f.DOWN:b(a,c,h,0,10);break;case f.ENTER:case f.SPACEBAR:a.preventDefault(),l()}})}function r(b){var c;c=d.map(z,function(a){return"#"+D+"-"+a.name}).concat(d.map(B,function(a){return"#"+D+"-"+a})).join(","),b?a(c,k).show():a(c,k).hide()}function s(b){function c(b,c){c.h<0&&(c.h=0),c.w<0&&(c.w=0),a("#"+D+"-"+b,k).css({left:c.x,top:c.y,width:c.w,height:c.h})}d.each(z,function(c){a("#"+D+"-"+c.name,k).css({left:b.w*c.xMul+b.x,top:b.h*c.yMul+b.y})}),c("top",{x:i.x,y:i.y,w:i.w,h:b.y-i.y}),c("right",{x:b.x+b.w,y:b.y,w:i.w-b.x-b.w+i.x,h:b.h}),c("bottom",{x:i.x,y:b.y+b.h,w:i.w,h:i.h-b.y-b.h+i.y}),c("left",{x:i.x,y:b.y,w:b.x-i.x,h:b.h}),c("move",b)}function t(a){h=a,s(h)}function u(a){i=a,s(h)}function v(a){t(m(j,a))}function w(a){j=a,s(h)}function x(){d.each(A,function(a){a.destroy()}),A=[]}var y,z,A,B,C="mce-",D=C+"crid-"+g++;return z=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],B=["top","right","bottom","left"],q(k),y=d.extend({toggleVisibility:r,setClampRect:w,setRect:t,getInnerRect:o,setInnerRect:v,setViewPortRect:u,destroy:x},e)}}),g("m",["u","v","w","6","7","x"],function(a,b,c,d,e,f){function g(a){return new d(function(b){function c(){a.removeEventListener("load",c),b(a)}a.complete?b(a):a.addEventListener("load",c)})}return b.extend({Defaults:{classes:"imagepanel"},selection:function(a){return arguments.length?(this.state.set("rect",a),this):this.state.get("rect")},imageSize:function(){var a=this.state.get("viewRect");return{w:a.w,h:a.h}},toggleCropRect:function(a){this.state.set("cropEnabled",a)},imageSrc:function(b){var c=this,d=new Image;d.src=b,g(d).then(function(){var b,e,f=c.state.get("viewRect");if(e=c.$el.find("img"),e[0])e.replaceWith(d);else{var g=document.createElement("div");g.className="mce-imagepanel-bg",c.getEl().appendChild(g),c.getEl().appendChild(d)}b={x:0,y:0,w:d.naturalWidth,h:d.naturalHeight},c.state.set("viewRect",b),c.state.set("rect",a.inflate(b,-20,-20)),f&&f.w==b.w&&f.h==b.h||c.zoomFit(),c.repaintImage(),c.fire("load")})},zoom:function(a){return arguments.length?(this.state.set("zoom",a),this):this.state.get("zoom")},postRender:function(){return this.imageSrc(this.settings.imageSrc),this._super()},zoomFit:function(){var a,b,c,d,e,f,g,h=this;g=10,a=h.$el.find("img"),b=h.getEl().clientWidth,c=h.getEl().clientHeight,d=a[0].naturalWidth,e=a[0].naturalHeight,f=Math.min((b-g)/d,(c-g)/e),f>=1&&(f=1),h.zoom(f)},repaintImage:function(){var a,b,c,d,e,f,g,h,i,j,k;k=this.getEl(),i=this.zoom(),j=this.state.get("rect"),g=this.$el.find("img"),h=this.$el.find(".mce-imagepanel-bg"),e=k.offsetWidth,f=k.offsetHeight,c=g[0].naturalWidth*i,d=g[0].naturalHeight*i,a=Math.max(0,e/2-c/2),b=Math.max(0,f/2-d/2),g.css({left:a,top:b,width:c,height:d}),h.css({left:a,top:b,width:c,height:d}),this.cropRect&&(this.cropRect.setRect({x:j.x*i+a,y:j.y*i+b,w:j.w*i,h:j.h*i}),this.cropRect.setClampRect({x:a,y:b,w:c,h:d}),this.cropRect.setViewPortRect({x:0,y:0,w:e,h:f}))},bindStates:function(){function a(a){b.cropRect=new f(a,b.state.get("viewRect"),b.state.get("viewRect"),b.getEl(),function(){b.fire("crop")}),b.cropRect.on("updateRect",function(a){var c=a.rect,d=b.zoom();c={x:Math.round(c.x/d),y:Math.round(c.y/d),w:Math.round(c.w/d),h:Math.round(c.h/d)},b.state.set("rect",c)}),b.on("remove",b.cropRect.destroy)}var b=this;b.state.on("change:cropEnabled",function(a){b.cropRect.toggleVisibility(a.value),b.repaintImage()}),b.state.on("change:zoom",function(){b.repaintImage()}),b.state.on("change:rect",function(c){var d=c.value;b.cropRect||a(d),b.cropRect.setRect(d)})}})}),g("n",[],function(){return function(){function a(a){var b;return b=f.splice(++g),f.push(a),{state:a,removed:b}}function b(){if(d())return f[--g]}function c(){if(e())return f[++g]}function d(){return g>0}function e(){return g!=-1&&g<f.length-1}var f=[],g=-1;return{data:f,add:a,undo:b,redo:c,canUndo:d,canRedo:e}}}),g("b",["1","2","i","j","k","l","6","7","m","n"],function(a,b,c,d,e,f,g,h,i,j){function k(a){return{blob:a,url:URL.createObjectURL(a)}}function l(a){a&&URL.revokeObjectURL(a.url)}function m(a){h.each(a,l)}function n(g,n,o){function p(a){var b,c,d,e;b=M.find("#w")[0],c=M.find("#h")[0],d=parseInt(b.value(),10),e=parseInt(c.value(),10),M.find("#constrain")[0].checked()&&ha&&ia&&d&&e&&("w"==a.control.settings.name?(e=Math.round(d*ja),c.value(e)):(d=Math.round(e*ka),b.value(d))),ha=d,ia=e}function q(a){return Math.round(100*a)+"%"}function r(){M.find("#undo").disabled(!la.canUndo()),M.find("#redo").disabled(!la.canRedo()),M.statusbar.find("#save").disabled(!la.canUndo())}function s(){M.find("#undo").disabled(!0),M.find("#redo").disabled(!0)}function t(a){a&&T.imageSrc(a.url)}function u(a){return function(){var b=h.grep(ga,function(b){return b.settings.name!=a});h.each(b,function(a){a.hide()}),a.show(),a.focus()}}function v(a){P=k(a),t(P)}function w(a){g=k(a),t(g),m(la.add(g).removed),r()}function x(){var c=T.selection();a.blobToImageResult(g.blob).then(function(a){b.crop(a,c.x,c.y,c.w,c.h).then(ma).then(function(a){w(a),A()})})}function y(b){var c=[].slice.call(arguments,1);return function(){var d=P||g;a.blobToImageResult(d.blob).then(function(a){b.apply(this,[a].concat(c)).then(ma).then(v)})}}function z(b){var c=[].slice.call(arguments,1);return function(){a.blobToImageResult(g.blob).then(function(a){b.apply(this,[a].concat(c)).then(ma).then(w)})}}function A(){t(g),l(P),u(N)(),r()}function B(){P&&(w(P.blob),A())}function C(){var a=T.zoom();a<2&&(a+=.1),T.zoom(a)}function D(){var a=T.zoom();a>.1&&(a-=.1),T.zoom(a)}function E(){g=la.undo(),t(g),r()}function F(){g=la.redo(),t(g),r()}function G(){n(g.blob),M.close()}function H(a){return new f({layout:"flex",direction:"row",labelGap:5,border:"0 0 1 0",align:"center",pack:"center",padding:"0 10 0 10",spacing:5,flex:0,minHeight:60,defaults:{classes:"imagetool",type:"button"},items:a})}function I(b,c){return H([{text:"Back",onclick:A},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:B}]).hide().on("show",function(){s(),a.blobToImageResult(g.blob).then(function(a){return c(a)}).then(ma).then(function(a){var b=k(a);t(b),l(P),P=b})})}function J(b,c,d,e,f){function h(b){a.blobToImageResult(g.blob).then(function(a){return c(a,b)}).then(ma).then(function(a){var b=k(a);t(b),l(P),P=b})}return H([{text:"Back",onclick:A},{type:"spacer",flex:1},{type:"slider",flex:1,ondragend:function(a){h(a.value)},minValue:e,maxValue:f,value:d,previewFilter:q},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:B}]).hide().on("show",function(){this.find("slider").value(d),s()})}function K(a,b){function c(){var a,c,d;a=M.find("#r")[0].value(),c=M.find("#g")[0].value(),d=M.find("#b")[0].value(),b(g.blob,a,c,d).then(function(a){var b=k(a);t(b),l(P),P=b})}return H([{text:"Back",onclick:A},{type:"spacer",flex:1},{type:"slider",label:"R",name:"r",minValue:0,value:1,maxValue:2,ondragend:c,previewFilter:q},{type:"slider",label:"G",name:"g",minValue:0,value:1,maxValue:2,ondragend:c,previewFilter:q},{type:"slider",label:"B",name:"b",minValue:0,value:1,maxValue:2,ondragend:c,previewFilter:q},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:B}]).hide().on("show",function(){M.find("#r,#g,#b").value(1),s()})}function L(a){a.control.value()===!0&&(ja=ia/ha,ka=ha/ia)}var M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la=new j,ma=function(a){return a.toBlob()};Q=H([{text:"Back",onclick:A},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:x}]).hide().on("show hide",function(a){T.toggleCropRect("show"==a.type)}).on("show",s),R=H([{text:"Back",onclick:A},{type:"spacer",flex:1},{type:"textbox",name:"w",label:"Width",size:4,onkeyup:p},{type:"textbox",name:"h",label:"Height",size:4,onkeyup:p},{type:"checkbox",name:"constrain",text:"Constrain proportions",checked:!0,onchange:L},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:"submit"}]).hide().on("submit",function(a){var c=parseInt(M.find("#w").value(),10),d=parseInt(M.find("#h").value(),10);a.preventDefault(),z(b.resize,c,d)(),A()}).on("show",s),S=H([{text:"Back",onclick:A},{type:"spacer",flex:1},{icon:"fliph",tooltip:"Flip horizontally",onclick:y(b.flip,"h")},{icon:"flipv",tooltip:"Flip vertically",onclick:y(b.flip,"v")},{icon:"rotateleft",tooltip:"Rotate counterclockwise",onclick:y(b.rotate,-90)},{icon:"rotateright",tooltip:"Rotate clockwise",onclick:y(b.rotate,90)},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:B}]).hide().on("show",s),W=I("Invert",b.invert),ca=I("Sharpen",b.sharpen),da=I("Emboss",b.emboss),X=J("Brightness",b.brightness,0,-1,1),Y=J("Hue",b.hue,180,0,360),Z=J("Saturate",b.saturate,0,-1,1),$=J("Contrast",b.contrast,0,-1,1),_=J("Grayscale",b.grayscale,0,0,1),aa=J("Sepia",b.sepia,0,0,1),ba=K("Colorize",b.colorize),ea=J("Gamma",b.gamma,0,-1,1),fa=J("Exposure",b.exposure,1,0,2),O=H([{text:"Back",onclick:A},{type:"spacer",flex:1},{text:"hue",icon:"hue",onclick:u(Y)},{text:"saturate",icon:"saturate",onclick:u(Z)},{text:"sepia",icon:"sepia",onclick:u(aa)},{text:"emboss",icon:"emboss",onclick:u(da)},{text:"exposure",icon:"exposure",onclick:u(fa)},{type:"spacer",flex:1}]).hide(),N=H([{tooltip:"Crop",icon:"crop",onclick:u(Q)},{tooltip:"Resize",icon:"resize2",onclick:u(R)},{tooltip:"Orientation",icon:"orientation",onclick:u(S)},{tooltip:"Brightness",icon:"sun",onclick:u(X)},{tooltip:"Sharpen",icon:"sharpen",onclick:u(ca)},{tooltip:"Contrast",icon:"contrast",onclick:u($)},{tooltip:"Color levels",icon:"drop",onclick:u(ba)},{tooltip:"Gamma",icon:"gamma",onclick:u(ea)},{tooltip:"Invert",icon:"invert",onclick:u(W)}]),T=new i({flex:1,imageSrc:g.url}),U=new d({layout:"flex",direction:"column",border:"0 1 0 0",padding:5,spacing:5,items:[{type:"button",icon:"undo",tooltip:"Undo",name:"undo",onclick:E},{type:"button",icon:"redo",tooltip:"Redo",name:"redo",onclick:F},{type:"button",icon:"zoomin",tooltip:"Zoom in",onclick:C},{type:"button",icon:"zoomout",tooltip:"Zoom out",onclick:D}]}),V=new d({type:"container",layout:"flex",direction:"row",align:"stretch",flex:1,items:[U,T]}),ga=[N,Q,R,S,O,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa],M=e.create("window",{layout:"flex",direction:"column",align:"stretch",minWidth:Math.min(c.DOM.getViewPort().w,800),minHeight:Math.min(c.DOM.getViewPort().h,650),title:"Edit image",items:ga.concat([V]),buttons:[{text:"Save",name:"save",subtype:"primary",onclick:G},{text:"Cancel",onclick:"close"}]}),M.renderTo(document.body).reflow(),M.on("close",function(){o(),m(la.data),la=null,P=null}),la.add(g),r(),T.on("load",function(){ha=T.imageSize().w,ia=T.imageSize().h,ja=ia/ha,ka=ha/ia,M.find("#w").value(ha),M.find("#h").value(ia)}),T.on("crop",x)}function o(a){return new g(function(b,c){return a.toBlob().then(function(a){n(k(a),b,c)})})}return{edit:o}}),g("0",["1","2","3","4","5","6","7","8","9","a","b"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(d){function l(a){d.notificationManager.open({text:a,type:"error"})}function m(){return d.selection.getNode()}function n(a){var b=a.match(/\/([^\/\?]+)?\.(?:jpeg|jpg|png|gif)(?:\?|$)/i);return b?d.dom.encode(b[1]):null}function o(){return"imagetools"+H++}function p(a){var b=a.src;return 0===b.indexOf("data:")||0===b.indexOf("blob:")||new h(b).host===d.documentBaseURI.host}function q(a){return g.inArray(d.settings.imagetools_cors_hosts,new h(a.src).host)!==-1}function r(){return d.settings.api_key||d.settings.imagetools_api_key}function s(b){var c,e=b.src;return q(b)?j.getUrl(b.src,null):p(b)?a.imageToBlob(b):(e=d.settings.imagetools_proxy,e+=(e.indexOf("?")===-1?"?":"&")+"url="+encodeURIComponent(b.src),c=r(),j.getUrl(e,c))}function t(){var a;return a=d.editorUpload.blobCache.getByUri(m().src),a?a.blob():s(m())}function u(){F=e.setEditorTimeout(d,function(){d.editorUpload.uploadImagesAuto()},d.settings.images_upload_timeout||3e4)}function v(){clearTimeout(F)}function w(a,b){return a.toBlob().then(function(c){var e,f,g,h,i;return g=d.editorUpload.blobCache,i=m(),e=i.src,d.settings.images_reuse_filename&&(h=g.getByUri(e),h?(e=h.uri(),f=h.name()):f=n(e)),h=g.create({id:o(),blob:c,base64:a.toBase64(),uri:e,name:f}),g.add(h),d.undoManager.transact(function(){function a(){d.$(i).off("load",a),d.nodeChanged(),b?d.editorUpload.uploadImagesAuto():(v(),u())}d.$(i).on("load",a),d.$(i).attr({src:h.blobUri()}).removeAttr("data-mce-src")}),h})}function x(b){return function(){return d._scanForImages().then(t).then(a.blobToImageResult).then(b).then(w,l)}}function y(a){return function(){return x(function(c){var d=i.getImageSize(m());return d&&i.setImageSize(m(),{w:d.h,h:d.w}),b.rotate(c,a)})()}}function z(a){return function(){return x(function(c){return b.flip(c,a)})()}}function A(){var b=m(),c=i.getNaturalImageSize(b),d=function(d){return new f(function(e){a.blobToImage(d).then(function(a){var f=i.getNaturalImageSize(a);c.w==f.w&&c.h==f.h||i.getImageSize(b)&&i.setImageSize(b,f),URL.revokeObjectURL(a.src),e(d)})})},e=function(b){return k.edit(b).then(d).then(a.blobToImageResult).then(function(a){w(a,!0)},function(){})};b&&a.imageToImageResult(b).then(e,l)}function B(){d.addButton("rotateleft",{title:"Rotate counterclockwise",cmd:"mceImageRotateLeft"}),d.addButton("rotateright",{title:"Rotate clockwise",cmd:"mceImageRotateRight"}),d.addButton("flipv",{title:"Flip vertically",cmd:"mceImageFlipVertical"}),d.addButton("fliph",{title:"Flip horizontally",cmd:"mceImageFlipHorizontal"}),d.addButton("editimage",{title:"Edit image",cmd:"mceEditImage"
}),d.addButton("imageoptions",{title:"Image options",icon:"options",cmd:"mceImage"})}function C(){d.on("NodeChange",function(a){G&&G.src!=a.element.src&&(v(),d.editorUpload.uploadImagesAuto(),G=void 0),D(a.element)&&(G=a.element)})}function D(a){var b=d.dom.is(a,"img:not([data-mce-object],[data-mce-placeholder])");return b&&(p(a)||q(a)||d.settings.imagetools_proxy)}function E(){var a=d.settings.imagetools_toolbar;a||(a="rotateleft rotateright | flipv fliph | crop editimage imageoptions"),d.addContextToolbar(D,a)}var F,G,H=0;c.fileApi&&(g.each({mceImageRotateLeft:y(-90),mceImageRotateRight:y(90),mceImageFlipVertical:z("v"),mceImageFlipHorizontal:z("h"),mceEditImage:A},function(a,b){d.addCommand(b,a)}),B(),E(),C())};return d.add("imagetools",l),function(){}}),d("0")()}();