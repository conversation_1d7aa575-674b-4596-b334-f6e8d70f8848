<template>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ pushNotification?.applicationData?.title }}</h1>
            </div>
            <div class="page-action">
                <div class="control-group notif-filter">
                    <span>{{ pushNotification?.applicationData?.usersTotalText }}: {{ pushNotification?.applicationData?.usersTotalCount }}</span> | <span>{{ pushNotification?.applicationData?.applicationInstalledText }}: {{ pushNotification?.applicationData?.applicationInstalledCount }}</span> | <span>{{ pushNotification?.applicationData?.applicationRemovedText }}: {{ pushNotification?.applicationData?.applicationRemovedCount }}</span>
                </div>
                <div class="control-group notif-filter">
                    <button
                        @click="$root?.showModal('addPushNotificationModalPanel'); functionModalTemplateReset( true );"
                        class="btn btn-lg btn-primary"
                    >
                        {{ pushNotification?.applicationData?.addPushNotificationButtontext }}
                    </button>
                </div>
                <div class="control-group notif-filter">
                    <input
                        type="text"
                        class="form-control control"
                        placeholder="Search Order"
                        @keyup="applyFilter( 'search', $event )"
                    />
                    <i class="icon search-icon search-btn"></i>
                </div>
                <div class="control-group notif-filter">
                    <select @change="applyFilter( 'filter', $event )" class="control">
                        <option v-for="orderstatus in orderTypeStatus" :value="orderstatus">{{ orderstatus }}</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="page-content">
            <div class="table">
                <div class="grid-container">
                    <div class="grid-top">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr style="height:65px;">
                                        <th id="mastercheckbox" class="grid_head" style="width:50px;">
                                            <span class="checkbox">
                                                <input type="checkbox"/>
                                                <label for="checkbox" class="checkbox-view"></label>
                                            </span>
                                        </th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableId }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableTitle }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableContent }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableIcon }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableStatus }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableAddedBy }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableAddedDate }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.tableArray?.tableAction }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-if="pushNotification?.data?.length > 0" v-for="notification in pushNotification?.data">
                                        <td class="grid_head" style="width:50px;text-align:center;">
                                            <span class="checkbox">
                                                <input type="checkbox" value="5"/>
                                                <label for="checkbox" class="checkbox-view"></label>
                                            </span>
                                        </td>
                                        <td class="grid_head" style="width:50px;text-align:center;">{{ notification?.id }}</td>
                                        <td>{{ notification?.title }}</td>
                                        <td style="width:350px;">{{ notification?.content }}</td>
                                        <td style="width:50px;text-align:center;">
                                            <img :src="notification?.pushLargeIcon === '' ? pushNotification.applicationData.tableDefautLargeIcon: notification?.pushLargeIcon" :alt="notification?.title" style="display:flex;width:100%;height:100%;"/>
                                        </td>
                                        <td>
                                            <span
                                                style="display:flex;width:65%;text-align:center;align-items:center;justify-content:center;margin:0 auto;font-weight:700;"
                                                :class="'badge badge-md ' + notification?.status.class + ' badge-' + notification?.status.class"
                                            >
                                                {{ notification?.status.text }}
                                            </span>
                                        </td>
                                        <td>{{ notification?.userName }}</td>
                                        <td>{{ notification?.createdAt }}</td>
                                        <td>
                                            <span
                                                class="icon eye-icon"
                                                @click="$root?.showModal('pushNotificationLogModal'); functionPushNotificationLogModal( notification?.id )"
                                            ></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <pagination
                                style="display:flex!important;justify-content:end!important;"
                                align="center"
                                :data="pushNotification?.pagination"
                                @pagination-change-page="functionGetPushNotoficationPagination"
                            >
                                <span slot="prev-nav">&lt;</span>
                                <span slot="next-nav">&gt;</span>
                            </pagination>
                            <ul class="notif" v-if="pushNotification?.data?.length === 0">
                                <li>{{ pushNotification?.applicationData?.noRecordText }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <modal id="addPushNotificationModalPanel" class="addPushNotificationModalPanel" :is-open="$root.modalIds.addPushNotificationModalPanel">
            <template v-if="addPushNotificationModalPanelApproval">
                <h3 slot="header"></h3>
                <div slot="body">
                    <div>
                        <h3 class="text-center-approval">{{ pushNotification?.applicationData?.addedModalArray?.modalApprovalTitle }}</h3>
                        <span class="icon promotion-icon"></span>
                        <button
                            type="button"
                            class="btn btn-lg btn-primary float-right"
                            @click="functionPushNotificationApprovalModalClose"
                        >
                            {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationCloseButtonText }}
                        </button>
                    </div>
                </div>
            </template>
            <template v-if="addPushNotificationModalPanel">
                <h3 slot="header">
                    {{ pushNotification?.applicationData?.addedModalArray?.modalTitle }}
                </h3>
                <div slot="body" id="modalTop">
                    <form
                        method="POST"
                        enctype="multipart/form-data"
                        @submit.prevent="functionOnPushNotificationSubmit"
                    >
                        <div class="control-group" id="controlTitleTopScroll">
                            <label for="pushNotificationTitle" class="required">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationTitle }}
                            </label>
                            <input
                                type="text"
                                :class="`control ${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationTitleError) ? 'controlRed': ''}`"
                                id="pushNotificationTitle"
                                @keyup="functionInputValidateControl( pushNotification?.applicationData?.pushNotificationErrorsText?.notificationTitleError )"
                                v-model="pushNotification?.form.notificationTitle"
                            />
                            <span class="control-error" :style="`${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationTitleError) ? 'display:block!important;': ''}`">{{ pushNotification?.applicationData?.pushNotificationErrorsText?.notificationTitleError }}</span>
                        </div>
                        <div class="control-group" id="controlContentTopScroll">
                            <label for="pushNotificationContent" class="required">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationContent }}
                            </label>
                            <textarea
                                style="padding-top:6px;"
                                id="pushNotificationContent"
                                @keyup="functionInputValidateControl( pushNotification?.applicationData?.pushNotificationErrorsText?.notificationContentError )"
                                v-model="pushNotification?.form.notificationContent"
                                :class="`control ${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationContentError) ? 'controlRed': ''}`"
                            ></textarea>
                            <span class="control-error" :style="`${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationContentError) ? 'display:block!important;': ''}`">{{ pushNotification?.applicationData?.pushNotificationErrorsText?.notificationContentError }}</span>
                        </div>
                        <div class="control-group">
                            <label for="pushNotification" class="required">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationUserSelect }}
                            </label>
                            <select
                                :multiple="true"
                                id="pushNotification"
                                v-model="pushNotification?.form.userIds"
                                @change="functionInputValidateControl( pushNotification?.applicationData?.pushNotificationErrorsText?.notificationUserSelectError )"
                                :class="`control ${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationUserSelectError) ? 'controlRed': ''}`"
                            >
                                <option value="all" :selected="true">{{ pushNotification?.applicationData?.addedModalArray?.modalNotificationAllUserSelect }}</option>
                                <option v-for="user in pushNotification?.userIds" :value="user?.id">{{ user?.user_name }}</option>
                            </select>
                            <span class="control-error" :style="`${pushNotification.errors.includes(pushNotification?.applicationData?.pushNotificationErrorsText?.notificationUserSelectError) ? 'display:block!important;': ''}`">{{ pushNotification?.applicationData?.pushNotificationErrorsText?.notificationUserSelectError }}</span>
                        </div>
                        <div class="control-group">
                            <label for="appPageAndScreen">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationAppPageSelect }}
                            </label>
                            <select
                                class="control"
                                id="appPageAndScreen"
                                @change="functionAppPageIdStatus"
                                v-model="pushNotification.form.appPage"
                            >
                                <option value="" :selected="true">{{ pushNotification?.applicationData?.addedModalArray?.modalNotificationAppPageSelect }}</option>
                                <optgroup v-for="item in pushNotification?.appPageAndScreen" :label="item?.label">
                                    <option v-for="itex in item?.option" :value="item?.labelSlug + '|' + itex">{{ itex }}</option>
                                </optgroup>
                            </select>
                            <span class="control-error"></span>
                        </div>
                        <div class="control-group" v-if="pushNotification.appPageIdStatus">
                            <label for="appPageId">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationProductSelect }}
                            </label>
                            <select
                                class="control"
                                id="appPageId"
                                v-model="pushNotification.form.appPageId"
                            >
                                <option value="" :selected="true">{{ pushNotification?.applicationData?.addedModalArray?.modalNotificationProductSelect }}</option>
                                <option v-for="item in pushNotification?.applicationData?.productList" :value="item?.id">{{ item?.sku }}</option>
                            </select>
                            <span class="control-error"></span>
                        </div>

                        <div class="control-group">
                            <label for="pushNotificationImage">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationImageTitle }}
                                <input type="checkbox" v-model="pushNotification.imageStatus" />
                            </label>
                            <div v-if="pushNotification.imageStatus">
                                <img v-if="pushNotification.previewImage" :src="pushNotification.previewImage" style="display:block;margin:0 auto;width:160px;height:auto;" alt=""/>
                                <input
                                    type="file"
                                    class="control"
                                    id="pushNotificationImage"
                                    @change="functionUploadImage"
                                />
                                <span class="control-error"></span>
                            </div>
                        </div>

                        <div class="control-group">
                            <label for="pushNotification">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalSchedulingDateTimeText }}
                                <input type="checkbox" v-model="pushNotification.schedulingStatus" />
                            </label>
                            <div v-if="pushNotification.schedulingStatus">
                                <datetime>
                                    <input
                                        type="text"
                                        class="control"
                                        id="pushNotification"
                                        v-model="pushNotification.form.scheduling"
                                    />
                                </datetime>
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="status">
                                {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationStatus }}
                            </label>
                            <label class="switch">
                                <input
                                    type="checkbox"
                                    id="status"
                                    v-model="pushNotification.form.status"
                                    value="true"
                                    :checked="pushNotification.form.status"
                                />
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <button
                            type="submit"
                            :disabled="pushNotification.sendButtonStatus"
                            class="btn btn-lg btn-primary float-right"
                        >
                            {{ pushNotification?.applicationData?.addedModalArray?.modalNotificationSendButtonText }}
                        </button>
                    </form>
                </div>
            </template>
        </modal>
        <modal id="pushNotificationLogModal" :is-open="$root.modalIds.pushNotificationLogModal">
            <h3 slot="header">
                {{ pushNotification?.applicationData?.logModalArray?.modalTitle }}
            </h3>
            <div slot="body">
                <div class="table">
                    <div class="grid-container">
                        <div class="grid-top">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                    <tr style="height:65px;">
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.logModalArray?.tableId }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.logModalArray?.tableTitle }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.logModalArray?.tableDelivered }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.logModalArray?.tableRead }}</th>
                                        <th class="grid_head sortable">{{ pushNotification?.applicationData?.logModalArray?.tableAddedDate }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="pushNotification.logData.length > 0" v-for="notificationLog in pushNotification.logData">
                                        <td class="grid_head" style="width:50px;text-align:center;" data-value="ID">{{ notificationLog?.id }}</td>
                                        <td>{{ notificationLog?.customerName }}</td>
                                        <td>
                                            <span
                                                style="display:flex;width:95%;text-align:center;align-items:center;justify-content:center;margin:0 auto;font-weight:700;"
                                                :class="'badge badge-md ' + notificationLog?.pushNotificationDelivered.class + ' badge-' + notificationLog?.pushNotificationDelivered.class"
                                            >
                                                {{ notificationLog?.pushNotificationDelivered.text }}
                                            </span>
                                        </td>
                                        <td>
                                            <span
                                                style="display:flex;width:95%;text-align:center;align-items:center;justify-content:center;margin:0 auto;font-weight:700;"
                                                :class="'badge badge-md ' + notificationLog?.pushNotificationRead.class + ' badge-' + notificationLog?.pushNotificationRead.class"
                                            >
                                                {{ notificationLog?.pushNotificationRead.text }}
                                            </span>
                                        </td>
                                        <td data-value="ID">{{ notificationLog?.createdAt }}</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <pagination
                                    style="display:flex!important;justify-content:end!important;"
                                    align="center"
                                    :data="pushNotification.logPagination"
                                    @pagination-change-page="functionGetPushNotoficationLogPagination"
                                >
                                    <span slot="prev-nav">&lt;</span>
                                    <span slot="next-nav">&gt;</span>
                                </pagination>
                                <ul class="notif" v-if="pushNotification.logData.length === 0">
                                    <li>{{ pushNotification?.applicationData?.noRecordText }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </modal>
    </div>
</template>

<script>
export default {
    name: "pushNotificationListComponent",
    props: {
        orderStatus:Array|Object,
        applicationData:Array|Object,
        appPageAndScreen:Array|Object,
        orderStatusMessages:Array|Object,
    },
    data: function() {
        return {
            pushNotification: {
                data: [],
                errors: [],
                logData: [],
                form: {
                    appPage: '',
                    userIds: [],
                    status: true,
                    appPageId: '',
                    scheduling: '',
                    notificationTitle: '',
                    notificationContent: '',
                },
                userIds: [],
                pagination: {},
                previewImage: '',
                imageStatus: false,
                logPagination: {},
                notificationId: '',
                appPageIdStatus: false,
                schedulingStatus: false,
                sendButtonStatus: false,
                notificationData: new FormData(),
                applicationData: JSON.parse(this.applicationData),
                appPageAndScreen: JSON.parse(this.appPageAndScreen),
            },
            addPushNotificationModalPanel: true,
            addPushNotificationModalPanelApproval: false,
            orderTypeStatus: JSON.parse(this.orderStatus),
            orderTypeMessages: JSON.parse(this.orderStatusMessages),
        }
    },
    mounted() {
        this.functionGetNotification();
        this.functionGetModalUserList();
    },
    methods: {
        /**
         * Bu function ile Bildirim için göndereceğimiz LargeImage varsa ekliyoruz.
         * @param event
         */
        functionUploadImage: function( event ) {

            const image = event.target.files[0];
            const reader = new FileReader();
            reader.readAsDataURL(image);
            this.pushNotification.notificationData.append('file', event.target.files[0]);
            reader.onload = (event) => {
                this.pushNotification.previewImage = event.target.result;
            };

        }, /** end functionUploadImage: function( event ) **/
        /**
         * Bu function ile Arayüz seçimine göre alt bölümde bulunan Ürün Listesini kontrol ediyoruz.
         */
        functionAppPageIdStatus: function() {

            if(this.pushNotification.form.appPage === 'page|FUNDDETAIL') {
                this.pushNotification.appPageIdStatus = true;
            } else {
                this.pushNotification.appPageIdStatus = false;
            }

        }, /** end functionAppPageIdStatus: function() **/
        /**
         * Bu function ile Modal Panelimizden girilen bilgileri Kayıt altına alıyoruz.
         */
        functionOnPushNotificationSubmit: function() {

            document.querySelectorAll(".addPushNotificationModalPanel.shakingTheModal").forEach(obj=>obj.classList.remove('shakingTheModal'));
            this.pushNotification.errors = [];
            if (this.pushNotification.form.notificationTitle && this.pushNotification.form.notificationContent) {
                this.pushNotification.sendButtonStatus = true;
                const bodyData = this.pushNotification.notificationData;
                for (const [key, value] of Object.entries(this.pushNotification.form)) {
                    if(typeof value === 'string') {
                        bodyData.append(key, String(value));
                    }
                    if(typeof value === 'boolean') {
                        bodyData.append(key, Boolean(value));
                    }
                    if(typeof value === 'object') {
                        bodyData.append(key, JSON.stringify(value));
                    }
                }
                axios
                    .post(this.pushNotification?.applicationData?.createRoute,
                        bodyData,
                        {
                            headers: {
                                'Content-Type' : 'image/*'
                            }
                        }
                    )
                    .then(response => {
                        this.addPushNotificationModalPanel = false;
                        this.addPushNotificationModalPanelApproval = true;
                        this.pushNotification.data = response?.data?.data?.data;
                        this.pushNotification.pagination = response?.data?.data?.links;
                        var entries = bodyData.entries();
                        for(var pair of entries ) {
                            bodyData.delete( pair[0] );
                        }
                    });
            }
            if(!this.pushNotification.form.notificationTitle) {
                this.pushNotification.errors.push(this.pushNotification?.applicationData?.pushNotificationErrorsText?.notificationTitleError);
                document.getElementById('modalTop').scrollIntoView({ behavior: "smooth" });
            }
            if(!this.pushNotification.form.notificationContent) {
                this.pushNotification.errors.push(this.pushNotification?.applicationData?.pushNotificationErrorsText?.notificationContentError);
                document.getElementById('modalTop').scrollIntoView({ behavior: "smooth" });
            }
            if(this.pushNotification.form.userIds.length === 0) {
                this.pushNotification.errors.push(this.pushNotification?.applicationData?.pushNotificationErrorsText?.notificationUserSelectError);
                document.getElementById('modalTop').scrollIntoView({ behavior: "smooth" });
            }
            if(this.pushNotification.errors.length > 0) {
                document.querySelectorAll(".addPushNotificationModalPanel").forEach(obj=>obj.classList.add('shakingTheModal'));
            }

        }, /** end functionOnPushNotificationSubmit: function() **/
        /**
         * Bu function ile Hangi Kullanıcı Hangi Bildirimleri Almış Bunların listesini yayınlıyoruz.
         * Bu listede İletildi, Okundu gibi bilgilerin yer aldığı bir tablomuz mevcut.
         * @param value
         */
        functionPushNotificationLogModal: function( value ) {

            this.pushNotification.logData = [];
            this.pushNotification.logPagination = {};
            this.pushNotification.notificationId = value;
            axios
                .post(this.pushNotification?.applicationData?.logListRoute, {
                    pushNotificationId: value,
                })
                .then(response => {
                    this.pushNotification.logData = response?.data?.data?.data;
                    this.pushNotification.logPagination = response?.data?.data?.links;
                });

        }, /** end functionPushNotificationLogModal: function( value ) **/
        /**
         * Bu function ile Tüm kayıtlı Bildirimlerin Listesini Tabloya ekliyoruz.
         */
        functionGetNotification: function() {

            axios
                .post(this.pushNotification?.applicationData?.listRoute, {})
                .then(response => {
                    this.pushNotification.data = response?.data?.data?.data;
                    this.pushNotification.pagination = response?.data?.data?.links;
                });

        }, /** end functionGetNotification: function() **/
        /**
         * Bu function ile AnaTablodaki Pagination listesini kontrol ediyoruz.
         * @param page
         */
        functionGetPushNotoficationPagination: function( page  = 1 ) {

            axios.post(this.pushNotification?.applicationData?.listRoute, {page: page})
                .then(response => {
                    this.pushNotification.data = response?.data?.data?.data;
                    this.pushNotification.pagination = response?.data?.data?.links;
                });

        }, /** end functionGetPushNotoficationPagination: function( page  = 1 ) **/
        /**
         * Bu function ile Log Tablosu Pagination Listesini Kontrol Ediyoruz.
         * @param page
         */
        functionGetPushNotoficationLogPagination: function( page  = 1 ) {

            axios.post(this.logListRoute, {page: page, pushNotificationId: this.pushNotification.notificationId})
                .then(response => {
                    this.pushNotification.logData = response?.data?.data?.data;
                    this.pushNotification.logPagination = response?.data?.data?.links;
                });

        }, /** end functionGetPushNotoficationLogPagination: function( page  = 1 ) **/
        /**
         * Bu function ile Bildirim Gönderme Modal Panelde Bulunan Kullanıcı Listesini Kontrol Ediyoruz.
         */
        functionGetModalUserList: function() {

            axios.post(this.pushNotification?.applicationData?.userListRoute, {})
                .then(response => {
                    this.pushNotification.userIds = response?.data?.data;
                });

        }, /** end functionGetModalUserList: function() **/
        /**
         * Bu function ile Modal Panelide Bulunan INPUT, SELECT Validate alanını temizliyoruz.
         * @param value
         */
        functionInputValidateControl: function( value ) {

            if(this.pushNotification.errors.indexOf(value) !== -1) {
                this.pushNotification.errors.splice(this.pushNotification.errors.indexOf(value), 1);
            }

        }, /** end functionInputValidateControl: function( value ) **/
        /**
         * Bu function ile Bildirim Gönderme Paneli içinde Bildirim Gönderildi template alanını Kontrol ediyoruz.
         */
        functionPushNotificationApprovalModalClose: function() {

            this.$root.modalIds.addPushNotificationModalPanel = false;

        }, /** end functionPushNotificationApprovalModalClose: function() **/
        /**
         * Bu function ile Tablo ve Template değerlerini resetliyoruz.
         */
        functionModalTemplateReset: function() {

            this.pushNotification.errors = [];
            this.pushNotification.previewImage = '';
            this.addPushNotificationModalPanel = true;
            this.pushNotification.schedulingStatus = false;
            this.pushNotification.sendButtonStatus = false;
            this.addPushNotificationModalPanelApproval = false;
            const formData = this.pushNotification.notificationData;
            const keys = [];
            for (const key of formData.keys()) {
                keys.push(key);
            }
            for (const idx in keys) {
                formData.delete(keys[idx]);
            }
            for(let item in this.pushNotification.form) {
                if(typeof this.pushNotification.form[item] === 'string') {
                    this.pushNotification.form[item] = '';
                }
                if(typeof this.pushNotification.form[item] === 'number') {
                    this.pushNotification.form[item] = '';
                }
                if(typeof this.pushNotification.form[item] === 'object') {
                    this.pushNotification.form[item] = [];
                }
                if(typeof this.pushNotification.form[item] === 'boolean') {

                }
            }

        }, /** end functionModalTemplateReset: function() **/
        /**
         * Tablo içinde arama alanı etkin edilecek.
         * Bu kısım daha sonra elden geçecek....
         */
        applyFilter: function( type, $event ) {

            type === 'search' ? this.id = $event.target.value : this.status = $event.target.value;

        }, /** end applyFilter: function( type, $event ) **/
    }
}
</script>

<style scoped>
    .promotion-icon {
        display: block;
        margin: 0 auto;
        border: 2px #dedede solid;
        padding: 45px;
    }
    .text-center-approval {
        display: block!important;
        text-align: center!important;
        margin-top: -30px!important;
        margin-bottom: 30px!important;
    }
    .controlRed {
        border: 2px #fc6868 solid!important;
    }
    .shakingTheModal {
        animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
        transform: translate3d(0, 0, 0);
    }
    @keyframes shake {
        10%, 90% {
            transform: translate3d(-1px, 0, 0);
        }

        20%, 80% {
            transform: translate3d(2px, 0, 0);
        }

        30%, 50%, 70% {
            transform: translate3d(-4px, 0, 0);
        }

        40%, 60% {
            transform: translate3d(4px, 0, 0);
        }
    }
</style>