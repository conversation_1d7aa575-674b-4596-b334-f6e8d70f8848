.dark-mode {
    background-color: $dark-mode-body-background-color;
    color: $dark-mode-text-color;

    .read-all {
        color: $dark-mode-text-color;
    }

    .navbar-top {
        background-color: $dark-mode-left-menubar-active-background-color;

        .navbar-top-left {
            background-color: $dark-mode-navtop-left-color;
            border-bottom: 1px solid $seprator;
        }

        .profile-info {
            .app-version {
                padding: 10px 20px 0px 20px;
                margin-bottom: -10px;
                display: block;
                cursor: default;
                color: $link-color;
            }

            .name {
                color: $dark-mode-text-color !important;
            }

            .dropdown-list {
                right: 0px;
                bottom: inherit !important;
                border: 1px solid $dark-mode-switch-border;
                border: 1px solid $dark-mode-switch-border;
            }
        }
    }

    .navbar-left {
        background-color: $dark-bg;
        border-right: 1px solid $dark-bg;

        ul.menubar {
            li.menu-item {

                > a {
                    padding: 5px 2px;
                    display: block;
                    color: $black;
                    width: 100%;

                    .icon {
                        display: inline-block;
                        vertical-align: middle;
                        transform: scale(0.7);
                    }

                    .menu-label {
                        display: none;
                        color: $menu-label-text-color;
                    }

                    .arrow-icon {
                        display: none;
                    }

                    &.active,
                    &:hover {
                        padding: 5px 2px;
                    }
                }

                &.active {
                    >a {
                        background-color: $dark-mode-left-menubar-background-color !important;
                    }
                }

                &:hover {
                    background-color: $dark-mode-left-menubar-background-color !important;

                    ul.sub-menubar {
                        background: $dark-mode-left-submenubar-background-color !important;
                        box-shadow: none;
                        border: 1px solid $dark-mode-switch-border;

                        .sub-menu-item {

                            .menu-label {
                                color: $white !important;
                            }

                            &.active,
                            &:hover {
                                background-color: $dark-mode-left-submenubar-background-color;

                                .menu-label {
                                    color: $white !important;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.open {
            ul.menubar {
                li.menu-item {
                    a {
                        .menu-label {
                            font-size: 14px;
                            font-weight: 200;
                            display: inline-block;
                            color: $menu-label-text-color;
                        }

                        .arrow-icon {
                            display: inline-block;
                        }
                    }

                    ul.sub-menubar {
                        display: none;
                        position: unset;
                        background-color: transparent;
                        border-radius: 0;
                        box-shadow: unset;
                        border: 0;

                        li.sub-menu-item {
                            a {
                                padding-left: 52px;
                            }
                        }
                    }

                    &.active {
                        background: $dark-mode-left-menubar-active-background-color;
                        width: 100%;

                        .menu-label {
                            color: $white;
                        }

                        ul.sub-menubar {
                            display: block;
                            background-color: $dark-mode-left-submenubar-background-color;
                            border: none !important;

                            .sub-menu-item {
                                .menu-label {
                                    color: $menu-label-text-color;
                                }

                                &.active,
                                &:hover {
                                    background-color: $dark-mode-left-menubar-background-color;

                                    .menu-label {
                                        color: $white !important;
                                    }
                                }
                            }
                        }
                    }

                    &:hover {
                        background: $dark-bg;

                        .menu-label {
                            color: $white;
                        }
                    }
                }
            }

        }

        .menubar-bottom {
            background-color: $dark-bg;
        }
    }

    .nav-container {

        .nav-top {

            background: $dark-bg;

            .pro-info {

                .profile-info-desc {
                    display: inline-block;
                    margin-left: 40px;

                    .name {
                        color: $white;
                    }

                    .role {
                        color: $white;
                    }
                }
            }
        }

        .nav-items {

            background: $dark-bg;

            .nav-item {

                a {
                    color: $white;
                    display: block;
                    padding: 5px;
                }

                ul {
                    .navbar-child {

                        &.active,
                        &:hover {
                            background-color: $dark-mode-left-menubar-background-color;
                            ;
                        }
                    }
                }

                &.active {
                    background-color: $dark-mode-left-submenubar-background-color;

                    >a {
                        background-color: $dark-mode-left-menubar-background-color;
                    }
                }

                &:hover {
                    >a {
                        background-color: $dark-mode-left-menubar-background-color;
                    }
                }
            }
        }

    }

    .content-container {
        .inner-section {
            .content-wrapper {
                .page-header {
                    background-color: $dark-mode-body-background-color;
                    border-bottom: 1px solid $dark-mode-switch-border;
                }
            }
        }

        .content {
            .page-header {
                background-color: $dark-mode-body-background-color;
                border-bottom: 1px solid $dark-mode-switch-border;
            }
        }
    }

    .table {
        background-color: $dark-mode-body-background-color;

        table {
            thead th {
                background-color: $dark-mode-table-head-backgound-color !important;
                color: $dark-mode-text-color;
            }

            tbody td {
                color: $dark-mode-text-color;
                border-bottom: 1px solid $seprator;
            }
        }
    }

    .modal-container {
        background: $dark-mode-body-background-color;

        .modal-header {

            h3 {
                display: inline-block;
                font-size: 20px;
                color: $dark-mode-text-color;
                margin: 0;
            }
        }
    }

    .accordian,
    accordian {

        .accordian-header,
        div[slot*="header"] {
            color: $font-color;
            border-top: solid 1px $border-color;
            border-bottom: solid 1px $border-color;

            .accordian-up-icon {
                background-image: url(../images/Accordion-Dark-Arrow-Up.svg);
            }

            .accordian-down-icon {
                background-image: url(../images/Accordion-Dark-Arrow-Up.svg);
            }
        }
    }

    @media only screen and (max-width: 1300px) {
        .table {
            table {

                tbody tr td {
                    background-color: $dark-mode-body-background-color;
                }

            }
        }
    }

    .grid-dropdown-header {
        background-color: $black;
        border: 1px solid $dark-mode-switch-border;
    }

    .dropdown-list {
        background-color: $black;

        .dropdown-container {
            background-color: $dark-mode-background-color;
            color: $dark-mode-text-color;

            label {
                color: $dark-mode-text-color;
            }

            ul {
                li {
                    a {
                        color: $dark-mode-text-color;

                        &:hover {
                            color: $white;
                        }
                    }

                }
            }
        }
    }

    .notification {
        background-color: $black;

        .dropdown-container {

            color: $dark-mode-text-color;
            border: 1px solid $dark-mode-notification-border-color;

            label {
                color: $dark-mode-text-color;
            }

            ul {
                .read {
                    background-color: $dark-mode-background-color;
                    border-bottom: 1px solid $dark-mode-switch-border;
                    color: $dark-mode-read-color;

                    .notif-content>a {
                        text-decoration: none;
                        color: $dark-mode-read-color;

                        &:hover {
                            color: $dark-mode-read-color;
                        }
                    }

                }

                li {
                    background-color: $dark-mode-background-color;
                    border-bottom: 1px solid $dark-mode-switch-border;
                    color: $dark-mode-unread-color;

                    .notif-content>a {
                        text-decoration: none;
                        color: rgba(255, 255, 255, 1);

                        &:hover {
                            color: $dark-mode-unread-color
                        }
                    }
                }

                .bottom-li {

                    a {
                        display: initial;
                        color: $dark-mode-text-color !important;
                    }
                }
            }
        }
    }

    .btn {
        &.btn-primary {
            background: $white;
            color: $black;
        }
    }

    .control-container {
        .control {
            background: $black;
        }
    }

    select.control {
        border: 1px solid $dark-mode-switch-border;
    }

    .search-filter {
        .control {
            background-color: $dark-mode-background-color;
            color: $dark-mode-text-color;
            border: 1px solid $dark-mode-switch-border;
        }

        .icon-wrapper {
            //border: 1px solid $dark-mode-switch-border;
            border: 0px!important;
        }
    }

    h1 {
        color: $dark-mode-text-color;
    }

    .control-group {
        label {
            color: $dark-mode-text-color;
        }

        .control {
            border: 1px solid $dark-mode-switch-border;
        }

        input[type=date]  {
            background-position-x: 95%;
            background-position-y: center;
            background-image: url(../images/Icon-remove.svg);
            background-size: 18px 30px;
            background-repeat: no-repeat;
            &::-webkit-inner-spin-button,
            &::-webkit-calendar-picker-indicator {
                -webkit-appearance: none;
                opacity: 0;
            }
        }
    }

    .grid-container {
        .grid-top {
            .datagrid-filters {
                .dropdown-filters {
                    &.per-page {
                        .control-group {
                            label {
                                color: $dark-mode-text-color;
                            }
                        }
                    }
                }
            }
        }
    }

    .tabs {
        ul {
            border-bottom: solid 1px $dark-mode-switch-border;

            li {
                a {
                    color: $dark-mode-text-color;
                }
            }
        }
    }

    .accordian,
    accordian {

        .accordian-header,
        div[slot*="header"] {
            background-color: $dark-mode-background-color;
            color: $dark-mode-read-color;
        }

        .accordian-content,
        div[slot*="body"] {
            background-color: $black;
            border-bottom: 1px solid $dark-mode-switch-border;
        }

        &.active>.accordian-content {
            background-color: $dark-mode-background-color;
        }

        &.active>.accordian-header {
            background-color: $black;
        }
    }

    .sale-container {
        .sale {
            .sale-section {
                .secton-title {
                    color: $dark-mode-text-color;
                    border-bottom: 1px solid $dark-mode-switch-border;
                }

                .section-content {
                    .row {
                        .title {
                            color: $dark-mode-text-color;
                        }

                        .value {
                            color: $dark-mode-text-color;
                        }
                    }
                }
            }
        }

        .summary-comment-container {
            .comment-container {
                .comment-list {
                    li {
                        p {
                            color: $dark-mode-text-color
                        }
                    }
                }
            }
        }
    }

    .control-group {
        .label {
            color: $dark-mode-text-color;
        }

        .control {
            background-color: $dark-mode-background-color;
            color: $dark-mode-text-color;
        }
    }

    .dashboard {
        .card {
            background: $dark-mode-navtop-left-color;
            box-shadow: 0 5px 10px 2px rgb(0 0 0 / 80%);

            .card-title {
                color: $dark-mode-text-color;
            }

            .card-info {
                ul {
                    li {
                        .description {
                            margin-top: 10px;

                            .name {
                                color: $dark-mode-text-color;
                            }

                            .info {
                                color: $dark-mode-text-color;
                                margin-top: 5px;
                            }
                        }
                    }
                }

                .no-result-found {
                    p {
                        color: $dark-mode-text-color;
                    }
                }
            }
        }

        .dashboard-stats {
            .dashboard-card {
                background: $dark-mode-navtop-left-color;
                box-shadow: 0 5px 10px 2px rgb(0 0 0 / 80%);

                .title {
                    color: $dark-mode-text-color;
                }

                .data {
                    color: $white;

                    .progress {
                        color: $dark-mode-text-color;
                    }
                }
            }
        }
    }

    .pagination .page-item {
        background: $black;
        border: 1px solid 000;
        color: $white;
    }

    .pagination .page-item.active {
        background-color: $dark-mode-pagination-page-item-active-background-color !important;
        color: $white;
        border-color: $dark-mode-pagination-page-item-active-border-color !important;
    }

    .filter-tag .wrapper {
        background: $dark-mode-background-color;
        border: 1px solid $dark-mode-background-color;
        color: $dark-mode-text-color;
    }

    .notif {
        background-color: $dark-mode-background-color;

        .read {
            background-color: hsl(0deg 0% 13%) !important;
            color: $dark-mode-read-color;

            .notif-content>a {
                text-decoration: none;
                color: $dark-mode-read-color;
            }
        }

        li {
            border: 1px solid $dark-mode-switch-border;

            color: $dark-mode-unread-color;

            .notif-content>a {
                text-decoration: none;
                color: $dark-mode-unread-color;
            }
        }
    }

    #notif-title {
        height: 50px;
        padding: 14px 10px;
        border-bottom: 1px solid $dark-mode-notification-border-color;
        cursor: default;
    }

    .flatpickr-calendar {
        background: transparent;
        opacity: 0;
        display: none;
        text-align: center;
        visibility: hidden;
        padding: 0;
        -webkit-animation: none;
        animation: none;
        direction: ltr;
        border: 0;
        font-size: 14px;
        line-height: 24px;
        border-radius: 5px;
        position: absolute;
        width: 307.875px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        background-color: $dark-mode-background-color;
        -webkit-box-shadow: 1px 0 0 $black, -1px 0 0 $black, 0 1px 0 $black, 0 -1px 0 $black, 0 3px 13px rgba(0, 0, 0, .08);
        box-shadow: 1px 0 0 $black, -1px 0 0 $black, 0 1px 0 $black, 0 -1px 0 $black, 0 3px 13px rgba(0, 0, 0, .08);
    }

    .flatpickr-months .flatpickr-month {
        color: $white;
    }

    .flatpickr-weekdays .flatpickr-weekdaycontainer,
    span.flatpickr-weekday {
        color: $white;
    }

    .flatpickr-day.flatpickr-disabled,
    .flatpickr-day.flatpickr-disabled:hover,
    .flatpickr-day.nextMonthDay,
    .flatpickr-day.notAllowed,
    .flatpickr-day.notAllowed.nextMonthDay,
    .flatpickr-day.notAllowed.prevMonthDay,
    .flatpickr-day.prevMonthDay {
        color: $dark-mode-text-color;
        background: transparent;
        border-color: transparent;
        cursor: default;
    }

    .flatpickr-weekwrapper span.flatpickr-day,
    .flatpickr-weekwrapper span.flatpickr-day:hover {
        color: $white;

    }

    .flatpickr-day {
        color: $white;
    }

    .flatpickr-months .flatpickr-prev-month,
    .flatpickr-months .flatpickr-next-month {
        color: $white;
        fill: $white;
    }

    .flatpickr-day.endRange,
    .flatpickr-day.endRange.inRange,
    .flatpickr-day.endRange.nextMonthDay,
    .flatpickr-day.endRange.prevMonthDay,
    .flatpickr-day.endRange:focus,
    .flatpickr-day.endRange:hover,
    .flatpickr-day.selected,
    .flatpickr-day.selected.inRange,
    .flatpickr-day.selected.nextMonthDay,
    .flatpickr-day.selected.prevMonthDay,
    .flatpickr-day.selected:focus,
    .flatpickr-day.selected:hover,
    .flatpickr-day.startRange,
    .flatpickr-day.startRange.inRange,
    .flatpickr-day.startRange.nextMonthDay,
    .flatpickr-day.startRange.prevMonthDay,
    .flatpickr-day.startRange:focus,
    .flatpickr-day.startRange:hover {
        background: $flatpickr-day-startRange-hover !important;
        color: $white;
        border-color: $flatpickr-day-startRange-hover !important;
    }

    .flatpickr-day.inRange,
    .flatpickr-day.prevMonthDay.inRange,
    .flatpickr-day.nextMonthDay.inRange,
    .flatpickr-day.today.inRange,
    .flatpickr-day.prevMonthDay.today.inRange,
    .flatpickr-day.nextMonthDay.today.inRange,
    .flatpickr-day:hover,
    .flatpickr-day.prevMonthDay:hover,
    .flatpickr-day.nextMonthDay:hover,
    .flatpickr-day:focus,
    .flatpickr-day.prevMonthDay:focus,
    .flatpickr-day.nextMonthDay:focus {
        cursor: pointer;
        outline: 0;
        background: $dark-mode-switch-border !important;
        border-color: $dark-mode-switch-border !important;
    }

    .control-group .slider {
        top: -2px !important;
        right: -2px !important;
        background-color: $dark-mode-switch-background !important;
        border: 1px solid $dark-mode-switch-border !important;
    }

    .control-group input:checked+.slider {
        background-color: $dark-mode-left-menubar-active-background-color !important;
    }
}

.navbar-left {
    .menubar-bottom {
        background-color: $dark-bg;

        .accordian-left-icon {
            background-image: url(../images/dark-chevron-left.svg);
        }

        .accordian-right-icon {
            background-image: url(../images/dark-chevron-right.svg);
        }
    }
}

.rtl.dark-mode {
    .navbar-left {
        background-color: $dark-bg;
        border-right: 1px solid $dark-bg;

        ul.menubar {
            li.menu-item {

                > a {
                    padding: 5px 2px;
                    display: block;
                    color: $black;
                    width: 100%;

                    .icon {
                        display: inline-block;
                        vertical-align: middle;
                        transform: scale(0.7);
                    }

                    .menu-label {
                        display: none;
                        color: $menu-label-text-color;
                    }

                    .arrow-icon {
                        display: none;
                    }

                    &.active,
                    &:hover {
                        padding: 5px 2px;
                        background-color: $dark-mode-left-menubar-background-color;
                    }
                }

                &.active {
                    >a {
                        background-color: $dark-mode-left-menubar-background-color;
                    }
                }

                &:hover {

                    ul.sub-menubar {
                        background: $dark-mode-left-submenubar-active-background-color;
                        box-shadow: none;
                        border: 1px solid $dark-mode-switch-border;


                        .sub-menu-item {

                            .menu-label {
                                color: $white !important;
                            }

                            &.active,
                            &:hover {
                                background-color: $dark-mode-left-submenubar-background-color;

                                .menu-label {
                                    color: $white !important;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.open {
            ul.menubar {
                li.menu-item {
                    a {
                        .menu-label {
                            font-size: 14px;
                            font-weight: 200;
                            display: inline-block;
                            color: $menu-label-text-color;
                        }

                        .arrow-icon {
                            display: inline-block;
                        }
                    }

                    ul.sub-menubar {
                        display: none;
                        position: unset;
                        background-color: transparent;
                        border-radius: 0;
                        box-shadow: unset;
                        border: 0;

                        li.sub-menu-item {
                            a {
                                padding-left: 52px;
                            }
                        }
                    }

                    &.active {
                        background: $dark-bg;
                        width: calc(100% - 1px);

                        .menu-label {
                            color: $white;
                        }

                        ul.sub-menubar {
                            display: block;
                            background-color: $dark-mode-left-submenubar-active-background-color;
                            border: none !important;

                            .sub-menu-item {
                                .menu-label {
                                    color: $menu-label-text-color;
                                }

                                &.active,
                                &:hover {
                                    background-color: $dark-mode-left-menubar-background-color;

                                    .menu-label {
                                        color: $white !important;
                                    }
                                }
                            }
                        }
                    }

                    &:hover {
                        background: $dark-bg;

                        .menu-label {
                            color: $white;
                        }
                    }
                }
            }
        }

        .menubar-bottom {
            background-color: $dark-bg;
        }
    }
}