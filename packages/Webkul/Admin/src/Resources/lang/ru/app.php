<?php

return [
    'save'          => 'Save',
    'copy-of'       => 'Copy of ',
    'copy-of-slug'  => 'copy-of-',
    'create'        => 'Create',
    'update'        => 'Update',
    'delete'        => 'Delete',
    'failed'        => 'Failed',
    'store'         => 'Store',
    'image'         => 'Image',
    'no result'     => 'No result',
    'product'       => 'Product',
    'attribute'     => 'Attribute',
    'actions'       => 'Actions',
    'id'            => 'ID',
    'action'        => 'action',
    'yes'           => 'Yes',
    'no'            => 'No',
    'true'          => 'True',
    'false'         => 'False',
    'apply'         => 'Apply',
    'label'         => 'Label',
    'name'          => 'Name',
    'title'         => 'Title',
    'code'          => 'Code',
    'type'          => 'Type',
    'required'      => 'Required',
    'unique'        => 'Unique',
    'locale-based'  => 'Locale based',
    'channel-based' => 'Channel based',
    'status'        => 'Status',
    'select-option' => 'Select Option',
    'category'      => 'Category',

    'common' => [
        'no-result-found' => 'We could not find any records.',
        'country'         => 'Country',
        'state'           => 'State',
        'true'            => 'True',
        'false'           => 'False',
    ],

    'layouts' => [
        'app-version'              => 'Version : :version',
        'account-title'            => 'Account',
        'my-account'               => 'My Account',
        'logout'                   => 'Logout',
        'visit-shop'               => 'Visit Shop',
        'dashboard'                => 'Dashboard',
        'sales'                    => 'Sales',
        'orders'                   => 'Orders',
        'shipments'                => 'Shipments',
        'invoices'                 => 'Invoices',
        'refunds'                  => 'Refunds',
        'catalog'                  => 'Catalog',
        'products'                 => 'Products',
        'categories'               => 'Categories',
        'attributes'               => 'Attributes',
        'attribute-families'       => 'Attribute Families',
        'customers'                => 'Customers',
        'groups'                   => 'Groups',
        'reviews'                  => 'Reviews',
        'configure'                => 'Configure',
        'settings'                 => 'Settings',
        'locales'                  => 'Locales',
        'currencies'               => 'Currencies',
        'exchange-rates'           => 'Exchange Rates',
        'inventory-sources'        => 'Inventory Sources',
        'channels'                 => 'Channels',
        'users'                    => 'Users',
        'roles'                    => 'Roles',
        'sliders'                  => 'Sliders',
        'taxes'                    => 'Taxes',
        'tax-categories'           => 'Tax Categories',
        'tax-rates'                => 'Tax Rates',
        'marketing'                => 'Marketing',
        'promotions'               => 'Promotions',
        'email-marketing'          => 'Email Marketing',
        'campaigns'                => 'Campaigns',
        'email-templates'          => 'Email Templates',
        'events'                   => 'Events',
        'sitemaps'                 => 'Sitemaps',
        'discount'                 => 'Discount',
        'cms'                      => 'CMS',
        'transactions'             => 'Transactions',
        'newsletter-subscriptions' => 'Newsletter Subscriptions',
        'mode'                     => 'Mode',
    ],

    'acl' => [
        'dashboard'                => 'Dashboard',
        'sales'                    => 'Sales',
        'cancel'                   => 'Cancel',
        'orders'                   => 'Orders',
        'shipments'                => 'Shipments',
        'invoices'                 => 'Invoices',
        'refunds'                  => 'Refunds',
        'catalog'                  => 'Catalog',
        'products'                 => 'Products',
        'copy'                     => 'Copy',
        'categories'               => 'Categories',
        'attributes'               => 'Attributes',
        'attribute-families'       => 'Attribute Families',
        'customers'                => 'Customers',
        'addresses'                => 'Addresses',
        'note'                     => 'Note',
        'groups'                   => 'Groups',
        'reviews'                  => 'Reviews',
        'configure'                => 'Configure',
        'settings'                 => 'Settings',
        'locales'                  => 'Locales',
        'currencies'               => 'Currencies',
        'exchange-rates'           => 'Exchange Rates',
        'inventory-sources'        => 'Inventory Sources',
        'channels'                 => 'Channels',
        'users'                    => 'Users',
        'roles'                    => 'Roles',
        'sliders'                  => 'Sliders',
        'taxes'                    => 'Taxes',
        'tax-categories'           => 'Tax Categories',
        'tax-rates'                => 'Tax Rates',
        'view'                     => 'View',
        'edit'                     => 'Edit',
        'create'                   => 'Add',
        'delete'                   => 'Delete',
        'mass-delete'              => 'Mass Delete',
        'mass-update'              => 'Mass Update',
        'marketing'                => 'Marketing',
        'promotions'               => 'Promotions',
        'cart-rules'               => 'Cart Rules',
        'catalog-rules'            => 'Catalog Rules',
        'email-marketing'          => 'Email Marketing',
        'email-templates'          => 'Email Templates',
        'campaigns'                => 'Campaigns',
        'subscribers'              => 'Newsletter Subscribers',
        'events'                   => 'Events',
        'sitemaps'                 => 'Sitemaps',
        'newsletter-subscriptions' => 'Newsletter Subscriptions',
    ],

    'dashboard' => [
        'title'                     => 'Dashboard',
        'from'                      => 'From',
        'to'                        => 'To',
        'total-customers'           => 'Total Customers',
        'total-orders'              => 'Total Orders',
        'total-sale'                => 'Total Sale',
        'average-sale'              => 'Average Order Sale',
        'total-unpaid-invoices'     => 'Total Unpaid Invoices',
        'increased'                 => ':progress%',
        'decreased'                 => ':progress%',
        'sales'                     => 'Sales',
        'top-performing-categories' => 'Top Performing Categories',
        'product-count'             => ':count Products',
        'top-selling-products'      => 'Top Selling Products',
        'sale-count'                => ':count Sales',
        'customer-with-most-sales'  => 'Customer With Most Sales',
        'order-count'               => ':count Orders',
        'revenue'                   => 'Revenue :total',
        'stock-threshold'           => 'Stock Threshold',
        'qty-left'                  => ':qty Left',
    ],

    'datagrid' => [
        'mass-ops' => [
            'method-error'   => 'Error! Wrong method detected, please check mass action configuration',
            'delete-success' => 'Selected :resource were successfully deleted',
            'partial-action' => 'Some actions were not performed due restricted system constraints on :resource',
            'update-success' => 'Selected :resource were successfully updated',
            'no-resource'    => 'The resource provided for insufficient for the action',
        ],

        'id'               => 'ID',
        'status'           => 'Status',
        'code'             => 'Code',
        'admin-name'       => 'Name',
        'name'             => 'Name',
        'copy'             => 'Copy',
        'direction'        => 'Direction',
        'fullname'         => 'Full Name',
        'type'             => 'Type',
        'required'         => 'Required',
        'unique'           => 'Unique',
        'per-locale'       => 'Locale Based',
        'per-channel'      => 'Channel Based',
        'position'         => 'Position',
        'locale'           => 'Locale',
        'hostname'         => 'Hostname',
        'email'            => 'Email',
        'group'            => 'Group',
        'phone'            => 'Phone',
        'gender'           => 'Gender',
        'title'            => 'Title',
        'layout'           => 'Layout',
        'url-key'          => 'URL Key',
        'comment'          => 'Comment',
        'product-name'     => 'Product',
        'currency-name'    => 'Currency Name',
        'exch-rate'        => 'Exchange Rate',
        'priority'         => 'Priority',
        'subscribed'       => 'Subscribed',
        'base-total'       => 'Base Total',
        'grand-total'      => 'Grand Total',
        'order-date'       => 'Order Date',
        'channel-name'     => 'Channel Name',
        'billed-to'        => 'Billed To',
        'shipped-to'       => 'Shipped To',
        'order-id'         => 'Order ID',
        'invoice-id'       => 'Invoice number',
        'invoice-date'     => 'Invoice Date',
        'total-qty'        => 'Total Qty',
        'inventory-source' => 'Inventory Source',
        'shipment-date'    => 'Shipment Date',
        'shipment-to'      => 'Shipping To',
        'sku'              => 'SKU',
        'product-number'   => 'Product Number',
        'price'            => 'Price',
        'qty'              => 'Quantity',
        'permission-type'  => 'Permission Type',
        'identifier'       => 'Identifier',
        'state'            => 'State',
        'country'          => 'Country',
        'tax-rate'         => 'Rate',
        'role'             => 'Role',
        'sub-total'        => 'Sub Total',
        'no-of-products'   => 'Number of Products',
        'attribute-family' => 'Attribute Family',
        'starts-from'      => 'Starts From',
        'ends-till'        => 'Ends Till',
        'per-cust'         => 'Per Customer',
        'usage-throttle'   => 'Usage Times',
        'for-guest'        => 'For Guest',
        'order_number'     => 'Order Number',
        'refund-date'      => 'Refund Date',
        'refunded'         => 'Refunded',
        'start'            => 'Start',
        'end'              => 'End',
        'active'           => 'Active',
        'inactive'         => 'Inactive',
        'draft'            => 'Draft',
        'true'             => 'True',
        'false'            => 'False',
        'approved'         => 'Approved',
        'pending'          => 'Pending',
        'disapproved'      => 'Disapproved',
        'coupon-code'      => 'Coupon Code',
        'times-used'       => 'Times Used',
        'created-date'     => 'Created Date',
        'expiration-date'  => 'Expiration Date',
        'edit'             => 'Edit',
        'delete'           => 'Delete',
        'view'             => 'View',
        'rtl'              => 'RTL',
        'ltr'              => 'LTR',
        'update-status'    => 'Update Status',
        'subject'          => 'Subject',
        'date'             => 'Date',
        'transaction-id'   => 'Transaction ID',
        'transaction-date' => 'Transaction Date',
        'file-name'        => 'File Name',
        'path'             => 'Path',
        'link-for-google'  => 'Link For Google',
    ],

    'account' => [
        'title'                => 'My Account',
        'save-btn-title'       => 'Save',
        'general'              => 'General',
        'upload-image-info'    => 'Upload a Profile Image (100px x 100px) in PNG or JPG Format',
        'remove-image'         => 'Remove Image',
        'image-upload-message' => 'Only images (.jpeg, .jpg, .png, ..) are allowed.',
        'name'                 => 'Name',
        'email'                => 'Email',
        'password'             => 'Password',
        'confirm-password'     => 'Confirm Password',
        'change-password'      => 'Change Account Password',
        'current-password'     => 'Current Password',
    ],

    'users' => [
        'forget-password' => [
            'title'            => 'Forget Password',
            'header-title'     => 'Recover Password',
            'email'            => 'Registered Email',
            'password'         => 'Password',
            'confirm-password' => 'Confirm Password',
            'back-link-title'  => 'Back to Sign In',
            'submit-btn-title' => 'Send Password Reset Email',
            'passwords'        => [
                'throttled'        => 'Warning: You have requested password reset recently, please check your email.',
            ],
        ],

        'reset-password' => [
            'title'            => 'Reset Password',
            'email'            => 'Registered Email',
            'password'         => 'Password',
            'confirm-password' => 'Confirm Password',
            'back-link-title'  => 'Back to Sign In',
            'submit-btn-title' => 'Reset Password',
        ],

        'roles' => [
            'title'           => 'Roles',
            'add-role-title'  => 'Add Role',
            'edit-role-title' => 'Edit Role',
            'save-btn-title'  => 'Save Role',
            'general'         => 'General',
            'name'            => 'Name',
            'description'     => 'Description',
            'access-control'  => 'Access Control',
            'permissions'     => 'Permissions',
            'custom'          => 'Custom',
            'all'             => 'All',
        ],

        'users' => [
            'title'                => 'Users',
            'add-user-title'       => 'Add User',
            'edit-user-title'      => 'Edit User',
            'save-btn-title'       => 'Save User',
            'general'              => 'General',
            'email'                => 'Email',
            'name'                 => 'Name',
            'password'             => 'Password',
            'confirm-password'     => 'Confirm Password',
            'status-and-role'      => 'Status and Role',
            'role'                 => 'Role',
            'status'               => 'Status',
            'account-is-active'    => 'Account is Active',
            'current-password'     => 'Enter Current Password',
            'confirm-delete'       => 'Confirm Delete This Account',
            'confirm-delete-title' => 'Confirm password before delete',
            'delete-last'          => 'At least one admin is required.',
            'delete-success'       => 'Success! User deleted',
            'incorrect-password'   => 'The password you entered is incorrect',
            'password-match'       => 'Current password does not match.',
            'account-save'         => 'Account changes saved successfully.',
            'login-error'          => 'Please check your credentials and try again.',
            'activate-warning'     => 'Your account is yet to be activated, please contact administrator.',
        ],

        'sessions' => [
            'title'                      => 'Sign In',
            'email'                      => 'Email',
            'password'                   => 'Password',
            'forget-password-link-title' => 'Forget Password ?',
            'remember-me'                => 'Remember Me',
            'submit-btn-title'           => 'Sign In',
        ],
    ],

    'sales' => [
        'orders' => [
            'title'                        => 'Orders',
            'view-title'                   => 'Order #:order_id',
            'cancel-btn-title'             => 'Cancel',
            'shipment-btn-title'           => 'Ship',
            'invoice-btn-title'            => 'Invoice',
            'info'                         => 'Information',
            'invoices'                     => 'Invoices',
            'shipments'                    => 'Shipments',
            'order-and-account'            => 'Order and Account',
            'order-info'                   => 'Order Information',
            'order-date'                   => 'Order Date',
            'order-status'                 => 'Order Status',
            'order-status-canceled'        => 'Canceled',
            'order-status-closed'          => 'Closed',
            'order-status-fraud'           => 'Fraud',
            'order-status-pending'         => 'Pending',
            'order-status-pending-payment' => 'Pending Payment',
            'order-status-processing'      => 'Processing',
            'order-status-success'         => 'Completed',
            'channel'                      => 'Channel',
            'customer-name'                => 'Customer Name',
            'email'                        => 'Email',
            'contact-number'               => 'Contact Number',
            'account-info'                 => 'Account Information',
            'address'                      => 'Address',
            'shipping-address'             => 'Shipping Address',
            'billing-address'              => 'Billing Address',
            'payment-and-shipping'         => 'Payment and Shipping',
            'payment-info'                 => 'Payment Information',
            'payment-method'               => 'Payment Method',
            'currency'                     => 'Currency',
            'shipping-info'                => 'Shipping Information',
            'shipping-method'              => 'Shipping Method',
            'shipping-price'               => 'Shipping Price',
            'products-ordered'             => 'Products Ordered',
            'SKU'                          => 'SKU',
            'product-name'                 => 'Product Name',
            'qty'                          => 'Qty',
            'item-status'                  => 'Item Status',
            'item-ordered'                 => 'Ordered (:qty_ordered)',
            'item-invoice'                 => 'Invoiced (:qty_invoiced)',
            'item-shipped'                 => 'Shipped (:qty_shipped)',
            'item-canceled'                => 'Canceled (:qty_canceled)',
            'item-refunded'                => 'Refunded (:qty_refunded)',
            'price'                        => 'Price',
            'total'                        => 'Total',
            'subtotal'                     => 'Subtotal',
            'shipping-handling'            => 'Shipping & Handling',
            'discount'                     => 'Discount',
            'tax'                          => 'Tax',
            'tax-percent'                  => 'Tax Percent',
            'tax-amount'                   => 'Tax Amount',
            'discount-amount'              => 'Discount Amount',
            'grand-total'                  => 'Grand Total',
            'total-paid'                   => 'Total Paid',
            'total-refunded'               => 'Total Refunded',
            'total-due'                    => 'Total Due',
            'cancel-confirm-msg'           => 'Are you sure you want to cancel this order ?',
            'refund-btn-title'             => 'Refund',
            'refunds'                      => 'Refunds',
            'comment-added-success'        => 'Comment addded successfully.',
            'comment'                      => 'Comment',
            'submit-comment'               => 'Submit Comment',
            'notify-customer'              => 'Notify Customer',
            'customer-notified'            => ':date | Customer <b>Notified</b>',
            'customer-not-notified'        => ':date | Customer <b>Not Notified</b>',
            'transactions'                 => 'Transactions',
        ],

        'invoices' => [
            'title'                  => 'Invoices',
            'id'                     => 'ID',
            'invoice'                => 'Invoice',
            'invoice-id'             => 'Invoice ID',
            'date'                   => 'Invoice Date',
            'order-id'               => 'Order ID',
            'customer-name'          => 'Customer Name',
            'status'                 => 'Status',
            'amount'                 => 'Amount',
            'action'                 => 'Action',
            'add-title'              => 'Create Invoice',
            'save-btn-title'         => 'Save Invoice',
            'send-duplicate-invoice' => 'Send Duplicate Invoice',
            'send'                   => 'Send',
            'invoice-sent'           => 'Invoice sent successfully!',
            'qty'                    => 'Qty',
            'qty-ordered'            => 'Qty Ordered',
            'qty-to-invoice'         => 'Qty to Invoice',
            'view-title'             => 'Invoice #:invoice_id',
            'bill-to'                => 'Bill to',
            'ship-to'                => 'Ship to',
            'print'                  => 'Print',
            'order-date'             => 'Order Date',
            'invalid-qty'            => 'We found an invalid quantity to invoice items.',
            'creation-error'         => 'Order invoice creation is not allowed.',
            'product-error'          => 'Invoice can not be created without products.',
            'status-overdue'         => 'Overdue',
            'status-pending'         => 'Pending Payment',
            'status-paid'            => 'Paid',
        ],

        'shipments' => [
            'title'             => 'Shipments',
            'id'                => 'ID',
            'date'              => 'Shipment Date',
            'order-id'          => 'Order ID',
            'order-date'        => 'Order date',
            'customer-name'     => 'Customer Name',
            'total-qty'         => 'Total Qty',
            'action'            => 'Action',
            'add-title'         => 'Create Shipment',
            'save-btn-title'    => 'Save Shipment',
            'qty-ordered'       => 'Qty Ordered',
            'qty-invoiced'      => 'Qty Invoiced',
            'qty-to-ship'       => 'Qty to Ship',
            'available-sources' => 'Available Sources',
            'source'            => 'Source',
            'select-source'     => 'Please Select Source',
            'qty-available'     => 'Qty Available',
            'inventory-source'  => 'Inventory Source',
            'carrier-title'     => 'Carrier Title',
            'tracking-number'   => 'Tracking Number',
            'view-title'        => 'Shipment #:shipment_id',
            'creation-error'    => 'Shipment can not be created for this order.',
            'order-error'       => 'Order shipment creation is not allowed.',
            'quantity-invalid'  => 'Requested quantity is invalid or not available.',
        ],

        'refunds' => [
            'title'                       => 'Refunds',
            'id'                          => 'ID',
            'add-title'                   => 'Create Refund',
            'save-btn-title'              => 'Refund',
            'order-id'                    => 'Order ID',
            'qty-ordered'                 => 'Qty Ordered',
            'qty-to-refund'               => 'Qty To Refund',
            'refund-shipping'             => 'Refund Shipping',
            'adjustment-refund'           => 'Adjustment Refund',
            'adjustment-fee'              => 'Adjustment Fee',
            'update-qty'                  => 'Update Quantities',
            'invalid-qty'                 => 'We found an invalid quantity to refund items.',
            'refund-limit-error'          => 'The most money available to refund is :amount.',
            'refunded'                    => 'Refunded',
            'date'                        => 'Refund Date',
            'customer-name'               => 'Customer Name',
            'status'                      => 'Status',
            'action'                      => 'Action',
            'view-title'                  => 'Refund #:refund_id',
            'invalid-refund-amount-error' => 'Refund amount should be non zero.',

        ],

        'transactions' => [
            'title'               => 'Transactions',
            'create-title'        => 'Add transaction',
            'id'                  => 'ID',
            'transaction-id'      => 'Transaction ID',
            'payment-method'      => 'Payment method',
            'transaction-amount'  => 'Transaction amount',
            'action'              => 'Action',
            'view-title'          => 'Transaction #:transaction_id',
            'transaction-data'    => 'Transaction Data',
            'order-id'            => 'Order ID',
            'invoice-id'          => 'Invoice ID',
            'status'              => 'Status',
            'created-at'          => 'Created At',
            'transaction-details' => 'Transaction Details',
            'response'            => [
                'invoice-missing'            => 'This invoice id does not exist',
                'transaction-saved'          => 'The transaction has been saved',
                'already-paid'               => 'This invoice has already been paid',
                'transaction-amount-exceeds' => 'The specified amount of this transaction exceeds the total amount of the invoice.',
                'transaction-amount-zero'    => 'Transaction amount can be zero or less',
            ],
        ],
    ],

    'catalog' => [
        'products' => [
            'title'                         => 'Products',
            'add-product-btn-title'         => 'Add Product',
            'add-title'                     => 'Add Product',
            'edit-title'                    => 'Edit Product',
            'save-btn-title'                => 'Save Product',
            'general'                       => 'General',
            'product-type'                  => 'Product Type',
            'type'                          => [
                'simple'                        => 'Simple',
                'booking'                       => 'Booking',
                'bundle'                        => 'Bundle',
                'downloadable'                  => 'Downloadable',
                'grouped'                       => 'Grouped',
                'virtual'                       => 'Virtual',
                'configurable'                  => 'Configurable',

            ],
            'familiy'                       => 'Attribute Family',
            'sku'                           => 'SKU',
            'configurable-attributes'       => 'Configurable Attributes',
            'attribute-header'              => 'Attribute(s)',
            'attribute-option-header'       => 'Attribute Option(s)',
            'no'                            => 'No',
            'yes'                           => 'Yes',
            'disabled'                      => 'Disabled',
            'enabled'                       => 'Enabled',
            'add-variant-btn-title'         => 'Add Variant',
            'name'                          => 'Name',
            'qty'                           => 'Qty',
            'price'                         => 'Price',
            'weight'                        => 'Weight',
            'status'                        => 'Status',
            'add-variant-title'             => 'Add Variant',
            'add-image-btn-title'           => 'Add Image',
            'mass-delete-success'           => 'All the selected products have been deleted successfully',
            'mass-update-success'           => 'All the selected products have been updated successfully',
            'configurable-error'            => 'Please select atleast one configurable attribute.',
            'categories'                    => 'Categories',
            'images'                        => 'Images',
            'inventories'                   => 'Inventories',
            'variations'                    => 'Variations',
            'downloadable'                  => 'Downloadable Information',
            'links'                         => 'Links',
            'add-link-btn-title'            => 'Add Link',
            'samples'                       => 'Samples',
            'add-sample-btn-title'          => 'Add Sample',
            'downloads'                     => 'Download Allowed',
            'file'                          => 'File',
            'sample'                        => 'Sample',
            'upload-file'                   => 'Upload File',
            'url'                           => 'Url',
            'sort-order'                    => 'Sort Order',
            'browse-file'                   => 'Browse File',
            'product-link'                  => 'Linked Products',
            'cross-selling'                 => 'Cross Selling',
            'up-selling'                    => 'Up Selling',
            'related-products'              => 'Related Products',
            'product-search-hint'           => 'Start typing product name',
            'no-result-found'               => 'Products not found with same name.',
            'searching'                     => 'Searching ...',
            'grouped-products'              => 'Grouped Products',
            'search-products'               => 'Search Products',
            'channel'                       => 'Channels',
            'bundle-items'                  => 'Bundle Items',
            'add-option-btn-title'          => 'Add Option',
            'option-title'                  => 'Option Title',
            'input-type'                    => 'Input Type',
            'is-required'                   => 'Is Required',
            'select'                        => 'Select',
            'radio'                         => 'Radio',
            'checkbox'                      => 'Checkbox',
            'multiselect'                   => 'Multiselect',
            'new-option'                    => 'New Option',
            'is-default'                    => 'Is Default',
            'customer-group'                => 'Customer Group',
            'add-group-price'               => 'Add Customer Group Price',
            'all-group'                     => 'All Groups',
            'fixed'                         => 'Fixed',
            'discount'                      => 'Discount',
            'remove-image-btn-title'        => 'Remove Image',
            'videos'                        => 'Videos',
            'video'                         => 'Video',
            'add-video-btn-title'           => 'Add Video',
            'remove-video-btn-title'        => 'Remove Video',
            'not-support-video'             => 'Your browser does not support the video tag.',
            'variant-already-exist-message' => 'Variant with same attribute options already exists.',
            'save'                          => 'Save',
            'cancel'                        => 'Cancel',
            'saved-inventory-message'       => 'Product inventory saved successfully.',
            'image-size'                    => 'Image resolution should be like 640px X 640px',
            'validations'                   => [
                'quantity-required' => 'Quantity is required.',
                'quantity-integer'  => 'Quantity should be integer.',
                'quantity-min-zero' => 'Quantity should be greater then zero.',
            ],
            'video-size'                    => 'Maximum video size should be like :size',
        ],

        'attributes' => [
            'title'                       => 'Attributes',
            'add-title'                   => 'Add Attribute',
            'edit-title'                  => 'Edit Attribute',
            'save-btn-title'              => 'Save Attribute',
            'general'                     => 'General',
            'code'                        => 'Attribute Code',
            'type'                        => 'Attribute Type',
            'text'                        => 'Text',
            'textarea'                    => 'Textarea',
            'enable-wysiwyg'              => 'Enable Wysiwyg Editor',
            'price'                       => 'Price',
            'boolean'                     => 'Boolean',
            'select'                      => 'Select',
            'multiselect'                 => 'Multiselect',
            'datetime'                    => 'Datetime',
            'date'                        => 'Date',
            'label'                       => 'Label',
            'admin'                       => 'Admin',
            'options'                     => 'Options',
            'position'                    => 'Position',
            'add-option-btn-title'        => 'Add Option',
            'load-more-options-btn-title' => 'Load More Options',
            'validations'                 => 'Validations',
            'input_validation'            => 'Input Validation',
            'is_required'                 => 'Is Required',
            'is_unique'                   => 'Is Unique',
            'number'                      => 'Number',
            'decimal'                     => 'Decimal',
            'email'                       => 'Email',
            'url'                         => 'URL',
            'configuration'               => 'Configuration',
            'status'                      => 'Status',
            'yes'                         => 'Yes',
            'no'                          => 'No',
            'value_per_locale'            => 'Value Per Locale',
            'value_per_channel'           => 'Value Per Channel',
            'is_filterable'               => 'Use in Layered Navigation',
            'is_configurable'             => 'Use To Create Configurable Product',
            'admin_name'                  => 'Admin Name',
            'is_visible_on_front'         => 'Visible on Product View Page on Front-end',
            'swatch_type'                 => 'Swatch Type',
            'dropdown'                    => 'Dropdown',
            'color-swatch'                => 'Color Swatch',
            'image-swatch'                => 'Image Swatch',
            'text-swatch'                 => 'Text Swatch',
            'swatch'                      => 'Swatch',
            'image'                       => 'Image',
            'file'                        => 'File',
            'checkbox'                    => 'Checkbox',
            'use_in_flat'                 => 'Create in Product Flat Table',
            'is_comparable'               => 'Attribute is comparable',
            'default_null_option'         => 'Create default empty option',
            'validation-messages'         => [
                'max-size' => 'The image size must be less than 600 KB',
            ],
        ],
        'families'   => [
            'title'                => 'Families',
            'add-family-btn-title' => 'Add Family',
            'add-title'            => 'Add Family',
            'edit-title'           => 'Edit Family',
            'save-btn-title'       => 'Save Family',
            'general'              => 'General',
            'code'                 => 'Family Code',
            'name'                 => 'Name',
            'groups'               => 'Groups',
            'add-group-title'      => 'Add Group',
            'edit-group-title'     => 'Edit Group',
            'update-group-title'   => 'Update Group',
            'position'             => 'Position',
            'attribute-code'       => 'Code',
            'type'                 => 'Type',
            'add-attribute-title'  => 'Add Attributes',
            'search'               => 'Search',
            'group-exist-error'    => 'Group with same name already exists.',
        ],
        'categories' => [
            'title'                    => 'Categories',
            'add-title'                => 'Add Category',
            'edit-title'               => 'Edit Category',
            'save-btn-title'           => 'Save Category',
            'general'                  => 'General',
            'name'                     => 'Name',
            'visible-in-menu'          => 'Visible In Menu',
            'yes'                      => 'Yes',
            'no'                       => 'No',
            'position'                 => 'Position',
            'display-mode'             => 'Display Mode',
            'products-and-description' => 'Products and Description',
            'products-only'            => 'Products Only',
            'description-only'         => 'Description Only',
            'description-and-images'   => 'Description and Images',
            'description'              => 'Description',
            'parent-category'          => 'Parent Category',
            'seo'                      => 'Search Engine Optimization',
            'products'                 => 'Products',
            'slug'                     => 'Slug',
            'meta_title'               => 'Meta Title',
            'meta_description'         => 'Meta Description',
            'meta_keywords'            => 'Meta Keywords',
            'image'                    => 'Image',
            'filterable-attributes'    => 'Filterable Attributes',
            'attributes'               => 'Attributes',
            'image-size'               => 'Image resolution should be like 300px X 168px',
            'image-size-logo'          => 'Image resolution should be like 20px X 20px',
            'mass-update-success'      => 'All the selected categories have been updated successfully',
        ],
    ],

    'configuration' => [
        'title'                       => 'Configuration',
        'save-btn-title'              => 'Save',
        'save-message'                => 'Configuration saved successfully',
        'yes'                         => 'Yes',
        'no'                          => 'No',
        'delete'                      => 'Delete',
        'enable-atleast-one-shipping' => 'Включите хотя бы один способ доставки.',
        'enable-atleast-one-payment'  => 'Включите хотя бы один способ оплаты.',

        'tax-categories' => [
            'title'           => 'Tax Categories',
            'add-title'       => 'Add Tax Category',
            'edit-title'      => 'Edit Tax Category',
            'save-btn-title'  => 'Save Tax Category',
            'general'         => 'Tax Category',
            'select-channel'  => 'Select Channel',
            'name'            => 'Name',
            'code'            => 'Code',
            'description'     => 'Description',
            'select-taxrates' => 'Select Tax Rates',
            'edit'            => [
                'title'             => 'Edit Tax Category',
                'edit-button-title' => 'Edit Tax Category',
            ],
        ],

        'tax-rates' => [
            'title'          => 'Tax Rates',
            'add-title'      => 'Add Tax Rate',
            'edit-title'     => 'Edit Tax Rate',
            'save-btn-title' => 'Save Tax Rate',
            'general'        => 'Tax Rate',
            'identifier'     => 'Identifier',
            'is_zip'         => 'Enable Zip Range',
            'zip_from'       => 'Zip From',
            'zip_to'         => 'Zip To',
            'state'          => 'State',
            'select-state'   => 'Select a region, state or province.',
            'country'        => 'Country',
            'tax_rate'       => 'Rate',
            'edit'           => [
                'title'             => 'Edit Tax Rate',
                'edit-button-title' => 'Edit Rate',
            ],
            'zip_code'       => 'Zip Code',
        ],

        'sales' => [
            'shipping-method' => [
                'title'          => 'Shipping Methods',
                'save-btn-title' => 'Save',
                'description'    => 'Description',
                'active'         => 'Active',
                'status'         => 'Status',
            ],
        ],
    ],

    'settings' => [
        'locales'           => [
            'title'             => 'Locales',
            'add-title'         => 'Add Locale',
            'edit-title'        => 'Edit Locale',
            'save-btn-title'    => 'Save Locale',
            'general'           => 'General',
            'code'              => 'Code',
            'name'              => 'Name',
            'direction'         => 'Direction',
            'create-success'    => 'Locale created successfully.',
            'update-success'    => 'Locale updated successfully.',
            'delete-success'    => 'Locale deleted successfully.',
            'last-delete-error' => 'At least one Locale is required.',
        ],
        'countries'         => [
            'title'          => 'Countries',
            'add-title'      => 'Add Country',
            'save-btn-title' => 'Save Country',
            'general'        => 'General',
            'code'           => 'Code',
            'name'           => 'Name',
        ],
        'currencies'        => [
            'title'             => 'Currencies',
            'add-title'         => 'Add Currency',
            'edit-title'        => 'Edit Currency',
            'save-btn-title'    => 'Save Currency',
            'general'           => 'General',
            'code'              => 'Code',
            'name'              => 'Name',
            'symbol'            => 'Symbol',
            'create-success'    => 'Currency created successfully.',
            'update-success'    => 'Currency updated successfully.',
            'delete-success'    => 'Currency deleted successfully.',
            'last-delete-error' => 'At least one Currency is required.',
        ],
        'exchange_rates'    => [
            'title'                    => 'Exchange Rates',
            'add-title'                => 'Add',
            'edit-title'               => 'Edit Exchange Rate',
            'save-btn-title'           => 'Save',
            'general'                  => 'General',
            'source_currency'          => 'Source Currency',
            'target_currency'          => 'Target Currency',
            'rate'                     => 'Rate',
            'exchange-class-not-found' => ':service exchange rate class not found',
            'update-rates'             => 'Update Rates',
            'create-success'           => 'Exchange Rate created successfully.',
            'update-success'           => 'Exchange Rate updated successfully.',
            'delete-success'           => 'Exchange Rate deleted successfully.',
            'last-delete-error'        => 'At least one Exchange Rate is required.',
        ],
        'inventory_sources' => [
            'title'             => 'Inventory Sources',
            'add'               => 'Add',
            'add-title'         => 'Add Inventory Source',
            'edit-title'        => 'Edit Inventory Source',
            'save-btn-title'    => 'Save',
            'general'           => 'General',
            'code'              => 'Code',
            'name'              => 'Name',
            'description'       => 'Description',
            'source-is-active'  => 'Source is Active',
            'contact-info'      => 'Contact Information',
            'contact_name'      => 'Name',
            'contact_email'     => 'Email',
            'contact_number'    => 'Contact Number',
            'contact_fax'       => 'Fax',
            'address'           => 'Source Address',
            'country'           => 'Country',
            'state'             => 'State',
            'city'              => 'City',
            'street'            => 'Street',
            'postcode'          => 'Postcode',
            'priority'          => 'Priority',
            'latitude'          => 'Latitude',
            'longitude'         => 'Longitude',
            'status'            => 'Status',
            'create-success'    => 'Inventory source created successfully.',
            'update-success'    => 'Inventory source updated successfully.',
            'delete-success'    => 'Inventory source deleted successfully.',
            'last-delete-error' => 'At least one Inventory source is required.',
        ],
        'channels'          => [
            'title'                  => 'Channels',
            'add-title'              => 'Add Channel',
            'edit-title'             => 'Edit Channel',
            'save-btn-title'         => 'Save Channel',
            'general'                => 'General',
            'code'                   => 'Code',
            'name'                   => 'Name',
            'description'            => 'Description',
            'hostname'               => 'Hostname',
            'hostname-placeholder'   => 'https://www.example.com (Don\'t add slash in the end.)',
            'currencies-and-locales' => 'Currencies and Locales',
            'locales'                => 'Locales',
            'default-locale'         => 'Default Locale',
            'currencies'             => 'Currencies',
            'base-currency'          => 'Default Currency',
            'root-category'          => 'Root Category',
            'inventory_sources'      => 'Inventory Sources',
            'design'                 => 'Design',
            'theme'                  => 'Theme',
            'home_page_content'      => 'Home Page Content',
            'footer_content'         => 'Footer Content',
            'logo'                   => 'Logo',
            'favicon'                => 'Favicon',
            'create-success'         => 'Channel created successfully.',
            'update-success'         => 'Channel updated successfully.',
            'delete-success'         => 'Channel deleted successfully.',
            'last-delete-error'      => 'At least one Channel is required.',
            'seo'                    => 'Home page SEO',
            'seo-title'              => 'Meta title',
            'seo-description'        => 'Meta description',
            'seo-keywords'           => 'Meta keywords',
            'maintenance-mode'       => 'Maintenance Mode',
            'maintenance-mode-text'  => 'Message',
            'allowed-ips'            => 'Allowed IPs',
            'logo-size'              => 'Image resolution should be like 192px X 50px',
            'favicon-size'           => 'Image resolution should be like 16px X 16px',
        ],

        'sliders' => [
            'title'           => 'Sliders',
            'name'            => 'Name',
            'add-title'       => 'Create Slider',
            'edit-title'      => 'Edit Slider',
            'save-btn-title'  => 'Save Slider',
            'general'         => 'General',
            'image'           => 'Image',
            'content'         => 'Content',
            'channels'        => 'Channel',
            'created-success' => 'Slider item created successfully',
            'created-fault'   => 'Error in creating slider item',
            'update-success'  => 'Slider item successfully updated',
            'update-fail'     => 'Slider cannot be updated',
            'delete-success'  => 'Cannot delete last slider item',
            'delete-fail'     => 'Slider item successfully deleted',
            'expired-at'      => 'Expire Date',
            'sort-order'      => 'Sort Order',
            'image-size'      => 'Image resolution should be like 1920px X 550px',
        ],

        'tax-categories' => [
            'title'           => 'Tax Categories',
            'create'          => 'Create',
            'add-title'       => 'Create Tax Category',
            'edit-title'      => 'Edit Tax Category',
            'save-btn-title'  => 'Save Tax Category',
            'general'         => 'Tax Category',
            'select-channel'  => 'Select Channel',
            'name'            => 'Name',
            'code'            => 'Code',
            'description'     => 'Description',
            'select-taxrates' => 'Select Tax Rates',
            'edit'            => [
                'title'             => 'Edit Tax Category',
                'edit-button-title' => 'Edit Tax Category',
            ],
            'create-success'  => 'New Tax Category Created',
            'create-error'    => 'Error, While Creating Tax Category',
            'update-success'  => 'Successfully Updated Tax Category',
            'update-error'    => 'Error While Updating Tax Category',
            'atleast-one'     => 'Cannot Delete The Last Tax Category',
            'delete'          => 'Tax Category Successfully Deleted',
        ],

        'tax-rates'   => [
            'title'          => 'Tax Rates',
            'add-title'      => 'Create Tax Rate',
            'edit-title'     => 'Edit Tax Rate',
            'save-btn-title' => 'Save Tax Rate',
            'general'        => 'Tax Rate',
            'identifier'     => 'Identifier',
            'is_zip'         => 'Enable Zip Range',
            'zip_from'       => 'Zip From',
            'zip_to'         => 'Zip To',
            'state'          => 'State',
            'select-state'   => 'Select a region, state or province.',
            'country'        => 'Country',
            'tax_rate'       => 'Rate',
            'edit'           => [
                'title'             => 'Edit Tax Rate',
                'edit-button-title' => 'Edit Rate',
            ],
            'zip_code'       => 'Zip Code',
            'create-success' => 'Tax Rate Created Successfully',
            'create-error'   => 'Cannot Create Tax Rate',
            'update-success' => 'Tax Rate Updated Successfully',
            'update-error'   => 'Error! Tax Rate Cannot Be Updated',
            'delete'         => 'Tax Rate Deleted Successfully',
            'atleast-one'    => 'Cannot Delete Last Tax Rate',
        ],
        'development' => [
            'title' => 'Development',
        ],
    ],

    'customers' => [
        'groups' => [
            'add-title'       => 'Add Group',
            'edit-title'      => 'Edit Group',
            'save-btn-title'  => 'Save Group',
            'title'           => 'Groups',
            'code'            => 'Code',
            'name'            => 'Name',
            'is_user_defined' => 'User Defined',
            'yes'             => 'Yes',
        ],

        'addresses' => [
            'title'               => ':customer_name\'s Addresses List',
            'vat_id'              => 'Vat ID',
            'create-title'        => 'Create Customer\'s Address',
            'edit-title'          => 'Update Customer\'s Address',
            'title-orders'        => ':customer_name\'s Orders List',
            'address-list'        => 'Address\'s List',
            'order-list'          => 'Order\'s List',
            'address-id'          => 'Address ID',
            'company-name'        => 'Company Name',
            'address-1'           => 'Address 1',
            'city'                => 'City',
            'state-name'          => 'State',
            'country-name'        => 'Country',
            'postcode'            => 'Post Code',
            'default-address'     => 'Default Address',
            'yes'                 => 'Yes',
            'not-approved'        => 'Not Approved',
            'no'                  => 'No',
            'dash'                => '-',
            'delete'              => 'Delete',
            'create-btn-title'    => 'Add Address',
            'save-btn-title'      => 'Save Address',
            'general'             => 'General',
            'success-create'      => 'Customer address created successfully.',
            'success-update'      => 'Customer address updated successfully.',
            'success-delete'      => 'Customer address deleted successfully.',
            'success-mass-delete' => 'Selected addresses deleted successfully.',
            'error-create'        => 'Customer address not created.',
        ],

        'note' => [
            'title'      => 'Add Note',
            'save-note'  => 'Save Note',
            'enter-note' => 'Enter Note',
            'help-title' => 'Add Note On This Customer',
        ],

        'customers' => [
            'add-title'                 => 'Add Customer',
            'edit-title'                => 'Edit Customer',
            'title'                     => 'Customers',
            'first_name'                => 'First Name',
            'last_name'                 => 'Last Name',
            'select-gender'             => 'Select Gender',
            'gender'                    => 'Gender',
            'email'                     => 'Email',
            'date_of_birth'             => 'Date of Birth',
            'date_of_birth_placeholder' => 'yyyy-mm-dd',
            'phone'                     => 'Phone',
            'customer_group'            => 'Customer Group',
            'save-btn-title'            => 'Save Customer',
            'channel_name'              => 'Channel Name',
            'state'                     => 'State',
            'select-state'              => 'Select a region, state or province.',
            'country'                   => 'Country',
            'other'                     => 'Other',
            'male'                      => 'Male',
            'female'                    => 'Female',
            'group-default'             => 'Cannot delete the default group.',
            'edit-help-title'           => 'Edit Customer',
            'delete-help-title'         => 'Delete Customer',
            'addresses'                 => 'Addresses',
            'mass-destroy-success'      => 'Customers deleted successfully',
            'mass-update-success'       => 'Customers updated successfully',
            'status'                    => 'Status',
            'active'                    => 'Active',
            'inactive'                  => 'Inactive',
            'is-suspended'              => 'Is Suspended',
            'suspend'                   => 'Suspend',
            'suspended'                 => 'Suspended',
        ],

        'reviews' => [
            'title'       => 'Reviews',
            'edit-title'  => 'Edit Review',
            'rating'      => 'Rating',
            'status'      => 'Status',
            'comment'     => 'Comment',
            'pending'     => 'Pending',
            'approved'    => 'Approve',
            'disapproved' => 'Disapprove',
        ],

        'subscribers' => [
            'title'          => 'Newsletter Subscribers',
            'title-edit'     => 'Edit Newsletter Subscriber',
            'email'          => 'Email',
            'is_subscribed'  => 'Subscribed',
            'edit-btn-title' => 'Update Subscriber',
            'update-success' => 'Subscriber was successfully updated',
            'update-failed'  => 'Error! You cannot unsubscribe the subscriber',
            'delete'         => 'Subscriber was successfully deleted',
            'delete-failed'  => 'Error! Subscriber cannot be deleted',
            'update-failed'  => 'Error! You cannot unsubscribe the subscriber',
            'delete'         => 'Subscriber was successfully deleted',
            'delete-failed'  => 'Error! Subscriber cannot be deleted',
        ],

        'orders' => [
            'list'  => ':customer_name\'s orders List',
            'title' => 'Orders',
        ],
    ],

    'promotions' => [
        'cart-rules' => [
            'title'                          => 'Cart Rules',
            'add-title'                      => 'Add Cart Rule',
            'edit-title'                     => 'Edit Cart Rule',
            'save-btn-title'                 => 'Save Cart Rule',
            'rule-information'               => 'Rule Information',
            'name'                           => 'Name',
            'description'                    => 'Description',
            'status'                         => 'Status',
            'is-active'                      => 'Cart Rule is Active',
            'channels'                       => 'Channels',
            'customer-groups'                => 'Customer Groups',
            'coupon-type'                    => 'Coupon Type',
            'no-coupon'                      => 'No Coupon',
            'specific-coupon'                => 'Specific Coupon',
            'auto-generate-coupon'           => 'Auto Generate Coupon',
            'no'                             => 'No',
            'yes'                            => 'Yes',
            'coupon-code'                    => 'Coupon Code',
            'uses-per-coupon'                => 'Uses Per Coupon',
            'uses-per-customer'              => 'Uses Per Customer',
            'uses-per-customer-control-info' => 'Will be used for logged in customers only.',
            'from'                           => 'From',
            'to'                             => 'To',
            'priority'                       => 'Priority',
            'conditions'                     => 'Conditions',
            'condition-type'                 => 'Condition Type',
            'all-conditions-true'            => 'All Conditions are True',
            'any-condition-true'             => 'Any Condition is True',
            'add-condition'                  => 'Add Condition',
            'choose-condition-to-add'        => 'Choose a condition to add',
            'cart-attribute'                 => 'Cart Attribute',
            'subtotal'                       => 'Subtotal',
            'additional'                     => 'Additional Information',
            'total-items-qty'                => 'Total Items Qty',
            'total-weight'                   => 'Total Weight',
            'payment-method'                 => 'Payment Method',
            'shipping-method'                => 'Shipping Method',
            'shipping-postcode'              => 'Shipping Zip/Postcode',
            'shipping-state'                 => 'Shipping State',
            'shipping-country'               => 'Shipping Country',
            'cart-item-attribute'            => 'Cart Item Attribute',
            'price-in-cart'                  => 'Price in Cart',
            'qty-in-cart'                    => 'Qty in Cart',
            'product-attribute'              => 'Product Attribute',
            'attribute-name-children-only'   => ':attribute_name (Children Only)',
            'attribute-name-parent-only'     => ':attribute_name (Parent Only)',
            'is-equal-to'                    => 'Is equal to',
            'is-not-equal-to'                => 'Is not equal to',
            'equals-or-greater-than'         => 'Equals or greater than',
            'equals-or-less-than'            => 'Equals or less than',
            'greater-than'                   => 'Greater than',
            'less-than'                      => 'Less than',
            'contain'                        => 'Contain',
            'contains'                       => 'Contains',
            'does-not-contain'               => 'Does not contain',
            'actions'                        => 'Actions',
            'action-type'                    => 'Action Type',
            'percentage-product-price'       => 'Percentage of Product Price',
            'fixed-amount'                   => 'Fixed Amount',
            'fixed-amount-whole-cart'        => 'Fixed Amount to Whole Cart',
            'buy-x-get-y-free'               => 'Buy X Get Y Free',
            'discount-amount'                => 'Discount Amount',
            'discount-quantity'              => 'Maximum Quantity Allowed to be Discounted',
            'discount-step'                  => 'Buy X Quantity',
            'free-shipping'                  => 'Free Shipping',
            'apply-to-shipping'              => 'Apply to Shipping',
            'coupon-codes'                   => 'Coupon Codes',
            'coupon-qty'                     => 'Coupon Qty',
            'code-length'                    => 'Code Length',
            'code-format'                    => 'Code Format',
            'alphanumeric'                   => 'Alphanumeric',
            'alphabetical'                   => 'Alphabetical',
            'numeric'                        => 'Numeric',
            'code-prefix'                    => 'Code Prefix',
            'code-suffix'                    => 'Code Suffix',
            'generate'                       => 'Generate',
            'cart-rule-not-defind-error'     => 'Cart rule is not defined',
            'end-other-rules'                => 'End Other Rules',
            'children-categories'            => 'Categories (Children Only)',
            'parent-categories'              => 'Categories (Parent Only)',
            'categories'                     => 'Categories',
            'attribute_family'               => 'Attribute Family',
            'mass-delete-success'            => 'All the selected coupons have been deleted successfully.',
        ],

        'catalog-rules' => [
            'title'                        => 'Catalog Rules',
            'add-title'                    => 'Add Catalog Rule',
            'edit-title'                   => 'Edit Catalog Rule',
            'save-btn-title'               => 'Save Catalog Rule',
            'rule-information'             => 'Rule Information',
            'name'                         => 'Name',
            'description'                  => 'Description',
            'status'                       => 'Status',
            'is-active'                    => 'Catalog Rule is Active',
            'channels'                     => 'Channels',
            'customer-groups'              => 'Customer Groups',
            'no'                           => 'No',
            'yes'                          => 'Yes',
            'from'                         => 'From',
            'to'                           => 'To',
            'priority'                     => 'Priority',
            'conditions'                   => 'Conditions',
            'end-other-rules'              => 'End Other Rules',
            'categories'                   => 'Categories',
            'attribute_family'             => 'Attribute Family',
            'condition-type'               => 'Condition Type',
            'all-conditions-true'          => 'All Conditions are True',
            'any-condition-true'           => 'Any Condition is True',
            'add-condition'                => 'Add Condition',
            'choose-condition-to-add'      => 'Choose a condition to add',
            'product-attribute'            => 'Product Attribute',
            'attribute-name-children-only' => ':attribute_name (Children Only)',
            'attribute-name-parent-only'   => ':attribute_name (Parent Only)',
            'is-equal-to'                  => 'Is equal to',
            'is-not-equal-to'              => 'Is not equal to',
            'equals-or-greater-than'       => 'Equals or greater than',
            'equals-or-less-than'          => 'Equals or less than',
            'greater-than'                 => 'Greater than',
            'less-than'                    => 'Less than',
            'contain'                      => 'Contain',
            'contains'                     => 'Contains',
            'does-not-contain'             => 'Does not contain',
            'actions'                      => 'Actions',
            'action-type'                  => 'Action Type',
            'percentage-product-price'     => 'Percentage of Product Price',
            'fixed-amount'                 => 'Fixed Amount',
            'fixed-amount-whole-cart'      => 'Fixed Amount to Whole Catalog',
            'buy-x-get-y-free'             => 'Buy X Get Y Free',
            'discount-amount'              => 'Discount Amount',
            'mass-delete-success'          => 'All the selected index of coupons have been deleted successfully.',
        ],
    ],

    'marketing' => [
        'templates' => [
            'title'          => 'Email Templates',
            'add-title'      => 'Add Email Template',
            'edit-title'     => 'Edit Email Template',
            'save-btn-title' => 'Save',
            'general'        => 'General',
            'name'           => 'Name',
            'status'         => 'Status',
            'active'         => 'Active',
            'inactive'       => 'Inactive',
            'draft'          => 'Draft',
            'content'        => 'Content',
            'create-success' => 'Email template created successfully.',
            'update-success' => 'Email template updated successfully.',
            'delete-success' => 'Email template deleted successfully',
        ],

        'campaigns' => [
            'title'          => 'Campaigns',
            'add-title'      => 'Add Campaign',
            'edit-title'     => 'Edit Campaign',
            'save-btn-title' => 'Save',
            'general'        => 'General',
            'name'           => 'Name',
            'status'         => 'Status',
            'active'         => 'Active',
            'inactive'       => 'Inactive',
            'subject'        => 'Subject',
            'email-template' => 'Email Template',
            'audience'       => 'Audience',
            'channel'        => 'Channel',
            'customer-group' => 'Customer Group',
            'schedule'       => 'Schedule',
            'schedule-type'  => 'Schedule Type',
            'once'           => 'Once',
            'events'         => 'Events',
            'schedule-date'  => 'Schedule Date',
            'spooling'       => 'Spooling',
            'event'          => 'Event',
            'birthday'       => 'Birthday',
            'create-success' => 'Campaign created successfully.',
            'update-success' => 'Campaign updated successfully.',
            'delete-success' => 'Campaign deleted successfully',
        ],

        'events' => [
            'title'          => 'Events',
            'add-title'      => 'Add Event',
            'edit-title'     => 'Edit Event',
            'save-btn-title' => 'Save',
            'general'        => 'General',
            'name'           => 'Name',
            'description'    => 'Description',
            'date'           => 'Date',
            'create-success' => 'Event created successfully.',
            'update-success' => 'Event updated successfully.',
            'delete-success' => 'Event deleted successfully.',
            'edit-error'     => 'Can not edit this event.',
        ],

        'sitemaps' => [
            'title'          => 'Sitemaps',
            'add-title'      => 'Add Sitemap',
            'edit-title'     => 'Edit Sitemap',
            'save-btn-title' => 'Save',
            'general'        => 'General',
            'file-name'      => 'File Name',
            'file-name-info' => 'Example: sitemap.xml',
            'path'           => 'Path',
            'path-info'      => 'Example: "/sitemap/" or "/" for base path',
            'create-success' => 'Sitemap created successfully.',
            'update-success' => 'Sitemap updated successfully.',
            'delete-success' => 'Sitemap deleted successfully.',
        ],
    ],

    'error' => [
        'go-to-home'    => 'GO TO HOME',
        'in-maitainace' => 'In Maintenance',
        'right-back'    => 'Be Right Back',

        '404' => [
            'page-title' => '404 Page not found',
            'name'       => '404',
            'title'      => 'Page Not found',
            'message'    => 'The Page you are looking for does not exist or have been moved. Navigate using sidemenu.',
        ],
        '403' => [
            'page-title' => '403 forbidden Error',
            'name'       => '403',
            'title'      => 'Forbidden error',
            'message'    => 'You do not have permission to access this page',
        ],
        '500' => [
            'page-title' => '500 Internal Server Error',
            'name'       => '500',
            'title'      => 'Internal Server Error',
            'message'    => 'The Server Encountered an internal error.',
        ],
        '401' => [
            'page-title' => '401 Unauthorized Error',
            'name'       => '401',
            'title'      => 'Unauthorized Error',
            'message'    => 'The request has not been applied because it lacks valid authentication credentials for the target resource.',
        ],

        'tinymce' => [
            'http-error'    => 'HTTP error.',
            'invalid-json'  => 'Invalid JSON.',
            'upload-failed' => 'Image upload failed due to a XHR Transport error.',
        ],
    ],

    'export' => [
        'export'           => 'Export',
        'import'           => 'Import',
        'format'           => 'Select Format',
        'download'         => 'Download',
        'upload'           => 'Upload',
        'csv'              => 'CSV',
        'xls'              => 'XLS',
        'file'             => 'File',
        'upload-error'     => 'The file must be a file of type: xls, xlsx, csv.',
        'duplicate-error'  => 'Identifier must be unique, duplicate identifier :identifier at row :position.',
        'enough-row-error' => 'file has not enough rows',
        'allowed-type'     => 'Allowed Type :',
        'file-type'        => 'csv, xls, xlsx.',
        'no-records'       => 'Nothing to export',
        'illegal-format'   => 'Error! This type of format is either not supported or its illegal format',
    ],

    'cms' => [
        'pages' => [
            'general'          => 'General',
            'seo'              => 'SEO',
            'pages'            => 'Pages',
            'title'            => 'Pages',
            'add-title'        => 'Add Page',
            'content'          => 'Content',
            'url-key'          => 'URL Key',
            'channel'          => 'Channels',
            'locale'           => 'Locales',
            'create-btn-title' => 'Save Page',
            'edit-title'       => 'Edit Page',
            'edit-btn-title'   => 'Save Page',
            'create-success'   => 'Page created successfully',
            'create-partial'   => 'Some of the pages requested already exists',
            'create-failure'   => 'All pages requested already exists',
            'update-success'   => 'Page updated successfully',
            'update-failure'   => 'Page cannot be updated',
            'page-title'       => 'Page Title',
            'layout'           => 'Layout',
            'meta_keywords'    => 'Meta Keywords',
            'meta_description' => 'Meta Description',
            'meta_title'       => 'Meta Title',
            'delete-success'   => 'CMS page deleted successfully',
            'delete-failure'   => 'CMS page cannot be deleted',
            'preview'          => 'Preview',
            'one-col'          => '<div class="mt-10">Use class: <b>"static-container one-column"</b> for one column layout.</div>',
            'two-col'          => '<div class="mt-10">Use class: <b>"static-container two-column"</b> for two column layout.</div>',
            'three-col'        => '<div class="mt-10">Use class: <b>"static-container three-column"</b> for three column layout.</div>',
            'helper-classes'   => 'Helper Classes',
        ],
    ],

    'response' => [
        'being-used'                => 'This :name is getting used in :source',
        'product-copied'            => 'The Product has been copied',
        'error-while-copying'       => 'Something went wrong while trying to copy the product',
        'product-can-not-be-copied' => 'Products of type :type can not be copied',
        'cannot-change'             => 'Cannot change the :name.',
        'cannot-delete-default'     => 'Cannot delete the default channel',
        'create-success'            => ':name created successfully.',
        'update-success'            => ':name updated successfully.',
        'delete-success'            => ':name deleted successfully.',
        'delete-failed'             => 'Error encountered while deleting :name.',
        'last-delete-error'         => 'At least one :name is required.',
        'user-define-error'         => 'Can not delete system :name',
        'attribute-error'           => ':name is used in configurable products.',
        'attribute-product-error'   => ':name is used in products.',
        'customer-associate'        => ':name can not be deleted because customer is associated with this group.',
        'currency-delete-error'     => 'This currency is set as channel base currency so it can not be deleted.',
        'upload-success'            => ':name uploaded successfully.',
        'delete-category-root'      => 'Cannot delete the root category',
        'create-root-failure'       => 'Category with name root already exists',
        'cancel-success'            => ':name canceled successfully.',
        'cancel-error'              => ':name can not be canceled.',
        'already-taken'             => 'The :name has already been taken.',
        'order-pending'             => 'Cannot delete :name account because some Order(s) are pending or processing state.',
        'something-went-wrong'      => 'Something went wrong!',
    ],

    'validations' => [
        'slug-being-used' => 'This slug is getting used in either categories or products.',
        'slug-reserved'   => 'This slug is reserved.',
    ],

    'footer' => [
        'copy-right' => 'Powered by <a href="https://bagisto.com/" target="_blank">Bagisto</a>, A Community Project by <a href="https://webkul.com/" target="_blank">Webkul</a>',
    ],

    'admin' => [
        'emails' => [
            'email'              => 'Email',
            'notification_label' => 'Notifications',
            'notifications'      => [
                'verification'                                     => 'Send a verification e-mail after customer registration',
                'registration'                                     => 'Send a confirmation e-mail after customer registration',
                'customer-registration-confirmation-mail-to-admin' => 'Send a confirmation e-mail to admin after customer registration',
                'customer'                                         => 'Send the customer account credentials after registration',
                'new-order'                                        => 'Send a confirmation e-mail to the customer after placing a new order',
                'new-admin'                                        => 'Send a confirmation e-mail to the admin after placing a new order',
                'new-invoice'                                      => 'Send a notification e-mail to the customer after creating a new invoice',
                'new-refund'                                       => 'Send a notification e-mail to the customer after creating a refund',
                'new-shipment'                                     => 'Send a notification e-mail to the customer after creating a shipment',
                'new-inventory-source'                             => 'Send a notification e-mail to the inventory source after creating a shipment',
                'cancel-order'                                     => 'Send a notification after canceling an order',
            ],

        ],

        'system' => [
            'catalog'                               => 'Catalog',
            'homepage'                              => 'Homepage configuration',
            'allow-out-of-stock-items'              => 'Allow out of stock items',
            'products'                              => 'Products',
            'guest-checkout'                        => 'Guest Checkout',
            'allow-guest-checkout'                  => 'Allow Guest Checkout',
            'allow-guest-checkout-hint'             => 'Hint: If turned on, this option can be configured for each product specifically.',
            'attribute'                             => 'Attribute',
            'image-upload-size'                     => 'Allowed Image Upload Size (in Kb)',
            'file-upload-size'                      => 'Allowed File Upload Size (in Kb)',
            'review'                                => 'Review',
            'allow-guest-review'                    => 'Allow Guest Review',
            'inventory'                             => 'Inventory',
            'stock-options'                         => 'Stock Options',
            'allow-backorders'                      => 'Allow Backorders',
            'customer'                              => 'Customer',
            'wishlist'                              => 'Wishlist',
            'wishlist-share'                        => 'Enable Sharing',
            'settings'                              => 'Settings',
            'address'                               => 'Address',
            'street-lines'                          => 'Lines in a Street Address',
            'sales'                                 => 'Sales',
            'shipping-methods'                      => 'Shipping Methods',
            'free-shipping'                         => 'Free Shipping',
            'flate-rate-shipping'                   => 'Flat Rate Shipping',
            'shipping'                              => 'Shipping',
            'origin'                                => 'Origin',
            'requirements'                          => 'Requirements',
            'country'                               => 'Country',
            'state'                                 => 'State',
            'zip'                                   => 'Zip',
            'city'                                  => 'City',
            'information'                           => 'Information',
            'street-address'                        => 'Street Address',
            'title'                                 => 'Title',
            'description'                           => 'Description',
            'rate'                                  => 'Rate',
            'status'                                => 'Status',
            'calculate-tax'                         => 'Calculate Tax',
            'type'                                  => 'Type',
            'payment-methods'                       => 'Payment Methods',
            'cash-on-delivery'                      => 'Cash On Delivery',
            'money-transfer'                        => 'Money Transfer',
            'paypal-standard'                       => 'PayPal Standard',
            'business-account'                      => 'Business Account',
            'newsletter'                            => 'Newsletter Subscription',
            'newsletter-subscription'               => 'Allow Newsletter Subscription',
            'email'                                 => 'Email Verification',
            'email-verification'                    => 'Allow Email Verification',
            'sort_order'                            => 'Sort Order',
            'general'                               => 'General',
            'footer'                                => 'Footer',
            'content'                               => 'Content',
            'footer-content'                        => 'Footer Text',
            'footer-toggle'                         => 'Toggle footer',
            'locale-options'                        => 'Unit Options',
            'weight-unit'                           => 'Weight Unit',
            'email-settings'                        => 'Email Settings',
            'email-sender-name'                     => 'Email Sender Name',
            'email-sender-name-tip'                 => 'This name will be displayed in the customers inbox',
            'shop-email-from'                       => 'Shop Email Address',
            'shop-email-from-tip'                   => 'The email address of this channel to send emails to your customers',
            'admin-name'                            => 'Admin Name',
            'admin-name-tip'                        => 'This name will be displayed in all admin emails',
            'admin-email'                           => 'Admin Email',
            'admin-email-tip'                       => 'The email address of the admin for this channel to receive emails',
            'admin-page-limit'                      => 'Default Items Per Page (Admin)',
            'design'                                => 'Design',
            'admin-logo'                            => 'Admin Logo',
            'logo-image'                            => 'Logo Image',
            'credit-max'                            => 'Customer Credit Max',
            'credit-max-value'                      => 'Credit Max Value',
            'use-credit-max'                        => 'Use Credit Max',
            'order-settings'                        => 'Order Settings',
            'orderNumber'                           => 'Order Number Settings',
            'order-number-prefix'                   => 'Order Number Prefix',
            'order-number-length'                   => 'Order Number Length',
            'order-number-suffix'                   => 'Order Number Suffix',
            'order-number-generator-class'          => 'Order Number Generator',
            'minimum-order'                         => 'Minimum Order Settings',
            'minimum-order-amount'                  => 'Minimum Order Amount',
            'invoice-settings'                      => 'Invoice Settings',
            'invoice-number'                        => 'Invoice Number Settings',
            'invoice-number-prefix'                 => 'Invoice Number Prefix',
            'invoice-number-length'                 => 'Invoice Number Length',
            'invoice-number-suffix'                 => 'Invoice Number Suffix',
            'invoice-number-generator-class'        => 'Invoice Number Generator',
            'payment-terms'                         => 'Payment Terms',
            'due-duration'                          => 'Due Duration',
            'due-duration-day'                      => ':due-duration Day',
            'due-duration-days'                     => ':due-duration Days',
            'invoice-slip-design'                   => 'Invoice Slip Design',
            'logo'                                  => 'Logo',
            'default'                               => 'Default',
            'invoice-reminders'                     => 'Invoice Reminders',
            'maximum-limit-of-reminders'            => 'Maximum limit of reminders',
            'interval-between-reminders'            => 'Interval between reminders',
            'sandbox'                               => 'Sandbox',
            'all-channels'                          => 'All Channels',
            'all-locales'                           => 'All Locales',
            'all-customer-groups'                   => 'All Customer groups',
            'storefront'                            => 'Storefront',
            'default-list-mode'                     => 'Default List Mode',
            'grid'                                  => 'Grid',
            'list'                                  => 'List',
            'products-per-page'                     => 'Products Per Page',
            'sort-by'                               => 'Sort By',
            'from-z-a'                              => 'From Z-A',
            'from-a-z'                              => 'From A-Z',
            'newest-first'                          => 'Newest First',
            'oldest-first'                          => 'Oldest First',
            'cheapest-first'                        => 'Cheapest First',
            'expensive-first'                       => 'Expensive First',
            'comma-seperated'                       => 'Comma Seperated',
            'favicon'                               => 'Favicon',
            'seo'                                   => 'SEO',
            'rich-snippets'                         => 'Rich Snippets',
            'enable'                                => 'Enable',
            'show-weight'                           => 'Show Weight',
            'show-categories'                       => 'Show Categories',
            'show-images'                           => 'Show Images',
            'show-reviews'                          => 'Show Reviews',
            'show-ratings'                          => 'Show Ratings',
            'show-offers'                           => 'Show Offers',
            'show-sku'                              => 'Show SKU',
            'categories'                            => 'Categories',
            'store-name'                            => 'Store Name',
            'vat-number'                            => 'Vat Number',
            'contact-number'                        => 'Contact Number',
            'bank-details'                          => 'Bank Details',
            'mailing-address'                       => 'Send Check to',
            'instructions'                          => 'Instructions',
            'custom-scripts'                        => 'Custom Scripts',
            'custom-css'                            => 'Custom CSS',
            'custom-javascript'                     => 'Custom Javascript',
            'paypal-smart-button'                   => 'PayPal',
            'client-id'                             => 'Client ID',
            'client-id-info'                        => 'Use "sb" for testing.',
            'client-secret'                         => 'Client Secret',
            'client-secret-info'                    => 'Add your secret key here',
            'accepted-currencies'                   => 'Accepted currencies',
            'accepted-currencies-info'              => 'Add currency code comma seperated e.g. USD,INR,...',
            'buy-now-button-display'                => 'Allow customers to directly buy products',
            'show-search-input-field'               => 'Show Search Input Field',
            'allow-no-of-new-product-homepage'      => 'Allowed No of New Product in Homepage',
            'allow-no-of-featured-product-homepage' => 'Allowed No of Featured Product in Homepage',
            'width'                                 => 'Width',
            'height'                                => 'Height',
            'cache-small-image'                     => 'Small Image',
            'cache-medium-image'                    => 'Medium Image',
            'cache-large-image'                     => 'Large Image',
            'generate-invoice'                      => 'Automatically generate the invoice after placing an order',
            'set-invoice-status'                    => 'Set the invoice status after creating the invoice to',
            'set-order-status'                      => 'Set the order status after creating the invoice to',
            'generate-invoice-applicable'           => 'Applicable if automatic generate invoice is enabled',
            'records-found'                         => 'Record(s) found',
            'logo-size'                             => 'Image resolution should be like 112px X 41px',
            'favicon-size'                          => 'Image resolution should be like 16px X 16px',
            'invoice-logo-size'                     => 'Image resolution should be like 192px X 50px',
        ],
    ],

    'api' => [
        'system' => [
            'api'                    => 'API',
            'basic-configuration'    => 'Basic Configuration',
            'customer-configuration' => 'Customer Configuration',
            'username'               => 'Username',
            'password'               => 'Password',
            'login-after-register'   => 'Login After Register',
            'info-login'             => 'Info: Customer must be login after registration API.',
        ],
        'auth'   => [
            'invalid-auth'       => 'Warning: You are not authorized to use APIs.',
            'required-token'     => 'Warning: token parameter is required.',
            'invalid-store'      => 'Warning: You are requesting an invalid store.',
            'login-required'     => 'Warning: Customer login is needed to add the product to compare list.',
            'resource-not-found' => 'Warning: Requested :resource not found in the record.',
        ],
    ],

    'notification' => [
        'notification-title'          => 'Notification',
        'title-plural'                => 'Notifications',
        'status'                      => [
            'all'        => 'All',
            'pending'    => 'Pending',
            'processing' => 'Processing',
            'canceled'   => 'Canceled',
            'closed'     => 'Closed',
            'completed'  => 'Completed',
        ],
        'view-all'                    => 'View All Notifications',
        'no-record'                   => 'No Record Found',
        'read-all'                    => 'Mark as Read',
        'notification-marked-success' => 'Notification Marked Successfully',
        'order-status-messages'       => [
            'completed'       => 'Order Completed',
            'closed'          => 'Order Closed',
            'canceled'        => 'Order Canceled',
            'pending'         => 'Order Pending',
            'processing'      => 'Order Processing',
            'pending_payment' => 'Pending Payment',
        ],
    ],
];
