<?php

namespace Webkul\Admin\Http\Controllers\Sales;

use App\Jobs\CryptoPaymentCheckTx;
use App\Jobs\CryptoPaymentCheckTxCustomer;
use App\Jobs\SendLaunchToken;
use Illuminate\Support\Facades\Event;
use Webkul\Admin\DataGrids\OrderDataGrid;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\OrderCommentRepository;
use Webkul\Sales\Repositories\OrderRepository;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    protected $_config;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected OrderCommentRepository $orderCommentRepository
    ) {
        $this->_config = request('_config');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        if (request()->ajax()) {
            return app(OrderDataGrid::class)->toJson();
        }

        return view($this->_config['view']);
    }

    /**
     * Show the view for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function view($id)
    {
        $order = $this->orderRepository->findOrFail($id);

        return view($this->_config['view'], compact('order'));
    }

    /**
     * Cancel action for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel($id)
    {
        $result = $this->orderRepository->cancel($id);

        if ($result) {
            session()->flash('success', trans('admin::app.response.cancel-success', ['name' => 'Order']));
        } else {
            session()->flash('error', trans('admin::app.response.cancel-error', ['name' => 'Order']));
        }

        return redirect()->back();
    }

    /**
     * Add comment to the order
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function comment($id)
    {
        Event::dispatch('sales.order.comment.create.before');

        $comment = $this->orderCommentRepository->create(array_merge(request()->all(), [
            'order_id'          => $id,
            'customer_notified' => request()->has('customer_notified'),
        ]));

        Event::dispatch('sales.order.comment.create.after', $comment);

        session()->flash('success', trans('admin::app.sales.orders.comment-added-success'));

        return redirect()->back();
    }

    public function confirmPayment($id)
    {
        $order = $this->orderRepository->findOrFail($id);

        if ($order->payment->method == 'moneytransfer') {
            dispatch(new CryptoPaymentCheckTxCustomer($order))->onQueue('launchpad');

            dispatch(new SendLaunchToken($order))->onQueue('launchpad')->delay(now()->addSeconds(5));
        }

        if ($order->payment->method == 'paywithcrypto') {
            dispatch(new CryptoPaymentCheckTx($order))->onQueue('launchpad')->delay(now()->addSeconds(5));
        }

        return redirect()->route('admin.sales.orders.view', $id);
    }
}
