<?php

namespace Webkul\Admin\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderCommentNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param  \Webkul\Sales\Contracts\OrderComment  $comment
     * @return void
     */
    public function __construct(public $comment)
    {
        $this->locale = $this->comment->order->customer?->locale ?? app()->getLocale();
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(core()->getSenderEmailDetails()['email'], core()->getSenderEmailDetails()['name'])
            ->to($this->comment->order->customer_email, $this->comment->order->customer_full_name)
            ->subject(trans('mail.order.comment.subject', ['app_name' => config('app.name'), 'order_id' => $this->comment->order->increment_id]))
            ->view('shop::emails.sales.order.comment', [
                'comment' => $this->comment,
                'order'   => $this->comment->order,
            ]);
    }
}
