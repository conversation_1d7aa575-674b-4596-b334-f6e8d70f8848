<?php

use Illuminate\Support\Facades\Route;
use Webkul\Velocity\Http\Controllers\Shop\ShopController;

/**
 * Auth routes.
 */
require 'auth-routes.php';

/**
 * Sales routes.
 */
require 'sales-routes.php';

/**
 * Catalog routes.
 */
require 'catalog-routes.php';

/**
 * Customers routes.
 */
require 'customers-routes.php';

/**
 * Marketing routes.
 */
require 'marketing-routes.php';

/**
 * CMS routes.
 */
require 'cms-routes.php';

/**
 * Settings routes.
 */
require 'settings-routes.php';

/**
 * Configuration routes.
 */
require 'configuration-routes.php';

/**
 * Notification routes.
 */
require 'notification-routes.php';

/**
 * Remaining routes.
 */
require 'rest-routes.php';

// Route::post('/shipments/mint', function () {
//     dd('welcome');
//  })->name('order-mint-post');

// Route::get('/shipments/mint', function () {
//     dd('welcome get');
//  })->name('order-mint');

Route::get('/london-land/{nftid}', [ShopController::class, 'GetSlugFromNftId'])->name('redirect.nftid');
