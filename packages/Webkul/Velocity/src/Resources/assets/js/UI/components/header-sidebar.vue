<template>
    <div
        id="main-category"
        :class="`main-category font-sans text-xl font-semibold left min-w-200p transition-all duration-300 hover:text-terra-light-green text-decoration-none cursor-pointer`"
        @mouseout="toggleSidebar('0', $event, 'mouseout')"
        @mouseover="toggleSidebar('0', $event, 'mouseover')"
    >
        <!-- <i class="rango-view-list align-vertical-top fs18"> </i> -->

        <a
            class="pl5 py-3 transition-all duration-300 hover:text-terra-light-green text-decoration-none"
            v-text="heading"
            @mouseover="toggleSidebar('0', $event, 'mouseover')"
            :href="route"
        >
        </a>
    </div>
</template>

<script>
export default {
    props: ['heading','route']
};
</script>
