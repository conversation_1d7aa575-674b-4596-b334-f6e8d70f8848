<?php

return [
    'congratulations' => 'Glückwunsch',
    'homepage'        => [
        'download'             => 'Herunterladen',
        'drsp'                 => 'Digitales Immobilienanteilszertifikat (DRESC)',
        'search-text'          => 'Suche...',
        'content'              => 'Immobilieninvestoren aus allen Regionen unseres Landes werden unsere Investmentplattform lieben, die schnell, sicher, benutzerfreundlich und rund um die Uhr verfügbar ist.',
        'content-line'         => 'Mit unserem Expertenteam sind wir immer an Ihrer Seite!',
        'easy-buy-sell'        => 'Einfaches Kaufen und Verkaufen',
        'buy-sell'             => 'Kaufen/Verkaufen',
        'drsp-card'            => 'Sie können Ihre Immobilien jederzeit nach Belieben mit "digitalen Immobilienanteilszertifikaten" kaufen oder verkaufen.',
        'mfnft-title'          => 'Multifractional Non-Fungible Token/ MFNFT',
        'mfnft-card'           => 'Tokeninhaber haben Eigentumsrechte an Mieteinnahmen und anderen Erträgen, die aus den Immobilien generiert werden. Sie können auch an Gemeinschaftsentscheidungen über die Immobilien teilnehmen, indem sie ihre Stimmrechte ausüben.',
        'nft-title'            => 'NFT (Non-Fungible Token) Dokumentation',
        'nft-card'             => 'Sie können Ihre Immobilien als NFTs (Non-Fungible Tokens) prägen und sie bei Bedarf auf nationalen und internationalen Marktplätzen in Bargeld umwandeln.',
        'ensure'               => 'Wie erreichen wir das?',
        'ensure-comment'       => 'Thorne bereitet Sie auf schnellere, sicherere und profitablere Immobilieninvestitionen vor, indem Blockchain-Technologie genutzt wird.',
        'ensure-text'          => 'In dieser neuen technologischen Welt, in der Sie Begriffe wie Blockchain, NFTs, Web 3, DeFi finden, möchten wir Sie durchführen.',
        'text'                 => 'Sie können Immobilien kaufen, verkaufen und Mieteinnahmen generieren.',
        'start-button'         => 'Loslegen',
        'portfolio'            => 'Werden Sie einer derjenigen, die die digitalen Immobilienanteilszertifikate (DRESC) der Produkte in unserem Portfolio kaufen und verkaufen!',
        'terramirum-text'      => 'Unser Marktplatz (TerraMirum) wird als moderner Investitionsraum auf Basis von Blockchain-Technologie präsentiert, um virtuelle Weltinvestoren mit einem ungefähren Transaktionsvolumen von 3 Billionen USD daran zu hindern, sich von der realen Welt zu trennen.',
        'tenant'               => 'Mieter',
        'thorne'               => 'Thorne',
        'income'               => 'Einkommen',
        'convert'              => 'Wird in Token umgewandelt',
        'token'                => 'Token-Inhaber erhalten es',
        'send-bank'            => 'Wird auf Ihr Bankkonto gesendet',
        'slider-text'          => 'Blockchain-basierte Immobilien',
        'slider-text-line'     => 'Investitionsplattform',
        'subscribe-newsletter' => 'Abonnieren Sie unseren Newsletter',
        'sign-up'              => 'Registrieren',
        'kvvk-agree-text'      => 'Ich akzeptiere die Datenschutzerklärung und die KVKK-Richtlinie',
        'subscribe-ok'         => 'E-Mail zur Newsletter-Liste hinzugefügt',
        'check-email'          => 'Überprüfen Sie Ihre E-Mail-Adresse!',
        'check-kvkk'           => 'Stellen Sie sicher, dass Sie die Verträge genehmigen!',
        'contact'              => [
            'submit-success' => 'Ihre Nachricht wurde erfolgreich gesendet.',
            'submit-error'   => 'Ihre Nachricht konnte nicht gesendet werden.',
        ],
    ],

    'subscriber' => [
        'subscribe'       => 'Abonnieren Sie unseren Newsletter',
        'kvkk'            => 'Ich akzeptiere die Datenschutzerklärung und KVKK-Richtlinie',
        'subscriber-text' => 'Thorne Bilişim A.Ş. hat zum Ziel, digitale Lösungsvorschläge im Immobiliensektor zu entwickeln, beginnend mit der Auswahl des "Modell-Digitalprodukts".',
    ],

    'products' => [
        'product-cat-title'   => 'In der Kategorie die luxuriösesten Anzeigen',
        'product-cat-text'    => '3. am besten bewerteter Beitrag',
        'breadcrumb-part1'    => 'Startseite',
        'breadcrumb-part2'    => 'Wohnen',
        'breadcrumb-land'     => 'Land',
        'unit-price'          => 'm² Preis',
        'unit-price-land'     => 'Stückpreis',
        'value'               => 'Wert',
        'drsp-sales'          => 'DRESC Verkaufsrate',
        'drsp-unit-price'     => 'DRESC Anteil Stückpreis',
        'property-value'      => 'Wert der Immobilie',
        'drsp-total'          => 'DRESC Gesamtanteilsmenge',
        'using'               => 'Wohnnutzung',
        'type'                => 'Gebäude-/Grundstückstyp',
        'usage-area'          => 'Nutzfläche',
        'using-status'        => 'Nutzungsstatus',
        'details'             => 'Details',
        'bath-color'          => 'Badezimmerfarbe',
        'kitchen-color'       => 'Küchenfarbe',
        'room'                => 'Zimmer',
        'usage-area-sqfit'    => 'Wohnnutzung / qm',
        'parking'             => 'Parkplatz',
        'price'               => 'Preis',
        'land-city'           => 'Stadt',
        'land-district'       => 'Bezirk',
        'land-neighborhood'   => 'Stadtteil',
        'land-ada'            => 'Block',
        'land-parcel'         => 'Grundstück',
        'land-expertise'      => 'Expertise',
        'land-area'           => 'Fläche',
        'land-kind'           => 'Art',
        'land-status'         => 'Aktueller Zustand',
        'land-unit-price'     => 'Stückpreis',
        'land-total-price'    => 'Gesamtpreis',
        'land-total-nft'      => 'Gesamt-NFT',
        'land-nft-unit-price' => 'NFT Stückpreis',
        'land-sold-nft'       => 'Verkaufte NFT',
        'land-sale-nft'       => 'NFT zum Verkauf',
        'land-increase'       => 'Wertsteigerung der NFT',
    ],

    'cart' => [
        'page-title'      => 'Warenkorb',
        'cart-text'       => 'Mein Warenkorb',
        'empty-msg'       => 'Ihr Warenkorb ist leer',
        'empty-btn-msg'   => 'Weiter durchsuchen',
        'share-amount'    => 'Menge',
        'total-amount'    => 'Gesamtbetrag (Anteil)',
        'remainig-share'  => 'Einzelbetrag',
        'address'         => 'Meine Rechnung wird an die gleiche Adresse gesendet.',
        'continue'        => 'Weiter',
        'payment-methods' => 'Zahlungsmethoden',
        'payment-options' => 'Zahlungsoptionen',
        'address-info'    => 'Adressinformationen',
        'share-info'      => 'Anteilsinformationen',
        'campaign-info'   => 'Rabatt angewendet innerhalb der',
        'discount'        => 'Rabatt',
        'discount-amount' => 'Rabattierter Betrag',
    ],

    'mini-cart' => [
        'total' => 'Gesamt',
        'cart'  => 'Warenkorb',
        'share' => 'Anzahl der Anteile',
        'pay'   => 'Bezahlen',
    ],

    'login' => [
        'remember'             => 'Angemeldet bleiben',
        'continue'             => 'Weiter ohne Mitglied',
        'signup'               => 'Registrieren!',
        'signin'               => 'Melden Sie sich bei Ihrem TerraMirum-Konto an',
        'page-title'           => 'Anmeldeseite',
        'singin-button'        => 'Anmelden',
        'forgot-your-password' => 'Passwort vergessen?',
        'email-required'       => 'E-Mail erforderlich.',
        'password-placeholder' => 'Passwort',
        'email-placeholder'    => 'E-Mail',
        'email-error-text'     => 'Bitte stellen Sie sicher, dass Sie die richtige E-Mail-Adresse eingegeben haben.',
        'password-error-text'  => 'Bitte stellen Sie sicher, dass Sie das richtige Passwort eingegeben haben.',
    ],

    'signup' => [
        'hello'              => 'Hallo',
        'start-msg'          => 'Erstellen Sie ein Konto und verpassen Sie nicht die Gelegenheit!',
        'terms'              => 'Ich akzeptiere die Mitgliedschaftsbedingungen, indem ich auf "Registrieren" klicke.',
        'id'                 => 'ID-Nummer',
        'verify'             => 'Verifizieren',
        'phone'              => 'Telefon',
        'pass-confirm'       => 'Passwort bestätigen',
        'uname'              => 'Benutzername',
        'btn-text'           => 'Registrieren',
        'nationality'        => 'Nationalität',
        'for-personal'       => 'PRIVATMITGLIEDSCHAFT',
        'for-instutional'    => 'Unternehmensmitgliedschaft',
        'personal-info'      => 'Informationen zu autorisierten Mitgliedern',
        'company-info'       => 'Unternehmensinformationen',
        'place-of-birth'     => 'Geburtsort',
        'personal-wallet'    => 'Wallet Addresse (Optional)',
    ],
    'forgot-password' => [
        'page-title'        => 'Passwort vergessen',
        'section-title'     => 'Probleme beim Einloggen?',
        'email'             => 'E-Mail',
        'remember-password' => 'Haben Sie Ihr Passwort wiedergefunden?',
        'submit-btn'        => 'Zurücksetzungs-Mail senden',
    ],
    'reset-password' => [
        'page-title'        => 'Passwort zurücksetzen',
        'section-title'     => 'Probleme beim Einloggen?',
        'email'             => 'E-Mail',
        'new-password'      => 'Neues Passwort',
        'confirm-password'  => 'Neues Passwort bestätigen',
        'remember-password' => 'Haben Sie Ihr Passwort wiedergefunden?',
        'submit-btn'        => 'Mein Passwort zurücksetzen',
    ],
    'updated-password' => [
        'page-title'    => 'Erfolg',
        'section-title' => 'Ihr Passwort wurde erfolgreich zurückgesetzt.',
        'login-btn'     => 'Anmelden',
    ],
    'verification' => [
        'page-title' => 'Verifizierung',
        'hello'      => 'Hallo',
        'start-msg'  => 'Bitte verifizieren Sie Ihr Konto, um fortzufahren.',

        'email' => [
            'required' => 'E-Mail ist erforderlich',
            'email'    => 'E-Mail ist ungültig',
            'unique'   => 'E-Mail ist bereits vergeben',
        ],
        'id_number' => [
            'required' => 'ID-Nummer ist erforderlich',
            'numeric'  => 'ID-Nummer muss numerisch sein',
            'digits'   => 'ID-Nummer muss 11 Stellen haben',
            'unique'   => 'ID-Nummer ist bereits vergeben',
        ],
        'first_name' => [
            'required' => 'Vorname ist erforderlich',
            'string'   => 'Vorname muss ein String sein',
        ],
        'last_name' => [
            'required' => 'Nachname ist erforderlich',
            'string'   => 'Nachname muss ein String sein',
        ],
        'date_of_birth' => [
            'required'    => 'Geburtsdatum ist erforderlich',
            'date_format' => 'Geburtsdatum muss im Format YYYY-MM-DD vorliegen',
        ],
        'phone' => [
            'required' => 'Telefonnummer ist erforderlich',
        ],
        'user_name' => [
            'required' => 'Benutzername ist erforderlich',
        ],
    ],
    'verification-pending' => [
        'page-title'  => 'Verifizierung',
        'hello'       => 'Hallo',
        'start-msg'   => 'Die Verifizierung ist noch im Gange. Bitte versuchen Sie es später erneut.',
        'contact-msg' => 'Ihre Verifizierung ist noch nicht abgeschlossen. Brauchen Sie Hilfe? Kontaktieren Sie uns.',
        'verify-info' => 'Ihr Konto-Verifizierungsprozess ist noch nicht abgeschlossen, aber Sie können Einkäufe tätigen. Für Auszahlungen muss die Verifizierung abgeschlossen sein.',
    ],
    'category' => [
        'price-range'    => 'Preisspanne',
        'all-categories' => 'Alle Kategorien',
    ],
    'wishlist' => [
        'title'         => 'Wunschliste',
        'empty-msg'     => 'Es gibt keine Produkte in Ihrer Wunschliste.',
        'empty-content' => 'Entdecken Sie die Token von speziellen Immobilien, aufgeteilt in Tausende von Anteilen.',
        'btn-text'      => 'Entdecken',
    ],
    'address' => [
        'empty-msg'     => 'Es gibt keine Adresse in Ihrem Profil.',
        'empty-content' => 'Sie haben hier keine gespeicherten Adressen, bitte versuchen Sie, eine zu erstellen, indem Sie auf die Schaltfläche Hinzufügen klicken.',
    ],

    'user-profile' => [
        'wallet'             => 'Wallet/Bestellungen',
        'wallet-address'     => 'Wallet-Adresse',
        'empty-wallet'       => 'Ihr Wallet ist leer',
        'table-product-name' => 'Produkt',
        'table-actions'      => 'Aktionen',
        'contract-button'    => 'Smart-Vertrag',
        'contract-notfound'  => 'Smart-Vertrag nicht gefunden',
        'advantage'          => 'Vorteile',
        'wishlist'           => 'Wunschliste',
        'notification'       => 'Benachrichtigungen',
        'address'            => 'Meine Adressen',
        'logout'             => 'Abmelden',
        'preferences'        => 'Kommunikationspräferenzen',
        'user-info'          => 'Benutzerinformationen',
        'account-info'       => 'Kontoinformationen',
        'profile'            => 'Informationsprofil',
        'info'               => 'Sie können Ihren Benutzernamen maximal 2 Mal ändern.',
        'change'             => 'Ändern',
        'pass-change'        => 'Passwort ändern',
        'pass-change-msg'    => 'Ihr Passwort muss mindestens einen Buchstaben, eine Zahl oder ein Sonderzeichen enthalten. Außerdem muss Ihr Passwort mindestens 8 Zeichen lang sein.',
        'info-msg'           => 'Im Rahmen des Zustimmungstextes können Sie die Methoden angeben, mit denen Sie über wichtige Kampagnen informiert werden möchten.',
        'info-email'         => 'Ich möchte E-Mails über Kampagnen und Marktberichte erhalten, die mich interessieren könnten.',
        'info-sms'           => 'Ich möchte SMS über Kampagnen und Marktberichte erhalten, die mich interessieren könnten.',
        'info-tel'           => 'Ich möchte Anrufe über Kampagnen und Marktberichte erhalten, die mich interessieren könnten.',
        'info-text'          => 'Wenn Sie Ihre Kommunikationspräferenzen in Bezug auf die Kampagnen deaktivieren, können Sie weiterhin E-Mails, Benachrichtigungen, SMS oder Anrufe zu Ihren Mitgliedschaftseinstellungen erhalten.',
        'info-part'          => 'Sie können die Methoden angeben, mit denen Sie über wichtige Kampagnen informiert werden möchten, im Rahmen des',
        'consent-text'       => 'Zustimmungstextes',
        'amount-vue'         => 'Betrag',
        'email'              => 'E-Mail',
        'sms'                => 'SMS',
        'call'               => 'Telefonanruf',
        'current-pass'       => 'Aktuelles Passwort',
        'new-pass'           => 'Neues Passwort',
        'confirm-pass'       => 'Passwort bestätigen',
        'wallet-tab'         => 'Wallet',
        'orders-tab'         => 'Bestellungen',
        'nft-tab'            => 'Meine NFTs',
        'trans-tab'          => 'Transaktionen',
        'orders-menu-title'  => 'Wallet/Bestellungen',
        'wallet-currency'    => 'Währung',
        'wallet-amount'      => 'Betrag',
        'nft-empty-msg'      => 'Entdecken Sie die Token von speziellen Immobilien und Grundstücken, aufgeteilt in Tausende von Anteilen.',
        'nft-empty-title'    => 'Sie haben noch keine NFTs.',
        'transactions'       => 'Transaktionen',
        'deposit'            => 'Einzahlung',
        'withdraw'           => 'Abhebung',
        'balance'            => 'Kontostand',
        'locked-balance'     => 'gesperrter Kontostand',
    ],

    'orders' => [
        'title'                     => 'Ihre Bestellung wurde empfangen, Glückwunsch.',
        'btn-text'                  => 'Zu meinen Einkäufen gehen',
        'content'                   => 'Details zu Ihrer Bestellung wurden an',
        'order-no'                  => 'Bestellnummer',
        'billing'                   => 'Rechnungsadresse',
        'amount'                    => 'Zu zahlender Betrag',
        'bank-info'                 => 'Bankkontoinformationen',
        'bank-name'                 => 'Bank',
        'account-name'              => 'Kontoinhaber',
        'iban'                      => 'IBAN',
        'branch-code'               => 'BIC',
        'success'                   => 'Ihre Bestellung wurde erfolgreich abgeschlossen.',
        'success-for-crypto'        => 'Ihre Bestellung wurde erfolgreich bearbeitet.',
        'thanks'                    => '<p class="p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">Vielen Dank, dass Sie der MiliGOLD familie beigetreten sind!</span> <br>Ihre Bestellung wurde erfolgreich erstellt. <br>Auf der Seite <a href="/customer/account/orders">Meine Bestellungen</a> können Sie auf die Bestelldetails zugreifen und den Status Ihrer Bestellung einsehen.<br>Sobald Sie die Zahlung für den Bestellbetrag zusammen mit der <span style="color:#ff9700;">Bestellnummer</span> auf die unten stehenden Kontoinformationen geleistet haben und Ihr <span style="color:#ff9700;">KYC-Prozess</span> abgeschlossen ist, wird Ihre Zahlung in Ihrer digitalen Wallet gesichert.<br>Sobald die Übertragung abgeschlossen ist, werden wir Sie per <span style="color:#ff9700;">E-Mail</span> kontaktieren. <br>Für weitere Informationen können Sie auf die <a href="/pages/faq">FAQ-Seite</a> gehen oder uns auf der<a href="/contact-us">Kontaktseite</a> schreiben.</p>',
        'thanks-for-crypto'         => '<p class=" p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">Vielen Dank, dass Sie der MiliGOLD Familie beigetreten sind!</span> <br>Ihre Bestellung wurde erfolgreich erstellt. <br>Auf der Seite <a href="/customer/account/orders">Meine Bestellungen</a> können Sie auf die Bestelldetails zugreifen und deren Status überprüfen.<br>Sobald Ihr <span style="color:#ff9700;">KYC-Prozess</span> genehmigt ist, erfolgt die Überweisung auf Ihr Wallet. <br>Sobald die Übertragung abgeschlossen ist, werden wir Sie per <span style="color:#ff9700;">E-Mail</span> kontaktieren. <br>Für weitere Informationen können Sie sich die <a href="/pages/faq">FAQ</a> ansehen oder uns <a href="/contact-us">kontaktieren.</a></p>',
    ],

    'advantages' => [
        'title'             => 'Vorteile',
        'coupons'           => 'Meine Gutscheine',
        'use'               => 'Verwenden',
        'purchase-amount'   => 'Kaufbetrag',
        'conditions'        => 'Bedingungen',
        'expire-date'       => 'Verfallsdatum',
        'last'              => 'Die letzten',
        'days'              => 'Tage',
        'campaigns'         => 'Kampagnen',
        'discount'          => 'Rabatt',
        'opportunity'       => 'Gelegenheit',
        'favorite'          => 'Zu Favoriten hinzufügen',
        'notify'            => 'Als Erster benachrichtigen!',
        'add-fav'           => 'Zu Ihren Favoriten hinzufügen!',
        'expired'           => 'Abgelaufen',
        'info-msg'          => 'Fangen Sie jetzt an, Favoriten hinzuzufügen, und seien Sie der Erste, der über die Angebote an den großen Rabatt-Tagen informiert wird',
        'empty-msg'         => 'Es gibt noch keine definierten Vorteile.',
    ],
    'profile' => [
        'index' => 'Konto',
    ],
    // # ilave ##
    'profilePage' => [
        'profile'                                      => 'Profil',
        'user-name'                                    => 'Benutzername',
        'last-name'                                    => 'Nachname',
        'panel-button'                                 => 'Ändern',
        'company-button'                               => 'Antrag',
        'my-profile'                                   => 'Mein Profil',
        'profile-orders'                               => 'Bestellungen',
        'profile-wallet'                               => 'Wallet',
        'logout'                                       => 'Abmelden',
        'first-name'                                   => 'Vorname',
        'profile-account'                              => 'Konto',
        'user-phone'                                   => 'Telefonnummer',
        'user-email'                                   => 'E-Mail-Adresse',
        'profile-security'                             => 'Sicherheit',
        'profile-page'                                 => 'Profilseite',
        'new-password'                                 => 'Neues Passwort',
        'profile-locked-coins'                         => 'Gesperrte Tokens',
        'current-password'                             => 'Aktuelles Passwort',
        'confirm-password'                             => 'Passwort bestätigen',
        'profile-transfer-details'                     => 'Überweisungsdetails',
        'two-factor-authentication'                    => 'Zwei-Faktor-Authentifizierung',
        'google-two-factor-authentication-is-verified' => 'Google Zwei-Faktor-Authentifizierung ist verifiziert',
        'institutional'                                => 'Für Unternehmen',
        'company-name'                                 => 'Firmenname',
        'company-mail'                                 => 'Firmen-E-Mail',
        'company-tax-number'                           => 'Handelsregisternummer',
        'company-phone'                                => 'Firmentelefon',
        'company-representative'                       => 'Unternehmensvertreter',
        'company-address'                              => 'Firmenadresse',
        'legal-form'                                   => 'Rechtsform',
        'company-name-error'                           => 'Das Firmenname-Feld ist erforderlich.',
        'company-mail-error'                           => 'Das Firmen-E-Mail-Feld ist erforderlich.',
        'company-tax-number-error'                     => 'Das Registernummer-Feld ist erforderlich.',
        'company-phone-error'                          => 'Das Firmen-Telefonnummer-Feld ist erforderlich.',
        'company-representative-error'                 => 'Das Firmenvertreter-Feld ist erforderlich.',
        'company-address-error'                        => 'Das Firmenadresse-Feld ist erforderlich.',
        'legal-form-error'                             => 'Das Rechtsform-Feld ist erforderlich.',
        'register-form'                                => 'Registrierungsformular',
        'error-message'                                => 'Bitte füllen Sie die untenstehenden Felder aus',
        'personal-address'                             => 'Adresse des Anlegers',
        'legal-type'                                   => 'Unternehmenstyp',
    ],
    // # ilave ##
    'walletPage' => [
        'wallet'                                                        => 'Wallet',
        'panel-balance'                                                 => 'Kontostand',
        'panel-currency'                                                => 'Kryptowährung',
        'wallet-page'                                                   => 'Wallet-Seite',
        'select-label-text'                                             => 'Blockchain auswählen',
        'address-label-text'                                            => 'Wallet-Adresse',
        'panel-pending-balance'                                         => 'Gesperrter Kontostand',
        'panel-drop-button-deposit-text'                                => 'Einzahlen',
        'panel-drop-button-withdraw-text'                               => 'Abheben',
        'panel-drop-menu-button-withcash-text'                          => 'Mit Bargeld',
        'panel-modal-content-title-text'                                => 'Mit Bargeld einzahlen',
        'panel-drop-menu-button-withcrypto-text'                        => 'Mit Krypto',
        'panel-drop-menu-button-withdraw-withcash-text'                 => 'Mit Bargeld',
        'panel-drop-menu-button-deposit-withcash-text'                  => 'Mit Bargeld',
        'panel-drop-menu-button-withdraw-withcrypto-text'               => 'Mit Krypto',
        'panel-drop-menu-button-deposit-withcrypto-text'                => 'Mit Krypto',
        'panel-modal-content-bank-account-information-iban-text'        => 'IBAN (TRY)',
        'panel-modal-content-bank-account-information-bankname-text'    => 'Bankname',
        'panel-modal-content-bank-account-information-text'             => 'Bankkontoinformationen',
        'panel-modal-content-bank-account-information-accountname-text' => 'Kontoname',
        'panel-modal-content-body2-text'                                => '<p>Sie müssen <span><b>{accountId}</b></span> im Beschreibungsabschnitt schreiben</p>',
        'panel-modal-content-body1-text'                                => '<p>Um das Guthaben aufzuladen, müssen Sie eine Zahlung über <span>Überweisung/EFT</span> an eine der relevanten Bankadressen vornehmen.</p>',
        'panel-modal-crypto-content-title-text'                         => 'Mit Krypto einzahlen',
        'panel-modal-crypto-content-h4-title-text'                      => 'Sie müssen an das folgende Wallet zahlen',
        'panel-modal-crypto-content-body-text'                          => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce elit ligula, vulputate vel egestas id, scelerisque vitae risus. Vestibulum ut auctor arcu, ac ornare urna. Ut non felis tortor.</p>',
        'panel-modal-content-withdraw-title-text'                       => 'Bargeld abheben',
        'panel-modal-content-withdraw-body-text'                        => '<p>Bitte geben Sie den <span>Google Zwei-Faktor-Authentifizierungs</span> Schlüssel ein</p>',
        'panel-modal-content-withdraw-error-title-text'                 => 'Bitte korrigieren Sie die folgenden Fehler:',
        'panel-modal-content-withdraw-button-text'                      => 'Abhebevorgang starten',
        'panel-modal-content-withdraw-body2-text'                       => '<p>Sie können Transaktionen durchführen, indem Sie die erforderlichen Felder für die Überweisung ausfüllen.</p><p><span>Sie können nur auf Ihr eigenes Konto überweisen</span></p>',
        'panel-modal-content-withdraw-input-amount-text'                => 'Betrag',
        'panel-modal-content-withdraw-textarea-text'                    => 'Schreiben Sie Ihre Notiz.',
        'panel-modal-content-withdrawcrypto-title-text'                 => 'Krypto abheben',
        'panel-modal-toast-title'                                       => 'Erfolg',
        'panel-model-toast-body'                                        => 'Wallet-Adresse erfolgreich kopiert',
        'panel-modal-content-withdrawsuccess-title-text'                => 'Überweisung erfolgreich.',
        'panel-modal-google2fa-button-text'                             => 'Ändern',
        'panel-modal-validate-text'                                     => 'Bitte füllen Sie die Pflichtfelder aus.',
    ],
    // # ilave ##
    'lockedCoins' => [
        'locked-coins'            => 'Gesperrte Tokens',
        'panel-amount-text'       => 'Währung',
        'panel-currency-text'     => 'Betrag',
        'panel-title-text'        => 'Freigabetyp',
        'select-coin-label-text'  => 'Kryptowährung auswählen',
        'locked-coins-page'       => 'Gesperrte Tokensseite',
        'panel-release-date-text' => 'Freigabedatum',
        'panel-empty-title'       => 'Keine gesperrten Tokens gefunden',
        'panel-empty-text'        => 'Derzeit sind keine gesperrten Tokens verfügbar.',
    ],
    // # ilave ##
    'ordersPage' => [
        'orders'                            => 'Bestellungen',
        'orders-page'                       => 'Bestellungsseite',
        'table-order-id'                    => 'Bestell-ID',
        'table-order-date'                  => 'Bestelldatum',
        'table-order-total'                 => 'Gesamt',
        'table-order-pcs'                   => 'Stk',
        'table-order-status'                => 'Status',
        'table-order-show'                  => 'Sicht',
        'table-order-action'                => 'Aktionen',
        'table-order-action-button-text'    => 'Abbrechen',
        'modal-content-title-text'          => 'Bestellte Produkte',
        'modal-content-table-sku'           => 'COIN',
        'modal-content-table-name'          => 'Name',
        'modal-content-table-pcs'           => 'Stk',
        'modal-content-table-price'         => 'Preis',
        'modal-content-table-total'         => 'Gesamt',
        'modal-content-payment-method'      => 'Zahlungsmethode',
        'modal-content-payment-subtotal'    => 'Zwischensumme',
        'modal-content-payment-tax'         => 'Steuer',
        'modal-content-payment-grand-total' => 'Gesamtsumme',
        'modal-content-tx-id'               => 'TX ID',
        'modal-content-cancel-text'         => 'Sind Sie sicher, dass Sie die Bestellung abbrechen möchten?',
        'modal-content-cancel-info-text'    => 'Über Blockchain getätigte Zahlungen können nicht STORNIERT werden, da sie dauerhaft in Blöcken verarbeitet werden.',
        'modal-button-cancel-text'          => 'Abbrechen',
        'modal-button-success-text'         => 'Bestätigen',
    ],
    // # ilave ##
    'transferDetail' => [
        'transfer-detail-title-text'      => 'Überweisungen',
        'transfer-detail-page-title-text' => 'Überweisungsdetails',
        'button-today-text'               => 'Heute',
        'button-thisweek-text'            => 'Diese Woche',
        'button-thismonth-text'           => 'Diesen Monat',
        'button-lastthreemonth-text'      => 'Letzte drei Monate',
        'table-title'                     => 'Titel',
        'table-currency'                  => 'Währung',
        'table-balance'                   => 'Betrag',
        'table-blocked-balance'           => 'Gesperrter Betrag',
        'table-date'                      => 'Datum',
    ],

    'alert' => [
        'success' => 'Erfolg',
        'error'   => 'Fehler',
        'warning' => 'Warnung',
        'info'    => 'Info',
    ],
    'checkout' => [
        'onepage' => [
            'page-title'                     => 'Kasse',
            'heading'                        => 'Kasse',
            'select-payment-type'            => 'Zahlungsart auswählen',
            'select-address'                 => 'Adresse auswählen',
            'payment-method-money-transfer'  => 'Überweisung',
            'payment-method-pay-with-crypto' => 'Mit Krypto bezahlen',
            'continue'                       => 'Weiter',
            'cart'                           => 'Mein Warenkorb',
            'total'                          => 'Gesamtbetrag',
            'update-cart'                    => 'Warenkorb aktualisieren',
        ],
    ],
    'payment' => [
        'summary' => [
            'check-order-question' => 'Ihre Rechnung wird entsprechend Ihrer Bestellung erstellt und an Ihre <span class="text-terra-orange">E-Mail</span> Adresse gesendet. <br><br> Bestätigen Sie Ihre Bestellung?',
            'check-order'          => 'Bestellung bestätigen',
            'cancel'               => 'Abbrechen',
        ],
    ],
    'fast-order' => [
        'summary' => [
            'page-title'                 => 'Gold kaufen / Gold verkaufen',
            'title'                      => 'Gold kaufen / verkaufen',
            'subtitle'                   => 'Wählen Sie den Transaktionstyp und geben Sie die Goldmenge zu den aktuellen Marktpreisen ein.',
            'buy-gold'                   => 'Gold kaufen',
            'sell-gold'                  => 'Gold verkaufen',
            'gold-purchase-confirmation' => 'Bestätigung Goldkauf',
            'gold-sale-confirmation'     => 'Bestätigung Goldverkauf',
            'locked-price'               => 'FESTGESETZTER PREIS',
            'transaction-type'           => 'Transaktionstyp:',
            'gold-amount'                => 'Goldmenge:',
            'unit-price'                 => 'Einheitspreis:',
            'total-amount'               => 'Gesamtbetrag:',
            'remaining-time'             => 'Verbleibende Zeit:',
            'seconds'                    => 'Sekunden',
            'cancel'                     => 'Abbrechen',
            'confirm'                    => 'Bestätigen',
            'price-locked-info'          => 'Der aktuelle Preis für Ihre Transaktion wurde für eine begrenzte Zeit festgelegt. Wenn die Zeit abläuft, werden Sie automatisch auf den Handelsbildschirm mit den aktualisierten Marktpreisen weitergeleitet.',
            'gold-amount-label'          => 'Goldmenge',
            'money-amount-label'         => 'Betrag',
            'enter-gold-amount'          => 'Goldmenge eingeben',
            'enter-money-amount'         => 'Betrag eingeben',
            'add-balance'                => 'Guthaben hinzufügen',
            'live-rate'                  => 'LIVE-KURS',
            'time-remaining'             => 'Verbleibende Zeit:',
            'rate-update-info'           => 'Der Wechselkurs wird am Ende der Zeit automatisch aktualisiert.',
            'buy-limit-info'             => 'Die Menge an Gold, die Sie kaufen können, darf <b>:amount :currency</b> nicht überschreiten. Für mehr, <a href=":route" class="text-[#D0AA49] font-bold">Guthaben hinzufügen</a>.',
            'sell-limit-info'            => 'Die Menge an Gold, die Sie verkaufen können, darf <b>:amount :currency</b> nicht überschreiten.',
        ],
        'result' => [
            'buy' => [
                'page-title'              => 'Bestellstatus',
                'order-success-title'     => 'Goldkauf erfolgreich!',
                'order-success-subtitle'  => 'Ihre Bestellung wurde erstellt, bitte folgen Sie den untenstehenden Schritten.',
                'order-created'           => 'Bestellung erstellt',
                'order-failed'            => 'Bestellung fehlgeschlagen',
                'order-failed-reason'     => 'Es gab ein Problem mit Ihrer Bestellung. Bitte kontaktieren Sie unser Support-Team.',
                'order-failed-support'    => 'Support kontaktieren',
                'completed'               => 'Abgeschlossen',
                'payment-processing'      => 'Zahlung wird bearbeitet',
                'processing'              => 'In Bearbeitung',
                'balance-will-be-loaded'  => 'Guthaben wird geladen',
                'waiting'                 => 'Warten',
                'failed'                  => 'Ein Fehler ist aufgetreten',
                'order-summary'           => 'Bestellübersicht',
                'order-number'            => 'Bestellnummer:',
                'order-content'           => 'Bestellinhalt:',
                'order-amount'            => 'Bestellmenge:',
                'order-status'            => 'Bestellstatus:',
                'order-status-pending'    => '<span class="text-yellow-500">Ausstehend</span>',
                'order-status-processing' => '<span class="text-blue-500">In Bearbeitung</span>',
                'order-status-completed'  => '<span class="text-green-500">Abgeschlossen</span>',
                'order-status-failed'     => '<span class="text-red-500">Fehlgeschlagen</span>',
                'order-status-canceled'   => '<span class="text-red-500">Abgebrochen</span>',
                'order-date'              => 'Bestelldatum:',
                'wallet'                  => 'Wallet',
                'wallet-after-completion' => '(Nach Abschluss der Bestellung)',
                'balance'                 => 'Guthaben:',
            ],
            'sell' => [
                'page-title'              => 'Bestellstatus',
                'order-success-title'     => 'Goldverkauf erfolgreich!',
                'order-success-subtitle'  => 'Ihre Bestellung wurde erstellt, bitte folgen Sie den untenstehenden Schritten.',
                'order-created'           => 'Bestellung erstellt',
                'order-failed'            => 'Bestellung fehlgeschlagen',
                'order-failed-reason'     => 'Es gab ein Problem mit Ihrer Bestellung. Bitte kontaktieren Sie unser Support-Team.',
                'order-failed-support'    => 'Support kontaktieren',
                'completed'               => 'Abgeschlossen',
                'payment-processing'      => 'Zahlung wird bearbeitet',
                'processing'              => 'In Bearbeitung',
                'balance-will-be-loaded'  => 'Guthaben wird geladen',
                'waiting'                 => 'Warten',
                'failed'                  => 'Ein Fehler ist aufgetreten',
                'order-summary'           => 'Bestellübersicht',
                'order-number'            => 'Bestellnummer:',
                'order-content'           => 'Bestellinhalt:',
                'order-amount'            => 'Bestellmenge:',
                'order-status'            => 'Bestellstatus:',
                'order-status-pending'    => '<span class="text-yellow-500">Ausstehend</span>',
                'order-status-processing' => '<span class="text-blue-500">In Bearbeitung</span>',
                'order-status-completed'  => '<span class="text-green-500">Abgeschlossen</span>',
                'order-status-failed'     => '<span class="text-red-500">Fehlgeschlagen</span>',
                'order-status-canceled'   => '<span class="text-red-500">Abgebrochen</span>',
                'order-date'              => 'Bestelldatum:',
                'wallet'                  => 'Wallet',
                'wallet-after-completion' => '(Nach Abschluss der Bestellung)',
                'balance'                 => 'Guthaben:',
            ],
        ],
    ],
    'sell-gold' => [
        'summary' => [
            'page-title' => 'Gold verkaufen',
            'heading'    => 'Gold verkaufen',

            'select-payment-type'      => 'Zahlungsmethode wählen',
            'select-address'           => 'Adresse wählen',
            'select-chain'             => 'Blockchain wählen',
            'select-chain-placeholder' => 'Bitte eine Blockchain auswählen',
            'select-coin'              => 'Coin wählen',
            'select-coin-placeholder'  => 'Bitte einen Coin auswählen',
            'continue'                 => 'Weiter',
            'add-balance'              => 'Guthaben hinzufügen',
            'my-cart'                  => 'Mein Warenkorb (Börse)',
            'total'                    => 'Gesamtbetrag',
            'maximum-selling'          => 'Maximaler Verkauf: <b>:max</b> Stück',
        ],
        'authorization' => [
            'page-title' => 'Gold verkaufen',
            'heading'    => 'Gold verkaufen',

            'show-payment-details'  => 'Zahlungsdetails anzeigen',
            'chain'                 => 'Blockchain',
            'wallet-balance'        => 'Wallet-Guthaben',
            'processing-amount'     => 'Verarbeitungsbetrag',
            'wallet-gas-fee'        => 'Wallet-Gebühr',
            'transaction-amount'    => 'Transaktionsbetrag',
            'expected-fee'          => 'Erwartete Gebühr',
            'expected-mirum-fee'    => 'MIRUM-Netzwerkgebühr',
            'risk-clauses'          => 'Ich habe die <a class="text-terra-orange" id="privacy-policy">Datenschutzrichtlinie</a> gelesen und akzeptiere sie.',
            'terms-conditions'      => 'Ich habe die <a class="text-terra-orange" id="terms-and-conditions">Allgemeinen Geschäftsbedingungen</a> gelesen und akzeptiere sie.',
            'data-protect'          => 'Ich habe gelesen und akzeptiere, dass die von mir gekauften RWAs im Mirum-Netzwerk gespeichert werden.',
            'cancellation-policy'   => 'Ich habe die <a class="text-terra-orange" id="cancellation-policy">Stornierungsrichtlinie</a> gelesen und akzeptiere sie.',
            'pay-now'               => 'Jetzt bezahlen',
            'order-confirm'         => 'Bestellung aufgeben',
            'cart'                  => 'Mein Warenkorb',
            'total'                 => 'Gesamtbetrag',
            'continue'              => 'Weiter',
            'cart_details'          => 'Warenkorbdetails',
            'my_cart'               => 'Mein Warenkorb',
            'pending-balance-title' => 'Erwartete Bestellgebühr überweisen',
            'pending-balance-fail'  => 'Es gibt ausstehende Zahlungsanfragen, die gerade bearbeitet werden. Bitte warten Sie, bis diese abgeschlossen sind.',
            'my-cart'               => 'Mein Warenkorb (Börse)',
            'exchange-note'         => 'Möchten Sie aktualisieren? <a href=":url" class="font-bold text-[#D0AA49]">Zurück</a>',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Zahlung abgeschlossen',
                'heading'     => 'Zahlung abgeschlossen',
                'title'       => 'Zahlung abgeschlossen',
                'description' => 'Ihre Zahlung wurde erfolgreich abgeschlossen. Vielen Dank für Ihre Transaktion. <br> Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'failed' => [
                'page-title'  => 'Zahlung fehlgeschlagen',
                'heading'     => 'Zahlung fehlgeschlagen',
                'title'       => 'Zahlung fehlgeschlagen',
                'description' => 'Ihre Zahlung war nicht erfolgreich. Bitte überprüfen Sie Ihre Zahlungsinformationen und versuchen Sie es erneut. <br> Falls das Problem weiterhin besteht, kontaktieren Sie uns bitte.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'pending' => [
                'page-title'  => 'Zahlung ausstehend',
                'heading'     => 'Zahlung ausstehend',
                'title'       => 'Zahlung ausstehend',
                'description' => 'Ihre Zahlung konnte noch nicht verifiziert werden. Bitte warten Sie, während der Prozess im Hintergrund weiterläuft. <br> Die Zahlung wird bearbeitet und bald abgeschlossen. Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'processing' => [
                'page-title'  => 'Zahlung wird bearbeitet',
                'heading'     => 'Zahlung wird bearbeitet',
                'title'       => 'Zahlung wird bearbeitet',
                'description' => 'Ihre Zahlung wurde bestätigt, und die gekauften Coins werden so schnell wie möglich in Ihr Wallet übertragen. <br> Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
        ],
    ],
    'pay-with-iyzico' => [
        'authorization' => [
            'page-title' => 'Kreditkartenzahlung',
            'heading'    => 'Sichere Zahlung mit Kreditkarte',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Zahlung Erfolgreich',
                'heading'     => 'Zahlung Abgeschlossen',
                'title'       => 'Zahlung Erfolgreich',
                'description' => 'Ihre Zahlung wurde erfolgreich verarbeitet. Eine Bestätigungs-E-Mail mit Ihrer Rechnung wird Ihnen in Kürze zugesandt.<br><br>Sie können Ihre Bestellung im Bereich "Meine Bestellungen" verfolgen.',
                'my-orders'   => 'Meine Bestellungen Anzeigen',
            ],
            'failed' => [
                'page-title'  => 'Zahlung Fehlgeschlagen',
                'heading'     => 'Zahlung Konnte Nicht Abgeschlossen Werden',
                'title'       => 'Transaktion Fehlgeschlagen',
                'description' => 'Ihre Zahlung konnte nicht verarbeitet werden. Bitte überprüfen Sie Ihre Kartendaten und versuchen Sie es erneut oder wählen Sie eine andere Zahlungsmethode.<br><br>Wenn das Problem weiterhin besteht, wenden Sie sich an den Kundenservice.',
                'my-orders'   => 'Meine Bestellungen Anzeigen',
            ],
        ],
    ],
    'pay-with-crypto' => [
        'summary' => [
            'page-title'               => 'Mit Krypto bezahlen - Zusammenfassung',
            'heading'                  => 'Mit Krypto bezahlen',
            'select-payment-type'      => 'Zahlungsart wählen',
            'select-address'           => 'Adresse auswählen',
            'select-chain'             => 'Blockchain auswählen',
            'select-chain-placeholder' => 'Bitte wählen Sie eine Blockhain aus',
            'select-coin'              => 'Token auswählen',
            'select-coin-placeholder'  => 'Bitte wählen Sie einen Token aus',
            'continue'                 => 'Weiter',
            'add-balance'              => 'Guthaben hinzufügen',
            'my-cart'                  => 'Mein Warenkorb',
            'total'                    => 'Gesamtbetrag',

            'deposit-with-crypto'             => 'Mit Krypto einzahlen',
            'deposit-with-crypto-warning'     => 'Das Guthaben auf Ihrem Konto reicht nicht aus. Für Ihre Bestellung müssen Sie Kryptowährung in Höhe des Bestellbetrags an Ihre unten angegebene Wallet-Adresse überweisen.',
            'deposit-with-crypto-description' => 'Sie müssen an die folgende Wallet bestellen',
        ],
        'authorization' => [
            'page-title'            => 'Mit Krypto bezahlen - Autorisierung',
            'heading'               => 'Mit Krypto bezahlen',
            'show-payment-details'  => 'Zahlungsdetails anzeigen',
            'chain'                 => 'Blockchain',
            'wallet-balance'        => 'Wallet-Guthaben',
            'processing-amount'     => 'Verarbeitungsbetrag',
            'wallet-gas-fee'        => 'Wallet-Gasgebühr',
            'transaction-amount'    => 'Transaktionsbetrag',
            'expected-fee'          => 'Erwartete Gebühr',
            'expected-mirum-fee'    => 'Erwartete MIRUM-Gebühr',
            'risk-clauses'          => 'Ich habe die <a class="text-terra-orange" id="privacy-policy">Datenschutzerklärung</a> gelesen und akzeptiert.',
            'terms-conditions'      => 'Ich habe die <a class="text-terra-orange" id="terms-and-conditions">Allgemeinen Geschäftsbedingungen</a> gelesen und akzeptiert.',
            'data-protection'       => 'Ich stimme der Speicherung von RWAs zu, die ich im <a class="text-terra-orange" id="data-protection">Mirum-Netzwerk kaufe.</a>',
            'data-protect'          => 'Ich stimme der Speicherung von RWAs zu, die ich im <a class="text-terra-orange" id="data-protection">Mirum-Netzwerk kaufe.</a>',
            'cancellation-policy'   => 'Ich habe die <a class="text-terra-orange" id="cancellation-policy">Widerrufsbelehrung</a> gelesen und akzeptiert.',
            'pay-now'               => 'Jetzt bezahlen',
            'order-confirm'         => 'Jetzt bestellen',
            'cart'                  => 'Mein Warenkorb',
            'total'                 => 'Gesamtbetrag',
            'continue'              => 'Weiter',
            'cart_details'          => 'Kaufübersicht',
            'my_cart'               => 'Mein Warenkorb',
            'pending-balance-title' => 'Erwartete Bestellgebühr überweisen',
            'pending-balance-fail'  => 'Wir haben ausstehende Zahlungsanfragen, die derzeit bearbeitet werden. Bitte warten Sie, bis diese abgeschlossen sind.',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Zahlung abgeschlossen',
                'heading'     => 'Zahlung abgeschlossen',
                'title'       => 'Zahlung abgeschlossen',
                'description' => 'Ihre Zahlung wurde erfolgreich abgeschlossen. Vielen Dank für Ihre Transaktion. <br> Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'failed' => [
                'page-title'  => 'Zahlung fehlgeschlagen',
                'heading'     => 'Zahlung fehlgeschlagen',
                'title'       => 'Zahlung fehlgeschlagen',
                'description' => 'Ihre Zahlung war nicht erfolgreich. Bitte überprüfen Sie Ihre Zahlungsinformationen und versuchen Sie es erneut. <br> Wenn das Problem weiterhin besteht, kontaktieren Sie uns bitte.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'pending' => [
                'page-title'  => 'Zahlung ausstehend',
                'heading'     => 'Zahlung ausstehend',
                'title'       => 'Zahlung ausstehend',
                'description' => 'Ihre Zahlung konnte noch nicht verifiziert werden, bitte warten Sie, während dieser Prozess im Hintergrund fortgesetzt wird. <br> Es wird bearbeitet und wird in Kürze abgeschlossen sein. Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
            'processing' => [
                'page-title'  => 'Zahlung wird bearbeitet',
                'heading'     => 'Zahlung wird bearbeitet',
                'title'       => 'Zahlung wird bearbeitet',
                'description' => 'Ihre Zahlung wurde bestätigt, und die gekauften Kryptowährung werden so bald wie möglich auf Ihre Mirum-Wallet überwiesen. <br> Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'my-orders'   => 'Meine Bestellungen',
            ],
        ],
    ],
    'moneytransfer' => [
        'authorization' => [
            'page-title'              => 'Geldüberweisung',
            'heading'                 => 'Geldüberweisung',
            'fee-note'                => 'Mirum Network erhebt keine Transaktionsgebühren für Transaktionen, die über sein Netzwerk abgewickelt werden.',
            'details'                 => 'Detail',
            'operation-fee'           => 'Bearbeitungsgebühr',
            'network-fee'             => 'Netzwerkgebühr',
            'grand-total'             => 'Gesamtsumme',
            'company-confirm'         => 'Ich habe die Ich bestätige, dass ich mich im Namen des Unternehmens bewerbe',
            'risk-clauses'            => 'Ich habe die <a class="text-terra-orange" id="privacy-policy">Datenschutzerklärung</a> gelesen und akzeptiert.',
            'terms-conditions'        => 'Ich habe die <a class="text-terra-orange" id="terms-and-conditions">allgemeinen Geschäftsbedingungen</a> gelesen und akzeptiert.',
            'data-protect'            => 'Ich stimme der Speicherung von RWAs zu, die ich im Mirum-Netzwerk kaufe.',
            'email-confirmation'      => 'Ich bin damit einverstanden, dass mir Informationen per <span class="text-terra-orange">E-Mail</span> zugesandt werden.',
            'personal-confirmation'   => 'Ich bestätige, dass ich den Antrag in meinem eigenen Namen stelle, dass ich ihn gelesen habe und akzeptiere.',
            'whitepaper-confirmation' => 'Ich habe das <a class="text-terra-orange" id="whitepaper">Informationsmemorandum</a> gelesen und bin mir der im Informationsmemorandum genannten Risiken bewusst.',
            'cancellation-policy'     => 'Ich habe die <a class="text-terra-orange" id="cancellation-policy">Widerrufsbelehrung</a> gelesen und akzeptiert.',
            'total'                   => 'Gesamtbetrag',
            'pay-now'                 => 'Jetzt bestellen',
            'order-confirm'           => 'Jetzt bestellen',
            'vat'                     => 'MwSt',
            'buy-button'              => 'KAUFEN',
            'continue'                => 'WEİTER',
            'money-transfer-fail'     => 'Sie haben eine offene Bestellung per Überweisung.<br />Sie können keine zweite Bestellung per Überweisung aufgeben, bis Ihre Bestellung abgeschlossen ist.<br />Bitte abbrechen Sie die Bestellung oder kontaktieren Sie uns für die Stornierung der Bestellung.',
            'data-protection'         => '<a href="#" class="text-terra-orange" id="risk-clauses">Ich akzeptiere, dass die RWAs, die ich gekauft habe, im Mirum-Netzwerk gespeichert werden.</a>',
        ],
        'success' => [
            'bank-info-not-exist' => [
                'heading'     => 'Auf Bankdaten konnte nicht zugegriffen werden',
                'description' => 'Ihre Zahlung wurde erfolgreich abgeschlossen. Vielen Dank für Ihre Transaktion. <br> Wir senden Ihnen eine E-Mail mit den Details Ihrer Bestellung.',
                'contact-us'  => 'Bitte kontaktieren Sie uns für Bankkontoinformationen.',
            ],
        ],
    ],

    'home' => [
        'main' => [
            'browser_not_supported'  => 'Ihr Browser unterstützt das Video-Tag nicht.',
            'banner_title'           => 'Der neue Name des digitalen <br> RWA-Investments in<span> TerraMirum</span>:<br><span style="color: #12D176">Mirum Token</span>',
            'ready_to_sale'          => 'Erste Runde',
            'seed_sale'              => 'Erste Runde',
            'private_sale'           => 'Zweite Runde',
            'public_sale'            => 'Dritte Runde',
            'metric_seed_sale'       => 'Erste Runde :date',
            'metric_private_sale'    => 'Zweite Runde :date',
            'metric_public_sale'     => 'Dritte Runde :date',
            'coming_soon'            => 'Kommt bald',
            'private_sale_countdown' => [
                'styleText' => 'Zweite Runde',
                'noneText'  => 'beginnt in..',
            ],
            'public_sale_countdown' => [
                'styleText' => 'Dritte Runde',
                'noneText'  => 'beginnt in..',
            ],
            // 'private_sale_countdown' => '<span style="color: #FF9700;">zweite runde</span> beginnt in..',
            'about_title'                          => 'Sind Sie bereit, in die Welt des digitalen Zeitalters?',
            'about_content'                        => 'Unser ICO bietet eine einzigartige Gelegenheit. Lesen Sie unser Informationsmemorandum und verpassen Sie nicht die Chance, die Zukunft gemeinsam zu gestalten!',
            'read_whitepaper'                      => 'Informationsmemorandum',
            'why_choose_us'                        => 'Warum uns wählen',
            'choose_our_token'                     => 'Warum unseren <br>Mirum Token wählen?',
            'simplifying_payments'                 => 'Vereinfachung des Zahlungsprozesses',
            'simplifying_payment_details'          => 'Entdecken Sie eine Welt der finanziellen Leichtigkeit mit unserem Engagement, den Zahlungsprozess zu vereinfachen, nahtlose Transaktionen und unvergleichliche Bequemlichkeit bei jeder Interaktion zu gewährleisten.',
            'timeless_transactions'                => 'Transaktionen, die zeitlos gestaltet sind.',
            'timeless_transaction_details'         => 'Erleben Sie eine finanzielle Reise, bei der jede Transaktion sorgfältig darauf ausgelegt ist, den Lauf der Zeit zu überdauern und ein nahtloses und dauerhaftes Erbe für Ihre finanziellen Unternehmungen zu gewährleisten.',
            'secure_wealth'                        => 'Sichern Sie Ihr Vermögen, behalten Sie die finanzielle Kontrolle!',
            'secure_wealth_details'                => 'Navigieren Sie mit Vertrauen durch die Finanzlandschaft – Ihr Vermögen ist gesichert und die Kontrolle liegt in Ihren Händen!',
            'privacy_priority'                     => 'Die Sicherstellung der Privatsphäre der Nutzer ist unsere oberste Priorität.',
            'privacy_priority_details'             => 'Ihr Vertrauen ist uns äußerst wichtig. Seien Sie versichert, dass wir uns der Schaffung einer sicheren Umgebung verschrieben haben, in der Ihre Daten geschützt sind und Ihr Online-Erlebnis sorgenfrei ist.',
            'mirum_chain_advantages'               => 'Vorteile des Mirum-Protokolls gegenüber der Blockchain',
            'mirum_chain_advantages_details'       => 'Vielfältigkeit und Flexibilität<br>IBC-Interaktion<br>NFT-Mietgeschäfte<br>Staking und passives Einkommen mit PoS<br>Gemeinschaftliches Engagement',
            'token_metrics'                        => 'Token-Metriken',
            'seed_sale_date'                       => 'Erste Runde 14/10/2024',
            'seed_sale_amount'                     => ':amount Mirum',
            'private_sale_date'                    => 'Zweite Runde 02/11/2024',
            'private_sale_amount'                  => ':amount Mirum',
            'public_sale_date'                     => 'Dritte Runde 30/11/2024',
            'public_sale_amount'                   => ':amount Mirum',
            'seed_sale_tab'                        => '<span class="yellow">Erste</span> Runde',
            'private_sale_tab'                     => '<span class="purple">Zweite</span> Runde',
            'public_sale_tab'                      => '<span class="green">Dritte</span> Runde',
            'seed_sale_price'                      => '1 MIRUM = 0,0040 €',
            'seed_sale_info'                       => 'Insgesamt 12.500.000 MIRUM Token werden im Rahmen des erste rundes angeboten. Diese Token werden zum günstigsten Preis von 0,0040 Euro verkauft und beginnen 8 Monate nach Ende des ICOs freigeschaltet zu werden. Danach werden die Sperren jeden Monat um 20% geöffnet.',
            'private_sale_price'                   => '1 MIRUM = 0,0072 €',
            'private_sale_info'                    => 'Insgesamt 20.833.333 MIRUM Token werden während des zweite rundes verfügbar sein. Diese Token werden zum günstigen Preis von 0,0072 Euro verkauft und beginnen 6 Monate nach Ende des ICOs freigeschaltet zu werden. Danach werden die Sperren jeden Monat um 10% geöffnet.',
            'public_sale_price'                    => '1 MIRUM = 0,0120 €',
            'public_sale_info'                     => 'Insgesamt 66.666.667 MIRUM Token werden während des öffentlichen Verkaufs verfügbar sein. Der Verkauf dieser Token wird zu 0,0120 Euro erfolgen und diese Token beginnen 3 Monate nach Ende des ICOs freigeschaltet zu werden. Danach werden die Sperren jeden Monat um 10% geöffnet.',
            'disclaimer'                           => '<span class="text-danger">*</span> Bitte lesen Sie die Informationsmemorandum vor dem Kauf.',
            'our_roadmap'                          => 'Unser Fahrplan',
            'terramirum_strategy_and_project_plan' => 'Terramirum-Strategie und Projektplan',
            'mid_of_q3_2021'                       => '2021 (Startpunkt)',
            //            'starting_point' => 'Ausgangspunkt',
            'starting_point_description' => 'Dies ist die Zeit, in der das mit der Pandemie entstandene Wohnungsproblem einen enormen Nachfrageboom nach Wohnraum auslöste. Die daraus resultierenden Angebotsprobleme stießen auf große Besorgnis, insbesondere bei den Zentralregierungen, die vom Bankensektor die Schaffung neuer Liquiditätsquellen zur Unterstützung der Wohnungsproduktion forderten. Es wurden Hindernisse für die Entwicklung des Immobiliensektors festgestellt.  Es wurde eine Ideenphase eingeleitet, um Lösungen für die verschiedenen Hindernisse, insbesondere die Liquidität, zu finden.',
            //            'obstacles_identified' => 'Hindernisse identifiziert',
            //            'idea_phase_begun' => 'Ideenphase begonnen',
            'mid_of_q1_2022' => 'Erste Hälfte des Jahres 2022',
            //            'find_solutions_for_real_estate_sectors' => 'Lösungen für den Immobiliensektor finden',
            'find_solutions_for_real_estate_sectors_description' => 'Die Roadmap wurde geplant, um Lösungen für die Probleme des Immobiliensektors zu finden. Die Idee, die Problemlöser der Wohnungsproduktion und des -verbrauchs, die mit Hilfe von Technologie verwaltet werden, auf einer Technologieplattform zusammenzubringen, wurde geplant (die erste Ideenphase der Produktentwicklung wurde abgeschlossen). Die Grundkonzeption der Web2-Modelle wurde erstellt. Phase 2 der Unternehmensverfahren wurde abgeschlossen.',
            //            'web2_models_basic_design_completed' => 'Grunddesign der Web2-Modelle abgeschlossen',
            //            'company_procedures_completed_for_stage_2' => 'Unternehmensverfahren für Phase 2 abgeschlossen',
            'mid_of_q2_2022' => 'Zweite Hälfte des Jahres 2022',
            //            'basic_design_of_web3_models_was_done' => 'Grunddesign der Web3-Modelle wurde erstellt',
            'web3_models_basic_design_completed' => 'Steigende Immobilienpreise und der rapide Rückgang der Kaufkraft weltweit haben eine neue Art der Investition in Immobilien geschaffen: Immobilienbeteiligungen (zweite Ideenphase der Produktentwicklung abgeschlossen). Die Grundkonzeption der Web3-Modelle ist abgeschlossen. Es wurde beschlossen, Non-Fungible Token und Semi-Fungible Token für Immobilienbeteiligungen zu produzieren. Die Unternehmensstruktur wurde erweitert. Die Gestaltung der Website wurde begonnen.',
            //            'produce_nfts_and_sfts_for_real_estate_share_ownership' => 'Erstellung von NFTs und SFTs für Immobilienanteilsbesitz',
            //            'company_structure_expanded' => 'Unternehmensstruktur erweitert',
            //            'website_design_started' => 'Webdesign gestartet',
            'mid_of_q1_2023' => 'Erste Hälfte 2023',
            //            'cityfund_was_created_to_meet_tokenization_needs' => 'CityFund wurde geschaffen, um Tokenisierungsbedürfnisse zu erfüllen',
            'support_agreements_aimed_with_key_companies' => 'Um das Projekt zu realisieren, sollten Unterstützungsvereinbarungen mit wichtigen Unternehmen, die in Immobilien investieren, getroffen werden. Es wurde beschlossen, Voruntersuchungen zum „Seed Investment“-Prozess durchzuführen. Es wurden Untersuchungen zur Infrastruktur für das Whitelabelling durchgeführt. Es wurde beschlossen, TerraMirum zu planen, einen gemeinsamen Marktplatz für alle Produkte. Die „CityFund“-Infrastruktur wurde eingerichtet, um den Bedürfnissen der „Tokenisierung“ gerecht zu werden, und das Projekt wurde verschiedenen lokalen Regierungen und Nichtregierungsorganisationen erläutert. Es wurde beschlossen, städtische Sparschweine für Investitionen in Städten zu entwickeln. Angesichts der sich abzeichnenden Hindernisse wurde beschlossen, den Prozess der Vermögensanlage fortzusetzen.',
            //            'negative_end_of_seed_investment_process' => 'Negatives Ende des Seed-Investitionsprozesses',
            //            'plan_for_terramirum_marketplace' => 'Plan für Terramirum-Marktplatz',
            //            'creation_of_urban_piggy_banks_for_investment' => 'Erstellung von städtischen Sparschweinen für Investitionen',
            'mid_of_q2_2023' => 'Zweite Hälfte des Jahres 2023',
            //            'advertising_activities_have_started' => 'Werbeaktivitäten gestartet',
            'focus_on_marketing_projects' => 'Es wurde beschlossen, sich auf Marketingprojekte zu konzentrieren, um unseren Marktanteil zu erhöhen. Kleinere Werbemaßnahmen wurden gestartet.  Es wurde damit begonnen, Untersuchungen für die Durchführung von Marktaktivitäten durchzuführen. Es wurden Alternativen für die Ausweitung der Werbeaktivitäten erarbeitet. Der Bedarf für eine weitere Expansion des Marktes wurde ermittelt. Die Werbung auf dem internationalen Parkett und globale Kooperationsmodelle wurden untersucht. Die Bastardisierung der Verbraucher hat begonnen, um verschiedene Informationsgemeinschaften zu schaffen.',
            //            'carrying_out_market_activities' => 'Durchführung von Marktaktivitäten',
            //            'expansion_of_advertising_activities' => 'Erweiterung der Werbeaktivitäten',
            //            'continued_market_expansion' => 'Fortgesetzte Markterweiterung',
            //            'promotion_in_international_arena' => 'Promotion auf internationaler Ebene',
            //            'global_cooperation' => 'Globale Kooperation',
            //            'community_building_in_marketplace' => 'Community-Building auf dem Marktplatz',
            'mid_of_q2_2024_1' => 'Erstes Quartal 2024',
            //            'launchpad' => 'Launchpad',
            'launchpad_setup_available_for_purchase' => 'Launchpad-Installationsinfrastruktur, die zu einem Vorverkaufspreis erworben werden kann, wurde vorbereitet. Beginn der Implementierung der technologischen Infrastruktur in Bezug auf die Anforderungen.',
            //            'launching_applications_related_to_requirements' => 'Starten von Anwendungen in Bezug auf Anforderungen',
            //            'marketplace_dashboard_tests_continue' => 'Tests des Marktplatz-Dashboards gehen weiter',
            'mid_of_q2_2024' => 'Zweites Quartal 2024',
            //            'activating_the_marketplace_dashboard' => 'Aktivierung des Marktplatz-Dashboards',
            'promotional_activities_for_real_estate_investors' => 'Fortsetzung der Werbemaßnahmen für RWA-investoren. Interviews mit verschiedenen Medienorganisationen geplant.',
            //            'announcement_campaign_for_supplying_products_to_terramirum' => 'Ankündigungskampagne zur Lieferung von Produkten an Terramirum',
            //            'interviews_with_various_media_organizations' => 'Interviews mit verschiedenen Medienorganisationen',
            'mid_of_q3_2024' => 'Drittes Quartal 2024',
            //            'new_real_estate_based_models' => 'Neue immobilienbasierte Modelle',
            'terra_mirum_chain_designed_and_services_put_into_service' => 'Nach den Launchpad-Einnahmen wird die TerraMirum-eigene Kette entworfen und alle Dienstleistungen werden in dieser Kette angeboten. Schaffung neuer Einkommensmodelle auf der Basis von RWA\'s (Vermietung, Tourismusvillen, Timesharing). Beginn des ICO-Prozesses (Seed, Private, Public). Erstellung einer Ankündigungskampagne, um TerraMirum Produkte und Nutzer zur Verfügung zu stellen.',
            //            'creation_of_new_real_estate_based_income_models' => 'Erstellung neuer immobilienbasierter Einkommensmodelle',
            'q4_2024'        => 'Letztes Quartal 2024',
            'q1_2025'        => 'Erstes Quartal 2025',
            'q2_2025'        => 'Zweites Quartal 2025',
            'q3_2025'        => 'Drittes Quartal 2025',
            'q4_2024_detail' => 'Etablierung neuer Kooperationen. Beginn des Crowdfunding-Prozesses. Beginn der Infrastrukturarbeiten für den Sekundärmarkt (Sekundärmarkt).',
            'q1_2025_detail' => 'Fortführung der Arbeiten am Sekundärmarkt. Beginn der Arbeiten für den Marketplace. Aktivierung des Marketplace-Dashboards, unterstützt durch verschiedene Datensätze; Fortgesetzte Tests des Marketplace-Dashboards.',
            'q2_2025_detail' => 'Fortführung der Sekundärmarktstudien. Etablierung von Kooperationen. Fortführung von Werbekampagnen.',
            'q3_2025_detail' => 'Abschluss und Inbetriebnahme der Studien zum Sekundärmarkt (Secondary Market).',
            'team_subtitle'  => 'Unser Team',
            'team_title'     => 'Das Führungsteam',
        ],
    ],

    'contact-us' => [
        'heading'     => 'Kontaktiere uns',
        'about-mirum' => [
            'title'       => 'Über Mirum',
            'description' => 'Der MIRUM Token wird als Utility-Token eingestuft, der für den Erwerb von Immobilien und Smart Contracts verwendet werden kann. Um die Liquidität des Credipto-Ökosystems zu sichern, wird das Angebot hoch gehalten, um übermäßige Spekulation und Preisinstabilität zu verhindern. Dieser Schritt ist notwendig, um die reibungslose Entwicklung des Ökosystems zu gewährleisten.',
        ],
        'contact-form' => [
            'title'     => 'Kontaktiere uns bei Fragen',
            'firstname' => 'Vorname',
            'lastname'  => 'Nachname',
            'email'     => 'E-Mail',
            'subject'   => 'Betreff',
            'message'   => 'Nachricht',
            'send'      => 'Senden',
        ],
    ],

    'footer' => [
        'home'           => 'Startseite',
        'about'          => 'Über Mirum',
        'faq'            => 'FAQ',
        'credipto'       => 'Credipto',
        'marketplace'    => 'Marktplatz',
        'contact'        => 'Kontaktieren',
        'us'             => 'Sie uns',
        'footer-desc'    => 'Der neue Name des digitalen <br>RWA-Investments in TerraMirum:<br> Mirum Token',
        'social-section' => [
            'title' => 'Sozial',
        ],
        'quick-links-section' => [
            'title' => 'Schnell Links',
        ],
        'newsletter-section' => [
            'title' => 'Newsletter',
        ],
        'copy-right' => 'Copyright © :date. Alle Rechte vorbehalten <a href=":url" target="_blank">:name</a>',
        'contracts'  => 'Verträge',
    ],

    'mirum-coin' => [
        'per-price'            => 'Preis pro Stück',
        'total-amount'         => 'Gesamtbetrag',
        'buy-now'              => 'Jetzt kaufen',
        'contract'             => 'Vertrag',
        'contract-description' => 'Vertragsbeschreibung',
        'contract-button'      => 'Vertrag anzeigen',
        'sold-meter'           => 'Verkaufszähler',
        'target-raised'        => 'Ziel erreicht',
        'overlay-title'        => 'Mirum Token kaufen',
        'overlay-description'  => '<p class="text-white"> Der Erlös aus dem Token-Verkauf soll den Finanzbedarf des Projekts durch die Deckung des Hardware- und Betriebsbedarfs sichern.</p>',
    ],
    'company_register' => [
        'companyMail-email'                 => 'Bitte geben Sie eine gültige E-Mail Adresse ein.',
        'companyMail-required'              => '1 Bitte füllen Sie die leeren Felder aus.',
        'companyMail-unique'                => 'Die von Ihnen eingegebene E-Mail-Adresse ist registriert',
        'companyName-required'              => '2 Bitte füllen Sie die leeren Felder aus.',
        'companyPhone-string'               => 'Bitte geben Sie eine gültige Telefonnummer ein.',
        'companyPhone-required'             => '3 Bitte füllen Sie die leeren Felder aus.',
        'companyAddress-required'           => '4 Bitte füllen Sie die leeren Felder aus.',
        'companyTaxNumber-string'           => 'Bitte geben Sie eine gültige Steuernummer ein.',
        'companyTaxNumber-required'         => '5 Bitte füllen Sie die leeren Felder aus.',
        'companyTaxNumber-min'              => 'Der von Ihnen eingegebene Wert muss min: {min}.',
        'companyRepresentative-required'    => '6 Bitte füllen Sie die leeren Felder aus.',
        'legalType-required'                => 'Bitte füllen Sie die leeren Felder aus.',
        'toast-popup-warning-title'         => 'Warnung!',
        'toast-popup-warning-message'       => 'Bitte füllen Sie die leeren Felder aus.',
        'toast-popup-warning-email-message' => 'Bitte geben Sie eine gültige E-Mail-Adresse ein.',
    ],
];
