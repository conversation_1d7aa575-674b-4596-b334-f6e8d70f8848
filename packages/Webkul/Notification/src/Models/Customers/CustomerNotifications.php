<?php

namespace Webkul\Notification\Models\Customers;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class CustomerNotifications extends Model
{
    use HasFactory, Notifiable;

    protected $connection = 'mysql';

    protected $primaryKey = 'id';

    protected $table      = 'customer_notifications';

    protected $guarded    = [];

    protected $appends    = [];

    protected $casts      = [
        'created_at'        => 'datetime:d-m-Y H:i:s',
        'updated_at'        => 'datetime:d-m-Y H:i:s',
    ];

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Attribute Area ---------------------------------------------------------- **/
    /**  ----------------------------------------------------------------------------------------------------------- **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Scope Area -------------------------------------------------------------- **/
    /**  ----------------------------------------------------------------------------------------------------------- **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Relationships Area ------------------------------------------------------ **/
    /**  ----------------------------------------------------------------------------------------------------------- **/
}   /** end class CustomerNotifications extends Model **/
