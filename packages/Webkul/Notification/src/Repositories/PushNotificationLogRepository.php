<?php

namespace Webkul\Notification\Repositories;

use Webkul\Core\Eloquent\Repository;

class PushNotificationLogRepository extends Repository
{
    public $paginate = 10;

    public function model(): string
    {

        return 'Webkul\Notification\Contracts\PushNotificationLog';

    }

    /** end model(): string **/
    public function getQueryData($request = null): mixed
    {

        $data = $this->model->with('customer')
            ->where(['push_notification_id' => $request->pushNotificationId])
            ->orderBy('id', 'DESC')
            ->paginate($this->paginate);

        return (object) [
            'data'        => $data->map(function ($item) {
                return [
                    'id'                        => $item?->id,
                    'customerName'              => $item?->customer->first_name.' '.$item?->customer->last_name,
                    'pushNotificationDelivered' => $this->getStatus($item?->push_notification_status),
                    'pushNotificationRead'      => $this->getStatus($item?->push_notification_read),
                    'createdAt'                 => $item?->created_at?->format('d.m.Y'),
                ];
            }),
            'total'       => $data->total(),
            'count'       => $data->count(),
            'lastPage'    => $data->lastPage(),
            'perPage'     => $data->perPage(),
            'currentPage' => $data->currentPage(),
            'links'       => $data,
        ];

    } /** end getQueryData( $request = null ): mixed **/

    /**
     * @return object
     */
    public function created($request = null): object|array|bool
    {

        $created = $this->model->create([
            'notification_title'   => $request->title,
            'notification_status'  => $request->status,
            'notification_content' => $request->content,
        ]);

        return true;

    } /** end created( $request = null ): mixed **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /** ------------------------------ Repository Model Private Function(s) ---------------------------------------- **/
    /** ------------------------------------------------------------------------------------------------------------ **/

    /**
     * @return string[]|void
     */
    private function getStatus($status = null)
    {

        switch ($status) {
            case 1:
                return [
                    'text'  => __('admin::app.push-notification.push-notification-log-status-text.delivered'),
                    'class' => 'success',
                ];
                break;
            case 2:
                return [
                    'text'  => __('admin::app.push-notification.push-notification-log-status-text.read'),
                    'class' => 'success',
                ];
                break;
            default:
                return [
                    'text'  => __('admin::app.push-notification.push-notification-log-status-text.waiting'),
                    'class' => 'noread',
                ];
                break;
        }

    } /** end getStatus( $status = null ) **/
}   /** end class PushNotificationLogRepository extends Repository **/
