<?php

namespace Webkul\Core\Repositories;

use Carbon\Carbon;
use Illuminate\Container\Container;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Prettus\Repository\Traits\CacheableRepository;
use Webkul\Core\Eloquent\Repository;

class SliderRepository extends Repository
{
    use CacheableRepository;

    /**
     * Create a new repository instance.
     *
     * @return void
     */
    public function __construct(
        protected ChannelRepository $channelRepository,
        Container $container
    ) {
        parent::__construct($container);
    }

    /**
     * Specify model class name.
     */
    public function model(): string
    {
        return 'Webkul\Core\Contracts\Slider';
    }

    /**
     * Save slider.
     *
     * @return bool|\Webkul\Core\Contracts\Slider
     */
    public function save(array $data)
    {
        Event::dispatch('core.settings.slider.create.before', $data);

        $channelName = $this->channelRepository->find($data['channel_id'])->name;

        $dir = 'slider_images/'.$channelName;

        $uploaded        = $image = false;
        $uploaded_mobile = $image_mobile = false;

        if (isset($data['image'])) {
            $image = $first = Arr::first($data['image'], function ($value, $key) {
                if ($value) {
                    return $value;
                } else {
                    return false;
                }
            });
        }

        if (isset($data['image_mobile'])) {
            $image_mobile = $first = Arr::first($data['image_mobile'], function ($value, $key) {
                if ($value) {
                    return $value;
                } else {
                    return false;
                }
            });
        }

        if ($image != false) {
            $uploaded = $image->store($dir);

            unset($data['image'], $data['_token']);
        }

        if ($image_mobile != false) {
            $uploaded_mobile = $image_mobile->store($dir);

            unset($data['image_mobile'], $data['_token']);
        }

        if ($uploaded) {
            $data['path'] = $uploaded;
        } else {
            unset($data['image']);
        }
        if ($uploaded_mobile) {
            $data['path_mobile'] = $uploaded_mobile;
        } else {
            unset($data['image_mobile']);
        }

        $slider = $this->create($data);

        Event::dispatch('core.settings.slider.create.after', $slider);

        return true;
    }

    /**
     * Update item.
     *
     * @param  int  $id
     * @return bool
     */
    public function updateItem(array $data, $id)
    {
        Event::dispatch('core.settings.slider.update.before', $id);

        $channelName = $this->channelRepository->find($data['channel_id'])->name;

        $dir = 'slider_images/'.$channelName;

        $uploaded        = $image = false;
        $uploaded_mobile = $image_mobile = false;

        if (isset($data['image'])) {
            $image = $first = Arr::first($data['image'], function ($value, $key) {
                return $value ? $value : false;
            });
        }

        if (isset($data['image_mobile'])) {
            $image_mobile = $first = Arr::first($data['image_mobile'], function ($value, $key) {
                return $value ? $value : false;
            });
        }

        if ($image != false) {
            $uploaded = $image->store($dir);

            unset($data['image'], $data['_token']);
        }

        if ($image_mobile != false) {
            $uploaded_mobile = $image_mobile->store($dir);

            unset($data['image_mobile'], $data['_token']);
        }

        if ($uploaded) {
            $sliderItem = $this->find($id);

            // Storage::delete($sliderItem->path);

            $data['path'] = $uploaded;
        } else {
            unset($data['image']);
        }

        if ($uploaded_mobile) {
            $sliderItem = $this->find($id);

            // Storage::delete($sliderItem->path_mobile);

            $data['path_mobile'] = $uploaded_mobile;
        } else {
            unset($data['image_mobile']);
        }

        $slider = $this->update($data, $id);

        Event::dispatch('core.settings.slider.update.after', $slider);

        return true;
    }

    /**
     * Delete a slider item and delete the image from the disk or where ever it is.
     *
     * @param  int  $id
     * @return bool
     */
    public function destroy($id)
    {
        $sliderItem = $this->find($id);

        $sliderItemImage       = $sliderItem->path;
        $sliderItemMobileImage = $sliderItem->path_mobile;

        Storage::delete($sliderItemImage);
        Storage::delete($sliderItemMobileImage);

        return $this->model->destroy($id);
    }

    /**
     * Get only active sliders.
     *
     * @return array
     */
    public function getActiveSliders()
    {
        $currentChannel = core()->getCurrentChannel();

        $currentLocale = core()->getCurrentLocale();

        return $this->where('channel_id', $currentChannel->id)
            ->whereRaw('find_in_set(?, locale)', [$currentLocale->code])
            ->where(function ($query) {
                $query->where('expired_at', '>=', Carbon::now()->format('Y-m-d'))
                    ->orWhereNull('expired_at');
            })
            ->orderBy('sort_order', 'ASC')
            ->get()
            ->toArray();
    }
}
