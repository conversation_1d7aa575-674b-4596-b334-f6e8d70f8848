[{"id": 1, "country_code": "US", "code": "AL", "default_name": "Alabama", "country_id": 244}, {"id": 2, "country_code": "US", "code": "AK", "default_name": "Alaska", "country_id": 244}, {"id": 3, "country_code": "US", "code": "AS", "default_name": "American Samoa", "country_id": 244}, {"id": 4, "country_code": "US", "code": "AZ", "default_name": "Arizona", "country_id": 244}, {"id": 5, "country_code": "US", "code": "AR", "default_name": "Arkansas", "country_id": 244}, {"id": 6, "country_code": "US", "code": "AE", "default_name": "Armed Forces Africa", "country_id": 244}, {"id": 7, "country_code": "US", "code": "AA", "default_name": "Armed Forces Americas", "country_id": 244}, {"id": 8, "country_code": "US", "code": "AE", "default_name": "Armed Forces Canada", "country_id": 244}, {"id": 9, "country_code": "US", "code": "AE", "default_name": "Armed Forces Europe", "country_id": 244}, {"id": 10, "country_code": "US", "code": "AE", "default_name": "Armed Forces Middle East", "country_id": 244}, {"id": 11, "country_code": "US", "code": "AP", "default_name": "Armed Forces Pacific", "country_id": 244}, {"id": 12, "country_code": "US", "code": "CA", "default_name": "California", "country_id": 244}, {"id": 13, "country_code": "US", "code": "CO", "default_name": "Colorado", "country_id": 244}, {"id": 14, "country_code": "US", "code": "CT", "default_name": "Connecticut", "country_id": 244}, {"id": 15, "country_code": "US", "code": "DE", "default_name": "Delaware", "country_id": 244}, {"id": 16, "country_code": "US", "code": "DC", "default_name": "District of Columbia", "country_id": 244}, {"id": 17, "country_code": "US", "code": "FM", "default_name": "Federated States Of Micronesia", "country_id": 244}, {"id": 18, "country_code": "US", "code": "FL", "default_name": "Florida", "country_id": 244}, {"id": 19, "country_code": "US", "code": "GA", "default_name": "Georgia", "country_id": 244}, {"id": 20, "country_code": "US", "code": "GU", "default_name": "Guam", "country_id": 244}, {"id": 21, "country_code": "US", "code": "HI", "default_name": "Hawaii", "country_id": 244}, {"id": 22, "country_code": "US", "code": "ID", "default_name": "Idaho", "country_id": 244}, {"id": 23, "country_code": "US", "code": "IL", "default_name": "Illinois", "country_id": 244}, {"id": 24, "country_code": "US", "code": "IN", "default_name": "Indiana", "country_id": 244}, {"id": 25, "country_code": "US", "code": "IA", "default_name": "Iowa", "country_id": 244}, {"id": 26, "country_code": "US", "code": "KS", "default_name": "Kansas", "country_id": 244}, {"id": 27, "country_code": "US", "code": "KY", "default_name": "Kentucky", "country_id": 244}, {"id": 28, "country_code": "US", "code": "LA", "default_name": "Louisiana", "country_id": 244}, {"id": 29, "country_code": "US", "code": "ME", "default_name": "Maine", "country_id": 244}, {"id": 30, "country_code": "US", "code": "MH", "default_name": "Marshall Islands", "country_id": 244}, {"id": 31, "country_code": "US", "code": "MD", "default_name": "Maryland", "country_id": 244}, {"id": 32, "country_code": "US", "code": "MA", "default_name": "Massachusetts", "country_id": 244}, {"id": 33, "country_code": "US", "code": "MI", "default_name": "Michigan", "country_id": 244}, {"id": 34, "country_code": "US", "code": "MN", "default_name": "Minnesota", "country_id": 244}, {"id": 35, "country_code": "US", "code": "MS", "default_name": "Mississippi", "country_id": 244}, {"id": 36, "country_code": "US", "code": "MO", "default_name": "Missouri", "country_id": 244}, {"id": 37, "country_code": "US", "code": "MT", "default_name": "Montana", "country_id": 244}, {"id": 38, "country_code": "US", "code": "NE", "default_name": "Nebraska", "country_id": 244}, {"id": 39, "country_code": "US", "code": "NV", "default_name": "Nevada", "country_id": 244}, {"id": 40, "country_code": "US", "code": "NH", "default_name": "New Hampshire", "country_id": 244}, {"id": 41, "country_code": "US", "code": "NJ", "default_name": "New Jersey", "country_id": 244}, {"id": 42, "country_code": "US", "code": "NM", "default_name": "New Mexico", "country_id": 244}, {"id": 43, "country_code": "US", "code": "NY", "default_name": "New York", "country_id": 244}, {"id": 44, "country_code": "US", "code": "NC", "default_name": "North Carolina", "country_id": 244}, {"id": 45, "country_code": "US", "code": "ND", "default_name": "North Dakota", "country_id": 244}, {"id": 46, "country_code": "US", "code": "MP", "default_name": "Northern Mariana Islands", "country_id": 244}, {"id": 47, "country_code": "US", "code": "OH", "default_name": "Ohio", "country_id": 244}, {"id": 48, "country_code": "US", "code": "OK", "default_name": "Oklahoma", "country_id": 244}, {"id": 49, "country_code": "US", "code": "OR", "default_name": "Oregon", "country_id": 244}, {"id": 50, "country_code": "US", "code": "PW", "default_name": "<PERSON><PERSON>", "country_id": 244}, {"id": 51, "country_code": "US", "code": "PA", "default_name": "Pennsylvania", "country_id": 244}, {"id": 52, "country_code": "US", "code": "PR", "default_name": "Puerto Rico", "country_id": 244}, {"id": 53, "country_code": "US", "code": "RI", "default_name": "Rhode Island", "country_id": 244}, {"id": 54, "country_code": "US", "code": "SC", "default_name": "South Carolina", "country_id": 244}, {"id": 55, "country_code": "US", "code": "SD", "default_name": "South Dakota", "country_id": 244}, {"id": 56, "country_code": "US", "code": "TN", "default_name": "Tennessee", "country_id": 244}, {"id": 57, "country_code": "US", "code": "TX", "default_name": "Texas", "country_id": 244}, {"id": 58, "country_code": "US", "code": "UT", "default_name": "Utah", "country_id": 244}, {"id": 59, "country_code": "US", "code": "VT", "default_name": "Vermont", "country_id": 244}, {"id": 60, "country_code": "US", "code": "VI", "default_name": "Virgin Islands", "country_id": 244}, {"id": 61, "country_code": "US", "code": "VA", "default_name": "Virginia", "country_id": 244}, {"id": 62, "country_code": "US", "code": "WA", "default_name": "Washington", "country_id": 244}, {"id": 63, "country_code": "US", "code": "WV", "default_name": "West Virginia", "country_id": 244}, {"id": 64, "country_code": "US", "code": "WI", "default_name": "Wisconsin", "country_id": 244}, {"id": 65, "country_code": "US", "code": "WY", "default_name": "Wyoming", "country_id": 244}, {"id": 66, "country_code": "CA", "code": "AB", "default_name": "Alberta", "country_id": 40}, {"id": 67, "country_code": "CA", "code": "BC", "default_name": "British Columbia", "country_id": 40}, {"id": 68, "country_code": "CA", "code": "MB", "default_name": "Manitoba", "country_id": 40}, {"id": 69, "country_code": "CA", "code": "NL", "default_name": "Newfoundland and Labrador", "country_id": 40}, {"id": 70, "country_code": "CA", "code": "NB", "default_name": "New Brunswick", "country_id": 40}, {"id": 71, "country_code": "CA", "code": "NS", "default_name": "Nova Scotia", "country_id": 40}, {"id": 72, "country_code": "CA", "code": "NT", "default_name": "Northwest Territories", "country_id": 40}, {"id": 73, "country_code": "CA", "code": "NU", "default_name": "Nunavut", "country_id": 40}, {"id": 74, "country_code": "CA", "code": "ON", "default_name": "Ontario", "country_id": 40}, {"id": 75, "country_code": "CA", "code": "PE", "default_name": "Prince Edward Island", "country_id": 40}, {"id": 76, "country_code": "CA", "code": "QC", "default_name": "Quebec", "country_id": 40}, {"id": 77, "country_code": "CA", "code": "SK", "default_name": "Saskatchewan", "country_id": 40}, {"id": 78, "country_code": "CA", "code": "YT", "default_name": "Yukon Territory", "country_id": 40}, {"id": 79, "country_code": "DE", "code": "NDS", "default_name": "Niedersachsen", "country_id": 88}, {"id": 80, "country_code": "DE", "code": "BAW", "default_name": "Baden-Württemberg", "country_id": 88}, {"id": 81, "country_code": "DE", "code": "BAY", "default_name": "Bayern", "country_id": 88}, {"id": 82, "country_code": "DE", "code": "BER", "default_name": "Berlin", "country_id": 88}, {"id": 83, "country_code": "DE", "code": "BRG", "default_name": "Brandenburg", "country_id": 88}, {"id": 84, "country_code": "DE", "code": "BRE", "default_name": "Bremen", "country_id": 88}, {"id": 85, "country_code": "DE", "code": "HAM", "default_name": "Hamburg", "country_id": 88}, {"id": 86, "country_code": "DE", "code": "HES", "default_name": "Hessen", "country_id": 88}, {"id": 87, "country_code": "DE", "code": "MEC", "default_name": "Mecklenburg-Vorpommern", "country_id": 88}, {"id": 88, "country_code": "DE", "code": "NRW", "default_name": "Nordrhein-Westfalen", "country_id": 88}, {"id": 89, "country_code": "DE", "code": "RHE", "default_name": "Rheinland-Pfalz", "country_id": 88}, {"id": 90, "country_code": "DE", "code": "SAR", "default_name": "Saarland", "country_id": 88}, {"id": 91, "country_code": "DE", "code": "SAS", "default_name": "Sachsen", "country_id": 88}, {"id": 92, "country_code": "DE", "code": "SAC", "default_name": "Sachsen-Anhalt", "country_id": 88}, {"id": 93, "country_code": "DE", "code": "SCN", "default_name": "Schleswig-Holstein", "country_id": 88}, {"id": 94, "country_code": "DE", "code": "THE", "default_name": "T<PERSON><PERSON><PERSON>en", "country_id": 88}, {"id": 95, "country_code": "AT", "code": "WI", "default_name": "Wien", "country_id": 16}, {"id": 96, "country_code": "AT", "code": "NO", "default_name": "Niederösterreich", "country_id": 16}, {"id": 97, "country_code": "AT", "code": "OO", "default_name": "Oberösterreich", "country_id": 16}, {"id": 98, "country_code": "AT", "code": "SB", "default_name": "Salzburg", "country_id": 16}, {"id": 99, "country_code": "AT", "code": "KN", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 16}, {"id": 100, "country_code": "AT", "code": "ST", "default_name": "Steiermark", "country_id": 16}, {"id": 101, "country_code": "AT", "code": "TI", "default_name": "Tirol", "country_id": 16}, {"id": 102, "country_code": "AT", "code": "BL", "default_name": "Burgenland", "country_id": 16}, {"id": 103, "country_code": "AT", "code": "VB", "default_name": "Vorarlberg", "country_id": 16}, {"id": 104, "country_code": "CH", "code": "AG", "default_name": "Aargau", "country_id": 220}, {"id": 105, "country_code": "CH", "code": "AI", "default_name": "Appenzell Innerrhoden", "country_id": 220}, {"id": 106, "country_code": "CH", "code": "AR", "default_name": "<PERSON><PERSON><PERSON><PERSON> Ausserrhoden", "country_id": 220}, {"id": 107, "country_code": "CH", "code": "BE", "default_name": "Bern", "country_id": 220}, {"id": 108, "country_code": "CH", "code": "BL", "default_name": "Basel-Landschaft", "country_id": 220}, {"id": 109, "country_code": "CH", "code": "BS", "default_name": "Basel-Stadt", "country_id": 220}, {"id": 110, "country_code": "CH", "code": "FR", "default_name": "Freiburg", "country_id": 220}, {"id": 111, "country_code": "CH", "code": "GE", "default_name": "Genf", "country_id": 220}, {"id": 112, "country_code": "CH", "code": "GL", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 220}, {"id": 113, "country_code": "CH", "code": "GR", "default_name": "Graubünden", "country_id": 220}, {"id": 114, "country_code": "CH", "code": "JU", "default_name": "<PERSON><PERSON>", "country_id": 220}, {"id": 115, "country_code": "CH", "code": "LU", "default_name": "Luzern", "country_id": 220}, {"id": 116, "country_code": "CH", "code": "NE", "default_name": "Neuenburg", "country_id": 220}, {"id": 117, "country_code": "CH", "code": "NW", "default_name": "Nidwalden", "country_id": 220}, {"id": 118, "country_code": "CH", "code": "OW", "default_name": "Obwalden", "country_id": 220}, {"id": 119, "country_code": "CH", "code": "SG", "default_name": "St. Gallen", "country_id": 220}, {"id": 120, "country_code": "CH", "code": "SH", "default_name": "Sc<PERSON>ffhausen", "country_id": 220}, {"id": 121, "country_code": "CH", "code": "SO", "default_name": "Solothurn", "country_id": 220}, {"id": 122, "country_code": "CH", "code": "SZ", "default_name": "Schwyz", "country_id": 220}, {"id": 123, "country_code": "CH", "code": "TG", "default_name": "Thurgau", "country_id": 220}, {"id": 124, "country_code": "CH", "code": "TI", "default_name": "<PERSON><PERSON>", "country_id": 220}, {"id": 125, "country_code": "CH", "code": "UR", "default_name": "<PERSON><PERSON>", "country_id": 220}, {"id": 126, "country_code": "CH", "code": "VD", "default_name": "Waadt", "country_id": 220}, {"id": 127, "country_code": "CH", "code": "VS", "default_name": "<PERSON>", "country_id": 220}, {"id": 128, "country_code": "CH", "code": "ZG", "default_name": "<PERSON>ug", "country_id": 220}, {"id": 129, "country_code": "CH", "code": "ZH", "default_name": "Zürich", "country_id": 220}, {"id": 130, "country_code": "ES", "code": "A Coruсa", "default_name": "A Coruña", "country_id": 206}, {"id": 131, "country_code": "ES", "code": "Alava", "default_name": "Alava", "country_id": 206}, {"id": 132, "country_code": "ES", "code": "Albacete", "default_name": "Albacete", "country_id": 206}, {"id": 133, "country_code": "ES", "code": "Alicante", "default_name": "Alicante", "country_id": 206}, {"id": 134, "country_code": "ES", "code": "Almeria", "default_name": "Almeria", "country_id": 206}, {"id": 135, "country_code": "ES", "code": "Asturias", "default_name": "Asturias", "country_id": 206}, {"id": 136, "country_code": "ES", "code": "A<PERSON>", "default_name": "A<PERSON>", "country_id": 206}, {"id": 137, "country_code": "ES", "code": "Badajoz", "default_name": "Badajoz", "country_id": 206}, {"id": 138, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 139, "country_code": "ES", "code": "Barcelona", "default_name": "Barcelona", "country_id": 206}, {"id": 140, "country_code": "ES", "code": "Burgos", "default_name": "Burgos", "country_id": 206}, {"id": 141, "country_code": "ES", "code": "Caceres", "default_name": "Caceres", "country_id": 206}, {"id": 142, "country_code": "ES", "code": "Cadiz", "default_name": "Cadiz", "country_id": 206}, {"id": 143, "country_code": "ES", "code": "Cantabria", "default_name": "Cantabria", "country_id": 206}, {"id": 144, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 145, "country_code": "ES", "code": "<PERSON><PERSON>", "default_name": "<PERSON><PERSON>", "country_id": 206}, {"id": 146, "country_code": "ES", "code": "Ciudad Real", "default_name": "Ciudad Real", "country_id": 206}, {"id": 147, "country_code": "ES", "code": "Cordoba", "default_name": "Cordoba", "country_id": 206}, {"id": 148, "country_code": "ES", "code": "Cuenca", "default_name": "Cuenca", "country_id": 206}, {"id": 149, "country_code": "ES", "code": "Girona", "default_name": "Girona", "country_id": 206}, {"id": 150, "country_code": "ES", "code": "Granada", "default_name": "Granada", "country_id": 206}, {"id": 151, "country_code": "ES", "code": "Guadalajara", "default_name": "Guadalajara", "country_id": 206}, {"id": 152, "country_code": "ES", "code": "Guipuzcoa", "default_name": "Guipuzcoa", "country_id": 206}, {"id": 153, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 154, "country_code": "ES", "code": "Huesca", "default_name": "Huesca", "country_id": 206}, {"id": 155, "country_code": "ES", "code": "<PERSON><PERSON>", "default_name": "<PERSON><PERSON>", "country_id": 206}, {"id": 156, "country_code": "ES", "code": "La Rioja", "default_name": "La Rioja", "country_id": 206}, {"id": 157, "country_code": "ES", "code": "Las Palmas", "default_name": "Las Palmas", "country_id": 206}, {"id": 158, "country_code": "ES", "code": "<PERSON>", "default_name": "<PERSON>", "country_id": 206}, {"id": 159, "country_code": "ES", "code": "Lleida", "default_name": "Lleida", "country_id": 206}, {"id": 160, "country_code": "ES", "code": "Lugo", "default_name": "Lugo", "country_id": 206}, {"id": 161, "country_code": "ES", "code": "Madrid", "default_name": "Madrid", "country_id": 206}, {"id": 162, "country_code": "ES", "code": "Malaga", "default_name": "Malaga", "country_id": 206}, {"id": 163, "country_code": "ES", "code": "Melilla", "default_name": "Melilla", "country_id": 206}, {"id": 164, "country_code": "ES", "code": "Murcia", "default_name": "Murcia", "country_id": 206}, {"id": 165, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 166, "country_code": "ES", "code": "Ourense", "default_name": "Ourense", "country_id": 206}, {"id": 167, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 168, "country_code": "ES", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 206}, {"id": 169, "country_code": "ES", "code": "Salamanca", "default_name": "Salamanca", "country_id": 206}, {"id": 170, "country_code": "ES", "code": "Santa Cruz de Tenerife", "default_name": "Santa Cruz de Tenerife", "country_id": 206}, {"id": 171, "country_code": "ES", "code": "Segovia", "default_name": "Segovia", "country_id": 206}, {"id": 172, "country_code": "ES", "code": "Sevilla", "default_name": "Sevilla", "country_id": 206}, {"id": 173, "country_code": "ES", "code": "Soria", "default_name": "Soria", "country_id": 206}, {"id": 174, "country_code": "ES", "code": "Tarragona", "default_name": "Tarragona", "country_id": 206}, {"id": 175, "country_code": "ES", "code": "Teruel", "default_name": "Teruel", "country_id": 206}, {"id": 176, "country_code": "ES", "code": "Toledo", "default_name": "Toledo", "country_id": 206}, {"id": 177, "country_code": "ES", "code": "Valencia", "default_name": "Valencia", "country_id": 206}, {"id": 178, "country_code": "ES", "code": "Valladoli<PERSON>", "default_name": "Valladoli<PERSON>", "country_id": 206}, {"id": 179, "country_code": "ES", "code": "Vizcaya", "default_name": "Vizcaya", "country_id": 206}, {"id": 180, "country_code": "ES", "code": "Zamora", "default_name": "Zamora", "country_id": 206}, {"id": 181, "country_code": "ES", "code": "Zaragoza", "default_name": "Zaragoza", "country_id": 206}, {"id": 182, "country_code": "FR", "code": "1", "default_name": "Ain", "country_id": 81}, {"id": 183, "country_code": "FR", "code": "2", "default_name": "Aisne", "country_id": 81}, {"id": 184, "country_code": "FR", "code": "3", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 185, "country_code": "FR", "code": "4", "default_name": "Alpes-de-Haute-Provence", "country_id": 81}, {"id": 186, "country_code": "FR", "code": "5", "default_name": "Hautes-Alpes", "country_id": 81}, {"id": 187, "country_code": "FR", "code": "6", "default_name": "Alpes-Maritimes", "country_id": 81}, {"id": 188, "country_code": "FR", "code": "7", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 189, "country_code": "FR", "code": "8", "default_name": "Ardennes", "country_id": 81}, {"id": 190, "country_code": "FR", "code": "9", "default_name": "Ariège", "country_id": 81}, {"id": 191, "country_code": "FR", "code": "10", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 192, "country_code": "FR", "code": "11", "default_name": "Aude", "country_id": 81}, {"id": 193, "country_code": "FR", "code": "12", "default_name": "Aveyr<PERSON>", "country_id": 81}, {"id": 194, "country_code": "FR", "code": "13", "default_name": "Bouches-du-Rhône", "country_id": 81}, {"id": 195, "country_code": "FR", "code": "14", "default_name": "Calvados", "country_id": 81}, {"id": 196, "country_code": "FR", "code": "15", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 197, "country_code": "FR", "code": "16", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 198, "country_code": "FR", "code": "17", "default_name": "Charente-Maritime", "country_id": 81}, {"id": 199, "country_code": "FR", "code": "18", "default_name": "Cher", "country_id": 81}, {"id": 200, "country_code": "FR", "code": "19", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 201, "country_code": "FR", "code": "2A", "default_name": "Corse-du-Sud", "country_id": 81}, {"id": 202, "country_code": "FR", "code": "2B", "default_name": "Haute-Corse", "country_id": 81}, {"id": 203, "country_code": "FR", "code": "21", "default_name": "Côte-d'Or", "country_id": 81}, {"id": 204, "country_code": "FR", "code": "22", "default_name": "Côtes-d'Armor", "country_id": 81}, {"id": 205, "country_code": "FR", "code": "23", "default_name": "Creuse", "country_id": 81}, {"id": 206, "country_code": "FR", "code": "24", "default_name": "Dordogne", "country_id": 81}, {"id": 207, "country_code": "FR", "code": "25", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 208, "country_code": "FR", "code": "26", "default_name": "Dr<PERSON>", "country_id": 81}, {"id": 209, "country_code": "FR", "code": "27", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 210, "country_code": "FR", "code": "28", "default_name": "Eure-et-Loir", "country_id": 81}, {"id": 211, "country_code": "FR", "code": "29", "default_name": "Finistère", "country_id": 81}, {"id": 212, "country_code": "FR", "code": "30", "default_name": "Gard", "country_id": 81}, {"id": 213, "country_code": "FR", "code": "31", "default_name": "Haute-Garonne", "country_id": 81}, {"id": 214, "country_code": "FR", "code": "32", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 215, "country_code": "FR", "code": "33", "default_name": "Gironde", "country_id": 81}, {"id": 216, "country_code": "FR", "code": "34", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 217, "country_code": "FR", "code": "35", "default_name": "Ille-et-Vilaine", "country_id": 81}, {"id": 218, "country_code": "FR", "code": "36", "default_name": "Indre", "country_id": 81}, {"id": 219, "country_code": "FR", "code": "37", "default_name": "Indre-et-Loire", "country_id": 81}, {"id": 220, "country_code": "FR", "code": "38", "default_name": "Isère", "country_id": 81}, {"id": 221, "country_code": "FR", "code": "39", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 222, "country_code": "FR", "code": "40", "default_name": "Landes", "country_id": 81}, {"id": 223, "country_code": "FR", "code": "41", "default_name": "Loir-et-Cher", "country_id": 81}, {"id": 224, "country_code": "FR", "code": "42", "default_name": "Loire", "country_id": 81}, {"id": 225, "country_code": "FR", "code": "43", "default_name": "Haute-Loire", "country_id": 81}, {"id": 226, "country_code": "FR", "code": "44", "default_name": "Loire-Atlantique", "country_id": 81}, {"id": 227, "country_code": "FR", "code": "45", "default_name": "Loiret", "country_id": 81}, {"id": 228, "country_code": "FR", "code": "46", "default_name": "Lot", "country_id": 81}, {"id": 229, "country_code": "FR", "code": "47", "default_name": "Lot-et-Garonne", "country_id": 81}, {"id": 230, "country_code": "FR", "code": "48", "default_name": "Lozère", "country_id": 81}, {"id": 231, "country_code": "FR", "code": "49", "default_name": "Maine-et-Loire", "country_id": 81}, {"id": 232, "country_code": "FR", "code": "50", "default_name": "Manche", "country_id": 81}, {"id": 233, "country_code": "FR", "code": "51", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 234, "country_code": "FR", "code": "52", "default_name": "Haute-Marne", "country_id": 81}, {"id": 235, "country_code": "FR", "code": "53", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 236, "country_code": "FR", "code": "54", "default_name": "Meurthe-et-Moselle", "country_id": 81}, {"id": 237, "country_code": "FR", "code": "55", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 238, "country_code": "FR", "code": "56", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 239, "country_code": "FR", "code": "57", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 240, "country_code": "FR", "code": "58", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 241, "country_code": "FR", "code": "59", "default_name": "Nord", "country_id": 81}, {"id": 242, "country_code": "FR", "code": "60", "default_name": "Oise", "country_id": 81}, {"id": 243, "country_code": "FR", "code": "61", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 244, "country_code": "FR", "code": "62", "default_name": "Pas-de-Calais", "country_id": 81}, {"id": 245, "country_code": "FR", "code": "63", "default_name": "Puy-de-Dôme", "country_id": 81}, {"id": 246, "country_code": "FR", "code": "64", "default_name": "Pyrénées-Atlantiques", "country_id": 81}, {"id": 247, "country_code": "FR", "code": "65", "default_name": "Hautes-Pyrénées", "country_id": 81}, {"id": 248, "country_code": "FR", "code": "66", "default_name": "Pyrénées-Orientales", "country_id": 81}, {"id": 249, "country_code": "FR", "code": "67", "default_name": "Bas-Rhin", "country_id": 81}, {"id": 250, "country_code": "FR", "code": "68", "default_name": "Haut-Rhin", "country_id": 81}, {"id": 251, "country_code": "FR", "code": "69", "default_name": "Rhône", "country_id": 81}, {"id": 252, "country_code": "FR", "code": "70", "default_name": "Haute-Saône", "country_id": 81}, {"id": 253, "country_code": "FR", "code": "71", "default_name": "Saône-et-Loire", "country_id": 81}, {"id": 254, "country_code": "FR", "code": "72", "default_name": "Sarthe", "country_id": 81}, {"id": 255, "country_code": "FR", "code": "73", "default_name": "Savoie", "country_id": 81}, {"id": 256, "country_code": "FR", "code": "74", "default_name": "Haute-Savoie", "country_id": 81}, {"id": 257, "country_code": "FR", "code": "75", "default_name": "Paris", "country_id": 81}, {"id": 258, "country_code": "FR", "code": "76", "default_name": "Seine-Maritime", "country_id": 81}, {"id": 259, "country_code": "FR", "code": "77", "default_name": "Seine-et-Marne", "country_id": 81}, {"id": 260, "country_code": "FR", "code": "78", "default_name": "Yvelines", "country_id": 81}, {"id": 261, "country_code": "FR", "code": "79", "default_name": "Deux-Sèvres", "country_id": 81}, {"id": 262, "country_code": "FR", "code": "80", "default_name": "Somme", "country_id": 81}, {"id": 263, "country_code": "FR", "code": "81", "default_name": "Tarn", "country_id": 81}, {"id": 264, "country_code": "FR", "code": "82", "default_name": "Tarn-et-Garonne", "country_id": 81}, {"id": 265, "country_code": "FR", "code": "83", "default_name": "Var", "country_id": 81}, {"id": 266, "country_code": "FR", "code": "84", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 81}, {"id": 267, "country_code": "FR", "code": "85", "default_name": "Vendée", "country_id": 81}, {"id": 268, "country_code": "FR", "code": "86", "default_name": "Vienne", "country_id": 81}, {"id": 269, "country_code": "FR", "code": "87", "default_name": "Haute-Vienne", "country_id": 81}, {"id": 270, "country_code": "FR", "code": "88", "default_name": "Vosges", "country_id": 81}, {"id": 271, "country_code": "FR", "code": "89", "default_name": "<PERSON><PERSON>", "country_id": 81}, {"id": 272, "country_code": "FR", "code": "90", "default_name": "Territoire-de-Belfort", "country_id": 81}, {"id": 273, "country_code": "FR", "code": "91", "default_name": "Essonne", "country_id": 81}, {"id": 274, "country_code": "FR", "code": "92", "default_name": "Hauts-de-Seine", "country_id": 81}, {"id": 275, "country_code": "FR", "code": "93", "default_name": "Seine-Saint-Denis", "country_id": 81}, {"id": 276, "country_code": "FR", "code": "94", "default_name": "Val-de-Marne", "country_id": 81}, {"id": 277, "country_code": "FR", "code": "95", "default_name": "Val-d'Oise", "country_id": 81}, {"id": 278, "country_code": "RO", "code": "AB", "default_name": "Alba", "country_id": 185}, {"id": 279, "country_code": "RO", "code": "AR", "default_name": "Arad", "country_id": 185}, {"id": 280, "country_code": "RO", "code": "AG", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 281, "country_code": "RO", "code": "BC", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 282, "country_code": "RO", "code": "BH", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 283, "country_code": "RO", "code": "BN", "default_name": "Bistriţa-Năsăud", "country_id": 185}, {"id": 284, "country_code": "RO", "code": "BT", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 285, "country_code": "RO", "code": "BV", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 286, "country_code": "RO", "code": "BR", "default_name": "Brăila", "country_id": 185}, {"id": 287, "country_code": "RO", "code": "B", "default_name": "Bucureşti", "country_id": 185}, {"id": 288, "country_code": "RO", "code": "BZ", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 289, "country_code": "RO", "code": "CS", "default_name": "Caraş-<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 290, "country_code": "RO", "code": "CL", "default_name": "Călăraşi", "country_id": 185}, {"id": 291, "country_code": "RO", "code": "CJ", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 292, "country_code": "RO", "code": "CT", "default_name": "Con<PERSON>ţ<PERSON>", "country_id": 185}, {"id": 293, "country_code": "RO", "code": "CV", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 294, "country_code": "RO", "code": "DB", "default_name": "Dâmboviţa", "country_id": 185}, {"id": 295, "country_code": "RO", "code": "DJ", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 296, "country_code": "RO", "code": "GL", "default_name": "Galaţi", "country_id": 185}, {"id": 297, "country_code": "RO", "code": "GR", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 298, "country_code": "RO", "code": "GJ", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 299, "country_code": "RO", "code": "HR", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 300, "country_code": "RO", "code": "HD", "default_name": "Hunedoara", "country_id": 185}, {"id": 301, "country_code": "RO", "code": "IL", "default_name": "<PERSON>alo<PERSON>ţ<PERSON>", "country_id": 185}, {"id": 302, "country_code": "RO", "code": "IS", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 303, "country_code": "RO", "code": "IF", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 304, "country_code": "RO", "code": "MM", "default_name": "Maramureş", "country_id": 185}, {"id": 305, "country_code": "RO", "code": "MH", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 306, "country_code": "RO", "code": "MS", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 307, "country_code": "RO", "code": "NT", "default_name": "Neamţ", "country_id": 185}, {"id": 308, "country_code": "RO", "code": "OT", "default_name": "Olt", "country_id": 185}, {"id": 309, "country_code": "RO", "code": "PH", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 310, "country_code": "RO", "code": "SM", "default_name": "Satu-Mare", "country_id": 185}, {"id": 311, "country_code": "RO", "code": "SJ", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 312, "country_code": "RO", "code": "SB", "default_name": "Sibiu", "country_id": 185}, {"id": 313, "country_code": "RO", "code": "SV", "default_name": "Su<PERSON>va", "country_id": 185}, {"id": 314, "country_code": "RO", "code": "TR", "default_name": "Teleorman", "country_id": 185}, {"id": 315, "country_code": "RO", "code": "TM", "default_name": "<PERSON><PERSON>ş", "country_id": 185}, {"id": 316, "country_code": "RO", "code": "TL", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 317, "country_code": "RO", "code": "VS", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 318, "country_code": "RO", "code": "VL", "default_name": "V<PERSON><PERSON><PERSON>", "country_id": 185}, {"id": 319, "country_code": "RO", "code": "VN", "default_name": "Vrancea", "country_id": 185}, {"id": 320, "country_code": "FI", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 321, "country_code": "FI", "code": "Pohjois-<PERSON><PERSON><PERSON>ma<PERSON>", "default_name": "Pohjois-<PERSON><PERSON><PERSON>ma<PERSON>", "country_id": 80}, {"id": 322, "country_code": "FI", "code": "Kai<PERSON><PERSON>", "default_name": "Kai<PERSON><PERSON>", "country_id": 80}, {"id": 323, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 324, "country_code": "FI", "code": "Pohjois-Savo", "default_name": "Pohjois-Savo", "country_id": 80}, {"id": 325, "country_code": "FI", "code": "Etelä-Savo", "default_name": "Etelä-Savo", "country_id": 80}, {"id": 326, "country_code": "FI", "code": "Etelä-Pohjanmaa", "default_name": "Etelä-Pohjanmaa", "country_id": 80}, {"id": 327, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 328, "country_code": "FI", "code": "Pirkanmaa", "default_name": "Pirkanmaa", "country_id": 80}, {"id": 329, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 330, "country_code": "FI", "code": "Keski-Pohjanmaa", "default_name": "Keski-Pohjanmaa", "country_id": 80}, {"id": 331, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 332, "country_code": "FI", "code": "Varsina<PERSON><PERSON><PERSON><PERSON>", "default_name": "Varsina<PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 333, "country_code": "FI", "code": "Etelä-<PERSON><PERSON><PERSON><PERSON>", "default_name": "Etelä-<PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 334, "country_code": "FI", "code": "Päijät-Häme", "default_name": "Päijät-Häme", "country_id": 80}, {"id": 335, "country_code": "FI", "code": "Kanta-Häme", "default_name": "Kanta-Häme", "country_id": 80}, {"id": 336, "country_code": "FI", "code": "Uusimaa", "default_name": "Uusimaa", "country_id": 80}, {"id": 337, "country_code": "FI", "code": "Itä-Uusimaa", "default_name": "Itä-Uusimaa", "country_id": 80}, {"id": 338, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 339, "country_code": "FI", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 80}, {"id": 340, "country_code": "EE", "code": "EE-37", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 74}, {"id": 341, "country_code": "EE", "code": "EE-39", "default_name": "Hiiumaa", "country_id": 74}, {"id": 342, "country_code": "EE", "code": "EE-44", "default_name": "Ida-<PERSON><PERSON><PERSON><PERSON>", "country_id": 74}, {"id": 343, "country_code": "EE", "code": "EE-49", "default_name": "Jõgevamaa", "country_id": 74}, {"id": 344, "country_code": "EE", "code": "EE-51", "default_name": "Järvamaa", "country_id": 74}, {"id": 345, "country_code": "EE", "code": "EE-57", "default_name": "Läänemaa", "country_id": 74}, {"id": 346, "country_code": "EE", "code": "EE-59", "default_name": "Lääne-Virumaa", "country_id": 74}, {"id": 347, "country_code": "EE", "code": "EE-65", "default_name": "Põlvamaa", "country_id": 74}, {"id": 348, "country_code": "EE", "code": "EE-67", "default_name": "P<PERSON><PERSON>uma<PERSON>", "country_id": 74}, {"id": 349, "country_code": "EE", "code": "EE-70", "default_name": "Raplamaa", "country_id": 74}, {"id": 350, "country_code": "EE", "code": "EE-74", "default_name": "Saarema<PERSON>", "country_id": 74}, {"id": 351, "country_code": "EE", "code": "EE-78", "default_name": "Tartumaa", "country_id": 74}, {"id": 352, "country_code": "EE", "code": "EE-82", "default_name": "Valgamaa", "country_id": 74}, {"id": 353, "country_code": "EE", "code": "EE-84", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country_id": 74}, {"id": 354, "country_code": "EE", "code": "EE-86", "default_name": "Võrumaa", "country_id": 74}, {"id": 355, "country_code": "LV", "code": "LV-DGV", "default_name": "Daugavpils", "country_id": 125}, {"id": 356, "country_code": "LV", "code": "LV-JEL", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 125}, {"id": 357, "country_code": "LV", "code": "Jēkabpils", "default_name": "Jēkabpils", "country_id": 125}, {"id": 358, "country_code": "LV", "code": "LV-JUR", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 125}, {"id": 359, "country_code": "LV", "code": "LV-LPX", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 125}, {"id": 360, "country_code": "LV", "code": "LV-LE", "default_name": "Liepājas novads", "country_id": 125}, {"id": 361, "country_code": "LV", "code": "LV-REZ", "default_name": "Rē<PERSON>kne", "country_id": 125}, {"id": 362, "country_code": "LV", "code": "LV-RIX", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 125}, {"id": 363, "country_code": "LV", "code": "LV-RI", "default_name": "Rīgas novads", "country_id": 125}, {"id": 364, "country_code": "LV", "code": "Valmiera", "default_name": "Valmiera", "country_id": 125}, {"id": 365, "country_code": "LV", "code": "LV-VEN", "default_name": "Ventspils", "country_id": 125}, {"id": 366, "country_code": "LV", "code": "Aglonas novads", "default_name": "Aglonas novads", "country_id": 125}, {"id": 367, "country_code": "LV", "code": "LV-AI", "default_name": "Aizkraukles novads", "country_id": 125}, {"id": 368, "country_code": "LV", "code": "Aizputes novads", "default_name": "Aizputes novads", "country_id": 125}, {"id": 369, "country_code": "LV", "code": "Akn<PERSON><PERSON>s no<PERSON>", "default_name": "Akn<PERSON><PERSON>s no<PERSON>", "country_id": 125}, {"id": 370, "country_code": "LV", "code": "<PERSON><PERSON><PERSON> no<PERSON>", "default_name": "<PERSON><PERSON><PERSON> no<PERSON>", "country_id": 125}, {"id": 371, "country_code": "LV", "code": "Alsungas novads", "default_name": "Alsungas novads", "country_id": 125}, {"id": 372, "country_code": "LV", "code": "LV-AL", "default_name": "Alūksnes novads", "country_id": 125}, {"id": 373, "country_code": "LV", "code": "Amatas novads", "default_name": "Amatas novads", "country_id": 125}, {"id": 374, "country_code": "LV", "code": "Apes novads", "default_name": "Apes novads", "country_id": 125}, {"id": 375, "country_code": "LV", "code": "Auces novads", "default_name": "Auces novads", "country_id": 125}, {"id": 376, "country_code": "LV", "code": "Babītes novads", "default_name": "Babītes novads", "country_id": 125}, {"id": 377, "country_code": "LV", "code": "Baldones novads", "default_name": "Baldones novads", "country_id": 125}, {"id": 378, "country_code": "LV", "code": "Baltinavas novads", "default_name": "Baltinavas novads", "country_id": 125}, {"id": 379, "country_code": "LV", "code": "LV-BL", "default_name": "Balvu novads", "country_id": 125}, {"id": 380, "country_code": "LV", "code": "LV-BU", "default_name": "Bauskas novads", "country_id": 125}, {"id": 381, "country_code": "LV", "code": "Beverīnas novads", "default_name": "Beverīnas novads", "country_id": 125}, {"id": 382, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 383, "country_code": "LV", "code": "Burtnieku novads", "default_name": "Burtnieku novads", "country_id": 125}, {"id": 384, "country_code": "LV", "code": "Carnikavas novads", "default_name": "Carnikavas novads", "country_id": 125}, {"id": 385, "country_code": "LV", "code": "Cesvaines novads", "default_name": "Cesvaines novads", "country_id": 125}, {"id": 386, "country_code": "LV", "code": "Ciblas novads", "default_name": "Ciblas novads", "country_id": 125}, {"id": 387, "country_code": "LV", "code": "LV-CE", "default_name": "<PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 388, "country_code": "LV", "code": "<PERSON><PERSON><PERSON> nova<PERSON>", "default_name": "<PERSON><PERSON><PERSON> nova<PERSON>", "country_id": 125}, {"id": 389, "country_code": "LV", "code": "LV-DA", "default_name": "Daugavpils novads", "country_id": 125}, {"id": 390, "country_code": "LV", "code": "LV-DO", "default_name": "Dobeles novads", "country_id": 125}, {"id": 391, "country_code": "LV", "code": "Dundagas novads", "default_name": "Dundagas novads", "country_id": 125}, {"id": 392, "country_code": "LV", "code": "Durbes novads", "default_name": "Durbes novads", "country_id": 125}, {"id": 393, "country_code": "LV", "code": "Engures novads", "default_name": "Engures novads", "country_id": 125}, {"id": 394, "country_code": "LV", "code": "Garkalnes novads", "default_name": "Garkalnes novads", "country_id": 125}, {"id": 395, "country_code": "LV", "code": "Grobiņas novads", "default_name": "Grobiņas novads", "country_id": 125}, {"id": 396, "country_code": "LV", "code": "LV-GU", "default_name": "Gulbenes novads", "country_id": 125}, {"id": 397, "country_code": "LV", "code": "Iecavas novads", "default_name": "Iecavas novads", "country_id": 125}, {"id": 398, "country_code": "LV", "code": "Ikšķiles novads", "default_name": "Ikšķiles novads", "country_id": 125}, {"id": 399, "country_code": "LV", "code": "Ilūkstes novads", "default_name": "Ilūkstes novads", "country_id": 125}, {"id": 400, "country_code": "LV", "code": "Inčukalna novads", "default_name": "Inčukalna novads", "country_id": 125}, {"id": 401, "country_code": "LV", "code": "Jaunjelgavas novads", "default_name": "Jaunjelgavas novads", "country_id": 125}, {"id": 402, "country_code": "LV", "code": "Jaunpiebalgas novads", "default_name": "Jaunpiebalgas novads", "country_id": 125}, {"id": 403, "country_code": "LV", "code": "Jaunpils novads", "default_name": "Jaunpils novads", "country_id": 125}, {"id": 404, "country_code": "LV", "code": "LV-JL", "default_name": "Jelgavas novads", "country_id": 125}, {"id": 405, "country_code": "LV", "code": "LV-JK", "default_name": "Jēkabpils novads", "country_id": 125}, {"id": 406, "country_code": "LV", "code": "Kandavas novads", "default_name": "Kandavas novads", "country_id": 125}, {"id": 407, "country_code": "LV", "code": "Kokneses novads", "default_name": "Kokneses novads", "country_id": 125}, {"id": 408, "country_code": "LV", "code": "Krimuldas novads", "default_name": "Krimuldas novads", "country_id": 125}, {"id": 409, "country_code": "LV", "code": "Krustpils novads", "default_name": "Krustpils novads", "country_id": 125}, {"id": 410, "country_code": "LV", "code": "LV-KR", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "country_id": 125}, {"id": 411, "country_code": "LV", "code": "LV-KU", "default_name": "Kuldīgas novads", "country_id": 125}, {"id": 412, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 413, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 414, "country_code": "LV", "code": "LV-LM", "default_name": "Limbažu novads", "country_id": 125}, {"id": 415, "country_code": "LV", "code": "Lubānas novads", "default_name": "Lubānas novads", "country_id": 125}, {"id": 416, "country_code": "LV", "code": "LV-LU", "default_name": "Ludzas novads", "country_id": 125}, {"id": 417, "country_code": "LV", "code": "Līgatnes novads", "default_name": "Līgatnes novads", "country_id": 125}, {"id": 418, "country_code": "LV", "code": "<PERSON>īvānu novads", "default_name": "<PERSON>īvānu novads", "country_id": 125}, {"id": 419, "country_code": "LV", "code": "LV-MA", "default_name": "Mad<PERSON>s no<PERSON>", "country_id": 125}, {"id": 420, "country_code": "LV", "code": "Mazsalacas novads", "default_name": "Mazsalacas novads", "country_id": 125}, {"id": 421, "country_code": "LV", "code": "Māl<PERSON><PERSON> novads", "default_name": "Māl<PERSON><PERSON> novads", "country_id": 125}, {"id": 422, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "country_id": 125}, {"id": 423, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 424, "country_code": "LV", "code": "Neretas novads", "default_name": "Neretas novads", "country_id": 125}, {"id": 425, "country_code": "LV", "code": "Nīcas novads", "default_name": "Nīcas novads", "country_id": 125}, {"id": 426, "country_code": "LV", "code": "LV-OG", "default_name": "Ogres novads", "country_id": 125}, {"id": 427, "country_code": "LV", "code": "<PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 428, "country_code": "LV", "code": "Ozolnieku novads", "default_name": "Ozolnieku novads", "country_id": 125}, {"id": 429, "country_code": "LV", "code": "LV-PR", "default_name": "Preiļu novads", "country_id": 125}, {"id": 430, "country_code": "LV", "code": "Priekules novads", "default_name": "Priekules novads", "country_id": 125}, {"id": 431, "country_code": "LV", "code": "Priekuļu novads", "default_name": "Priekuļu novads", "country_id": 125}, {"id": 432, "country_code": "LV", "code": "Pārgaujas novads", "default_name": "Pārgaujas novads", "country_id": 125}, {"id": 433, "country_code": "LV", "code": "Pāvilostas novads", "default_name": "Pāvilostas novads", "country_id": 125}, {"id": 434, "country_code": "LV", "code": "Pļaviņu novads", "default_name": "Pļaviņu novads", "country_id": 125}, {"id": 435, "country_code": "LV", "code": "Raunas novads", "default_name": "Raunas novads", "country_id": 125}, {"id": 436, "country_code": "LV", "code": "Riebiņ<PERSON> novads", "default_name": "Riebiņ<PERSON> novads", "country_id": 125}, {"id": 437, "country_code": "LV", "code": "<PERSON><PERSON><PERSON>", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 125}, {"id": 438, "country_code": "LV", "code": "Ropažu novads", "default_name": "Ropažu novads", "country_id": 125}, {"id": 439, "country_code": "LV", "code": "Rucavas novads", "default_name": "Rucavas novads", "country_id": 125}, {"id": 440, "country_code": "LV", "code": "Rugāju novads", "default_name": "Rugāju novads", "country_id": 125}, {"id": 441, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "country_id": 125}, {"id": 442, "country_code": "LV", "code": "LV-RE", "default_name": "Rēzeknes novads", "country_id": 125}, {"id": 443, "country_code": "LV", "code": "Rūjienas novads", "default_name": "Rūjienas novads", "country_id": 125}, {"id": 444, "country_code": "LV", "code": "Salacgrīvas novads", "default_name": "Salacgrīvas novads", "country_id": 125}, {"id": 445, "country_code": "LV", "code": "Salas novads", "default_name": "Salas novads", "country_id": 125}, {"id": 446, "country_code": "LV", "code": "Salaspils novads", "default_name": "Salaspils novads", "country_id": 125}, {"id": 447, "country_code": "LV", "code": "LV-SA", "default_name": "Saldus novads", "country_id": 125}, {"id": 448, "country_code": "LV", "code": "Saulkrastu novads", "default_name": "Saulkrastu novads", "country_id": 125}, {"id": 449, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 450, "country_code": "LV", "code": "Sk<PERSON>das novads", "default_name": "Sk<PERSON>das novads", "country_id": 125}, {"id": 451, "country_code": "LV", "code": "Skrīveru novads", "default_name": "Skrīveru novads", "country_id": 125}, {"id": 452, "country_code": "LV", "code": "Smiltenes novads", "default_name": "Smiltenes novads", "country_id": 125}, {"id": 453, "country_code": "LV", "code": "Stopiņu novads", "default_name": "Stopiņu novads", "country_id": 125}, {"id": 454, "country_code": "LV", "code": "Strenču novads", "default_name": "Strenču novads", "country_id": 125}, {"id": 455, "country_code": "LV", "code": "Sējas novads", "default_name": "Sējas novads", "country_id": 125}, {"id": 456, "country_code": "LV", "code": "LV-TA", "default_name": "Talsu novads", "country_id": 125}, {"id": 457, "country_code": "LV", "code": "LV-TU", "default_name": "Tukuma novads", "country_id": 125}, {"id": 458, "country_code": "LV", "code": "Tērvetes novads", "default_name": "Tērvetes novads", "country_id": 125}, {"id": 459, "country_code": "LV", "code": "Vaiņodes novads", "default_name": "Vaiņodes novads", "country_id": 125}, {"id": 460, "country_code": "LV", "code": "LV-VK", "default_name": "Valkas novads", "country_id": 125}, {"id": 461, "country_code": "LV", "code": "LV-VM", "default_name": "Valmieras novads", "country_id": 125}, {"id": 462, "country_code": "LV", "code": "Varakļānu novads", "default_name": "Varakļānu novads", "country_id": 125}, {"id": 463, "country_code": "LV", "code": "Vecpiebalgas novads", "default_name": "Vecpiebalgas novads", "country_id": 125}, {"id": 464, "country_code": "LV", "code": "Vecumnieku novads", "default_name": "Vecumnieku novads", "country_id": 125}, {"id": 465, "country_code": "LV", "code": "LV-VE", "default_name": "Ventspils novads", "country_id": 125}, {"id": 466, "country_code": "LV", "code": "Viesī<PERSON> novads", "default_name": "Viesī<PERSON> novads", "country_id": 125}, {"id": 467, "country_code": "LV", "code": "Viļaka<PERSON> no<PERSON>", "default_name": "Viļaka<PERSON> no<PERSON>", "country_id": 125}, {"id": 468, "country_code": "LV", "code": "Viļānu novads", "default_name": "Viļānu novads", "country_id": 125}, {"id": 469, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> novads", "default_name": "<PERSON><PERSON><PERSON><PERSON> novads", "country_id": 125}, {"id": 470, "country_code": "LV", "code": "Zilupes novads", "default_name": "Zilupes novads", "country_id": 125}, {"id": 471, "country_code": "LV", "code": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>", "country_id": 125}, {"id": 472, "country_code": "LV", "code": "Ērgļu novads", "default_name": "Ērgļu novads", "country_id": 125}, {"id": 473, "country_code": "LV", "code": "Ķeguma novads", "default_name": "Ķeguma novads", "country_id": 125}, {"id": 474, "country_code": "LV", "code": "Ķekavas novads", "default_name": "Ķekavas novads", "country_id": 125}, {"id": 475, "country_code": "LT", "code": "LT-AL", "default_name": "Alytaus Apskritis", "country_id": 131}, {"id": 476, "country_code": "LT", "code": "LT-KU", "default_name": "<PERSON><PERSON>", "country_id": 131}, {"id": 477, "country_code": "LT", "code": "LT-KL", "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Apskrit<PERSON>", "country_id": 131}, {"id": 478, "country_code": "LT", "code": "LT-MR", "default_name": "Marijampolė<PERSON>", "country_id": 131}, {"id": 479, "country_code": "LT", "code": "LT-PN", "default_name": "Panevė<PERSON><PERSON> Apskrit<PERSON>", "country_id": 131}, {"id": 480, "country_code": "LT", "code": "LT-SA", "default_name": "Šiaulių Apskritis", "country_id": 131}, {"id": 481, "country_code": "LT", "code": "LT-TA", "default_name": "Taurag<PERSON><PERSON>", "country_id": 131}, {"id": 482, "country_code": "LT", "code": "LT-TE", "default_name": "Telšių Apskritis", "country_id": 131}, {"id": 483, "country_code": "LT", "code": "LT-UT", "default_name": "Utenos <PERSON>", "country_id": 131}, {"id": 484, "country_code": "LT", "code": "LT-VL", "default_name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 131}, {"id": 485, "country_code": "BR", "code": "AC", "default_name": "Acre", "country_id": 31}, {"id": 486, "country_code": "BR", "code": "AL", "default_name": "Alagoas", "country_id": 31}, {"id": 487, "country_code": "BR", "code": "AP", "default_name": "Amapá", "country_id": 31}, {"id": 488, "country_code": "BR", "code": "AM", "default_name": "Amazonas", "country_id": 31}, {"id": 489, "country_code": "BR", "code": "BA", "default_name": "Bahia", "country_id": 31}, {"id": 490, "country_code": "BR", "code": "CE", "default_name": "Ceará", "country_id": 31}, {"id": 491, "country_code": "BR", "code": "ES", "default_name": "Espírito Santo", "country_id": 31}, {"id": 492, "country_code": "BR", "code": "GO", "default_name": "Goiás", "country_id": 31}, {"id": 493, "country_code": "BR", "code": "MA", "default_name": "Maranhão", "country_id": 31}, {"id": 494, "country_code": "BR", "code": "MT", "default_name": "<PERSON><PERSON>", "country_id": 31}, {"id": 495, "country_code": "BR", "code": "MS", "default_name": "Mato Grosso do Sul", "country_id": 31}, {"id": 496, "country_code": "BR", "code": "MG", "default_name": "Minas Gerais", "country_id": 31}, {"id": 497, "country_code": "BR", "code": "PA", "default_name": "Pará", "country_id": 31}, {"id": 498, "country_code": "BR", "code": "PB", "default_name": "Paraíba", "country_id": 31}, {"id": 499, "country_code": "BR", "code": "PR", "default_name": "Paraná", "country_id": 31}, {"id": 500, "country_code": "BR", "code": "PE", "default_name": "Pernambuco", "country_id": 31}, {"id": 501, "country_code": "BR", "code": "PI", "default_name": "Piauí", "country_id": 31}, {"id": 502, "country_code": "BR", "code": "RJ", "default_name": "Rio de Janeiro", "country_id": 31}, {"id": 503, "country_code": "BR", "code": "RN", "default_name": "Rio Grande do Norte", "country_id": 31}, {"id": 504, "country_code": "BR", "code": "RS", "default_name": "Rio Grande do Sul", "country_id": 31}, {"id": 505, "country_code": "BR", "code": "RO", "default_name": "Rondônia", "country_id": 31}, {"id": 506, "country_code": "BR", "code": "RR", "default_name": "Roraima", "country_id": 31}, {"id": 507, "country_code": "BR", "code": "SC", "default_name": "Santa Catarina", "country_id": 31}, {"id": 508, "country_code": "BR", "code": "SP", "default_name": "São Paulo", "country_id": 31}, {"id": 509, "country_code": "BR", "code": "SE", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 31}, {"id": 510, "country_code": "BR", "code": "TO", "default_name": "Tocantins", "country_id": 31}, {"id": 511, "country_code": "BR", "code": "DF", "default_name": "Distrito Federal", "country_id": 31}, {"id": 512, "country_code": "HR", "code": "HR-01", "default_name": "Zagrebačka županija", "country_id": 59}, {"id": 513, "country_code": "HR", "code": "HR-02", "default_name": "Krapinsko-zagorska županija", "country_id": 59}, {"id": 514, "country_code": "HR", "code": "HR-03", "default_name": "Sisačko-moslavačka županija", "country_id": 59}, {"id": 515, "country_code": "HR", "code": "HR-04", "default_name": "Karlovačka županija", "country_id": 59}, {"id": 516, "country_code": "HR", "code": "HR-05", "default_name": "Varaždinska županija", "country_id": 59}, {"id": 517, "country_code": "HR", "code": "HR-06", "default_name": "Koprivničko-križevačka županija", "country_id": 59}, {"id": 518, "country_code": "HR", "code": "HR-07", "default_name": "Bjelovarsko-bilogorska županija", "country_id": 59}, {"id": 519, "country_code": "HR", "code": "HR-08", "default_name": "Primorsko-goranska županija", "country_id": 59}, {"id": 520, "country_code": "HR", "code": "HR-09", "default_name": "Ličko-senjska županija", "country_id": 59}, {"id": 521, "country_code": "HR", "code": "HR-10", "default_name": "Virovitičko-podravska županija", "country_id": 59}, {"id": 522, "country_code": "HR", "code": "HR-11", "default_name": "Požeško-slavonska županija", "country_id": 59}, {"id": 523, "country_code": "HR", "code": "HR-12", "default_name": "Brodsko-posavska županija", "country_id": 59}, {"id": 524, "country_code": "HR", "code": "HR-13", "default_name": "Zadarska županija", "country_id": 59}, {"id": 525, "country_code": "HR", "code": "HR-14", "default_name": "Osječko-baranjska županija", "country_id": 59}, {"id": 526, "country_code": "HR", "code": "HR-15", "default_name": "Šibensko-kninska županija", "country_id": 59}, {"id": 527, "country_code": "HR", "code": "HR-16", "default_name": "Vukovarsko-srijemska županija", "country_id": 59}, {"id": 528, "country_code": "HR", "code": "HR-17", "default_name": "Splitsko-dalmatinska županija", "country_id": 59}, {"id": 529, "country_code": "HR", "code": "HR-18", "default_name": "Istarska županija", "country_id": 59}, {"id": 530, "country_code": "HR", "code": "HR-19", "default_name": "Dubrovačko-neretvanska županija", "country_id": 59}, {"id": 531, "country_code": "HR", "code": "HR-20", "default_name": "Međimurska županija", "country_id": 59}, {"id": 532, "country_code": "HR", "code": "HR-21", "default_name": "Grad Zagreb", "country_id": 59}, {"id": 533, "country_code": "IN", "code": "AN", "default_name": "Andaman and Nicobar Islands", "country_id": 106}, {"id": 534, "country_code": "IN", "code": "AP", "default_name": "Andhra Pradesh", "country_id": 106}, {"id": 535, "country_code": "IN", "code": "AR", "default_name": "Arunachal Pradesh", "country_id": 106}, {"id": 536, "country_code": "IN", "code": "AS", "default_name": "Assam", "country_id": 106}, {"id": 537, "country_code": "IN", "code": "BR", "default_name": "Bihar", "country_id": 106}, {"id": 538, "country_code": "IN", "code": "CH", "default_name": "Chandigarh", "country_id": 106}, {"id": 539, "country_code": "IN", "code": "CT", "default_name": "Chhattisgarh", "country_id": 106}, {"id": 540, "country_code": "IN", "code": "DN", "default_name": "Dadra and Nagar Haveli", "country_id": 106}, {"id": 541, "country_code": "IN", "code": "DD", "default_name": "<PERSON><PERSON> and <PERSON><PERSON>", "country_id": 106}, {"id": 542, "country_code": "IN", "code": "DL", "default_name": "Delhi", "country_id": 106}, {"id": 543, "country_code": "IN", "code": "GA", "default_name": "Goa", "country_id": 106}, {"id": 544, "country_code": "IN", "code": "GJ", "default_name": "Gujarat", "country_id": 106}, {"id": 545, "country_code": "IN", "code": "HR", "default_name": "Haryana", "country_id": 106}, {"id": 546, "country_code": "IN", "code": "HP", "default_name": "Himachal Pradesh", "country_id": 106}, {"id": 547, "country_code": "IN", "code": "JK", "default_name": "Jammu and Kashmir", "country_id": 106}, {"id": 548, "country_code": "IN", "code": "JH", "default_name": "Jharkhand", "country_id": 106}, {"id": 549, "country_code": "IN", "code": "KA", "default_name": "Karnataka", "country_id": 106}, {"id": 550, "country_code": "IN", "code": "KL", "default_name": "Kerala", "country_id": 106}, {"id": 551, "country_code": "IN", "code": "LD", "default_name": "Lakshadweep", "country_id": 106}, {"id": 552, "country_code": "IN", "code": "MP", "default_name": "Madhya Pradesh", "country_id": 106}, {"id": 553, "country_code": "IN", "code": "MH", "default_name": "Maharashtra", "country_id": 106}, {"id": 554, "country_code": "IN", "code": "MN", "default_name": "Manipur", "country_id": 106}, {"id": 555, "country_code": "IN", "code": "ML", "default_name": "<PERSON><PERSON><PERSON>", "country_id": 106}, {"id": 556, "country_code": "IN", "code": "MZ", "default_name": "Mizoram", "country_id": 106}, {"id": 557, "country_code": "IN", "code": "NL", "default_name": "Nagaland", "country_id": 106}, {"id": 558, "country_code": "IN", "code": "OR", "default_name": "Odisha", "country_id": 106}, {"id": 559, "country_code": "IN", "code": "PY", "default_name": "Puducherry", "country_id": 106}, {"id": 560, "country_code": "IN", "code": "PB", "default_name": "Punjab", "country_id": 106}, {"id": 561, "country_code": "IN", "code": "RJ", "default_name": "Rajasthan", "country_id": 106}, {"id": 562, "country_code": "IN", "code": "SK", "default_name": "Sikkim", "country_id": 106}, {"id": 563, "country_code": "IN", "code": "TN", "default_name": "Tamil Nadu", "country_id": 106}, {"id": 564, "country_code": "IN", "code": "TG", "default_name": "Telangana", "country_id": 106}, {"id": 565, "country_code": "IN", "code": "TR", "default_name": "<PERSON>ura", "country_id": 106}, {"id": 566, "country_code": "IN", "code": "UP", "default_name": "Uttar Pradesh", "country_id": 106}, {"id": 567, "country_code": "IN", "code": "UT", "default_name": "Uttarakhand", "country_id": 106}, {"id": 568, "country_code": "IN", "code": "WB", "default_name": "West Bengal", "country_id": 106}, {"id": 569, "country_code": "PY", "code": "PY-16", "default_name": "Alto Paraguay", "country_id": 176}, {"id": 570, "country_code": "PY", "code": "PY-10", "default_name": "Alto Paraná", "country_id": 176}, {"id": 571, "country_code": "PY", "code": "PY-13", "default_name": "Amam<PERSON>", "country_id": 176}, {"id": 572, "country_code": "PY", "code": "PY-ASU", "default_name": "Asunción", "country_id": 176}, {"id": 573, "country_code": "PY", "code": "PY-19", "default_name": "Boquerón", "country_id": 176}, {"id": 574, "country_code": "PY", "code": "PY-5", "default_name": "Caaguazú", "country_id": 176}, {"id": 575, "country_code": "PY", "code": "PY-6", "default_name": "Caazapá", "country_id": 176}, {"id": 576, "country_code": "PY", "code": "PY-14", "default_name": "Canindeyú", "country_id": 176}, {"id": 577, "country_code": "PY", "code": "PY-11", "default_name": "Central", "country_id": 176}, {"id": 578, "country_code": "PY", "code": "PY-1", "default_name": "Concepción", "country_id": 176}, {"id": 579, "country_code": "PY", "code": "PY-3", "default_name": "Cordillera", "country_id": 176}, {"id": 580, "country_code": "PY", "code": "PY-4", "default_name": "Guairá", "country_id": 176}, {"id": 581, "country_code": "PY", "code": "PY-7", "default_name": "Itapúa", "country_id": 176}, {"id": 582, "country_code": "PY", "code": "PY-8", "default_name": "Misiones", "country_id": 176}, {"id": 583, "country_code": "PY", "code": "PY-9", "default_name": "Paraguarí", "country_id": 176}, {"id": 584, "country_code": "PY", "code": "PY-15", "default_name": "<PERSON><PERSON>", "country_id": 176}, {"id": 585, "country_code": "PY", "code": "PY-2", "default_name": "San Pedro", "country_id": 176}, {"id": 586, "country_code": "PY", "code": "PY-12", "default_name": "Ñeembucú", "country_id": 176}]