<?php

namespace Webkul\Customer\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Webkul\Customer\Contracts\DeliveryPoints as DeliveryPointsContract;
use Webkul\User\Models\AdminProxy;

class DeliveryPoints extends Model implements DeliveryPointsContract
{
    use HasFactory;

    protected $table = 'delivery_points';

    protected $fillable = [
        'point_title',
        'point_phone',
        'point_email',
        'point_status',
        'point_address',
    ];

    public function pointAdmin(): BelongsTo
    {

        return $this->belongsTo(AdminProxy::modelClass(), 'user_id', 'id');

    } /** end pointAdmin() **/
}   /** end class DeliveryPoints extends Model implements DeliveryPointsContract **/
