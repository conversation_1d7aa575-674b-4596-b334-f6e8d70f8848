<?php

namespace Webkul\Customer\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CustomerSubscriptionCompleted extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(public $email, public $token) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(core()->getSenderEmailDetails()['email'], core()->getSenderEmailDetails()['name'])
            ->to($this->email)
            ->subject(trans('mail.customer.newsletter.completed.subject', ['app_name' => config('app.name')]))
            ->view('shop::emails.customer.newsletter.completed', [
                'customer_email' => $this->email,
                'company_name'   => config('app.name'),
                'support_email'  => env('SUPPORT_MAIL', '<EMAIL>'),
                'support_phone'  => env('SUPPORT_PHONE', '+91 ************'),
                'token'          => $this->token,
            ]);
    }
}
