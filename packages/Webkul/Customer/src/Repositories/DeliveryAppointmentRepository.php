<?php

namespace Webkul\Customer\Repositories;

use Carbon\Carbon;
use Webkul\Core\Eloquent\Repository;

class DeliveryAppointmentRepository extends Repository
{
    public $paginate = 10;

    /**
     * Specify model class name.
     */
    public function model(): string
    {

        return 'Webkul\Customer\Contracts\DeliveryAppointments';

    }

    /** end model(): string **/
    public function getQueryData($request = null): mixed
    {

        $data = $this->model
            ->with('deliveryPoint', 'user')
            ->orderBy('id', 'DESC')
            ->paginate($this->paginate);

        return (object) [
            'data'        => $data->map(function ($item) {
                return [
                    'id'            => $item?->id,
                    'title'         => $item?->deliveryPoint?->point_title,
                    'status'        => $this?->getStatus($item?->point_status),
                    'phone'         => $item?->point_phone,
                    'email'         => $item?->point_email,
                    'userName'      => $item?->user?->name,
                    'amount'        => $item->point_amount,
                    'createdAt'     => Carbon::parse($item?->point_date)->format('d.m.Y H:i:s'),
                ];
            }),
            'total'       => $data->total(),
            'count'       => $data->count(),
            'lastPage'    => $data->lastPage(),
            'perPage'     => $data->perPage(),
            'currentPage' => $data->currentPage(),
            'links'       => $data,
        ];

    }

    public function getQueryCreated($request = null): mixed
    {

        $created = $this->model->create([
            'point_status' => true,
            'user_id'      => $request->user()->id,
            'point_phone'  => $this->phoneFilter($request->pointPhone),
            'point_email'  => $request->pointEmail,
            'point_amount' => $request->pointAmount,
            'point_id'     => $request->deliveryPoint,
            'point_date'   => Carbon::parse($request->deliveryDateTime)->format('d-m-Y H:i:s'),
        ]);

        return $created;

    } /** end getQueryCreated( $request = null ): mixed **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /** ------------------------------ Repository Model Private Function(s) ---------------------------------------- **/
    /** ------------------------------------------------------------------------------------------------------------ **/

    /**
     * @return string[]|void
     */
    private function getStatus($status = null)
    {

        switch ($status) {
            case 0:
                return [
                    'text'  => __('admin::app.delivery-point.delivery-point-status-text.close'),
                    'class' => 'danger',
                ];
                break;
            case 1:
                return [
                    'text'  => __('admin::app.delivery-point.delivery-point-status-text.active'),
                    'class' => 'success',
                ];
                break;
            case 2:
                return [
                    'text'  => __('admin::app.delivery-point.delivery-point-status-text.Waiting'),
                    'class' => 'warning',
                ];
                break;
        }

    }

    /** end getStatus( $status = null ) **/
    private function phoneFilter($phone)
    {

        $phone = str_replace(['(', ')', ' '], [''], $phone);

        return $phone;

    } /** end phoneFilter( $phone ) **/
}   /** end class DeliveryPointRepository extends Repository **/
