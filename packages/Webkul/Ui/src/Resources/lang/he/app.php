<?php

return [
    'datagrid' => [
        'actions' => 'Actions',
        'id'      => 'Index columns have value greater than zero only',

        'massaction' => [
            'mass-delete-confirm'     => 'Do you really want to delete these selected :resource?',
            'mass-update-status'      => 'Do you really want to update status of these selected :resource?',
            'delete'                  => 'Do you really want to perform this action?',
            'edit'                    => 'Do you really want to edit this :resource?',
            'delete-category-product' => 'The selected categories contains products. Performing this action will remove the related products. Do you really want to perform this action?',
        ],

        'error' => [
            'multiple-sort-keys-error'   => 'Fatal Error! Multiple sort keys found, please resolve the URL manually',
            'multiple-search-keys-error' => 'Multiple search keys found, please resolve the URL manually',
            'mapped-keys-error'          => 'Mapped key not found. Make sure you have given valid options.',
        ],

        'zero-index'            => 'Index columns can have values greater than zero only',
        'no-records'            => 'No Records Found',
        'filter-fields-missing' => 'Some of the required field is null, please check column, condition and value properly',
        'filter-exists'         => 'Filter value already exists.',
        'click_on_action'       => 'Do you really want to perform this action?',
        'search'                => 'Search Here...',
        'search-title'          => 'Search',
        'channel'               => 'Channel',
        'locale'                => 'Locale',
        'customer-group'        => 'Customer Group',
        'filter'                => 'Filter',
        'column'                => 'Select Column',
        'condition'             => 'Select Condition',
        'contains'              => 'Contains',
        'ncontains'             => 'Does not contains',
        'equals'                => 'Is Equals to',
        'nequals'               => 'Is Not equals to',
        'greater'               => 'Greater than',
        'less'                  => 'Less than',
        'greatere'              => 'Greater than equals to',
        'lesse'                 => 'Less than equals to',
        'value'                 => 'Select Value',
        'true'                  => 'True / Active',
        'false'                 => 'False / Inactive',
        'between'               => 'Is between',
        'apply'                 => 'Apply',
        'items-per-page'        => 'Items Per Page',
        'value-here'            => 'Value here',
        'numeric-value-here'    => 'Numeric Value here',
        'submit'                => 'Submit',
        'edit'                  => 'Edit',
        'delete'                => 'Delete',
        'view'                  => 'View',
        'active'                => 'Active',
        'inactive'              => 'Inactive',
        'all-channels'          => 'All Channels',
        'all-locales'           => 'All Locales',
        'all-customer-groups'   => 'All Customer groups',
        'records-found'         => 'Record(s) found',
        'clear-all'             => 'Clear All',
    ],
];
