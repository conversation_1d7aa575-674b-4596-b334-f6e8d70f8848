<?php

namespace Webkul\Inventory\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InventoryTableSeeder extends Seeder
{
    public function run()
    {
        DB::table('inventory_sources')->delete();

        DB::table('inventory_sources')->insert([
            'id'             => 1,
            'code'           => 'default',
            'name'           => 'Default',
            'contact_name'   => 'THORNE BİLİŞİM',
            'contact_email'  => '<EMAIL>',
            'contact_number' => '+90 (532) 282 91 18',
            'status'         => 1,
            'country'        => 'TR',
            'state'          => 'Kadıköy',
            'street'         => 'Acıbadem, Gömeç Sk. No:37/4, Kat:1',
            'city'           => 'Istanbul',
            'postcode'       => '34718',
        ]);
    }
}
