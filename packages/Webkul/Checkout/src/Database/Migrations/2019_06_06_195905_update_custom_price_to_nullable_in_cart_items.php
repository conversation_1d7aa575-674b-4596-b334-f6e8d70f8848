<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCustomPriceToNullableInCartItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropColumn('custom_price');
        });

        Schema::table('cart_items', function (Blueprint $table) {
            $table->decimal('custom_price', 12, 4)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropColumn('custom_price');
        });

        Schema::table('cart_items', function (Blueprint $table) {
            $table->decimal('custom_price', 12, 4)->default(0);
        });
    }
}
