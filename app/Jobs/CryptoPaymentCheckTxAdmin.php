<?php

namespace App\Jobs;

use App\Mail\CryptoPaymentCheckedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Webkul\Sales\Models\Order;

class CryptoPaymentCheckTxAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Order $order;

    private $sender;

    public function __construct(
        $order
    ) {
        $this->order  = $order;
        $this->sender = core()->getSenderEmailDetails()['email'];
    }

    public function handle()
    {
        Mail::to($this->sender)->send(new CryptoPaymentCheckedNotification($this->order));
    }
}
