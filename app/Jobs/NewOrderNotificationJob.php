<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Webkul\Admin\Mail\NewOrderNotification;
use Webkul\Sales\Models\Order;

class NewOrderNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Order $order;

    private string $recipientType;

    public function __construct(Order $order, string $recipientType)
    {
        $this->order         = $order;
        $this->recipientType = $recipientType;
    }

    public function handle()
    {
        Mail::send(new NewOrderNotification($this->order, $this->recipientType));
    }

    public function failed(Exception $exception)
    {
        Log::error("NewOrderNotificationJob:{$this->recipientType}:failed - email failed", [
            'order_id'       => $this->order->id,
            'recipient_type' => $this->recipientType,
            'exception'      => $exception->getMessage(),
        ]);
    }
}
