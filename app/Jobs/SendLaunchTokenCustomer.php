<?php

namespace App\Jobs;

use App\Mail\LaunchTokenCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendLaunchTokenCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    private $lockToken;

    private $email;

    public function __construct(
        $order,
        $lockToken
    ) {
        $this->order     = $order;
        $this->lockToken = $lockToken;
        $this->email     = $order->customer_email;
    }

    public function handle()
    {
        Mail::to($this->email)->send(new LaunchTokenCompletedNotification($this->lockToken, $this->order));
    }
}
