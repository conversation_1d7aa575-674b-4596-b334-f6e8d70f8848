<?php

namespace App\Jobs;

use App\Mail\ContactRequestRegistrationNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ContactRequestRegistration implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 6;

    public int $backoff = 15;

    public int $timeout = 300;

    public int $maxAttempts = 6;

    public $contactRequest;

    public function __construct($contactRequest)
    {
        $this->contactRequest = $contactRequest;
    }

    public function handle()
    {
        try {
            Mail::to($this->contactRequest->email)->send(new ContactRequestRegistrationNotification($this->contactRequest));
        } catch (\Exception $error) {
            Log::channel('contact_request_registration')
                ->error('Error sending contact request registration email', [
                    'error'           => $error->getMessage(),
                    'contact_request' => $this->contactRequest,
                ]);
        }
    }
}
