<?php

namespace App\Jobs;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CryptoPaymentCheckTx implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 6;

    public int $backoff = 15;

    public int $timeout = 300;

    public int $maxAttempts = 6;

    private $apiService;

    private $order;

    private string $status;

    public function __construct($order)
    {
        $this->apiService = (new ApiClientAuth10Service);
        $this->order      = $order;
    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            if ($this->order->status === 'completed') {
                return;
            }
            if ($this->order->status === 'processing') {
                dispatch(new CryptoPaymentCheckTxCustomer($this->order))->onQueue('launchpad');
                dispatch(new SendLaunchToken($this->order))->onQueue('launchpad');

                return;
            }

            $orderTxData               = json_decode($this->order?->tx_data, true);
            $cryptoWithdrawalCheckData = [
                'isSuccessful' => $orderTxData['isSuccessful'] ?? false,
                'txId'         => $orderTxData['txId']         ?? '',
                'errorMessage' => $orderTxData['errorMessage'] ?? '',
                'txnDate'      => $orderTxData['txnDate']      ?? '',
                'userId'       => $orderTxData['userId']       ?? '',
                'tenantId'     => $orderTxData['tenantId']     ?? '',
            ];

            $cryptoWithdrawalCheckResponse = $this->apiService->baseUrl2('/tx/check')
                ->baseMethod('post')
                ->baseClient($cryptoWithdrawalCheckData);

            if ($cryptoWithdrawalCheckResponse->successful() && $cryptoWithdrawalCheckResponse?->json('status') === 1) {
                dispatch(new CryptoPaymentCheckTxCustomer($this->order))->onQueue('launchpad');
                dispatch(new SendLaunchToken($this->order))->onQueue('launchpad');
            } else {
                $this->status = 'Step3 : Check Launhpad Payment Process | Ödeme doğrulanamadı.';
            }
        } catch (Exception $e) {
            $this->status = 'Step3 : Check Launhpad Payment Process | Ödeme doğrulama süreci başarısız oldu.';
        }

        if ($this->attempts() == $this->maxAttempts) {
            dispatch(new CryptoPaymentCheckTxAdmin($this->order))->onQueue('launchpad');
        }

        if ($this->status) {
            throw new Exception($this->status);
        }
    }

    public function failed($exception)
    {
        Log::channel('endpoint')
            ->error('Job: CryptoPaymentCheckTx failed', [
                'message' => $exception->getMessage(),
                'order'   => $this->order,
                'status'  => $this->status ?? '',
            ]);
    }
}
