<?php

namespace App\Jobs;

use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use onesignal\client\api\DefaultApi;
use onesignal\client\Configuration;
use Webkul\Notification\Models\Customers\CustomerNotifications;
use Webkul\Notification\Models\PushNotificationLogItems;

class PushNotificationChecked implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $pushNotification;

    public function __construct(object $pushNotification)
    {

        $this->pushNotification = $pushNotification;

    } /** end  __construct( object $pushNotification ) **/

    /**
     * @return void
     */
    public function index(?array $pushNotification = null) {}

    /** end index( array $pushNotification = null ) **/
    public function handle(): void
    {

        try {
            $config = Configuration::getDefaultConfiguration()
                ->setAppKeyToken(config('onesignal-config.app_key_token'))
                ->setUserKeyToken(config('onesignal-config.user_key_token'));

            $apiInstance = new DefaultApi(
                new Client,
                $config
            );

            $notification = createNotification(
                $this?->pushNotification?->notification_title,
                $this?->pushNotification?->notification_content,
                $this?->pushNotification?->notification_large_icon,
                $this?->pushNotification?->customer_group,
                $this?->pushNotification?->app_data,
                $this?->pushNotification?->notification_scheduling,
                $this?->pushNotification?->notification_scheduling_boolean
            );

            $result = $apiInstance->createNotification($notification);

            if ($this?->pushNotification?->customer_group[0] == 'all') {
                CustomerNotifications::query()->update(['onesignal_status' => 1]);
            } else {
                CustomerNotifications::whereIn('customer_id', $this?->pushNotification?->customer_group)
                    ->update(['onesignal_status' => 1]);
            }

            if ($result->getErrors()) {
                CustomerNotifications::whereIn('onesignal_player_id', $result->getErrors()->getInvalidPlayerIds())
                    ->update([
                        'onesignal_status' => 2,
                    ]);
            }

            if ($this?->pushNotification?->customer_group[0] == 'all') {
                $pushNotificationSend = CustomerNotifications::where(['onesignal_status' => 1])
                    ->get();
            } else {
                $pushNotificationSend = CustomerNotifications::where(['onesignal_status' => 1])
                    ->whereIn('customer_id', $this?->pushNotification?->customer_group)
                    ->get();
            }

            foreach ($pushNotificationSend as $customer) {
                PushNotificationLogItems::create([
                    'push_notification_status' => 1,
                    'customer_id'              => $customer->customer_id,
                    'push_notification_id'     => $this?->pushNotification?->id,
                ]);
            }

            Log::channel('pushlogs')
                ->info('Push Notification', [
                    'response' => response()->json($result),
                ]);

        } catch (\Exception $e) {
            Log::channel('pushlogs')
                ->info('Push Notification', [
                    'Error' => $e->getMessage(),
                ]);
        }

    } /** end handle(  ): void **/
}   /** end class PushNotificationChecked implements ShouldQueue **/
