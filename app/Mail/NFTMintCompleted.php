<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Webkul\Sales\Models\OrderItem;

class NFTMintCompleted extends Mailable
{
    use Queueable, SerializesModels;

    private OrderItem $orderItem;

    private string $txh;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(OrderItem $orderItem, string $txh)
    {
        $this->orderItem = $orderItem;
        $this->txh       = $txh;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>')
            ->markdown('emails.orders.nftmintcompleted', [
                'url'       => '#',
                'orderItem' => $this->orderItem,
                'txh'       => $this->txh,
            ]);
    }
}
