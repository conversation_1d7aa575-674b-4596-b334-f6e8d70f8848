<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LaunchTokenErrorNotification extends Mailable
{
    use Queueable, SerializesModels;

    private $error;

    private $lockToken;

    private $order;

    private $email;

    public function __construct($error, $lockToken, $order)
    {
        $this->error     = $error;
        $this->lockToken = $lockToken;
        $this->order     = $order;
        $this->email     = $order->customer_email;
    }

    public function build()
    {
        $sender = core()->getSenderEmailDetails();

        return $this->from(data_get($sender, 'email'), data_get($sender, 'name'))
            ->to(data_get($sender, 'email'), data_get($sender, 'name'))
            ->subject(trans('mail.sales.order.token-error.subject', ['order_id' => $this->order->order_id]))
            ->view('shop::emails.sales.token-error')
            ->with([
                'order'          => $this->order,
                'wallet_address' => $this->order->customer->wallet,
                'error_message'  => $this->error,
                'amount'         => $this->order->total_qty_ordered,
            ]);
    }
}
