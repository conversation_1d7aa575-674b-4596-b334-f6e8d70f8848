<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Webkul\Customer\Models\Customer;

class ContactRequestRegistrationNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $registrationLink;

    public function __construct(public $contactRequest)
    {
        $this->registrationLink = urldecode(route('customer.register.index', ['hash' => $this->contactRequest->createHash]));
        $customer               = Customer::where('email', $this->contactRequest->email)->first();
        $this->locale           = $customer?->locale ?? app()->getLocale();
    }

    public function build()
    {
        $sender = core()->getSenderEmailDetails();

        return $this->from(data_get($sender, 'email'), data_get($sender, 'name'))
            ->to($this->contactRequest->email, $this->contactRequest->full_name)
            ->subject(trans('mail.customer.invitation.subject'))
            ->view('shop::emails.customer.requests.invitation', [
                'contactRequest'   => $this->contactRequest,
                'registrationLink' => $this->registrationLink,
                'customer_name'    => $this->contactRequest->full_name,
            ]);
    }
}
