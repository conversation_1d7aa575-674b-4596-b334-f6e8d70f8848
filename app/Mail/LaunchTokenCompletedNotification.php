<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LaunchTokenCompletedNotification extends Mailable
{
    use Queueable, SerializesModels;

    private $lockToken;

    private $order;

    private $email;

    public function __construct($lockToken, $order)
    {
        $this->lockToken = $lockToken;
        $this->order     = $order;
        $this->email     = $order->customer_email;
    }

    public function build()
    {
        $sender = core()->getSenderEmailDetails();

        return $this->from(data_get($sender, 'email'), data_get($sender, 'name'))
            ->to(data_get($sender, 'email'), data_get($sender, 'name'))
            ->subject(trans('mail.sales.order.token-completed.subject', ['order_id' => $this->order->order_id]))
            ->view('shop::emails.sales.token-completed')
            ->with([
                'customer_name'  => $this->order->customer_full_name,
                'order'          => $this->order,
                'wallet_address' => $this->order->customer->wallet,
                'amount'         => $this->order->total_qty_ordered,
            ]);
    }
}
