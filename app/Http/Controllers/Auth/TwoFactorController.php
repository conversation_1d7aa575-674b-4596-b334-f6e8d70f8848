<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Google2faRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use PragmaRX\Google2FA\Google2FA;

class TwoFactorController extends Controller
{
    public function show(Request $request)
    {
        $key = Google2FA::generateSecretKey(32);

        return view('auth.2fa')->with([
            'key' => $key,
        ]);
    }

    public function active(Google2faRequest $request)
    {
        $request->validated();

        $user = auth()->user();

        $google2fa = new Google2FA;
        // $otp_secret = $user->google2fa_secret;
        if (! $google2fa->verifyKey($request->key, $request->one_time_password)) {
            /*throw ValidationException::withMessages([
                'one_time_password' => [__('The one time password is invalid.')],
            ]);*/
            return response()->json([
                'status'            => false,
                'one_time_password' => __('The one time password is invalid.'),
            ]);
        }

        $user->update([
            'google2fa_secret'  => $request->key,
            'google2fa_enabled' => 1,
        ]);

        return response()->json([
            'status'           => 'success',
            'google2faEnabled' => 1,
        ]);

    }

    public function verify(Request $request)
    {
        $request->validate([
            'one_time_password' => 'required|string',
        ]);
        // $user_id = $request->session()->get('2fa:user:id');
        // $remember = $request->session()->get('2fa:auth:remember', false);
        // $attempt = $request->session()->get('2fa:auth:attempt', false);

        // if (!$user_id || !$attempt) {
        //   return redirect()->route('login');
        // }

        $user = auth()->user();

        if (! $user || ! $user->google2fa_enabled) {
            return redirect()->back();
        }

        $google2fa  = new Google2FA;
        $otp_secret = $user->google2fa_secret;

        if (! $google2fa->verifyKey($otp_secret, $request->one_time_password)) {
            /*throw ValidationException::withMessages([
                'one_time_password' => [__('The one time password is invalid.')],
            ]);*/
            return response()->json([
                'status'            => false,
                'one_time_password' => __('The one time password is invalid.'),
            ]);
        }

        $guard       = config('auth.defaults.guard');
        $credentials = [$user->getAuthIdentifierName() => $user->getAuthIdentifier(), 'password' => $user->getAuthPassword()];

        if ($remember) {
            $guard = config('auth.defaults.remember_me_guard', $guard);
        }

        if ($attempt) {
            $guard = config('auth.defaults.attempt_guard', $guard);
        }

        if (Auth::guard($guard)->attempt($credentials, $remember)) {
            $request->session()->remove('2fa:user:id');
            $request->session()->remove('2fa:auth:remember');
            $request->session()->remove('2fa:auth:attempt');

            return redirect()->intended('/');
        }

        return redirect()->route('customer.profile.index')->withErrors([
            'password' => __('The provided credentials are incorrect.'),
        ]);
    }
}
