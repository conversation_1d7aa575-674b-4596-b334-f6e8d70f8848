<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Maintenance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $inactiveRouteNames = [
            'shop.checkout.cart.index',
            'customer.account.index',
            'customer.advantages.index',
            'customer.wishlist.index',
            'customer.addresses.index',
            'customer.address.create',
        ];
        $inactiveRoutePaths = [
            //
        ];

        if (in_array($request->route()->getName(), $inactiveRouteNames) || in_array($request->path(), $inactiveRoutePaths)) {
            return redirect()->route('shop.home.index');
        }

        return $next($request);
    }
}
