<?php

/**
 * Oluşturan: <PERSON><PERSON><PERSON> Ali <PERSON>K
 * Oluşturma: 22.12.2023
 */

namespace App\Services\OAuth;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiClientAuth10Service
{
    public $baseURL = '';

    public $method  = '';

    public $timeout = null;

    public function baseUrl($baseURL = null)
    {

        $this->baseURL = config('app.web3_ep').$baseURL;

        return $this;

    }

    public function baseUrl2($baseURL = null)
    {

        $this->baseURL = config('app.web3_ep-2').$baseURL;

        return $this;

    }

    public function baseMethod($method = null)
    {

        $this->method = $method;

        return $this;

    }

    public function baseTimeout($timeout = null)
    {

        $this->timeout = $timeout;

        return $this;

    }

    public function baseClient($bodyData = null)
    {

        if ($this->method == 'post') {
            if ($this->timeout == null) {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->getAuthorizationSignature($bodyData),
                ])->{$this->method}($this->baseURL, $bodyData);
            } else {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->getAuthorizationSignature($bodyData),
                ])->timeout((int) $this->timeout)->{$this->method}($this->baseURL, $bodyData);
            }
        } elseif ($this->method == 'get') {
            if ($this->timeout == null) {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->getAuthorizationSignature($bodyData),
                ])->{$this->method}($this->baseURL);
            } else {
                $data = Http::withHeaders([
                    'Content-Type'  => 'application/json',
                    'Authorization' => $this->getAuthorizationSignature($bodyData),
                ])->timeout((int) $this->timeout)->{$this->method}($this->baseURL);
            }
        }

        $requestData = request();
        Log::channel('endpoint')
            ->info('requestLog', [
                'data'     => ($bodyData),
                'header'   => $data->headers(),
                'response' => $data->json(),
                'body'     => $data->body(),
                'endpoint' => $this->baseURL,
                'panelUrl' => $requestData->url(),
                'username' => isset($requestData) ? 'CronJobs' : ($requestData->user()->first_name.' '.$requestData->user()->last_name),
            ]);

        return $data;

    }

    public function getHash($message)
    {

        $privateKeyPath = base_path('private-key.pem');
        $privateKey     = openssl_pkey_get_private(file_get_contents($privateKeyPath));
        if (openssl_sign($message, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {

        } else {

        }

        return $signature;

    }

    public function randNonce($length)
    {

        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce      = '';
        $max        = strlen($characters) - 1;
        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $max)];
        }

        return $nonce;

    }

    public function getAuthorizationHeader($authParams, $signature, $r)
    {

        $h                             = [];
        $authParams['oauth_signature'] = $signature;

        foreach ($authParams as $k => $v) {
            if (empty($h)) {
                $h = ['OAuth '];
            } else {
                $h[] = ', ';
            }
            $h[] = $k;
            $h[] = '="';
            $h[] = $v;
            $h[] = '"';
        }

        return $r->header('Authorization', implode('', $h));

    }

    public function getAuthorizationSignature($bodyData = null)
    {

        $endPointUrl   = explode('?', $this->baseURL);
        $endPointUrl   = $endPointUrl[0];
        $endPointQuery = parse_url($this->baseURL);
        $jsonData      = $bodyData != null ? json_encode($bodyData) : '';

        $hash        = hash('sha256', $jsonData, true);
        $base64Data  = base64_encode($hash);
        $headerArray = [
            'oauth_consumer_key'     => config('app.oauth_consumer_key'),
            'oauth_signature_method' => 'RSA-SHA256',
            'oauth_timestamp'        => strval(time()),
            'oauth_nonce'            => $this->randNonce(11),
            'oauth_version'          => '1.0',
            'oauth_body_hash'        => $base64Data,
        ];
        foreach ($headerArray as $key => $value) {
            if ($key == 'oauth_signature') {
                continue;
            }
            $paramArray[] = urlencode($key).'='.urlencode($value);
        }
        if (isset($endPointQuery['query'])) {
            parse_str($endPointQuery['query'], $output);
            foreach ($output as $keys => $values) {
                $paramArray[] = urlencode($keys).'='.urlencode($values);
            }
        }
        sort($paramArray);
        $paramStr   = implode('&', $paramArray);
        $baseString = sprintf('%s&%s&%s', strtoupper($this->method), urlencode($endPointUrl), urlencode($paramStr));
        $signature  = base64_encode($this->getHash($baseString));

        return $this->getAuthorizationHeader($headerArray, $signature, request());

    }
}
