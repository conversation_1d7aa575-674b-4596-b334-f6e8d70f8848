<?php

namespace App\Services;

use App\Jobs\ProcessNotificationWebhook;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification as BaseNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification as NotificationFacade;
use Illuminate\Support\Facades\Password;
use InvalidArgumentException;
use ReflectionClass;
use Webkul\Customer\Models\Customer;

class NotificationWebhookService
{
    public function processWebhook(array $payload): array
    {
        $alias = $payload['class'];
        $map   = config('notification-webhook.class_aliases', []);

        if (! isset($map[$alias])) {
            throw new InvalidArgumentException("Unknown webhook class alias [{$alias}].");
        }

        dispatch(new ProcessNotificationWebhook($payload['class'], $payload['parameters']))->onQueue('launchpad');

        return [
            'status'  => 'queued',
            'message' => "Webhook [{$alias}] dispatched to queue.",
        ];
    }

    public function sendByAlias(string $classAlias, array $parameters): void
    {
        $map = config('notification-webhook.class_aliases', []);
        if (! isset($map[$classAlias])) {
            throw new InvalidArgumentException("Unknown webhook class alias [{$classAlias}].");
        }

        $class = $map[$classAlias];

        if ($classAlias === 'UpdatePassword') {
            $this->handleCustomerUpdatePasswordMail($class, $parameters);

            return;
        }

        if ($classAlias === 'ResetPassword') {
            $this->handleResetPasswordNotification($class, $parameters);

            return;
        }

        if (is_subclass_of($class, Mailable::class)) {
            $this->sendMailable($class, $parameters);

            return;
        }

        if (is_subclass_of($class, BaseNotification::class)) {
            $this->sendNotification($class, $parameters);

            return;
        }

        throw new InvalidArgumentException(
            "Class [{$class}] is neither a Mailable nor a Notification."
        );
    }

    protected function handleCustomerUpdatePasswordMail($mailClass, array $parameters): void
    {
        $customer = Customer::where('email', $parameters['email'])->first() ?? throw new InvalidArgumentException("Customer not found for email {$parameters['email']}.");

        $this->sendMailable($mailClass, ['customer' => $customer]);
    }

    protected function handleResetPasswordNotification(string $notificationClass, array $parameters): void
    {
        $email = $parameters['email']
            ?? throw new InvalidArgumentException("Missing 'email' for {$notificationClass}.");

        $customer = Customer::where('email', $email)->first()
            ?? throw new InvalidArgumentException("Customer not found for email {$email}.");

        $token = Password::broker('customers')->createToken($customer);

        ResetPassword::createUrlUsing(function ($notifiable, $token) {
            return route('customer.reset-password.create', $token);
        });

        $notification = new $notificationClass($token, $customer);

        $customer->notify($notification);
    }

    protected function sendMailable(string $class, array $parameters): void
    {
        $reflection  = new ReflectionClass($class);
        $constructor = $reflection->getConstructor();

        if (! $constructor) {
            $mailable = $reflection->newInstance();
        } else {
            $arguments = [];
            foreach ($constructor->getParameters() as $parameter) {
                $name = $parameter->getName();
                if (array_key_exists($name, $parameters)) {
                    $arguments[] = $parameters[$name];
                } elseif ($parameter->isDefaultValueAvailable()) {
                    $arguments[] = $parameter->getDefaultValue();
                } else {
                    throw new InvalidArgumentException("Missing required parameter [{$name}] for {$class}.");
                }
            }
            $mailable = $reflection->newInstanceArgs($arguments);
        }

        Mail::queue($mailable);
    }

    protected function sendNotification(string $class, ?array $parameters): void
    {
        $email      = $parameters['email'] ?? throw new InvalidArgumentException("Missing 'email' for Notification [{$class}].");
        $notifiable = Customer::where('email', $email)->first()
            ?? throw new InvalidArgumentException("Customer not found for email {$email}.");

        $notification = new $class(...$parameters ?? []);

        NotificationFacade::send($notifiable, $notification);
    }
}
