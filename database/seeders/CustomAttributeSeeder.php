<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Event;
use Webkul\Attribute\Models\Attribute;

class CustomAttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $now        = Carbon::now();
        $attributes = [
            [
                'code'                          => 'chain_id',
                'admin_name'                    => 'Chain ID',
                'name'                          => 'Chain ID',
                'type'                          => 'select',
                'validation'                    => null,
                'position'                      => '1',
                'is_required'                   => '0',
                'is_unique'                     => '0',
                'value_per_locale'              => '0',
                'value_per_channel'             => '0',
                'is_filterable'                 => '1',
                'is_configurable'               => '1',
                'is_user_defined'               => '1',
                'is_visible_on_front'           => '0',
                'use_in_flat'                   => '1',
                'show_in_all_products_filter'   => '1',
                'created_at'                    => $now,
                'updated_at'                    => $now,
                'is_comparable'                 => '1',
                'options'                       => [
                    [
                        'admin_name' => 'terramirum-testnet',
                        'sort_order' => '1',
                    ],
                    [
                        'admin_name' => 'sepolia-testnet',
                        'sort_order' => '2',
                    ],
                    [
                        'admin_name' => 'bsc-testnet',
                        'sort_order' => '3',
                    ],
                ],
            ],
        ];

        collect($attributes)->each(function ($properties) {
            $attribute = Attribute::create(collect($properties)->except(['options'])->toArray());

            Event::dispatch('catalog.attribute.create.before');

            Event::dispatch('catalog.attribute.create.after', $attribute);

            if (isset($properties['options']) && count($properties['options'])) {
                collect($properties['options'])->each(function ($optionProperties) use ($attribute) {
                    $attribute->options()->create($optionProperties);
                });
            }
        });
    }
}
