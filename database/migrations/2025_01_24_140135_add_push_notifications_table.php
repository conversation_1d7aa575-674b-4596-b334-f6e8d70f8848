<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::table('push_notifications', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('notification_large_icon');
            $table->dateTime('notification_scheduling', precision: 0);
            $table->json('app_data');
        });

    } /** end up() **/

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::table('push_notifications', function (Blueprint $table) {
            $table->dropColumn('user_id');
            $table->dropColumn('app_data');
            $table->dropColumn('notification_large_icon');
            $table->dropColumn('notification_scheduling');
        });

    } /** end down() **/
};  /** end return new class extends Migration **/
