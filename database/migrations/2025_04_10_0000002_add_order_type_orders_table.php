<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void {

        Schema::table('orders', function (Blueprint $table) {
            $table->string('order_type')->nullable();
        });

    } /** end up() **/

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {

        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('order_type');
        });

    } /** end down() **/

};
