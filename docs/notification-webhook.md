# Notification Webhook


---

## 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (`config/notification-webhook.php`)

```php
return [
    // Alias → Sınıf eşlemesi
    'class_aliases'   => [
        'Registration'   => Webkul\Customer\Mail\RegistrationEmail::class,
        'ResetPassword'  => Webkul\Customer\Notifications\CustomerResetPassword::class,
        'UpdatePassword' => Webkul\Customer\Notifications\CustomerUpdatePassword::class,
    ],

    // Her alias için geçerlilik kuralları (validator)
    'class_validators' => [
        'Registration' => [
            'parameters.data.email'      => 'required|email|exists:customers,email',
            'parameters.data.first_name' => 'required|string',
            'parameters.data.last_name'  => 'required|string',
            'parameters.mailType'        => 'required|string|in:customer,admin',
        ],
        'ResetPassword' => [
            'parameters.email' => 'required|email|exists:customers,email',
        ],
        'UpdatePassword' => [
            'parameters.email' => 'required|email|exists:customers,email',
        ],
    ],

    'webhook_token' => env('NOTIFICATION_WEBHOOK_TOKEN'),
];
```

* `class_aliases`: <PERSON><PERSON>n `class` alias’ını, işlenecek Mailable veya Notification sınıfıyla eşler.
* `class_validators`: HTTP isteğinde `parameters` içindeki alanları doğrular.
* `webhook_token`: `X-Webhook-Token` başlığı bu değerle kontrol edilir.

---

## 2. API Uç Noktası

### `routes/api.php`

```
Route::post('webhooks/notification', [NotificationWebhookController::class, 'handle'])->name('webhooks.notification');
```

```
POST /api/webhooks/notification
```

* **Headers**:

    * `Content-Type: application/json`
    * `X-Webhook-Token: <NOTIFICATION_WEBHOOK_TOKEN>`

### İstek Gövdesi

| Alan         | Tip    | Açıklama                                                   |
| ------------ | ------ | ---------------------------------------------------------- |
| `class`      | string | `class_aliases` içinde tanımlı alias (örn. `Registration`) |
| `parameters` | object | Alias’a özel veri (örn. `data`, `email`)                   |

#### Örnek: Registration

```http
POST /api/webhooks/notification
X-Webhook-Token: sha1-değeriniz
Content-Type: application/json

{
  "class": "Registration",
  "parameters": {
    "data": {
      "email": "<EMAIL>",
      "first_name": "Deniz",
      "last_name": "Öztürk"
    },
    "mailType": "customer"
  }
}
```

#### Örnek: ResetPassword

```json
{
  "class": "ResetPassword",
  "parameters": {
    "email": "<EMAIL>"
  }
}
```

#### Örnek: UpdatePassword

```json
{
  "class": "UpdatePassword",
  "parameters": {
    "email": "<EMAIL>"
  }
}
```

---

## 3. Controller (`NotificationWebhookController`)

* **Namespace**: `App\Http\Controllers\Webhooks`
* **Method**: `handle(Request $request)`

    1. `Log::channel('notification-webhook')->info(...)` ile başlık ve gövde log’lanır.
    2. `X-Webhook-Token` header’ı, config içindeki `webhook_token` ile eşleşirse devam edilir; değilse **401 Unauthorized** döner.
    3. `class` ve `parameters` alanları temel validasyondan geçirilir.
    4. Alias’a özel `class_validators` kuralları çalıştırılır; hata varsa **422 Unprocessable Entity** döner.
    5. `NotificationWebhookService::processWebhook()` çağrılır, hata olursa log’lanır ve **422** ile döner; başarılıysa **202 Accepted** ile sonuç JSON’u döndürülür.

---

## 4. Service (`NotificationWebhookService`)

* **Namespace**: `App\Services`

### 4.1 `processWebhook(array $payload): array`

1. Alias’ın `config('notification-webhook.class_aliases')` içinde varlığını kontrol eder.
2. `ProcessNotificationWebhook` job’unu `launchpad` kuyruğuna dispatch eder.
3. Aşağıdaki yapıyı döner:

   ```json
   {
     "status": "queued",
     "message": "Webhook [<Alias>] dispatched to queue."
   }
   ```

### 4.2 `sendByAlias(string $classAlias, array $parameters): void`

1. Alias doğrulaması yapılır.
2. `UpdatePassword` ve `ResetPassword` için özel handler metotlarına yönlendirme:

    * `handleCustomerUpdatePasswordMail()`
    * `handleResetPasswordNotification()`
3. * Eğer sınıf `Mailable` ise `sendMailable()`
* Eğer sınıf `Notification` ise `sendNotification()`
4. Gerekli parametreler eksikse `InvalidArgumentException` fırlatılır.

#### Mailable Gönderimi (`sendMailable`)

* Reflection ile constructor parametreleri çözülür ve mail kuyruğa atılır.

#### Notification Gönderimi (`sendNotification`)

* `Customer` bulunur, `Notification::send()` ile gönderilir.
* `handleResetPasswordNotification` içinde özel URL callback ile reset link oluşturulur.

---

## 5. Job (`ProcessNotificationWebhook`)

* **Namespace**: `App\Jobs`
* **Implements**: `ShouldQueue`
* **Retry**: 5 deneme (`$tries`)
* **Backoff**: \[15, 30, 60, 120, 240]
* **Timeout**: 300s
* **handle()**: `NotificationWebhookService->sendByAlias($class, $parameters)`

---

## 6. Olası Yanıtlar

### 401 Unauthorized

```json
{
  "status": "unauthorized",
  "message": "Invalid or missing hook token."
}
```

### 422 Unprocessable Entity

#### Alias Hatası

```json
{
  "status": "error",
  "errors": {
    "class": [
      "The selected class is invalid."
    ]
  }
}
```

#### Validasyon Hataları

* **Required**:

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "parameters.data.email": [
      "The parameters.data.email field is required."
    ]
  }
}
```

* **Invalid**:

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "parameters.data.email": [
      "The selected parameters.data.email is invalid."
    ]
  }
}
```

## 7. Yeni Bildirim Tipi Ekleme Yeni Bildirim Tipi Ekleme

1. `class_aliases` dizisine alias ve sınıfı ekleyin.
2. `class_validators` altına gerekli kuralları tanımlayın.
3. Varsayılan Mailable veya Notification uygun değilse, `NotificationWebhookService::sendByAlias()` metoduna özel handler ekleyin.