<?php

return [
    'class_aliases'   => [
        'Registration'    => Webkul\Customer\Mail\RegistrationEmail::class,
        'ResetPassword'   => Webkul\Customer\Notifications\CustomerResetPassword::class,
        'UpdatePassword'  => Webkul\Customer\Notifications\CustomerUpdatePassword::class,
    ],
    'class_validators' => [
        'Registration' => [
            'parameters.data.email'      => 'required|email|exists:customers,email',
            'parameters.data.first_name' => 'required|string',
            'parameters.data.last_name'  => 'required|string',
            'parameters.mailType'        => 'required|string|in:customer,admin',
        ],
        'ResetPassword' => [
            'parameters.email' => 'required|email|exists:customers,email',
        ],
        'UpdatePassword' => [
            'parameters.email' => 'required|email|exists:customers,email',
        ],
    ],

    // SHA1: THORNE-IT-NOTIFICATION-WEBHOOK-TOKEN
    'webhook_token' => env('NOTIFICATION_WEBHOOK_TOKEN'),
];
