@php
    $isRendered = false;
    $advertisementFour = null;
    $isLazyLoad = ! isset($lazyload) ? true : ( $lazyload ? true : false );
@endphp

{{-- @if (
    $velocityMetaData
    && $velocityMetaData->advertisement
)
    @php
        $advertisement = json_decode($velocityMetaData->advertisement, true);

        if (
            isset($advertisement[4])
            && is_array($advertisement[4])
        ) {
            $advertisementFour = array_values(array_filter($advertisement[4]));
        }
    @endphp

    @if ($advertisementFour)
        @php
            $isRendered = true;
        @endphp

        <div class="container-fluid advertisement-four-container">
            <div class="row">
                <div class="col-lg-4 col-12 advertisement-container-block no-padding">
                    @if (isset($advertisementFour[0]))
                        <a @if (isset($one)) href="{{ $one }}" @endif aria-label="Advertisement">
                            <img
                                class="{{ $isLazyLoad ? 'lazyload' : '' }}"
                                @if (! $isLazyLoad) src="{{ Storage::url($advertisementFour[0]) }}" @endif
                                data-src="{{ Storage::url($advertisementFour[0]) }}" alt="" />
                        </a>
                    @endif
                </div>

                <div class="col-lg-4 col-12 advertisement-container-block offers-ct-panel">
                    @if (isset($advertisementFour[1]))
                        <a @if (isset($two)) href="{{ $two }}" @endif class="row col-12 remove-padding-margin" aria-label="Advertisement">
                            <img
                                class="offers-ct-top {{ $isLazyLoad ? 'lazyload' : '' }}"
                                @if (! $isLazyLoad) src="{{ Storage::url($advertisementFour[1]) }}" @endif
                                data-src="{{ Storage::url($advertisementFour[1]) }}" alt="" />
                        </a>
                    @endif

                    <div style="height: 10px;"></div>

                    @if (isset($advertisementFour[2]))
                        <a @if (isset($three)) href="{{ $three }}" @endif class="row col-12 remove-padding-margin" aria-label="Advertisement">
                            <img
                                class="offers-ct-bottom {{ $isLazyLoad ? 'lazyload' : '' }}"
                                @if (! $isLazyLoad) src="{{ Storage::url($advertisementFour[2]) }}" @endif
                                data-src="{{ Storage::url($advertisementFour[2]) }}" alt="" />
                        </a>
                    @endif
                </div>

                <div class="col-lg-4 col-12 advertisement-container-block no-padding">
                    @if (isset($advertisementFour[3]))
                        <a @if (isset($four)) href="{{ $four }}" @endif aria-label="Advertisement">
                            <img
                                class="{{ $isLazyLoad ? 'lazyload' : '' }}"
                                @if (! $isLazyLoad) src="{{ Storage::url($advertisementFour[3]) }}" @endif
                                data-src="{{ Storage::url($advertisementFour[3]) }}" alt="" />
                        </a>
                    @endif
                </div>
            </div>
        </div>
    @endif
@endif --}}

 @if (! $isRendered)
 <div class=" relative -mt-2 lg:-mt-40 z-999">
    <div class="gap-4 lg:gap-7 grid grid-cols-2 lg:grid-cols-4">
        <!-- <div class="w-full rounded hover:opacity-50">  -->
        <div class=" w-full rounded-50p relative shadow-institutional lg:shadow-advertisementshadow ">
            <a class="unset group transition-all duration-700" href="/konut">
            <img class="rounded-50p w-full cursor-pointer" src="/deneme/grid/konut.png?v=173252" alt="image">
            <div class="absolute bottom-0 left-0 h-full w-full">
                <div class=" h-3/4 lg:h-1/2 rounded-t-50p bg-gradient-to-b from-[#0000009c] group-hover:from-[#000000b3] to-transparent transition-all duration-700 px-2 flex flex-col justify-start pt-3 lg:pt-5 items-center">
                        <h3 class="font-terramirum font-bold text-base lg:text-xl text-[#ffffff] text-center uppercase lg:mt-4 group-hover:text-[#F5993D] transition-all duration-700" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{ __('velocity::app.admin.meta-data.housing-independent')}} <span class="block lg:inline  text-3xl text-[#fba91a] group-hover:text-white  transition-all duration-700">NFT</span>
                            {{-- KONUT BAĞIMSIZ --}}
                        </h3>
                        <h3 class="font-terramirum font-bold text-2xl text-terra-dark-khaki-green text-center uppercase lg:mt-2" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{-- __('velocity::app.admin.meta-data.tokenization')--}}
                            {{-- BİRİMİ TOKENİZASYONU --}}

                        </h3>
                </div>
            </div>
            </a>

        </div>
        <div class="w-full rounded-50p relative shadow-institutional lg:shadow-advertisementshadow">
            <a class="group unset transition-all duration-700" href="/shopoffice">
            <img class="rounded-50p w-full" src="/deneme/grid/ofis.png?v=173252" alt="image">
            <div class="absolute bottom-0 left-0 h-full w-full">
                <div class="h-3/4 lg:h-1/2 rounded-t-50p bg-gradient-to-b from-[#0000009c] group-hover:from-[#000000b3] to-transparent transition-all duration-700 px-2 flex flex-col justify-start pt-3 lg:pt-5 items-center">
                        <h3 class="font-terramirum font-bold text-base lg:text-xl text-[#ffffff] text-center uppercase lg:mt-4 group-hover:text-[#F5993D] transition-all duration-700" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{ __('velocity::app.admin.meta-data.shop-office')}} <span class="block lg:inline  text-3xl text-[#fba91a] group-hover:text-white  transition-all duration-700">NFT</span>
                            {{-- DÜKKAN ve OFİS --}}
                        </h3>
                        <h3 class="font-terramirum font-bold text-2xl text-terra-dark-khaki-green text-center uppercase lg:mt-2" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{-- __('velocity::app.admin.meta-data.tokenization') --}}
                            {{-- TOKENİZASYONU --}}

                        </h3>
                </div>
            </div>
            </a>
        </div>
        <div class="w-full rounded-50p relative shadow-institutional lg:shadow-advertisementshadow">
            <a class="group unset transition-all duration-700" href="/hotelhostel">
            <img class="rounded-50p w-full" src="/deneme/grid/otel.png?v=173252" alt="image">
            <div class="absolute bottom-0 left-0 h-full w-full">
                <div class="h-3/4 lg:h-1/2 rounded-t-50p bg-gradient-to-b from-[#0000009c] group-hover:from-[#000000b3] to-transparent transition-all duration-700 px-2 flex flex-col justify-start pt-3 lg:pt-5 items-center">
                        <h3 class="font-terramirum font-bold text-sm lg:text-xl text-[#ffffff] text-center uppercase lg:mt-4 group-hover:text-[#F5993D] transition-all duration-700" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{ __('velocity::app.admin.meta-data.industry-independent')}} <span class="block lg:inline text-3xl text-[#fba91a] group-hover:text-white  transition-all duration-700">NFT</span>
                            {{-- SANAYİ BAĞIMSIZ --}}
                            {{-- OTEL TOKENİZASYONU --}}
                        </h3>
                        <h3 class="font-terramirum font-bold text-3xl text-terra-dark-khaki-green text-center uppercase lg:mt-2" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{-- __('velocity::app.admin.meta-data.tokenization')--}}
                            {{-- BİRİMİ TOKENİZASYONU --}}

                        </h3>
                </div>
            </div>
            </a>
        </div>
        <div class="w-full rounded-50p relative shadow-institutional lg:shadow-advertisementshadow">
            <a class="group unset transition-all duration-700" href="/land">
            <img class="rounded-50p w-full" src="/deneme/grid/arsa.png?v=173252" alt="image">
            <div class="absolute bottom-0 left-0 h-full w-full">
                <div class="h-3/4 lg:h-1/2 rounded-t-50p bg-gradient-to-b from-[#0000009c] group-hover:from-[#000000b3] to-transparent transition-all duration-700 px-2 flex flex-col justify-start pt-3 lg:pt-5 items-center">
                        <h3 class="font-terramirum font-bold text-base lg:text-xl text-[#ffffff] text-center uppercase lg:mt-4 group-hover:text-[#F5993D] transition-all duration-700" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{ __('velocity::app.admin.meta-data.land')}} <span class="block lg:inline text-3xl text-[#fba91a] group-hover:text-white  transition-all duration-700">NFT</span>
                            {{-- ARSA --}}
                        </h3>
                        <h3 class="font-terramirum font-bold text-2xl text-terra-dark-khaki-green text-center uppercase lg:mt-2" style="filter: drop-shadow(0px 0px 1px rgb(0 0 0 /10%))">
                            {{-- __('velocity::app.admin.meta-data.tokenization') --}}
                            {{-- TOKENİZASYONU --}}

                        </h3>
                </div>
            </div>
            </a>
        </div>
    </div>
</div>
@endif

