@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif

    <style>
        .scrollbar-slider {
            overflow: hidden;
            padding: 32px 16px !important;
        }
        .swiper-scrollbar {
            height: 4px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            margin-top: 40px;
        }
    </style>
@endsection

@section('body')
    <main>
        <!-- Hero -->
        <section class="relative pb-48 pt-20 lg:pt-32 2xl:pt-48 bg-no-repeat bg-cover min-h-[90vh] bg-left-top" style="background-image: url('https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/background-2.png');">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gold.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gold.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gold.png" alt="külçe donut" class="absolute left-0 bottom-[15%] max-w-[400px] opacity-50 z-10" />
            </picture>

            <div class="ml-auto mr-auto h-full max-w-[91rem] px-4 z-20 relative">
                <div class="h-full items-center gap-4 lg:grid lg:grid-cols-12">
                    <div class="col-span-7 flex h-full flex-col items-center justify-start py-10 lg:items-start lg:py-20">
                        <!-- Slider container -->
                        <div class="slider-container relative w-full overflow-hidden mb-12">
                            <div class="slider-wrapper flex">
                                <!-- Slide 1 -->
                                <div class="slide w-full flex-shrink-0 px-4">
                                    <div class="slide-content">
                                        <p class="slide-text mb-2 text-xl dark:text-jacarta-200">
                                            {!! __('velocity::app-gold.hero_text_1') !!}
                                        </p>
                                        <h1 class="slide-title mb-8 font-display text-4xl sm:text-5xl lg:text-6xl text-jacarta-700 dark:text-white leading-tight lg:leading-tight">
                                            {!! __('velocity::app-gold.hero_text_5') !!}
                                        </h1>
                                    </div>
                                </div>
                                <!-- Slide 2 -->
                                <div class="slide w-full flex-shrink-0 px-4">
                                    <div class="slide-content">
                                        <p class="slide-text mb-2 text-xl dark:text-jacarta-200">
                                            {!! __('velocity::app-gold.hero_text_1') !!}
                                        </p>
                                        <h1 class="slide-title mb-8 font-display text-4xl sm:text-5xl lg:text-6xl text-jacarta-700 dark:text-white leading-tight lg:leading-tight">
                                            {!! __('velocity::app-gold.hero_text_2') !!}
                                        </h1>
                                    </div>
                                </div>
                                <!-- Slide 3 -->
                                <div class="slide w-full flex-shrink-0 px-4">
                                    <div class="slide-content">
                                        <p class="slide-text mb-2 text-xl dark:text-jacarta-200">
                                            <{!! __('velocity::app-gold.hero_text_1') !!}
                                        </p>
                                        <h1 class="slide-title mb-8 font-display text-4xl sm:text-5xl lg:text-6xl text-jacarta-700 dark:text-white leading-tight lg:leading-tight">
                                            {!! __('velocity::app-gold.hero_text_3') !!}
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-wrap justify-end w-full">
                            <div class="w-full md:w-1/2 mb-2 px-2">
                                <div class="hover:bg-milyem cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-all ease-in-out group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" onclick="window.location.href='/miligram';">
                                    <figure class="mr-1 2xl:mr-4 shrink-0 bg-white rounded-full">
                                        <a href="/miligram" class="relative block">
                                            <img src="/miligold/img/mili-ikon.png" alt="miligold" class="rounded-2lg max-w-[80px] transition-transform duration-500 animate-reverse-spin" loading="lazy">
                                        </a>
                                    </figure>
                                    <div>
                                        <a href="/miligram" class="relative block">
                                            @isset($productRepository)
                                            <span class="font-bold text-lg 2xl:text-xl text-jacarta-700 group-hover:text-white dark:text-white whitespace-nowrap">miliGRAM <span class="text-sm dark:text-jacarta-300">{{ $productRepository['price']['price'] }} {{ $channel->base_currency->symbol }}</span></span>
                                            @endisset
                                        </a>
                                        <div class="crypto-price__trade w-full text-center font-display text-lg mt-1">
                                            <a href="/miligram" class="rounded-full group-hover:bg-jacarta-500 bg-green px-10 py-1 text-white font-display font-semibold text-lg whitespace-nowrap">{{__('velocity::app-gold.profile.buy')}}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full md:w-1/2 mb-2 px-2">
                                <div class="hover:bg-milyem cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-all ease-in-out group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" onclick="window.location.href='/miligram';">
                                <figure class="mr-1 2xl:mr-4 shrink-0 bg-white rounded-full">
                                    <a href="/miligram" class="relative block">
                                        <img src="/miligold/img/mili-ikon.png" alt="miligold" class="rounded-2lg max-w-[80px] transition-transform duration-500 animate-reverse-spin" loading="lazy">
                                    </a>
                                </figure>
                                <div>
                                    <a href="/miligram" class="block">
                                        @isset($productRepository)
                                        <span class="font-bold text-lg 2xl:text-xl text-jacarta-700 group-hover:text-white dark:text-white whitespace-nowrap">miliGRAM <span class="text-sm dark:text-jacarta-300">{{ $productRepository['cost']['price'] }} {{ $channel->base_currency->symbol }}</span></span>
                                        @endisset
                                    </a>
                                    <div class="crypto-price__trade w-full text-center font-display text-lg mt-1">
                                        <a href="/miligram" class="rounded-full group-hover:bg-jacarta-500 bg-green px-10 py-1 text-white font-display font-semibold text-lg whitespace-nowrap">{{__('velocity::app-gold.profile.sell')}}</a>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="w-full md:w-1/2 px-2">
                            <div class="hover:bg-milyem cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-all ease-in-out group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" onclick="window.location.href='/miligram';">
                                <figure class="mr-1 2xl:mr-4 bg-white rounded-full">
                                    <a href="#" class="relative block">
                                        <img src="/miligold/img/miligram-coin.png" alt="miligold" class="rounded-2lg max-w-[80px] transition-transform duration-500 animate-reverse-spin" loading="lazy">
                                    </a>
                                </figure>
                                <div class="flex flex-col items-start justify-start space-y-1">
                                    <a href="/miligram" class="block">
                                        <span class="font-bold text-lg text-jacarta-700 group-hover:text-white dark:text-white {{app()->getLocale() !='de' ? '2xl:text-xl' : ''}}">{!! __('velocity::app-gold.convert_to_physical') !!}</span>
                                    </a>
                                    <a href="/miligram" class="@if(app()->getLocale() == 'tr'){{'ml-5'}}@endif mt-1 rounded-full group-hover:bg-jacarta-500 bg-green px-5 py-1 text-white font-display font-semibold text-lg whitespace-nowrap">{{__('velocity::app-gold.profile.make_appointment')}}</a>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                    <!-- Hero image -->
                    <div class="col-span-5">
                        <div class="relative text-center lg:pl-16 lg:text-right">
                            <div class="absolute top-1/3 lg:top-[12%] inline-block animate-fly rounded-2lg border-2 border-[#caa754] bg-white p-3 lg:p-4 2xl:p-6 shadow-2xl sm:left-[5%] md:left-20 lg:-left-0 2xl:left-[8%] cursor-pointer group hover:bg-milyem transition-all ease-in-out"  onclick="window.location.href='/miligram';">
                                <div class="flex gap-4">
                                    <span class="inline-flex h-12 w-12 items-center justify-center rounded-full bg-white">
                                        <picture>
                                            {{--                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/1000_1000_karesiz.webp" type="image/webp">--}}
                                            <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                                            <img src="/miligold/img/mili-ikon.png"/>
                                        </picture>
                                    </span>
                                    <div class="text-left">
                                        <span class="block font-display text-xl lg:text-2xl 2xl:text-3xl text-black group-hover:text-white transition-all ease-in-out">1 MLGR</span>
                                        @isset($productRepository)
                                        <span class="block font-display text-sm text-jacarta-600 group-hover:text-white transition-all ease-in-out">{{ $productRepository['price']['price'] }} {{ $channel->base_currency->symbol }}</span>
                                        @endisset
                                    </div>
                                </div>
                            </div>
                            <picture>
                                {{--                                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/kapalıcars.webp" type="image/webp">--}}
                                <source srcset="/miligold/img/grandbazaar.png" type="image/png">
                                <img src="/miligold/img/grandbazaar.png" class="inline-block rounded-2.5xl border-2 border-[#caa754]"/>
                            </picture>

                            <div
                                class="absolute bottom-0 sm:bottom-28 lg:-bottom-12 2xl:bottom-8 inline-block animate-fly rounded-2lg border-2 border-[#caa754] bg-white p-3 lg:p-4 2xl:p-6 shadow-2xl right-[7%] sm:right-[7%] lg:right-[3%] 2xl:-right-[17%]">
                                <div class="text-left">
                                    <span class="block font-display text-3xl">{!! __('velocity::app-gold.total_volume') !!}</span>
                                    <span class=" block font-display text-sm text-jacarta-600"> 675.434.538 MLGR</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section id="cryptochart" class="hidden md:block dark:bg-jacarta-900 -translate-y-24 lg:-translate-y-32 2xl:-translate-y-36 -mb-12">
            <div class="scrollbar-custom overflow-x-auto py-4">

                <div class="rounded-lg bg-white dark:bg-jacarta-700 dark:text-jacarta-300 container" style="box-shadow: 0 0 20px 10px #f9f4d59e;">
                    <div class="flex items-center border-b border-jacarta-600 dark:border-jacarta-600">
                        <div class="text-xl font-bold w-[25%] px-3 py-5 text-center text-[#caa754]">{!! __('velocity::app-gold.name') !!}</div>
                        <div class="text-xl font-bold w-[20%] px-3 py-5 text-center text-[#caa754]">{!! __('velocity::app-gold.buy') !!}</div>
                        <div class="text-xl font-bold w-[20%] px-3 py-5 text-center text-[#caa754]">{!! __('velocity::app-gold.sell') !!}</div>
                        <div class="text-xl font-bold w-[15%] px-3 py-5 text-center text-[#caa754]">{!! __('velocity::app-gold.difference') !!}</div>
                        <div class="text-xl font-bold w-[20%] px-3 py-5 text-center text-[#caa754]">{!! __('velocity::app-gold.action') !!}</div>
                    </div>
                    <div id="priceTableBody" class="divide-y divide-jacarta-100 dark:divide-jacarta-600">
                        <!-- Veriler JavaScript ile doldurulacak -->
                    </div>
                    <!--              <div class="border-t border-jacarta-100 px-4 pt-4 pb-8 text-center dark:border-jacarta-600">-->
                    <!--                <a href="#" class="inline-flex items-center justify-center text-lg text-[#caa754]"-->
                    <!--                  >Tümünü Görüntüle-->
                    <!--                  <svg-->
                    <!--                    xmlns="http://www.w3.org/2000/svg"-->
                    <!--                    viewBox="0 0 24 24"-->
                    <!--                    width="24"-->
                    <!--                    height="24"-->
                    <!--                    class="h-rotate ml-2 fill-[#caa754]"-->
                    <!--                  >-->
                    <!--                    <path fill="none" d="M0 0h24v24H0z" />-->
                    <!--                    <path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z" />-->
                    <!--                  </svg>-->
                    <!--                </a>-->
                    <!--              </div>-->
                </div>
            </div>
        </section>
        <!-- end hero -->
        <section class="relative py-12 lg:py-24 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/dark-bg.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/dark-bg.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/dark-bg.png" class="h-full w-full" />
            </picture>
            <div class="container">
                <div class="lg:flex lg:justify-between">
                    <!-- Image -->
                    <div class="relative pr-6 lg:w-[33%]">
                        <picture>
{{--                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/milyem.webp" type="image/webp">--}}
                            <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                            <img src="/miligold/img/mili-ikon.png" class="max-w-[200px] mx-auto w-full" />
                        </picture>
                    </div>

                    <!-- Info -->
                    <div class="py-10 px-6 lg:w-[67%] lg:pl-12 flex flex-col justify-center items-start">
                        <h2 class="mb-6 font-display text-3xl text-white dark:text-white">
                            {!! __('velocity::app-gold.milyem_story_title') !!}
                        </h2>
                        <p class="mb-12 text-lg leading-normal text-white dark:text-jacarta-300 italic">
                            {!! __('velocity::app-gold.milyem_story_description') !!}
                        </p>

                    </div>
                </div>
            </div>
        </section>
        <section class="py-4 md:py-8 lg:py-24 dark:bg-jacarta-900">
            <div class="container">
                <div class="mx-auto mb-12 max-w-xl text-center">
                    <h2 class="mb-6 text-center font-display text-3xl font-medium text-jacarta-700 dark:text-white">
                        {!! __('velocity::app-gold.how_to_buy') !!}
                    </h2>
                </div>
                <div class="lg:flex lg:flex-nowrap lg:space-x-10">
                    <div class="lg:w-[43%]">
                        <!-- tabs -->
                        <ul class="nav nav-tabs lg:mb-12 space-y-2" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link nav-link--style-2 active group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                        id="ownership-tab" data-bs-toggle="tab" data-bs-target="#ownership" type="button" role="tab" aria-controls="ownership" aria-selected="true">
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/uyelik.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                                      <span class="mb-2 mt-1 block font-display text-xl font-medium group-hover:text-[#caa754] dark:text-white">
                                        {!! __('velocity::app-gold.create_account') !!}
                                      </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.create_account_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button
                                    class="nav-link nav-link--style-2 group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                    id="voting-power-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#voting-power"
                                    type="button"
                                    role="tab"
                                    aria-controls="voting-power"
                                    aria-selected="false"
                                >
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/guvenlik.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                      <span class="mb-2 mt-1 block font-display text-xl font-medium text-jacarta-700 group-hover:text-[#caa754] dark:text-white">
                        {!! __('velocity::app-gold.secure_account') !!}
                        </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.secure_account_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button
                                    class="nav-link nav-link--style-2 group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                    id="rewards-income-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#rewards-income"
                                    type="button"
                                    role="tab"
                                    aria-controls="rewards-income"
                                    aria-selected="false"
                                >
                                    <picture>
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/sepet.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_sepet.png" type="image/png">
                                        <img src="/miligold/img/mili_sepet.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                      <span class="mb-2 mt-1 block font-display text-xl font-medium text-jacarta-700 group-hover:text-[#caa754] dark:text-white">
                         {!! __('velocity::app-gold.add_to_cart_title') !!}
                      </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.add_to_cart_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button
                                    class="nav-link nav-link--style-2 group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                    id="complete-decentralization-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#complete-decentralization"
                                    type="button"
                                    role="tab"
                                    aria-controls="complete-decentralization"
                                    aria-selected="false"
                                >
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/odeme.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                      <span class="mb-2 mt-1 block font-display text-xl font-medium text-jacarta-700 group-hover:text-[#caa754] dark:text-white">
                          {!! __('velocity::app-gold.make_payment_title') !!}
                      </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.make_payment_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button
                                    class="nav-link nav-link--style-2 group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                    id="nft-yield-farming-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#nft-yield-farming"
                                    type="button"
                                    role="tab"
                                    aria-controls="nft-yield-farming"
                                    aria-selected="false"
                                >
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/kontrol.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/kontrol.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/kontrol.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                      <span class="mb-2 mt-1 block font-display text-xl font-medium text-jacarta-700 group-hover:text-[#caa754] dark:text-white">
                        {!! __('velocity::app-gold.track_order_title') !!}
                      </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.track_order_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button
                                    class="nav-link nav-link--style-2 group relative flex w-full border-b border-jacarta-100 p-6 text-left dark:border-jacarta-600"
                                    id="nft-yield-farming-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#nft-yield-farming"
                                    type="button"
                                    role="tab"
                                    aria-controls="nft-yield-farming"
                                    aria-selected="false"
                                >
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/cevir.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" class="mr-2 h-12 w-12 flex-shrink-0" />
                                    </picture>
                                    <div>
                      <span class="mb-2 mt-1 block font-display text-xl font-medium text-jacarta-700 group-hover:text-[#caa754] dark:text-white">
                        {!! __('velocity::app-gold.convert_to_physical_title') !!}
                      </span>
                                        <div class="nav-link-content hidden">
                                            <p class="text-jacarta-500 dark:text-jacarta-300">
                                                {!! __('velocity::app-gold.convert_to_physical_description') !!}
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="hidden lg:flex items-center justify-center overflow-hidden lg:w-[57%] lg:overflow-visible">
                        <!-- content -->
                        <div class="tab-content flex-1">
                            <div class="tab-pane fade show active relative" id="ownership" role="tabpanel" aria-labelledby="ownership-tab">
                                <figure class="flex items-center justify-center">
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/uyelik.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" class="p-14 dark:border-jacarta-600 max-w-[400px]" />
                                    </picture>
                                    <picture class="absolute animate-spin-slow">
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/how-purchase.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_step.png" type="image/png">
                                        <img src="/miligold/img/mili_step.png" />
                                    </picture>
                                </figure>
                            </div>
                            <div
                                class="tab-pane fade relative"
                                id="voting-power"
                                role="tabpanel"
                                aria-labelledby="voting-power-tab"
                            >
                                <figure class="flex items-center justify-center">
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/guvenlik.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" alt="" class="p-14 dark:border-jacarta-600 max-w-[400px]" />
                                    </picture>
                                    <picture class="absolute animate-spin-slow">
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/how-purchase.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_step.png" type="image/png">
                                        <img src="/miligold/img/mili_step.png" />
                                    </picture>
                                </figure>
                            </div>
                            <div
                                class="tab-pane fade relative"
                                id="rewards-income"
                                role="tabpanel"
                                aria-labelledby="rewards-income-tab"
                            >
                                <figure class="flex items-center justify-center">
                                    <picture>
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/sepet.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_sepet.png" type="image/png">
                                        <img src="/miligold/img/mili_sepet.png" alt="" class="p-14 dark:border-jacarta-600 max-w-[400px]" />
                                    </picture>
                                    <picture class="absolute animate-spin-slow">
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/how-purchase.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_step.png" type="image/png">
                                        <img src="/miligold/img/mili_step.png" />
                                    </picture>
                                </figure>
                            </div>
                            <div
                                class="tab-pane fade relative"
                                id="complete-decentralization"
                                role="tabpanel"
                                aria-labelledby="complete-decentralization-tab"
                            >
                                <figure class="flex items-center justify-center">
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/odeme.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" alt="" class="p-14 dark:border-jacarta-600 max-w-[400px]" />
                                    </picture>
                                    <picture class="absolute animate-spin-slow">
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/how-purchase.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_step.png" type="image/png">
                                        <img src="/miligold/img/mili_step.png" />
                                    </picture>
                                </figure>
                            </div>
                            <div
                                class="tab-pane fade relative"
                                id="nft-yield-farming"
                                role="tabpanel"
                                aria-labelledby="nft-yield-farming-tab"
                            >
                                <figure class="flex items-center justify-center">
                                    <picture>
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/cevir.webp" type="image/webp">
                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" type="image/png">
                                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" alt="" class="p-14 dark:border-jacarta-600 max-w-[400px]" />
                                    </picture>
                                    <picture class="absolute animate-spin-slow">
{{--                                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/how-purchase.webp" type="image/webp">--}}
                                        <source srcset="/miligold/img/mili_step.png" type="image/png">
                                        <img src="/miligold/img/mili_step.png" />
                                    </picture>
                                </figure>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- Process -->
        <section class="relative py-4 md:py-8 lg:py-24 dark:bg-jacarta-800">
            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/milyem-tarih.png" alt="külçe donut" class="absolute left-0 bottom-0 max-w-[400px]" />

            <div class="container">
                <h2 class="mb-10 text-center font-display text-3xl text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-gold.benefits_main') !!}
                </h2>
                <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    <div class="relative rounded-2.5xl border border-jacarta-100 bg-white p-8 shadow-[0_5px_0_0_#AB842E] transition-shadow hover:shadow-[0_16px_24px_-8px_rgba(171,132,46,.3)] dark:border-jacarta-700 dark:bg-jacarta-700">
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/iscilik.png" class="grayscale opacity-50 absolute right-3 top-3 h-16 w-16 " alt="">
                        <div class="mb-6 inline-flex items-center justify-center">
                            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/iscilik.png" class="h-12 w-12" alt="">
                        </div>
                        <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.benefits_1_title') !!}</h3>
                        <p class="dark:text-jacarta-300">
                            {!! __('velocity::app-gold.benefits_1_description') !!}
                        </p>
                    </div>
                    <div
                        class="relative rounded-2.5xl border border-jacarta-100 bg-white p-8 shadow-[0_5px_0_0_#AB842E] transition-shadow hover:shadow-[0_16px_24px_-8px_rgba(171,132,46,.3)] dark:border-jacarta-700 dark:bg-jacarta-700"
                    >
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/guvence.png" class="grayscale opacity-50 absolute right-3 top-3 h-16 w-16 " alt="">

                        <div class="mb-6 inline-flex items-center justify-center">
                            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/guvence.png" class="h-12 w-12" alt="">

                        </div>
                        <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.benefits_2_title') !!}</h3>
                        <p class="dark:text-jacarta-300">
                            {!! __('velocity::app-gold.benefits_2_description') !!}
                        </p>
                    </div>
                    <div
                        class="relative rounded-2.5xl border border-jacarta-100 bg-white p-8 shadow-[0_5px_0_0_#AB842E] transition-shadow hover:shadow-[0_16px_24px_-8px_rgba(171,132,46,.3)] dark:border-jacarta-700 dark:bg-jacarta-700"
                    >
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/kucukbutce.png" class="grayscale opacity-50 absolute right-3 top-3 h-16 w-16 " alt="">

                        <div class="mb-6 inline-flex items-center justify-center">
                            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/why-buy/kucukbutce.png" class="h-12 w-12" alt="">

                        </div>
                        <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.benefits_3_title') !!}</h3>
                        <p class="dark:text-jacarta-300">
                            {!! __('velocity::app-gold.benefits_3_description') !!}
                        </p>
                    </div>
                </div>
            </div>
        </section>
        <!-- end process -->

        <!-- Benefits -->
        <section class="py-8 my-4 mt-12 bg-[#d8d8d8] dark:bg-jacarta-900">
            <div class="container">
                <h2 class="mb-10 text-center font-display text-3xl text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-gold.how_to_buy_main') !!}
                </h2>
                <div class="swiper scrollbar-slider !pt-10">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/profilim.gif" class="rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_1') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_1_description') !!}
                                </p>
                            </div>
                        </div>
                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/fiziki-altin.gif" class="rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_2') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_2_description') !!}
                                </p>
                            </div>
                        </div>
                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/1000milyem.gif" class=" rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_3') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_3_description') !!}
                                </p>
                            </div>
                        </div>

                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/odeme.gif" class="rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_4') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_4_description') !!}
                                </p>
                            </div>
                        </div>
                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/kargo.gif" class="rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_5') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_5_description') !!}
                                </p>
                            </div>
                        </div>
                        <div class="swiper-slide !h-auto">
                            <div class="h-full rounded-2.5xl border border-jacarta-100 bg-white p-8 pt-0 text-center transition-shadow hover:shadow-xl dark:border-jacarta-600 dark:bg-jacarta-700">
                                <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gif/avcunun_icinde.gif" class="rounded-full h-14 w-14">
                                </div>

                                <h3 class="mb-4 font-display text-lg text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.how_to_buy_6') !!}</h3>
                                <p class="dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.how_to_buy_6_description') !!}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-scrollbar swiper-scrollbar-horizontal"></div>
                </div>
            </div>
        </section>
        <!-- end benefits -->

        <section class="relative py-4 md:py-8 lg:py-24 dark:bg-jacarta-800">
            <div class="container">
                <div class="justify-between flex flex-col-reverse md:flex-row lg:space-x-20">
                    <div class="lg:w-[70%]">
                        <h2 class="my-6 font-display text-3xl font-medium text-jacarta-700 dark:text-white">
                            {!! __('velocity::app-gold.faq_title') !!}
                        </h2>
                        <div class="accordion mb-14" id="accordionFAQ">
                            @for ($i = 1; $i <= 4; $i++)
                                <div class="accordion-item mb-5 overflow-hidden rounded-lg border border-jacarta-100 dark:border-jacarta-600">
                                    <h2 class="accordion-header" id="faq-heading-{{ $i }}">
                                        <button class="accordion-button collapsed relative flex w-full items-center justify-between bg-white px-4 py-3 text-left font-display text-jacarta-700 dark:bg-jacarta-700 dark:text-white" type="button" data-bs-toggle="collapse" data-bs-target="#faq-{{ $i }}" aria-expanded="false" aria-controls="faq-{{ $i }}">
                                        <span>
                                          {!! __('velocity::app-gold.faq_page.'.$i.'.question') !!}
                                        </span>
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="accordion-arrow h-4 w-4 shrink-0 fill-jacarta-700 transition-transform dark:fill-white">
                                                <path fill="none" d="M0 0h24v24H0z"></path>
                                                <path d="M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"></path>
                                            </svg>
                                        </button>
                                    </h2>
                                    <div id="faq-{{ $i }}" class="accordion-collapse collapse" aria-labelledby="faq-heading-{{ $i }}" data-bs-parent="#accordionFAQ">
                                        <div class="accordion-body border-t border-jacarta-100 bg-white p-4 dark:border-jacarta-600 dark:bg-jacarta-700">
                                            <p class="dark:text-jacarta-200">
                                                {!! __('velocity::app-gold.faq_'.$i.'_answer') !!}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endfor
                        </div>
                        <p class="text-lg text-jacarta-700 dark:text-jacarta-100 text-center">
                            <a href="/faq" class="inline-flex items-center justify-center text-lg text-[#caa754]">
                                {!! __('velocity::app-gold.show_all') !!}
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="h-rotate ml-2 fill-[#caa754]">
                                    <path fill="none" d="M0 0h24v24H0z" />
                                    <path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z" />
                                </svg>
                            </a>
                        </p>
                    </div>
                    <div class="lg:w-[30%]">
                        <div class="mt-12">
                            <svg viewbox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" class="mx-auto mt-8 rotate-[8deg]">
                                <defs>
                                    <clipPath id="clipping" clipPathUnits="userSpaceOnUse">
                                        <path d="M 0, 100 C 0, 17.000000000000004 17.000000000000004, 0 100, 0 S 200, 17.000000000000004 200, 100 183, 200 100, 200 0, 183 0, 100" fill="#9446ED"></path>
                                    </clipPath>
                                </defs>
                                <g clip-path="url(#clipping)">
                                    <image href="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/faq.png" width="200" height="200" clip-path="url(#clipping)" />
                                </g>
                            </svg>
                            <!--                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/faq.png" class="mb-8 inline-block rounded-2.5xl" />-->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="overflow-hidden pb-24 dark:bg-jacarta-900">
            <div class="container">
                <div class="mx-auto mb-14 max-w-xl text-center">
                    <h2 class="mb-6 text-center font-display text-3xl font-medium text-jacarta-700 dark:text-white">
                        {!! __('velocity::app-gold.milyem_places') !!}
                    </h2>
                </div>
            </div>
            <div class="mb-8 flex animate-marquee space-x-8">
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_1.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_1.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_1.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_2.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_2.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_2.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_3.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_3.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_3.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_4.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_4.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_4.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_5.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_5.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_5.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_6.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_6.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_6.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_7.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_7.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_7.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_1.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_1.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_1.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_2.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_2.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_2.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_3.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_3.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_3.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_4.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_4.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_4.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_5.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_5.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_5.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_6.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_6.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_6.jpg" alt="" />
                    </picture>
                </div>
                <div
                    class="flex flex-shrink-0 items-center justify-center rounded-2.5xl border border-jacarta-100 bg-white p-6"
                >
                    <picture>
                        <source srcset="/miligold/img/webp/milyem/dao/partner_dao_7.webp" type="image/webp">
                        <source srcset="/miligold/img/milyem/dao/partner_dao_7.jpg" type="image/jpeg">
                        <img src="/miligold/img/milyem/dao/partner_dao_7.jpg" alt="" />
                    </picture>
                </div>
            </div>

        </section>

        <section class="relative bg-cover bg-center bg-no-repeat py-16 after:absolute after:inset-0 after:bg-[#000000]/50" style="background-image: url(/miligold/img/milyem/dark-image-bg.png)">
            <div class="container relative z-10">
                <h2 class="mx-auto mb-6 max-w-lg text-center font-display text-2xl text-white">
                    {!! __('velocity::app-gold.newest_listen') !!}
                </h2>
                <div class="mx-auto mt-10 max-w-md text-center">
                    <form class="relative">
                        <input type="email" placeholder="{!! __('velocity::app-gold.email_placeholder') !!}" class="w-full rounded-full border border-jacarta-600 bg-white py-3 px-4 text-black placeholder-black focus:ring-[#caa754]"/>
                        <button class="absolute top-2 right-2 rounded-full bg-[#caa754] px-6 py-2 font-display text-sm text-white hover:bg-[#caa754]-dark">
                            {!! __('velocity::app-gold.send') !!}
                        </button>
                    </form>
                </div>
            </div>
        </section>
        <!-- CTA -->
        <div class="relative z-10 my-20 dark:bg-jacarta-900">
            <div class="container">
                <div class="relative overflow-hidden rounded-2.5xl px-16 py-8 shadow-md lg:px-24">
                    <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" alt="gradient" class="h-full w-full" />
                    </picture>
                    <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                        <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" alt="gradient dark" class="h-full w-full" />
                    </picture>
                    <div class="items-center justify-between md:flex">
                        <div class="mb-6 md:mb-0 md:w-1/2">
                            <h2 class="mb-4 font-display text-2xl text-jacarta-700 dark:text-white sm:text-3xl">
                                {!! __('velocity::app-gold.contact_us') !!}
                            </h2>
                            <p class="mb-8 text-lg dark:text-jacarta-300">
                                {!! __('velocity::app-gold.contact_help') !!}
                            </p>
                            <a
                                href="#"
                                class="inline-block rounded-full bg-[#caa754] py-3 px-8 text-center font-semibold text-white shadow-[#caa754]-volume transition-all hover:bg-[#caa754]-dark"
                            >
                                {!! __('velocity::app-gold.contact_us_2') !!}
                            </a>
                        </div>
                        <picture>
{{--                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/iletisim-new.webp" type="image/webp">--}}
                            <source srcset="/miligold/img/mili_iletisim.png" type="image/png">
                            <img src="/miligold/img/mili_iletisim.png" class="max-w-[350px] w-full" alt="" />
                        </picture>
                    </div>
                </div>
            </div>
        </div>
        <!-- end cta -->

    </main>
@endsection
@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>

    <script src="https://cdn.socket.io/4.0.1/socket.io.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const swiper = new Swiper('.scrollbar-slider', {
                slidesPerView: 3,
                spaceBetween: 20,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                scrollbar: {
                    el: '.swiper-scrollbar',
                    draggable: true,
                },
                breakpoints: {
                    640: {
                        slidesPerView: 1,
                        spaceBetween: 20,
                    },
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30,
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 30,
                    },
                }
            });
        });
    </script>
    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let previousPrices = {};
            let ws = null;
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;

            function connect() {
                ws = new WebSocket('wss://socket.haremaltin.com/socket.io/?EIO=4&transport=websocket');

                ws.onopen = () => {
                    console.log('WebSocket bağlantısı açıldı');
                    reconnectAttempts = 0;

                    // Socket.IO el sıkışma protokolü
                    ws.send('40');

                    // Ping mesajlarını başlat
                    startPing();
                };

                ws.onmessage = (event) => {
                    // console.log('Ham veri alındı:', event.data);

                    try {
                        // Socket.IO protokol mesajlarını işle
                        if (event.data === '40') {
                            // Bağlantı kabul edildi, subscribe gönder
                            const subscribeMsg = '42["subscribe",["USDTRY","EURTRY","GA","XAU/USD","ALTIN","GUMUS"]]';
                            ws.send(subscribeMsg);
                        }
                        else if (event.data === '2') {
                            // Ping mesajına pong ile yanıt ver
                            ws.send('3');
                        }
                        else if (event.data.startsWith('42')) {
                            const jsonStr = event.data.substring(2);
                            const data = JSON.parse(jsonStr);
                            // console.log('Parse edilmiş veri:', data);

                            // price_changed eventini yakala
                            if (data[0] === 'price_changed' && data[1].data) {
                                updatePriceTable(data[1].data);
                            }
                        }
                    } catch (error) {
                        console.error('Veri parse hatası:', error);
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket hatası:', error);
                };

                ws.onclose = (event) => {
                    console.log('WebSocket bağlantısı kapandı:', event.code, event.reason);

                    // Yeniden bağlanma mantığı
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        console.log(`Yeniden bağlanma denemesi ${reconnectAttempts}/${maxReconnectAttempts}`);
                        setTimeout(connect, 5000);
                    }
                };
            }

            // Ping mesajları gönder
            function startPing() {
                const pingInterval = setInterval(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send('2');
                    } else {
                        clearInterval(pingInterval);
                    }
                }, 25000); // Her 25 saniyede bir ping gönder

                // Sayfa kapatılırken interval'i temizle
                window.addEventListener('beforeunload', () => {
                    clearInterval(pingInterval);
                });
            }

            function updatePriceTable(data) {
                const tableBody = document.getElementById('priceTableBody');

                if (!data) {
                    console.error('Data boş geldi');
                    return;
                }

                // İstenen sembolleri tanımla
                const wantedSymbols = ['ALTIN', 'ONS', 'AYAR22', 'GUMUSTRY'];

                // Tabloyu temizle
                tableBody.innerHTML = '';

                try {
                    // Milyem değerlerini hesapla (ALTIN'ın binde biri)
                    const altinData = data['ALTIN'];
                    if (altinData) {
                        const milyemData = {
                            code: 'miliGRAM',
                            alis: (parseFloat(altinData.alis) / 1000).toString(),
                            satis: (parseFloat(altinData.satis) / 1000).toString(),
                            dir: altinData.dir // Altın ile aynı yön
                        };
                        // Milyem'i data objesine ekle
                        data['miliGRAM'] = milyemData;
                        // İstenen sembollere Milyem'i ekle
                        wantedSymbols.unshift('miliGRAM'); // Listenin başına ekle
                    }

                    // Sadece istenen sembolleri işle
                    wantedSymbols.forEach(symbol => {
                        const priceData = data[symbol];

                        if (!priceData) {
                            console.warn(`${symbol} için veri bulunamadı`);
                            return;
                        }

                        // Fiyat verilerini güvenli bir şekilde al
                        const bid = parseFloat(priceData.alis || 0);
                        const ask = parseFloat(priceData.satis || 0);

                        if (isNaN(bid) || isNaN(ask)) {
                            console.warn(`Geçersiz fiyat verisi: ${symbol}`, priceData);
                            return;
                        }

                        // Değişim yüzdesini hesapla
                        const prev = previousPrices[symbol] || { satis: ask };
                        const changePercent = ((ask - prev.satis) / prev.satis) * 100;

                        // Sembol isimlerini Türkçeleştir
                        const symbolNames = {
                            'miliGRAM': '{!! __('velocity::app-gold.miliGRAM') !!}',
                            'ALTIN': '{!! __('velocity::app-gold.ALTIN') !!}',
                            'ONS': '{!! __('velocity::app-gold.ONS') !!}',
                            'AYAR22': '{!! __('velocity::app-gold.AYAR22') !!}',
                            'GUMUSTRY': '{!! __('velocity::app-gold.GUMUSTRY') !!}'
                        };

                        // Yön okları için class ve icon belirle
                        const directionClass = priceData.dir?.satis_dir === 'up' ? '!text-green' :
                            priceData.dir?.satis_dir === 'down' ? '!text-red' :
                                'text-jacarta-700 dark:text-white';

                        const directionIcon = priceData.dir?.satis_dir === 'up' ? '↑' :
                            priceData.dir?.satis_dir === 'down' ? '↓' :
                                '';

                        // Nokta ile ayrılmış sayıyı virgülle ayrılmış formata çeviren fonksiyon
                        function formatPrice(number) {
                            // Önce sayıyı 3 basamaklı ondalık kısmıyla string'e çevir
                            let priceStr = number.toFixed(3);

                            // Nokta ile ondalık kısmı ayır
                            let [wholePart, decimalPart] = priceStr.split('.');

                            // Tam kısmı 3'erli grupla ve nokta ile ayır
                            wholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

                            // Tam kısım ve ondalık kısmı virgül ile birleştir
                            return `${wholePart},${decimalPart}`;
                        }

                        // Satır div'ini oluştur
                        const row = document.createElement('div');
                        row.className = 'flex items-center border-t border-jacarta-100 dark:border-jacarta-600 px-3';

                        row.innerHTML = `
                <div class="crypto-price__coin flex w-[25%] items-center px-3 py-2">
                    <div class="crypto-price__name flex-1 font-display font-semibold text-lg text-center">
                        <span class="text-jacarta-700 dark:text-white mr-3">${symbolNames[symbol] || symbol}</span>
                    </div>
                </div>
                <div class="crypto-price__price text-center text-lg w-[20%] px-3 py-2 text-jacarta-700 dark:text-white ${directionClass}">
                  ${formatPrice(bid)} ${directionIcon}
                </div>
                <div class="crypto-price__volume w-[20%] text-center text-lg md:block px-3 py-2 ${directionClass}">
                  ${formatPrice(ask)} ${directionIcon}
                </div>
                <div class="crypto-price__change  text-center text-lg w-[15%] px-3 py-2 ${directionClass}">
                  ${changePercent > 0 ? '+' : ''}${formatPrice(changePercent)}% ${directionIcon}
                </div>
                <div class="crypto-price__trade w-[20%] pl-3 pr-4 py-5 text-center font-display text-lg"><a href="/miligram" class="rounded-full hover:bg-jacarta-700 bg-green px-5 py-2 text-white font-display font-semibold text-lg">{!! __('velocity::app-gold.purchase') !!}</a></div>
              `;

                        tableBody.appendChild(row);
                    });

                    // Önceki fiyatları güncelle
                    const filteredData = {};
                    wantedSymbols.forEach(symbol => {
                        if (data[symbol]) {
                            filteredData[symbol] = data[symbol];
                        }
                    });
                    previousPrices = filteredData;

                } catch (error) {
                    console.error('Tablo güncelleme hatası:', error);
                }
            }

            // İlk bağlantıyı başlat
            connect();

            // Sayfa kapatılırken bağlantıyı temiz bir şekilde kapat
            window.addEventListener('beforeunload', () => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.close(1000, 'Sayfa kapatıldı');
                }
            });
        });
    </script>

@endpush

