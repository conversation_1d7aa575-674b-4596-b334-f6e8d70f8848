@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')

    <main class="">
        <!-- Benefits -->
        <section class="relative pt-32 pb-24">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="mx-auto max-w-5xl relative">
                <h1 class="mb-6 text-4xl text-center font-bold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-gold.faq_title') !!}

                </h1>
                <picture class="absolute right-0 top-0 -z-10">
                    <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/soru_.webp" type="image/webp">
                    <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/soru_.png" type="image/png">
                    <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/soru_.png" class="w-full">
                </picture>
            </div>
            <div class="container">
                <div class="accordion mx-auto max-w-5xl" id="accordionFAQ">
                    @for ($i = 1; $i <= 15; $i++)
                        <div class="accordion-item mb-5 overflow-hidden rounded-lg border border-jacarta-100 dark:border-jacarta-600">
                            <h2 class="accordion-header" id="faq-heading-{{ $i }}">
                                <button class="accordion-button collapsed relative flex w-full items-center justify-between bg-white px-4 py-3 text-left font-display text-jacarta-700 dark:bg-jacarta-700 dark:text-white" type="button" data-bs-toggle="collapse" data-bs-target="#faq-{{ $i }}" aria-expanded="false" aria-controls="faq-{{ $i }}">
                                    <span>
                                        {!! __('velocity::app-gold.faq_page.' . $i . '.question') !!}
                                    </span>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="accordion-arrow h-4 w-4 shrink-0 fill-jacarta-700 transition-transform dark:fill-white">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"></path>
                                    </svg>
                                </button>
                            </h2>
                            <div id="faq-{{ $i }}" class="accordion-collapse collapse" aria-labelledby="faq-heading-{{ $i }}" data-bs-parent="#accordionFAQ">
                                <div class="accordion-body border-t border-jacarta-100 bg-white p-4 dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <p class="dark:text-jacarta-200">
                                        {!! __('velocity::app-gold.faq_page.' . $i . '.answer') !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endfor

                </div>
            </div>
            <div class="relative z-10 my-20 dark:bg-jacarta-900">
                <div class="container">
                    <div class="relative overflow-hidden rounded-2.5xl px-16 py-8 shadow-md lg:px-24">
                        <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" class="h-full w-full">
                        </picture>
                        <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/f4f4f4.webp" type="image/webp">
                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" type="image/png">
                            <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/f4f4f4.png" class="h-full w-full">
                        </picture>
                        <div class="items-center justify-between md:flex">
                            <div class="mb-6 md:mb-0 md:w-1/2">
                                <h2 class="mb-4 font-display text-2xl text-jacarta-700 dark:text-white sm:text-3xl">
                                    {!! __('velocity::app-gold.contact_us') !!}
                                </h2>
                                <p class="mb-8 text-lg dark:text-jacarta-300">
                                    {!! __('velocity::app-gold.contact_help') !!}
                                </p>
                                <a href="/contact" class="inline-block rounded-full bg-[#caa754] py-3 px-8 text-center font-semibold text-white shadow-[#caa754]-volume transition-all hover:bg-[#caa754]-dark">
                                    {!! __('velocity::app-gold.contact_us_2') !!}
                                </a>
                            </div>
                            <picture>
{{--                                <source srcset="/miligold/img/webp/milyem/iletisim-new.webp" type="image/webp">--}}
                                <source srcset="/miligold/img/mili_iletisim.png" type="image/png">
                                <img src="/miligold/img/mili_iletisim.png" class="max-w-[350px] w-full">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush