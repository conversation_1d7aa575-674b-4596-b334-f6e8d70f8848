@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')

    <main>
        <section class="relative bg-contain bg-right-top bg-no-repeat pt-32 pb-8 lg:pb-16">
            <div class="container relative z-10">
                <h1 class="text-center font-display text-4xl font-medium text-jacarta-700">{!! __('velocity::app-static.contact-us.heading') !!} </h1>
            </div>
        </section>

        <!-- Contact -->
        <section class="relative py-4 md:py-8 lg:py-24 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="mx-auto max-w-kbmobile md:max-w-tablet lg:max-w-5xl">
                <div class="lg:flex">
                    <!-- Contact Form -->
                    <div class="mb-12 lg:mb-0 lg:w-2/3 lg:pr-12">
                        <h2 class="mb-4 font-display text-xl text-jacarta-700 dark:text-white text-center">
                            {!! __('velocity::app-gold.contact_page.title') !!}
                        </h2>
                        @if($errors->any())
                            <div class="col-md-12">
                                <div class="alert alert-danger">
                                    <ul>
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        @endif
                        @if(session()->has('success'))
                            <div class="col-md-12">
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            </div>
                        @endif
                        <form class="col-md-12 amount-content row" action="{{ route('shop.home.contact-us.submit') }}" method="post">
                            @csrf
                            @method('post')
                            <div class="block md:flex md:space-x-7">
                                <div class="mb-6 w-full md:w-1/2">
                                    <label for="first_name" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-static.contact-us.contact-form.firstname') !!}
                                        <span class="text-red">*</span>
                                    </label>
                                    <input name="first_name" id="first_name" type="text" required="" class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                                </div>
                                <div class="mb-6 w-full md:w-1/2">
                                    <label for="last_name" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-static.contact-us.contact-form.lastname') !!}
                                        <span class="text-red">*</span>
                                    </label>
                                    <input name="last_name" id="last_name" type="text" required="" class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                                </div>
                            </div>
                            <div class="block md:flex md:space-x-7">
                                <div class="mb-6 w-full md:w-1/2">
                                    <label for="email" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-static.contact-us.contact-form.email') !!}
                                        <span class="text-red">*</span>
                                    </label>
                                    <input name="email" id="email" type="email" required="" class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                                </div>
                                <div class="mb-6 w-full md:w-1/2">
                                    <label for="subject" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-static.contact-us.contact-form.subject') !!}
                                        <span class="text-red">*</span>
                                    </label>
                                    <input name="subject" id="subject" type="text" required="" class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="message" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                                    {!! __('velocity::app-static.contact-us.contact-form.message') !!}
                                    <span class="text-red">*</span>
                                </label>
                                <textarea id="message" required="" name="message" rows="5" class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">                                </textarea>
                            </div>

{{--                            <div class="mb-6 flex items-center space-x-2">--}}
{{--                                <input type="checkbox" id="contact-form-consent-input" name="agree-to-terms" class="h-5 w-5 self-start rounded border-jacarta-200 text-jacarta-700 checked:bg-[#D0AA49] focus:ring-accent/20 focus:ring-offset-0 dark:border-jacarta-500 dark:bg-jacarta-600">--}}
{{--                                <label for="contact-form-consent-input" class="text-sm dark:text-jacarta-200">--}}
{{--                                    <a href="#" class="text-[#D0AA49] underline">KVKK maddelerini</a> onaylıyorum                                </label>--}}
{{--                            </div>--}}

                            <button type="submit" id="contact-form-submit" class="rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]">
                                {!! __('velocity::app-static.contact-us.contact-form.send') !!}
                            </button>

                            <div id="contact-form-notice" class="relative mt-4 hidden rounded-lg border border-transparent p-4"></div>
                        </form>
                    </div>

                    <div class="lg:w-1/3 lg:pl-5">
                        <h2 class="mb-4 font-display text-xl text-jacarta-700 dark:text-white text-center">
                            {!! __('velocity::app-gold.contact_page.contact_info_title') !!}
                        </h2>
                        <div class="rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700">
                            <div class="mb-6 flex items-center space-x-5">
                                <span class="flex h-11 w-11 shrink-0 items-center justify-center rounded-full border border-jacarta-100 bg-light-base dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="fill-[#D0AA49]">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M9.366 10.682a10.556 10.556 0 0 0 3.952 3.952l.884-1.238a1 1 0 0 1 1.294-.296 11.422 11.422 0 0 0 4.583 1.364 1 1 0 0 1 .921.997v4.462a1 1 0 0 1-.898.995c-.53.055-1.064.082-1.602.082C9.94 21 3 14.06 3 5.5c0-.538.027-1.072.082-1.602A1 1 0 0 1 4.077 3h4.462a1 1 0 0 1 .997.921A11.422 11.422 0 0 0 10.9 8.504a1 1 0 0 1-.296 1.294l-1.238.884zm-2.522-.657l1.9-1.357A13.41 13.41 0 0 1 7.647 5H5.01c-.006.166-.009.333-.009.5C5 12.956 11.044 19 18.5 19c.167 0 .334-.003.5-.01v-2.637a13.41 13.41 0 0 1-3.668-1.097l-1.357 1.9a12.442 12.442 0 0 1-1.588-.75l-.058-.033a12.556 12.556 0 0 1-4.702-4.702l-.033-.058a12.442 12.442 0 0 1-.75-1.588z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <span class="block font-display text-base text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-gold.contact_page.contact_phone_label') !!}
                                    </span>
                                    <a href="tel:+4917692992455" class="text-sm hover:text-accent dark:text-jacarta-300">+49 176 92992455</a>
                                </div>
                            </div>
                            <div class="mb-6 flex items-center space-x-5">
                                <span class="flex h-11 w-11 shrink-0 items-center justify-center rounded-full border border-jacarta-100 bg-light-base dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="fill-[#D0AA49]">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M12 20.9l4.95-4.95a7 7 0 1 0-9.9 0L12 20.9zm0 2.828l-6.364-6.364a9 9 0 1 1 12.728 0L12 23.728zM12 13a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a4 4 0 1 1 0-8 4 4 0 0 1 0 8z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <span class="block font-display text-base text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-gold.contact_page.contact_address_label') !!}
                                    </span>
                                    <address class="text-sm not-italic dark:text-jacarta-300">
                                        FeWo GmbH <br>
                                        Speditionsstr. 15a <br>
                                        40221 Düsseldorf
                                    </address>
                                </div>
                            </div>
                            <div class="flex items-center space-x-5">
                                <span class="flex h-11 w-11 shrink-0 items-center justify-center rounded-full border border-jacarta-100 bg-light-base dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="fill-[#D0AA49]">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M2.243 6.854L11.49 1.31a1 1 0 0 1 1.029 0l9.238 5.545a.5.5 0 0 1 .243.429V20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.283a.5.5 0 0 1 .243-.429zM4 8.133V19h16V8.132l-7.996-4.8L4 8.132zm8.06 5.565l5.296-4.463 1.288 1.53-6.57 5.537-6.71-5.53 1.272-1.544 5.424 4.47z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <span class="block font-display text-base text-jacarta-700 dark:text-white">
                                        {!! __('velocity::app-gold.contact_page.contact_email_label') !!}
                                    </span>
                                    <a href="mailto:<EMAIL>" class="text-sm not-italic hover:text-accent dark:text-jacarta-300">
                                        <EMAIL>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>


@endsection