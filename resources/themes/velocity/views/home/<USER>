@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')
    <main>
        <section class="relative pt-32 pb-24">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="container">
                <div class="mx-auto max-w-5xl text-center lg:mt-16">
                    <h1 class="mb-6 text-center font-display text-4xl font-medium text-jacarta-700 dark:text-white">
                        {!! __('velocity::app-gold.physical_delivery_title') !!}
                    </h1>
                    <p class="mb-3 text-lg leading-normal dark:text-jacarta-200 text-left">
                        <img src="/miligold/img/about-us.png" class="md:max-w-lg float-right rounded-lg md:pl-2">
                        {!! __('velocity::app-gold.physical_delivery_intro') !!}
                    </p>
                    <p class="mb-3 text-lg leading-normal dark:text-jacarta-200 text-left">
                        {!! __('velocity::app-gold.physical_delivery_accumulation') !!}
                    </p>
                    <p class="mb-3 text-lg leading-normal dark:text-jacarta-200 text-left">
                        {!! __('velocity::app-gold.physical_delivery_process_title') !!}
                    </p>
                    <div class="mb-3 text-lg leading-normal dark:text-jacarta-200 text-left">
                        <p class="mb-2">{!! __('velocity::app-gold.physical_delivery_step_1') !!}</p>
                        <p class="mb-2">{!! __('velocity::app-gold.physical_delivery_step_2') !!}</p>
                        <p class="mb-2">{!! __('velocity::app-gold.physical_delivery_step_3') !!}</p>
                        <p class="mb-2">{!! __('velocity::app-gold.physical_delivery_step_4') !!}</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush

