@inject ('toolbarHelper', 'Webkul\Product\Helpers\Toolbar')
@inject ('productRepository', 'Webkul\Product\Repositories\ProductRepository')

@extends('shop::layouts.master')

@section('page_title')
    {{ $meta['title'] ?? 'All Products' }}
@stop

@section('seo')
    <meta name="description" content="{{ $meta['description'] ?? 'All Products' }}"/>
    <meta name="keywords" content="{{ $meta['keywords'] ?? '' }}"/>

    @if (core()->getConfigData('catalog.rich_snippets.categories.enable'))
        <script type="application/ld+json">
            {!! app('Webkul\Product\Helpers\SEO')->getCategoryJsonLd($category) !!}
        </script>
    @endif
@stop

@push('css')
    <style type="text/css">
        .product-price span:first-child, .product-price span:last-child {
            font-size: 0.75rem;
            font-weight: bold;        }

        @media only screen and (max-width: 992px) {
            .main-content-wrapper .vc-header {
                box-shadow: unset;
            }
        }

        .sort-by {
            display: flex;
            align-items: center;
            justify-content: end;
            margin: 16px 0;
        }

        .sort-by select {
            background: #f2f2f2;
            border-radius: 17px;
            padding: 8px 16px;
            margin-left: 8px;
        }
    </style>
@endpush

@php
    $isProductsDisplayMode = 'products_and_description';

    $isDescriptionDisplayMode = 'products_and_description';
@endphp

@section('content-wrapper')
    <category-component></category-component>
@stop

@push('scripts')
    <script type="text/x-template" id="category-template">
        <div class="container">
            <section class="row col-12 velocity-divide-page category-page-wrapper">
                {!! view_render_event('bagisto.shop.productOrCategory.index.before', ['category' => 'All Products']) !!}

                @include ('shop::products.list.layered-navigation')

                <div class="category-container right">
                    {{--                <div class="row remove-padding-margin">
                                        <div class="pl0 col-12">--}}
                    {{--                        <h2 class="fw6 mb10">{{ $category->name }}</h2>--}}

                    {{--                        @if ($isDescriptionDisplayMode)--}}
                    {{--                            @if ($category->description)--}}
                    {{--                                <div class="category-description">--}}
                    {{--                                    {!! $category->description !!}--}}
                    {{--                                </div>--}}
                    {{--                            @endif--}}
                    {{--                        @endif--}}
                    {{--                    </div>--}}

                    {{--                    <div class="col-12 no-padding">--}}
                    {{--                        <div class="hero-image">--}}
                    {{--                            @if (!is_null($category->image))--}}
                    {{--                                <img class="logo" src="{{ $category->image_url }}" alt="" width="20" height="20" />--}}
                    {{--                            @endif--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                    {{--                </div>--}}

                    @if ($isProductsDisplayMode)
                        <div class="hidden filters-container">
                            <template v-if="products.length >= 0">
                                @include ('shop::products.list.toolbar')
                            </template>
                        </div>

                        <div
                            class="category-block"
                            style="width: 100%">

                            <div class="flex items-center justify-between">
                                <div></div>
                                <div class="sort-by">
                                    <label for="sort">Sort by:</label>
                                    <select v-model="selectedSort" @change="handleSortBy">
                                        <option value="price_lth">Price: Low to High</option>
                                        <option value="price_htl">Price: High to Low</option>
                                        <option value="created_at">Newest First</option>
                                        <option value="name" selected>Name: A to Z</option>
                                        <option value="name_desc">Name: Z to A</option>
                                        <option value="unit_price_lth">Unit Price: Low to High</option>
                                        <option value="unit_price_htl">Unit Price: High to Low</option>
                                    </select>
                                </div>
                            </div>

                            <shimmer-component v-if="isLoading" shimmer-count="4"></shimmer-component>

                            <template v-else-if="products.length > 0">
                                @if ($toolbarHelper->getCurrentMode() == 'grid')
                                    <div class="grid grid-cols-1 md:grid-col-2 lg:grid-cols-4 remove-padding-margin justify-items-center" >
                                        <product-card
                                            :key="index"
                                            :product="product"
                                            v-for="(product, index) in products"
                                            amount="{{ __('velocity::app-static.user-profile.amount-vue')}}"
                                            unit-price="{{ __('velocity::app-static.products.unit-price') }}">
                                        </product-card>
                                    </div>
                                @else
                                    <div class="product-list">
                                        <product-card
                                            list=true
                                            :key="index"
                                            :product="product"
                                            v-for="(product, index) in products"
                                            amount="{{ __('velocity::app-static.user-profile.amount-vue')}}"
                                            unit-price="{{ __('velocity::app-static.products.unit-price') }}">
                                        </product-card>
                                    </div>
                                @endif

                                {!! view_render_event('bagisto.shop.productOrCategory.index.pagination.before', ['category' => 'All Products']) !!}

                                <div class="bottom-toolbar pagination justify-center" v-html="paginationHTML"></div>

                                {!! view_render_event('bagisto.shop.productOrCategory.index.pagination.after', ['category' => 'All Products']) !!}
                            </template>

                            <div class="product-list empty" v-else>
                                <div class="empty">
                                    <div class="pt-10 pb-14 rounded-50p mt-5 flex flex-col justify-center items-center space-y-7">
                                        <div class="p-5 flex justify-center rounded-2xl">
                                            <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 184.94 184.94" width="100" height="100">
                                                <circle  cx="92.47" cy="92.47" r="92.47" fill="#b6a131" opacity=".35" stroke-width="0"/>
                                                <g id="inWt4G.tif">
                                                    <g>
                                                        <path fill="#2d4921" d="M138.23,156.4H46.72c-.07-.05-.14-.12-.22-.14-4.82-1.25-6.7-3.69-6.7-8.69,0-17.66,0-35.32,0-52.97,0-.51,0-1.03,0-1.83-.64.54-1.04.87-1.43,1.2-1.08.93-2.1,1.94-3.24,2.79-2.97,2.23-7.06,1.81-9.41-.92-2.48-2.87-2.31-6.99.43-9.66.83-.81,1.71-1.56,2.57-2.34,19.43-17.6,38.85-35.21,58.29-52.8,3.72-3.37,7.16-3.3,10.87.05,19.77,17.88,39.55,35.76,59.35,53.62,1.61,1.45,3.09,2.93,3.75,5.06v2.94c-.06.06-.17.1-.19.17-1.5,5.81-8.47,6.89-12.1,3.05-1.04-1.1-2.29-2.01-3.67-3.21v1.79c0,17.61,0,35.22,0,52.83,0,.67,0,1.34-.05,2-.26,3.4-2.63,6.07-6,6.79-.25.05-.48.18-.72.27ZM92.4,77.66c-18.69,0-33.69,14.96-33.75,33.69-.06,18.62,15.06,33.81,33.66,33.82,18.69,0,33.9-15.17,33.87-33.8-.02-18.62-15.14-33.7-33.79-33.7Z"/>
                                                        <path fill="#2d4921" d="M96.92,104c-.14,4.1-.29,8.19-.43,12.29-.08,2.32-1.79,4.11-4.01,4.21-2.16.09-4.14-1.67-4.23-3.97-.31-8.19-.59-16.39-.82-24.58-.07-2.58,1.81-4.59,4.35-4.92,2.4-.31,4.62,1.13,5.31,3.51.24.81.2,1.48.19,1.71-.11,2.01-.22,6.18-.36,11.76Z"/>
                                                        <path fill="#2d4921" d="M92.39,135.79c-3.22,0-5.34-1.99-5.34-5.02,0-3,2.17-5.11,5.29-5.12,3.17-.02,5.47,2.14,5.45,5.13-.01,2.95-2.22,5-5.4,5.01Z"/>
                                                    </g>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="font-terramirum font-bold text-2xl text-center">{{ __('shop::app.products.whoops') }}</div>
                                        <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">{{ __('shop::app.products.empty') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                @include('shop::home.new-products')

                {!! view_render_event('bagisto.shop.productOrCategory.index.after', ['category' => 'All Products']) !!}
            </section>
        </div>
    </script>

    <script>
        Vue.component('category-component', {
            template: '#category-template',

            data: function () {
                return {
                    'products': [],
                    'isLoading': true,
                    'paginationHTML': '',
                    'selectedSort': '{{ request()->get('sort') ?? 'name' }}'
                }
            },

            created: function () {
                this.getCategoryProducts();
            },

            methods: {
                'getCategoryProducts': function () {
                    this.$http.get(`${this.$root.baseUrl}/category-products/all${window.location.search}`)
                        .then(response => {
                            this.isLoading = false;
                            this.products = response.data.products;
                            this.paginationHTML = response.data.paginationHTML;
                        })
                        .catch(error => {
                            this.isLoading = false;
                            console.log(this.__('error.something_went_wrong'));
                        })
                },
                handleSortBy() {
                    let otherParams = '';
                    new URLSearchParams(window.location.search).forEach((value, key) => {
                        if (key !== 'sort') {
                            otherParams += `&${key}=${value}`;
                        }
                    });

                    window.location.href = `${window.location.pathname}?sort=${this.selectedSort}${otherParams}`;
                }
            }
        })
    </script>
@endpush