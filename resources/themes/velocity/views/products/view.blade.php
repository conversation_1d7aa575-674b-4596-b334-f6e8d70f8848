@extends('shop::layouts.miligold-default')

@inject ('reviewHelper', 'Webkul\Product\Helpers\Review')
@inject ('customHelper', 'Webkul\Velocity\Helpers\Helper')

@php
    $total = $reviewHelper->getTotalReviews($product);

    $avgRatings = $reviewHelper->getAverageRating($product);
    $avgStarRating = round($avgRatings);

    $productImages = [];
    $images = productimage()->getGalleryImages($product);

    foreach ($images as $key => $image) {
        array_push($productImages, $image['medium_image_url']);
    }
@endphp

@section('page_title')
    {{ trim($product->meta_title) != "" ? $product->meta_title : $product->name }}
@stop

@section('seo')
    <meta name="description"
          content="{{ trim($product->meta_description) != "" ? $product->meta_description : \Illuminate\Support\Str::limit(strip_tags($product->description), 120, '') }}"/>

    <meta name="keywords" content="{{ $product->meta_keywords }}"/>

    @if (core()->getConfigData('catalog.rich_snippets.products.enable'))
        <script type="application/ld+json">
            {!! app('Webkul\Product\Helpers\SEO')->getProductJsonLd($product) !!}
        </script>
    @endif

    <?php $productBaseImage = productimage()->getProductBaseImage($product, $images); ?>

    <meta name="twitter:card" content="summary_large_image"/>

    <meta name="twitter:title" content="{{ $product->name }}"/>

    <meta name="twitter:description" content="{{ $product->description }}"/>

    <meta name="twitter:image:alt" content=""/>

    <meta name="twitter:image" content="{{ $productBaseImage['medium_image_url'] }}"/>

    <meta property="og:type" content="og:product"/>

    <meta property="og:title" content="{{ $product->name }}"/>

    <meta property="og:image" content="{{ $productBaseImage['medium_image_url'] }}"/>

    <meta property="og:description" content="{{ $product->description }}"/>

    <meta property="og:url" content="{{ route('shop.productOrCategory.index', $product->url_key) }}"/>
@stop

@section('body')
    <main class="mt-24">
        <!-- Item -->
        <section class="relative pt-12 pb-24 lg:py-24">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="container">
                <!-- Item -->
                <div class="md:flex md:flex-wrap items-center">
                    <!-- Image -->
                    <figure class="mb-8 md:mb-0 md:w-2/5 md:flex-shrink-0 md:flex-grow-0 md:basis-auto lg:w-2/6">
                        <picture>
{{--                            <source srcset="/miligold/img/miligram-coin.png" type="image/webp">--}}
                            <source srcset="/miligold/img/miligram-coin.png" type="image/png">
                            <img src="/miligold/img/miligram-coin.png" alt="Milyem Coin" class="rounded-2.5xl transition-transform duration-500 animate-reverse-spin-slow">
                        </picture>
                    </figure>



                    <!-- Details -->
                    <div class="md:w-3/5 md:basis-auto lg:w-4/6 lg:pl-[3.75rem]">
                        <!-- Collection / Likes / Actions -->
                        <div class="mb-8 flex items-start">
                            <picture>
{{--                                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/milyem-text-dark.webp" type="image/webp">--}}
                                <source srcset="/miligold/img/miligram-text.png" type="image/png">
                                <img src="/miligold/img/miligram-text.png" alt="Miligold Text" class="max-h-12">
                            </picture>
                            <div class="ml-auto flex space-x-2">
                                <div class="flex items-center space-x-1 rounded-xl border border-jacarta-100 bg-white py-2 px-4 dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <span class="js-likes relative cursor-pointer before:absolute before:h-4 before:w-4 before:bg-[url('../img/heart-fill.svg')] before:bg-cover before:bg-center before:bg-no-repeat before:opacity-0" data-tippy-content="Favorite">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="h-4 w-4 fill-jacarta-500 hover:fill-red dark:fill-jacarta-200 dark:hover:fill-red">
                                            <path fill="none" d="M0 0H24V24H0z"></path>
                                            <path d="M12.001 4.529c2.349-2.109 5.979-2.039 8.242.228 2.262 2.268 2.34 5.88.236 8.236l-8.48 8.492-8.478-8.492c-2.104-2.356-2.025-5.974.236-8.236 2.265-2.264 5.888-2.34 8.244-.228zm6.826 1.641c-1.5-1.502-3.92-1.563-5.49-.153l-1.335 1.198-1.336-1.197c-1.575-1.412-3.99-1.35-5.494.154-1.49 1.49-1.565 3.875-.192 5.451L12 18.654l7.02-7.03c1.374-1.577 1.299-3.959-.193-5.454z"></path>
                                        </svg>
                                    </span>
                                    <span class="text-sm dark:text-jacarta-200">188</span>
                                </div>
                            </div>
                        </div>
                        <product-detail></product-detail>

                        <!-- Bid -->
                        <div class="rounded-2lg border-2 border-jacarta-100 bg-white p-8 dark:border-jacarta-600 dark:bg-jacarta-700">
                                <!-- Highest bid -->
                                <product-view>
                                </product-view>

                        </div>
                        <!-- end bid -->
                    </div>
                    <!-- end details -->
                </div>

                <!-- Tabs -->
                <div class="scrollbar-custom mt-14 overflow-x-auto rounded-lg">
                    <div class="min-w-fit">
                        <!-- Tabs Nav -->
                        <ul class="nav nav-tabs flex items-center justify-center" role="tablist">

                            <!-- Properties -->
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active relative flex items-center whitespace-nowrap py-3 px-6 text-jacarta-400 hover:text-jacarta-700 dark:hover:text-white" id="properties-tab" data-bs-toggle="tab" data-bs-target="#properties" type="button" role="tab" aria-controls="properties" aria-selected="true">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="mr-1 h-8 w-8 fill-current">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M11.95 7.95l-1.414 1.414L8 6.828 8 20H6V6.828L3.465 9.364 2.05 7.95 7 3l4.95 4.95zm10 8.1L17 21l-4.95-4.95 1.414-1.414 2.537 2.536L16 4h2v13.172l2.536-2.536 1.414 1.414z"></path>
                                    </svg>
                                    <span class="font-display text-lg font-medium">{!! __('velocity::app-gold.milyem_page.markets') !!}</span>
                                </button>
                            </li>

                            <!-- Details -->
                            <li class="nav-item" role="presentation">
                                <button class="nav-link relative flex items-center whitespace-nowrap py-3 px-6 text-jacarta-400 hover:text-jacarta-700 dark:hover:text-white" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="false" tabindex="-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="mr-1 h-8 w-8 fill-current">
                                        <path fill="none" d="M0 0h24v24H0z"></path>
                                        <path d="M20 22H4a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1zm-1-2V4H5v16h14zM7 6h4v4H7V6zm0 6h10v2H7v-2zm0 4h10v2H7v-2zm6-9h4v2h-4V7z"></path>
                                    </svg>
                                    <span class="font-display text-lg font-medium">{!! __('velocity::app-gold.milyem_page.details') !!}</span>
                                </button>
                            </li>

                            <!-- Price History -->
                            <li class="nav-item" role="presentation">
                                <button class="nav-link relative flex items-center whitespace-nowrap py-3 px-6 text-jacarta-400 hover:text-jacarta-700 dark:hover:text-white" id="price-history-tab" data-bs-toggle="tab" data-bs-target="#price-history" type="button" role="tab" aria-controls="price-history" aria-selected="false" tabindex="-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="mr-1 h-8 w-8 fill-current">
                                        <path fill="none" d="M0 0H24V24H0z"></path>
                                        <path d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z"></path>
                                    </svg>
                                    <span class="font-display text-lg font-medium">{!! __('velocity::app-gold.milyem_page.price_history') !!}</span>
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Properties -->
                            <div class="tab-pane fade show active" id="properties" role="tabpanel" aria-labelledby="properties-tab">
                                <div class="rounded-t-2lg rounded-b-2lg border border-jacarta-100 bg-white p-6 dark:border-jacarta-600 dark:bg-jacarta-700 md:p-10">
                                    <div class="grid grid-cols-2 gap-5 sm:grid-cols-3 md:grid-cols-4">
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">miliGRAM</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Alış = 0,081 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">miliGRAM</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Satış = 0,079 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">{!! __('velocity::app-gold.ALTIN') !!}</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Alış = 81,00 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">{!! __('velocity::app-gold.ALTIN') !!}</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Satış = 79,00 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">{!! __('velocity::app-gold.GUMUSTRY') !!}</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Alış = 0.90 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">{!! __('velocity::app-gold.GUMUSTRY') !!}</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Satış = 1.40 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">Platin</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Alış = 28,46 €</span>
                                        </div>
                                        <div class="flex flex-col space-y-2 rounded-2lg border border-milyem bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                                            <span class="text-lg uppercase text-milyem font-bold">Platin</span>
                                            <span class="text-base text-jacarta-700 dark:text-white">Satış = 38,95 €</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="tab-pane fade" id="details" role="tabpanel" aria-labelledby="details-tab">
                                <div class="rounded-t-2lg rounded-b-2lg border border-jacarta-100 bg-white p-6 dark:border-jacarta-600 dark:bg-jacarta-700 md:p-10">
                                    <div class="text-lg uppercase text-milyem font-bold mb-8">
                                        {!! __('velocity::app-gold.whats_milyem_gold') !!}
                                    </div>
                                    <p class="text-base text-jacarta-500 mb-4">
                                        {!! __('velocity::app-gold.whats_milyem_gold_description_1') !!}
                                    </p>
                                    <p class="text-base text-jacarta-500 mb-4">
                                        {!! __('velocity::app-gold.whats_milyem_gold_description_2') !!}
                                    </p>
                                    <p class="text-base text-jacarta-500 mb-4">
                                        {!! __('velocity::app-gold.whats_milyem_gold_description_3') !!}
                                    </p>
                                    <p class="text-base text-jacarta-500 mb-4">
                                        {!! __('velocity::app-gold.whats_milyem_gold_description_4') !!}
                                    </p>
                                    <p class="text-base text-jacarta-500 mb-4">
                                        {!! __('velocity::app-gold.whats_milyem_gold_description_5') !!}
                                    </p>
                                </div>
                            </div>
                            <!-- Price History -->
                            <div class="tab-pane fade" id="price-history" role="tabpanel" aria-labelledby="price-history-tab">
                                <div class="rounded-t-2lg rounded-b-2lg border border-jacarta-100 bg-white p-6 dark:border-jacarta-600 dark:bg-jacarta-700">
                                    <!-- Period / Stats -->
                                    <div class="mb-10 flex flex-wrap items-center">
                                        <!--                      <select-->
                                        <!--                        class="mr-8 min-w-[12rem] rounded-lg border-jacarta-100 py-3.5 text-sm dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white"-->
                                        <!--                      >-->
                                        <!--                        <option value="7-days">Last 7 Days</option>-->
                                        <!--                        <option value="14-days">Last 14 Days</option>-->
                                        <!--                        <option value="30-days">Last 30 Days</option>-->
                                        <!--                        <option value="60-days">Last 60 Days</option>-->
                                        <!--                        <option value="90-days" selected>Last 90 Days</option>-->
                                        <!--                        <option value="last-year">Last Year</option>-->
                                        <!--                        <option value="all-time">All Time</option>-->
                                        <!--                      </select>-->
                                    </div>

                                    <!-- Chart -->
                                    <div class="chart-container relative h-80 w-full">
                                        <canvas id="activityChart" style="display: block; box-sizing: border-box; height: 0px; width: 0px;" height="0" width="0"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- end tab content -->
                    </div>
                </div>
                <!-- end tabs -->
            </div>
        </section>
    </main>
@endsection

@push('scripts')
    <style>
        .public-sale.section-title h3.title::after, .public-sale.section-title h3.title::before{
            background:#12D176;
        }
    </style>
    <script type="text/javascript" src="{{ asset('vendor/webkul/ui/assets/js/ui.js') }}"></script>

    <script type="text/javascript" src="{{ asset('themes/velocity/assets/js/jquery-ez-plus.js') }}"></script>

    <script type='text/javascript' src='https://unpkg.com/spritespin@4.1.0/release/spritespin.js'></script>
    <script type="text/x-template" id="product-detail-template">
        <div class="mb-5">
            <p v-if="isExpanded" v-html="text"></p>
            <p v-else>@{{ previewText }}...
                <button v-if="!isExpanded" @click="isExpanded = true" class="text-milyem underline">Daha Fazla</button>
            </p>

        </div>
    </script>

    <script>
        Vue.component('ProductDetail', {
            template: '#product-detail-template',
            data() {
                return {
                    isExpanded: false,
                    text: ` {!! __('velocity::app-gold.milyem_page.description') !!}`,
                };
            },
            computed: {
                previewText() {
                    return this.text.substring(0, Math.floor(this.text.length / 2));
                }
            }
        });
    </script>

    <script type="text/x-template" id="product-view-template">
        <form
            class="sm:flex sm:flex-wrap"
            method="POST"
            id="product-form"
            @click="onSubmit($event)"
            @submit.enter.prevent="onSubmit($event)"
            action="{{ route('fast-order.summary') }}"
        >
            @csrf
            <input type="hidden" name="type" value="buy">
            <input type="hidden" name="product_id" value="{{ $product->product_id }}">

            <div class="sm:w-7/12 sm:pr-4 lg:pr-8">
            <div class="flex flex-row justify-between space-x-2 rounded-2lg border border-milyem bg-light-base px-5 py-3 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                <picture>
{{--                    <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/milyem.webp" type="image/webp">--}}
                    <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                    <img src="/miligold/img/mili-ikon.png" alt="miliGRAM Coin" class="max-w-[100px]">
                </picture>
                <div class="flex justify-around flex-col">
                    <span class="text-lg text-jacarta-700 dark:text-white">
                        <b>miliGRAM ({!! __('velocity::app-gold.profile.buy') !!})</b>
                        = @{{ cart.totalPrice }}
                    </span>
                    <span class="text-lg text-jacarta-700 dark:text-white">
                        <b>miliGRAM ({!! __('velocity::app-gold.profile.sell') !!})</b>
                        = @{{ cart.totalCost }}
                    </span>
                </div>
            </div>
            <div class="relative mt-6">
                <button class=" absolute top-2 left-2 rounded-lg bg-milyem px-6 py-2 font-display text-2xl text-white hover:opacity-90" @click="decrementCount">
                    -
                </button>
                <input name="count" required v-model="cart.count" @input="calculatePrice" type="number" placeholder="Adet" class="w-full text-xl rounded-lg border-2 border-jacarta-200 py-4 px-4 focus:ring-milyem text-center">
                <button class=" absolute top-2 right-2 rounded-lg bg-milyem px-6 py-2 font-display text-2xl text-white hover:opacity-90" @click="incrementCount">
                    +
                </button>
            </div>
        </div>
        <div class="sm:w-5/12 sm:pl-4 lg:pl-8">
            <div class="flex flex-wrap space-y-4 w-full">
                <button @click="buySubmit" type="submit" class="w-full text-center cursor-pointer group rounded-2.5xl border border-[#caa754] bg-white py-3.5 px-3 2xl:px-5 transition-shadow hover:shadow-lg hover:bg-milyem font-bold text-lg 2xl:text-xl text-jacarta-700 whitespace-nowrap hover:text-white">
                       {!! __('velocity::app-gold.profile.buy') !!} (@{{ product.name }})
                </button>

                <button @click="sellSubmit" type="submit" class="w-full text-center cursor-pointer group rounded-2.5xl border border-[#caa754] bg-white py-3.5 px-3 2xl:px-5 transition-shadow hover:shadow-lg hover:bg-milyem font-bold text-lg 2xl:text-xl text-jacarta-700 whitespace-nowrap hover:text-white">
                   {!! __('velocity::app-gold.profile.sell') !!} (@{{ product.name }})
                </button>

                <a href="#" class="w-full text-center cursor-pointer group rounded-2.5xl border border-[#caa754] bg-white py-3.5 px-3 2xl:px-5 transition-shadow hover:shadow-lg dark:border-transparent dark:bg-jacarta-700 hover:bg-milyem">
                    <span class="font-bold text-lg 2xl:text-xl text-jacarta-700 dark:text-white whitespace-nowrap group-hover:text-white">
                        {!! __('velocity::app-gold.profile.convert_to_physical_gold') !!}
                    </span>
                </a>
            </div>
        </div>
            <slot v-if="slot"></slot>

            <div class="d-none" id="product-star">
                @include ('shop::products.add-to-cart', [
                'form' => false,
                'product' => $product,
                'showCartIcon' => false,
                'showWishlist' => false,
                'showCompare' =>  false,
                ])
            </div>
        </form>
    </script>
    <script>
        Vue.component('product-view', {
            inject: ['$validator'],
            template: '#product-view-template',
            data: function () {
                return {
                    slot: true,
                    is_buy_now: 0,
                    product: {
                        name: '{{ $product->name }}',
                    },
                    cart: {
                        count: 1,
                        price: '{{ $product->getTypeInstance()->getMinimalPrice() }}',
                        cost: '{{$product->cost}}' ,
                        totalPrice: '{{ $product->getTypeInstance()->getMinimalPrice() }}',
                        totalCost: '{{$product->cost}}',
                    },
                    checkout: {
                        checkoutURL: '{{ route('shop.checkout.onepage.index') }}',
                    },
                    isCustomer: '{{ auth()->guard('customer')->user() ? true : false }}',
                    isLoading: false,
                    formattedPrice:0,
                    perPrice:0
                }
            },

            mounted: function () {
                this.perPrice =
                    new Intl.NumberFormat('de-DE', {
                        style: 'currency',
                        currency: 'EUR',
                        minimumFractionDigits: 4
                    }).format((this.cart.price * this.cart.count).toFixed(4));

                this.calculatePrice();

                let currentProductId = '{{ $product->url_key }}';
                let existingViewed = window.localStorage.getItem('recentlyViewed');

                if (!existingViewed) {
                    existingViewed = [];
                } else {
                    existingViewed = JSON.parse(existingViewed);
                }

                if (existingViewed.indexOf(currentProductId) == -1) {
                    existingViewed.push(currentProductId);

                    if (existingViewed.length > 3)
                        existingViewed = existingViewed.slice(Math.max(existingViewed.length - 4, 1));

                    window.localStorage.setItem('recentlyViewed', JSON.stringify(existingViewed));
                } else {
                    var uniqueNames = [];

                    $.each(existingViewed, function (i, el) {
                        if ($.inArray(el, uniqueNames) === -1) uniqueNames.push(el);
                    });

                    uniqueNames.push(currentProductId);

                    uniqueNames.splice(uniqueNames.indexOf(currentProductId), 1);

                    window.localStorage.setItem('recentlyViewed', JSON.stringify(uniqueNames));
                }
            },

            methods: {
                incrementCount: function() {
                    this.cart.count++;
                    this.calculatePrice();
                },
                decrementCount: function() {
                    if (this.cart.count > 1) {
                        this.cart.count--;
                        this.calculatePrice();
                    }
                },
                calculatePrice: function() {
                    formattedPrice = new Intl.NumberFormat('de-DE', {
                        style: 'currency',
                        currency: 'EUR',
                        minimumFractionDigits: 4
                    }).format((this.cart.price * this.cart.count).toFixed(4));

                    formattedCost = new Intl.NumberFormat('de-DE', {
                        style: 'currency',
                        currency: 'EUR',
                        minimumFractionDigits: 4
                    }).format((this.cart.cost * this.cart.count).toFixed(4));

                    this.cart.totalCost = formattedCost;
                    this.cart.totalPrice = formattedPrice;
                },
                getPaymentMethods: async function() {
                    try {
                        this.isLoading = true;
                        const response = await axios.get('{{ route('shop.checkout.payment-methods') }}');
                        this.cart.paymentMethods = response.data.paymentMethods;
                        this.cart.selectedPaymentMethod = response.data.paymentMethods[1].method;
                        console.log(response.data.paymentMethods);
                    } catch (error) {
                        console.error('Payment methods could not be retrieved:', error);
                    } finally {
                        this.isLoading = false;
                    }
                },
                setPaymentMethod: function(value) {
                    this.cart.selectedPaymentMethod = value;
                    let self = this;
                    if (self.isCustomer) {
                        this.$http.post("{{ route('shop.checkout.save-payment') }}", {'payment': value})
                            .then(response => {
                                console.log('Payment method:', response.data);
                            })
                            .then(() => {
                                axios.post("{{ route('shop.checkout.save-order') }}", {'_token': "{{ csrf_token() }}"})
                                    .then(function(response) {
                                        {{--if (response.data.success) {--}}
                                            {{--    if (response.data.redirect_url) {--}}
                                            {{--        self.checkout.checkoutURL = response.data.redirect_url;--}}
                                            {{--        console.log(self.checkout.checkoutURL);--}}
                                            {{--    } else {--}}
                                            {{--        window.location.href = "{{ route('shop.checkout.onepage.index') }}";--}}
                                            {{--    }--}}
                                            {{--}--}}
                                            window.location.href = "{{ route('shop.checkout.onepage.index') }}";
                                    })
                                    .catch(function (error) {
                                        console.error('Product view:', error);
                                    })
                            })
                            .catch(error => {
                                console.error('Product view:', error);
                            });
                    } else {
                        window.location.href = "{{ route('customer.session.index') }}";
                    }
                },
                onSubmit: function (event) {
                    if (event.target.getAttribute('type') != 'submit')
                        return;

                    event.preventDefault();

                    setTimeout(function () {
                        document.getElementById('product-form').submit();
                    }, 0);
                },
                buySubmit: function () {
                    document.querySelector('[name="type"]').value = 'buy';

                    this.onSubmit(event);
                },
                sellSubmit: function () {
                    document.querySelector('[name="type"]').value = 'sell';

                    this.onSubmit(event);
                },
                activateAutoScroll: function (event) {

                    /**
                     * This is normal Element
                     */
                    const normalElement = document.querySelector(
                        '.control-error:first-of-type'
                    );

                    /**
                     * Scroll Config
                     */
                    const scrollConfig = {
                        behavior: 'smooth',
                        block: 'end',
                        inline: 'nearest',
                    }

                    if (normalElement) {
                        normalElement.scrollIntoView(scrollConfig);
                        return;
                    }
                }
            }
        });

        Vue.component('timers-counter', {
            template: '#timers-counter-template',
            props: {
                timerNoneTitle: String,
                timerCountdowns:String,
                timerStyleTitle: String,
            },
            data() {
                return {
                    countdowns: this.timerCountdowns,
                    timers: {
                        days: null,
                        hours: null,
                        minutes: null,
                        seconds: null
                    },
                    countdownStop: null,
                }
            },
            mounted() {
                this.functionTimer();
                this.countdownStop = setInterval(() => {
                    this.functionTimer();
                }, 1000)
            },
            methods: {
                functionTimer: function() {
                    var siki = new Date().getTime();
                    var dtStr = this.countdowns;
                    var date = new Date(dtStr).getTime();
                    var sels = date - siki;
                    this.timers.days = Math.floor(sels / (1000 * 60 * 60 * 24)).toString().padStart(2, '0');
                    this.timers.hours = Math.floor((sels % (1000 * 60 * 60 * 18)) / (1000 * 60 * 60)).toString().padStart(2, '0');
                    this.timers.minutes = Math.floor((sels % (1000 * 60 * 40)) / (1000 * 60)).toString().padStart(2, '0');
                    this.timers.seconds = Math.floor((sels % (1000 * 60)) / 1000).toString().padStart(2, '0');
                    if (sels < 0) {
                        this.timers.days = 0;
                        this.timers.hours = 0;
                        this.timers.minutes = 0;
                        this.timers.seconds = 0;
                    }
                    if (sels < 0) {
                        clearInterval(this.countdownStop);
                    }
                }
            }
        });

        window.onload = function () {
            var thumbList = document.getElementsByClassName('thumb-list')[0];
            var thumbFrame = document.getElementsByClassName('thumb-frame');
            var productHeroImage = document.getElementsByClassName('product-hero-image')[0];

            if (thumbList && productHeroImage) {
                for (let i = 0; i < thumbFrame.length; i++) {
                    thumbFrame[i].style.height = (productHeroImage.offsetHeight / 4) + "px";
                    thumbFrame[i].style.width = (productHeroImage.offsetHeight / 4) + "px";
                }

                if (screen.width > 720) {
                    thumbList.style.width = (productHeroImage.offsetHeight / 4) + "px";
                    thumbList.style.minWidth = (productHeroImage.offsetHeight / 4) + "px";
                    thumbList.style.height = productHeroImage.offsetHeight + "px";
                }
            }

            window.onresize = function () {
                if (thumbList && productHeroImage) {

                    for (let i = 0; i < thumbFrame.length; i++) {
                        thumbFrame[i].style.height = (productHeroImage.offsetHeight / 4) + "px";
                        thumbFrame[i].style.width = (productHeroImage.offsetHeight / 4) + "px";
                    }

                    if (screen.width > 720) {
                        thumbList.style.width = (productHeroImage.offsetHeight / 4) + "px";
                        thumbList.style.minWidth = (productHeroImage.offsetHeight / 4) + "px";
                        thumbList.style.height = productHeroImage.offsetHeight + "px";
                    }
                }
            }
        };
    </script>
    @if ($product->getAttributeFamilyAttribute()->id === 3)
        <style>
            .product-detail .thumb-list {
                top: 30px !important;
            }
        </style>
    @endif
@endpush
