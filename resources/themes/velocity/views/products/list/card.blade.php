@inject ('reviewHelper', 'Webkul\Product\Helpers\Review')
@inject ('toolbarHelper', 'Webkul\Product\Helpers\Toolbar')

@php
    $list = $toolbarHelper->getCurrentMode() == 'list'
        ? true
        : false;

    $productBaseImage = productimage()->getProductBaseImage($product);

    $totalReviews = $reviewHelper->getTotalReviews($product);

    $avgRatings = ceil($reviewHelper->getAverageRating($product));
@endphp

{!! view_render_event('bagisto.shop.products.list.card.before', ['product' => $product]) !!}
    @if (isset($list) && $list)
        <div class="card grid-card w-full rounded-34p bg-terra-light-wheat pb-4 px-0 mb-5 overflow-hidden " style="box-shadow: rgba(0, 0, 0, 0.16) 0px 5px 10px;" >
            <a
                href="{{ route('shop.productOrCategory.index', $product->url_key) }}"
                title="{{ $product->name }}"
                class=" product-image-container max-h-[200px] lg:max-h-auto overflow-y-hidden lg:overflow-y-visible">
                {{--            class="{{ $cardClass ?? 'product-image-container !max-h-[220px] lg:max-h-auto overflow-y-hidden lg:overflow-y-visible' }}"--}}

                <img
                    loading="lazy"
                    class="w-full max-w-full card-img-top lzy_img max-h-[220px] transition-all duration-500" style="border-radius: 34px 34px 0px 0px; max-height: 220px;"
                    alt="{{ $product->name }}"
                    src="{{ $productBaseImage['large_image_url'] }}"
                    :onerror="`this.src='${this.$root.baseUrl}/deneme/large-product-placeholder.png'`"
                />

                {{-- <product-quick-view-btn :quick-view-details="{{ json_encode($velocityHelper->formatProduct($product)) }}"></product-quick-view-btn> --}}
            </a>

            <div class="bg-terra-light-wheat z-50 w-full">

                {{-- @if (! $product->getTypeInstance()->haveSpecialPrice() && $product->new)
                    <div class="sticker new">
                        {{ __('shop::app.products.new') }}
                    </div>
                @endif --}}

                {{-- <div class="card-body"> --}}

                <div class="w-full flex justify-center items-center pt-2 bg-terra-light-wheat relative z-40">
                    <a
                        href="{{ route('shop.productOrCategory.index', $product->url_key) }}"
                        title="{{ $product->name }}"
                        class="unset">

                        <div class="w-full flex justify-center items-center mt-2" >
                            <div class="font-terramirum font-bold text-sm px-1 text-center flex items-center" style="min-height: 40px;">
                                @if ($product->short_name)
                                    {{ $product->short_name }}
                                @else
                                    {{ $product->name }}
                                @endif
                            </div>
                        </div>

                        @if (
                            isset($additionalAttributes)
                            && $additionalAttributes
                        )
                            @if (isset($item->additional['attributes']))
                                <div class="item-options">

                                    @foreach ($item->additional['attributes'] as $attribute)
                                        <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}<br>
                                    @endforeach

                                </div>
                            @endif
                        @endif
                    </a>
                </div>

                {{-- <div class="flex flex-wrap mt-3 px-3">
                    <div class="w-1/2">
                        <div class="flex items-center mb-3 ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">Amount</div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex items-center">
                            <img src="/deneme/svg/up-icon.svg">
                            <img src="/deneme/svg/tl-icon.svg" class="ml-2">
                            <div class="font-terramirum font-bold text-xs ml-0.5" >{{ thorne_money_format($product->price) }}</div>
                        </div>
                    </div>
                    <div class="w-1/2 pl-1">
                        <div class="flex items-center mb-3 ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">Unit Price</div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex items-center">
                            <img src="/deneme/svg/hand-icon.svg">

                            <img src="/deneme/svg/tl-icon.svg" class="ml-2">
                            <div class="font-terramirum font-bold text-xs ml-0.5" >{{  thorne_money_format($product->birimFiyat) }}</div>
                        </div>
                    </div>
                </div> --}}

                <div class="mt-1 px-4 bg-terra-light-wheat relative z-40">
                    <div class="flex flex-wrap border-b-1p pb-1 border-terra-khaki-green">
                        <div class="w-1/2 " style="border-right: 1px solid rgb(121, 145, 78)">
                            <div class="flex items-center justify-center ml-1">
                                <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">
                                    {{ __('velocity::app-static.user-profile.amount-vue') }}
                                </div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                            </div>
                            <div class="flex items-center">
                                <img src="/deneme/svg/up-icon.svg">
                                <div class="text-base">£</div>
                                <div class="font-terramirum font-bold text-xs ml-0.5" >
                                    @if ($product->productAttributeFamilyId === 3)
                                        {{ thorne_money_format($product->arsa_toplam_bedel) }}
                                    @else
                                        {{ thorne_money_format($product->price) }}
                                    @endif
                                </div>
                            </div>
                        </div >
                        <div class="w-1/2 pl-1">
                            <div class="flex items-center justify-center ml-1">
                                <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">
                                    @if ($product->productAttributeFamilyId === 3)
                                        {{ __('velocity::app-static.products.unit-price-land') }}
                                    @else
                                        {{ __('velocity::app-static.products.unit-price') }}
                                    @endif

                                </div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                            </div>
                            <div class="flex justify-center items-center">
                                <img src="/deneme/svg/hand-icon.svg">
                                @if ($product->productAttributeFamilyId === 3)
                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap">
                                        <span class="text-base">£</span>
                                        {{  thorne_money_format($product->price) }}
                                    </div>
                                @else
                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap" >
                                        <span class="text-base">£</span>
                                        {{  thorne_money_format($product->birimFiyat) }}
                                    </div>
                                @endif

                            </div>
                        </div>
                    </div>
                </div>


                {{-- <div class="product-price fs16">
                    @include ('shop::products.price', ['product' => $product])
                </div> --}}

                {{-- @if ($totalReviews)
                    <div class="product-rating col-12 no-padding">
                        <star-ratings ratings="{{ $avgRatings }}"></star-ratings>
                        <span class="align-top">
                            {{ __('velocity::app.products.ratings', ['totalRatings' => $totalReviews ]) }}
                        </span>
                    </div>
                @else
                    <div class="product-rating col-12 no-padding">
                        <span class="fs14">{{ __('velocity::app.products.be-first-review') }}</span>
                    </div>
                @endif --}}

                <div class="px-15 flex items-center justify-center mt-2">
                    @include ('shop::products.add-to-cart', [
                        'product'           => $product,
                        //'btnText'           => $btnText ?? null,
                        //'moveToCart'        => $moveToCart ?? null,
                        //'wishlistMoveRoute' => $wishlistMoveRoute ?? null,
                        //'reloadPage'        => $reloadPage ?? null,
                        //'addToCartForm'     => $addToCartForm ?? false,
                        //'addToCartBtnClass' => $addToCartBtnClass ?? '',
                        //'showCompare'       => core()->getConfigData('general.content.shop.compare_option') == "1"? true : false,
                    ])
                </div>
            </div>
        </div>

        {{--        card list version--}}
{{--        <div class="col-12 lg-card-container list-card product-card row">--}}
{{--            <div class="product-image">--}}
{{--                <a--}}
{{--                    title="{{ $product->name }}"--}}
{{--                    href="{{ route('shop.productOrCategory.index', $product->url_key) }}">--}}
{{--                    <img--}}
{{--                        src="{{ $productBaseImage['medium_image_url'] }}"--}}
{{--                        :onerror="`this.src='${this.$root.baseUrl}/deneme/large-product-placeholder.png'`" alt="" />--}}

{{--                    <div class="quick-view-in-list">--}}
{{--                        <product-quick-view-btn :quick-view-details="{{ json_encode($velocityHelper->formatProduct($product)) }}"></product-quick-view-btn>--}}
{{--                    </div>--}}
{{--                </a>--}}
{{--            </div>--}}

{{--            <div class="product-information">--}}
{{--                <div>--}}
{{--                    <div class="product-name">--}}
{{--                        <a--}}
{{--                            href="{{ route('shop.productOrCategory.index', $product->url_key) }}"--}}
{{--                            title="{{ $product->name }}" class="unset">--}}

{{--                            <span class="fs16">{{ $product->name }}</span>--}}
{{--                        </a>--}}

{{--                        @if (--}}
{{--                            isset($additionalAttributes)--}}
{{--                            && $additionalAttributes--}}
{{--                        )--}}
{{--                            @if (isset($item->additional['attributes']))--}}
{{--                                <div class="item-options">--}}

{{--                                    @foreach ($item->additional['attributes'] as $attribute)--}}
{{--                                        <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>--}}
{{--                                    @endforeach--}}

{{--                                </div>--}}
{{--                            @endif--}}
{{--                        @endif--}}
{{--                    </div>--}}

{{--                    <div class="product-price">--}}
{{--                        @include ('shop::products.price', ['product' => $product])--}}
{{--                    </div>--}}

{{--                    @if( $totalReviews )--}}
{{--                        <div class="product-rating">--}}
{{--                            <star-ratings ratings="{{ $avgRatings }}"></star-ratings>--}}

{{--                            <span>{{ $totalReviews }} Ratings</span>--}}
{{--                        </div>--}}
{{--                    @endif--}}

{{--                    <div class="cart-wish-wrap mt5">--}}
{{--                        @include ('shop::products.add-to-cart', [--}}
{{--                            'addWishlistClass'  => 'pl10',--}}
{{--                            'product'           => $product,--}}
{{--                            'addToCartBtnClass' => 'medium-padding',--}}
{{--                            'showCompare'       => core()->getConfigData('general.content.shop.compare_option') == "1"--}}
{{--                                                    ? true : false,--}}
{{--                        ])--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
    @else
        <div class="card grid-card w-full rounded-34p bg-terra-light-wheat pb-4 px-0 mb-5 overflow-hidden " style="box-shadow: rgba(0, 0, 0, 0.16) 0px 5px 10px;" >
            <a
            href="{{ route('shop.productOrCategory.index', $product->url_key) }}"
            title="{{ $product->name }}"
            class=" product-image-container max-h-[200px] lg:max-h-auto overflow-y-hidden lg:overflow-y-visible">
{{--            class="{{ $cardClass ?? 'product-image-container !max-h-[220px] lg:max-h-auto overflow-y-hidden lg:overflow-y-visible' }}"--}}

            <img
                loading="lazy"
                class="w-full max-w-full card-img-top lzy_img max-h-[220px] transition-all duration-500" style="border-radius: 34px 34px 0px 0px; max-height: 220px;"
                alt="{{ $product->name }}"
                src="{{ $productBaseImage['large_image_url'] }}"
                :onerror="`this.src='${this.$root.baseUrl}/deneme/large-product-placeholder.png'`"
                />

                {{-- <product-quick-view-btn :quick-view-details="{{ json_encode($velocityHelper->formatProduct($product)) }}"></product-quick-view-btn> --}}
            </a>

            <div class="bg-terra-light-wheat z-50 w-full">

            {{-- @if (! $product->getTypeInstance()->haveSpecialPrice() && $product->new)
                <div class="sticker new">
                    {{ __('shop::app.products.new') }}
                </div>
            @endif --}}

            {{-- <div class="card-body"> --}}

                <div class="w-full flex justify-center items-center pt-2 bg-terra-light-wheat relative z-40">
                    <a
                        href="{{ route('shop.productOrCategory.index', $product->url_key) }}"
                        title="{{ $product->name }}"
                        class="unset">

                        <div class="w-full flex justify-center items-center mt-2" >
                            <div class="font-terramirum font-bold text-sm px-1 text-center flex items-center" style="min-height: 40px;">
                                @if ($product->short_name)
                                    {{ $product->short_name }}
                                @else
                                    {{ $product->name }}
                                @endif
                            </div>
                        </div>

                        @if (
                            isset($additionalAttributes)
                            && $additionalAttributes
                        )
                            @if (isset($item->additional['attributes']))
                                <div class="item-options">

                                    @foreach ($item->additional['attributes'] as $attribute)
                                        <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}<br>
                                    @endforeach

                                </div>
                            @endif
                        @endif
                    </a>
                </div>

                {{-- <div class="flex flex-wrap mt-3 px-3">
                    <div class="w-1/2">
                        <div class="flex items-center mb-3 ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">Amount</div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex items-center">
                            <img src="/deneme/svg/up-icon.svg">
                            <img src="/deneme/svg/tl-icon.svg" class="ml-2">
                            <div class="font-terramirum font-bold text-xs ml-0.5" >{{ thorne_money_format($product->price) }}</div>
                        </div>
                    </div>
                    <div class="w-1/2 pl-1">
                        <div class="flex items-center mb-3 ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">Unit Price</div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex items-center">
                            <img src="/deneme/svg/hand-icon.svg">

                            <img src="/deneme/svg/tl-icon.svg" class="ml-2">
                            <div class="font-terramirum font-bold text-xs ml-0.5" >{{  thorne_money_format($product->birimFiyat) }}</div>
                        </div>
                    </div>
                </div> --}}

                <div class="mt-1 px-4 bg-terra-light-wheat relative z-40">
                    <div class="flex flex-wrap border-b-1p pb-1 border-terra-khaki-green">
                    <div class="w-1/2 " style="border-right: 1px solid rgb(121, 145, 78)">
                        <div class="flex items-center justify-center ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">
                                {{ __('velocity::app-static.user-profile.amount-vue') }}
                            </div>
                            <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex items-center">
                            <img src="/deneme/svg/up-icon.svg">
                            <div class="text-base">£</div>
                            <div class="font-terramirum font-bold text-xs ml-0.5" >
                                @if ($product->productAttributeFamilyId === 3)
                                    {{ thorne_money_format($product->arsa_toplam_bedel) }}
                                @else
                                    {{ thorne_money_format($product->price) }}
                                @endif
                            </div>
                        </div>
                    </div >
                    <div class="w-1/2 pl-1">
                        <div class="flex items-center justify-center ml-1">
                            <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">
                                @if ($product->productAttributeFamilyId === 3)
                                    {{ __('velocity::app-static.products.unit-price-land') }}
                                @else
                                    {{ __('velocity::app-static.products.unit-price') }}
                                @endif

                            </div>
                            <img src="/deneme/svg/i-icon-orange.svg">
                        </div>
                        <div class="flex justify-center items-center">
                            <img src="/deneme/svg/hand-icon.svg">
                                @if ($product->productAttributeFamilyId === 3)
                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap">
                                        <span class="text-base">£</span>
                                        {{  thorne_money_format($product->price) }}
                                    </div>
                                @else
                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap" >
                                        <span class="text-base">£</span>
                                        {{  thorne_money_format($product->birimFiyat) }}
                                    </div>
                                @endif

                        </div>
                    </div>
                    </div>
                </div>


                {{-- <div class="product-price fs16">
                    @include ('shop::products.price', ['product' => $product])
                </div> --}}

                {{-- @if ($totalReviews)
                    <div class="product-rating col-12 no-padding">
                        <star-ratings ratings="{{ $avgRatings }}"></star-ratings>
                        <span class="align-top">
                            {{ __('velocity::app.products.ratings', ['totalRatings' => $totalReviews ]) }}
                        </span>
                    </div>
                @else
                    <div class="product-rating col-12 no-padding">
                        <span class="fs14">{{ __('velocity::app.products.be-first-review') }}</span>
                    </div>
                @endif --}}

                <div class="px-15 flex items-center justify-center mt-2">
                    @include ('shop::products.add-to-cart', [
                        'product'           => $product,
                        //'btnText'           => $btnText ?? null,
                        //'moveToCart'        => $moveToCart ?? null,
                        //'wishlistMoveRoute' => $wishlistMoveRoute ?? null,
                        //'reloadPage'        => $reloadPage ?? null,
                        //'addToCartForm'     => $addToCartForm ?? false,
                        //'addToCartBtnClass' => $addToCartBtnClass ?? '',
                        //'showCompare'       => core()->getConfigData('general.content.shop.compare_option') == "1"? true : false,
                    ])
                </div>
            </div>
        </div>
        {{-- </div> --}}
    @endif

{!! view_render_event('bagisto.shop.products.list.card.after', ['product' => $product]) !!}
@push('css')
    <style>
        .card-img-top.lzy_img:hover {
            transform: scale(1.3);
        }
    </style>
@endpush