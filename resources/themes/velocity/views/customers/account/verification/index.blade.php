@extends('shop::layouts.master')

@section('page_title')
    {{ __('velocity::app-static.verification.page-title') }}
@endsection

@section('content-wrapper')

    <section class="mt-6 bg-cover bg-center " style="background-image: url('/logo-bg.png')">
        <div class="flex items-center w-full justify-center space-x-2 my-2">
            <img class="w-full max-w-70p" src="/../deneme/svg/logo.svg" />
            <div class="flex">
                <span class="font-sans text-4xl font-semibold text-terra-green tracking-wider">T</span><span class="font-sans text-4xl font-semibold tracking-wider">erra</span><span class="font-sans text-4xl font-semibold text-terra-light-green tracking-wider">M</span><span class="font-sans text-4xl font-semibold tracking-wider">irum</span>
            </div>
        </div>
        <div class="w-full text-center text-black font-terramirum font-bold text-lg">
            {{ __('velocity::app-static.verification.hello')}},

        </div>
        <div class="w-full text-center text-black font-terramirum  text-lg mb-4">
            {{ __('velocity::app-static.verification.start-msg')}}
        </div>

        <div class="bg-gradient-to-t from-terra-light-soft-gray from-100% via-terra-text-white via-30% to-transparent to-0% py-15 flex flex-col gap-4">
            <div class="d-flex flex flex-col w-1/3 align-self-center">
                @if($errors)
                    @foreach($errors->all() as $error)
                        <div class="alert alert-danger mb-4" role="alert">
                            {{ $error }}
                        </div>
                    @endforeach
                @endif
            </div>

            <div class="d-flex flex flex-col w-1/3 align-self-center">
                <verification-list></verification-list>
            </div>
        </div>
    </section>
@endsection

@push('scripts')

    <script type="text/x-template" id="verification-list-template">
        <div>
            <div class="flex justify-content-center" id="emailVerificationForm">
                <form class="flex mx-auto flex-col w-full py-8 rounded-54p bg-white shadow-searchshadow px-4 mt-16 lg:px-12" id="email-form"
                    action="{{ route('customer.account.verification.store', ['type' => 'email']) }}" method="POST" data-vv-scope="email-form" @submit.prevent="submitForm('email-form')">
                    <div class="page-content">
                        <div class="form-container flex flex-col gap-2">
                            @csrf()

                            <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('email-form.email') ? 'has-error' : '']">
                                <label for="email" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                    {{ __('shop::app.customer.signup-form.email') }}
                                    <span class="text-red-700">*</span>
                                </label>
                                <input type="email"
                                       v-validate="'required'"
                                       v-model="email_form.email" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="email" name="email" data-vv-as="&quot;{{ __('shop::app.customer.signup-form.email') }}&quot;"/>
                                <span class="control-error" v-if="errors.has('email-form.email')">@{{ errors.first('email-form.email') }}</span>
                            </div>

                            <div class="flex mt-4 self-center w-full">
                                <button class="bg-terra-other-green font-terramirum text-white text-xl font-bold rounded-2xl py-2 px-4 self-center w-full hover:opacity-90" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                    @disabled(!is_null($customer->email_verified_at))>
                                    {{ __('velocity::app-static.signup.verify')}}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="flex justify-content-center" id="IdentityVerificationForm">
                <form class="flex mx-auto flex-col w-full py-8 rounded-54p bg-white shadow-searchshadow px-4 mt-16 lg:px-12" id="id_number-form"
                      action="{{ route('customer.account.verification.store', ['type' => 'id_number']) }}" method="POST" data-vv-scope="id_number-form" @submit.prevent="submitForm('id_number-form')">
                    <div class="page-content">
                        <div class="form-container flex flex-col gap-2">
                            @csrf()

                            <div class="self-center flex my-4 mt-2 relative group w-full space-x-4">
                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('id_number-form.first_name') ? 'has-error' : '']">
                                    <label for="first_name" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.signup-form.firstname') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="id_number_form.first_name" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="first_name" name="first_name" data-vv-as="&quot;{{ __('shop::app.customer.signup-form.firstname') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('id_number-form.first_name')">@{{ errors.first('id_number-form.first_name') }}</span>
                                </div>

                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('id_number-form.last_name') ? 'has-error' : '']">
                                    <label for="last_name" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.signup-form.lastname') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="id_number_form.last_name" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="last_name" name="last_name" data-vv-as="&quot;{{ __('shop::app.customer.signup-form.lastname') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('id_number-form.last_name')">@{{ errors.first('id_number-form.last_name') }}</span>
                                </div>
                            </div>

                            <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('id_number-form.date_of_birth') ? 'has-error' : '']">
                                <label for="date_of_birth" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                    {{ __('shop::app.customer.account.profile.dob') }}
                                    <span class="text-red-700">*</span>
                                </label>
                                <input type="date"
                                       v-validate="'required'"
                                       v-model="id_number_form.date_of_birth" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="date_of_birth" name="date_of_birth" data-vv-as="&quot;{{ __('shop::app.customer.account.profile.dob') }}&quot;"/>
                                <span class="control-error" v-if="errors.has('id_number-form.date_of_birth')">@{{ errors.first('id_number-form.date_of_birth') }}</span>
                            </div>

                            <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('id_number-form.id_number') ? 'has-error' : '']">
                                <label for="id_number" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                    {{ __('velocity::app-static.signup.id')}}
                                    <span class="text-red-700">*</span>
                                </label>
                                <input type="text"
                                       v-validate="'required'"
                                       v-model="id_number_form.id_number" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="id_number" name="id_number" data-vv-as="&quot;{{ __('velocity::app-static.signup.id') }}&quot;"/>
                                <span class="control-error" v-if="errors.has('id_number-form.id_number')">@{{ errors.first('id_number-form.id_number') }}</span>
                            </div>

                            <div class="flex mt-4 self-center w-full">
                                <button class="bg-terra-other-green font-terramirum text-white text-xl font-bold rounded-2xl py-2 px-4 self-center w-full hover:opacity-90" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                    @disabled(!is_null($customer->identity_verified_at))>
                                    {{ __('velocity::app-static.signup.verify')}}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>


            <div class="flex justify-content-center" id="addressVerificationForm">
                <form class="flex mx-auto flex-col w-full py-8 rounded-54p bg-white shadow-searchshadow px-4 mt-16 lg:px-12" id="address-form"
                      action="{{ route('customer.account.verification.store', ['type' => 'address']) }}" method="POST" data-vv-scope="address-form" @submit.prevent="submitForm('address-form')">
                    <div class="page-content">
                        <div class="form-container flex flex-col gap-2">
                            @csrf()

                            <div class="self-center flex my-4 mt-2 relative group w-full space-x-4">
                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.first_name') ? 'has-error' : '']">
                                    <label for="first_name" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.signup-form.firstname') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="address_form.first_name" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="first_name" name="first_name" data-vv-as="&quot;{{ __('shop::app.customer.signup-form.firstname') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('address-form.first_name')">@{{ errors.first('address-form.first_name') }}</span>
                                </div>

                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.last_name') ? 'has-error' : '']">
                                    <label for="last_name" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.signup-form.lastname') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="address_form.last_name" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="last_name" name="last_name" data-vv-as="&quot;{{ __('shop::app.customer.signup-form.lastname') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('address-form.last_name')">@{{ errors.first('address-form.last_name') }}</span>
                                </div>
                            </div>

                            <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.address1') ? 'has-error' : '']">
                                <label for="address1" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                    {{ __('shop::app.customer.account.address.create.street-address')}}
                                    <span class="text-red-700">*</span>
                                </label>
                                <input type="text"
                                       v-validate="'required'"
                                       v-model="address_form.address1" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="address1" name="address1" data-vv-as="&quot;{{ __('shop::app.customer.account.address.create.street-address') }}&quot;"/>
                                <span class="control-error" v-if="errors.has('address-form.address1')">@{{ errors.first('address-form.address1') }}</span>
                            </div>

                            <div class="self-center flex my-4 mt-2 relative group w-full space-x-4">
                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.city') ? 'has-error' : '']">
                                    <label for="city" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.account.address.create.city') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="address_form.city" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="city" name="city" data-vv-as="&quot;{{ __('shop::app.customer.account.address.create.city') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('address-form.city')">@{{ errors.first('address-form.city') }}</span>
                                </div>

                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.state') ? 'has-error' : '']">
                                    <label for="state" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.account.address.create.state') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="address_form.state" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="state" name="state" data-vv-as="&quot;{{ __('shop::app.customer.account.address.create.state') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('address-form.state')">@{{ errors.first('address-form.state') }}</span>
                                </div>
                            </div>

                            <div class="self-center flex my-4 mt-2 relative group w-full space-x-4">
                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.postcode') ? 'has-error' : '']">
                                    <label for="postcode" class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 px-1 text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                        {{ __('shop::app.customer.account.address.create.postcode') }}
                                        <span class="text-red-700">*</span>
                                    </label>
                                    <input type="text"
                                           v-validate="'required'"
                                           v-model="address_form.postcode" class="pl-3 peer placeholder:text-placeholdergray text-lg font-terramirum font-bold bg-terra-bg-light-green border-1 rounded-14p border-terra-bg-light-gray hover:border-terra-khaki-green w-full min-h-50p focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white placeholder:text-xl" id="postcode" name="postcode" data-vv-as="&quot;{{ __('shop::app.customer.account.address.create.postcode') }}&quot;"/>
                                    <span class="control-error" v-if="errors.has('address-form.postcode')">@{{ errors.first('address-form.postcode') }}</span>
                                </div>

                                <div class="self-center my-4 mb-2 relative group w-full control-group" :class="[errors.has('address-form.country') ? 'has-error' : '']">
                                    <div class="control-group position-relative" :class="[errors.has('country') ? 'has-error' : '']">
                                        <label for="country"
                                               class="font-terramirum font-bold transform transition-all absolute top-0 peer-valid:top-0 left-3 left-3 px-1 pl-0 text-lg text-base peer-valid:text-base -translate-y-full peer-valid:-translate-y-full pl-0 peer-valid:pl-0 text-black group-focus-within:text-black peer-valid:text-black">
                                            {{ __('shop::app.customer.account.address.create.country') }}
                                            <span class="text-red-700">*</span>
                                        </label>
                                        <select
                                            @disabled(!is_null($customer->address_verified_at))
                                            class="control styled-select font-terramirum font-bold transform transition-all pl-3 rounded-14p w-full min-h-50p text-lg font-terramirum font-bold bg-terra-bg-light-green border-terra-bg-light-gray"
                                            id="country"
                                            type="text"
                                            name="country"
                                            v-model="address_form.country"
                                            v-validate="'{{ core()->isCountryRequired() ? 'required' : '' }}'"
                                            data-vv-as="&quot;{{ __('shop::app.customer.account.address.create.country') }}&quot;">
                                            @foreach (core()->countries() as $country)
                                                <option {{ $country->code === old('country', $customer->addresses()->first()->country ?? '') ? 'selected' : '' }}  value="{{ $country->code }}">{{ $country->name }}</option>
                                            @endforeach
                                        </select>

                                        <div class="select-icon-container">
                                            <span class="select-icon top--30p rango-arrow-down"></span>
                                        </div>
                                    </div>
                                    <span class="control-error" v-if="errors.has('address-form.country')">@{{ errors.first('address-form.country') }}</span>
                                </div>
                            </div>

                            <div class="flex mt-4 self-center w-full">
                                <button class="bg-terra-other-green font-terramirum text-white text-xl font-bold rounded-2xl py-2 px-4 self-center w-full hover:opacity-90" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);">
                                    {{ __('velocity::app-static.signup.verify')}}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>


        </div>
    </script>

    <script>
        Vue.component('verification-list', {
            template: '#verification-list-template',

            data: function() {
                return {
                    email_form: {
                        email: '{{ old('email', $customer->email) }}',
                        isDisabled: {{ !is_null($customer->email_verified_at) ? 1 : 0 }},
                    },
                    id_number_form: {
                        id_number: '{{ old('id_number', $customer->id_number) }}',
                        first_name: '{{ old('first_name', $customer->first_name) }}',
                        last_name: '{{ old('last_name', $customer->last_name) }}',
                        date_of_birth: '{{ old('date_of_birth', $customer->date_of_birth) }}',
                        isDisabled: {{ !is_null($customer->identity_verified_at) ? 1 : 0 }},
                    },
                    address_form: {
                        first_name: '{{ old('first_name', $customer->addresses()->first()->first_name ?? '') }}',
                        last_name: '{{ old('last_name', $customer->addresses()->first()->last_name ?? '') }}',
                        address1: '{{ old('address1', $customer->addresses()->first()->address1 ?? '') }}',
                        city: '{{ old('city', $customer->addresses()->first()->city ?? '') }}',
                        state: '{{ old('state', $customer->addresses()->first()->state ?? '') }}',
                        postcode: '{{ old('postcode', $customer->addresses()->first()->postcode ?? '') }}',
                        country: '{{ old('country', $customer->addresses()->first()->country ?? '') }}',
                        isDisabled: {{ !is_null($customer->address_verified_at) ? 1 : 0 }},
                    },
                }
            },

            methods: {
                submitForm: function(formScope) {
                    this.$validator.validateAll(formScope).then(function (result) {
                        if (result) {
                            var form = document.getElementById(formScope);
                            form.submit();
                        }
                    });
                }
            }
        })
    </script>
@endpush