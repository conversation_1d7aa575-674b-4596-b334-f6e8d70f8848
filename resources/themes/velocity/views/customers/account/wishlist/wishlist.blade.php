@inject('toolbarHelper', 'Webkul\Product\Helpers\Toolbar')

@extends('shop::customers.account.index')

@section('page_title')
    {{-- Favorilerim --}}
    {{ __('velocity::app-static.wishlist.title') }}
@endsection

@section('page-detail-wrapper')
    <div class="account-head">
        <span class="account-heading ml-5">{{ __('velocity::app-static.wishlist.title') }}</span>

        @if (count($items))
            <span class="account-action d-inline-flex">
                <form id="remove-all-wishlist" class="d-none" action="{{ route('customer.wishlist.removeall') }}" method="POST">
                    @method('DELETE')

                    @csrf
                </form>

                @if ($isSharingEnabled)
                    <a
                        class="remove-decoration theme-btn light"
                        href="javascript:void(0);"
                        onclick="window.showShareWishlistModal();">
                        {{ __('shop::app.customer.account.wishlist.share') }}
                    </a>

                    &nbsp;
                @endif

                <div class="bg-terra-soft-khaki-green px-4 py-2 rounded-xl hover:opacity-90 mt-3" onclick="window.deleteAllWishlist();" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                    <a href="javascript:void(0);" class="unset">
                        <div class="cursor-pointer font-terramirum text-white font-bold text-md text-truncate text-center">
                            {{ __('shop::app.customer.account.wishlist.deleteall') }}
                        </div>
                    </a>
                </div>

                {{-- <a
                    class="remove-decoration theme-btn light"
                    href="javascript:void(0);"
                    onclick="window.deleteAllWishlist();">
                    {{ __('shop::app.customer.account.wishlist.deleteall') }}
                </a> --}}

            </span>
        @endif
    </div>

    {!! view_render_event('bagisto.shop.customers.account.wishlist.list.before', ['wishlist' => $items]) !!}

    <div class="wishlist-container ml-5">
        @if ($items->count())
            <div class="flex flex-wrap justify-center">
                <div class="w-10/12">
                    <div class="grid grid-col-1 lg:grid-cols-4">
                        @foreach ($items as $item)
                            @include ('shop::customers.account.wishlist.wishlist-product', [
                                'item' => $item,
                                'visibility' => $isSharingEnabled
                            ])
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="pagination justify-center">
                {{ $items->links()  }}
            </div>
        @else
            <div class="empty">
                <div class="pt-10 pb-14 bg-off-white rounded-50p mt-5 flex flex-col justify-center items-center space-y-7">
                    <div class="p-5 bg-terra-khaki-green rounded-2xl">
                        <img src="/deneme/svg/green-star.svg" alt="">
                    </div>
                    <div class="font-terramirum font-bold text-2xl text-center">{{ __('velocity::app-static.wishlist.empty-msg') }}</div>
                    <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">{{ __('velocity::app-static.wishlist.empty-content') }}</div>
                    <div class="font-terramirum font-bold text-2xl text-center text-terra-khaki-green">
                        <a href="/" class="flex unset">
                            <u>{{ __('velocity::app-static.wishlist.btn-text') }}</u>
                            <img src="/deneme/svg/arrow.svg" class="ml-2" alt="">
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    @if($isSharingEnabled)
        <div id="shareWishlistModal" class="d-none">
            <modal id="shareWishlist" :is-open="modalIds.shareWishlist">
                <h3 slot="header">
                    {{ __('shop::app.customer.account.wishlist.share-wishlist') }}
                </h3>

                <i class="rango-close"></i>

                <div slot="body">
                    <share-component></share-component>
                </div>
            </modal>
        </div>
    @endif

    {!! view_render_event('bagisto.shop.customers.account.wishlist.list.after', ['wishlist' => $items]) !!}
@endsection

@push('scripts')
    @if($isSharingEnabled)
        <script type="text/x-template" id="share-component-template">
            <form method="POST">
                @csrf

                <div class="form-group">
                    <label class="label-style mandatory">
                        {{ __('shop::app.customer.account.wishlist.wishlist-sharing') }}
                    </label>

                    <select name="shared" class="form-control" @change="shareWishlist($event.target.value)">
                        <option value="0" :selected="! isWishlistShared">{{ __('shop::app.customer.account.wishlist.disable') }}</option>
                        <option value="1" :selected="isWishlistShared">{{ __('shop::app.customer.account.wishlist.enable') }}</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="label-style mandatory">
                        {{ __('shop::app.customer.account.wishlist.visibility') }}
                    </label>

                    <div>
                        <span class="badge badge-success" v-if="isWishlistShared">
                            {{ __('shop::app.customer.account.wishlist.public') }}
                        </span>

                        <span class="badge badge-danger" v-else>
                            {{ __('shop::app.customer.account.wishlist.private') }}
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="label-style mandatory">
                        {{ __('shop::app.customer.account.wishlist.shared-link') }}
                    </label>

                    <div class="input-group" v-if="isWishlistShared">
                        <input
                            type="text"
                            class="form-control"
                            v-model="wishlistSharedLink"
                            v-on:focus="$event.target.select()"
                            ref="sharedLink"
                        />

                        <div class="input-group-append">
                            <button
                                class="btn btn-outline-secondary theme-btn"
                                style="padding: 6px 20px"
                                id="copy-btn"
                                title="{{ __('shop::app.customer.account.wishlist.copy-link') }}"
                                type="button"
                                @click="copyToClipboard"
                            >
                                {{ __('shop::app.customer.account.wishlist.copy') }}
                            </button>
                        </div>
                    </div>

                    <p class="alert alert-danger" v-else>
                        {{ __('shop::app.customer.account.wishlist.enable-wishlist-info') }}
                    </p>
                </div>
            </form>
        </script>

        <script>
            /**
             * Show share wishlist modal.
             */
            function showShareWishlistModal() {
                document.getElementById('shareWishlistModal').classList.remove('d-none');

                window.app.showModal('shareWishlist');
            }

            Vue.component('share-component', {
                template: '#share-component-template',

                inject: ['$validator'],

                data: function () {
                    return {
                        isWishlistShared: parseInt("{{ $isWishlistShared }}"),

                        wishlistSharedLink: "{{ $wishlistSharedLink }}".replace("&amp;", "&"),
                    }
                },

                methods: {
                    shareWishlist: function(val) {
                        var self = this;

                        this.$root.showLoader();

                        this.$http.post("{{ route('customer.wishlist.share') }}", {
                            shared: val
                        })
                        .then(function(response) {
                            self.$root.hideLoader();

                            self.isWishlistShared = response.data.isWishlistShared;

                            self.wishlistSharedLink = response.data.wishlistSharedLink;
                        })
                        .catch(function (error) {
                            self.$root.hideLoader();

                            window.location.reload();
                        })
                    },

                    copyToClipboard: function() {
                        this.$refs.sharedLink.focus();
                        document.execCommand('copy');
                        showCopyMessage();
                    }
                }
            });
        </script>
    @endif

    <script>
        /**
         * Delete all wishlist.
         */
        function deleteAllWishlist() {
            if (confirm('{{ __('shop::app.customer.account.wishlist.confirm-delete-all') }}')) document.getElementById('remove-all-wishlist').submit();

            return;
        }

        function showCopyMessage()
        {
            $('#copy-btn').text("{{ __('shop::app.customer.account.wishlist.copied') }}");
            $('#copy-btn').css({backgroundColor: '#146e24'});
        }
    </script>
@endpush
@push('css')
    <style>
        .close-when-wishlist{
            display: none!important;
            width: 0px!important;
        }
        .unset.wishlist-icon.pl10.text-right.flex.items-center.close-when-detail{
            padding: 0 0 0 0!important;
        }
    </style>
    @endpush