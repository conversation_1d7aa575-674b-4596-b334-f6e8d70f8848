@extends('shop::customers.account.index')

@section('page_title')
                {{ __('velocity::app-static.user-profile.account-info') }}
    {{-- Hesa<PERSON> --}}
@endsection

@push('css')
    <style>
        .account-head {
            height: 50px;
            margin-left: 1.25rem;
        }

        .popup {
            display: none;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #79914e;
            padding: 10px;
            border: 1px solid #79914e;
            border-radius: 20px;
            box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .popup-content {
            font-size: 14px;
            color: #fff;
        }

    </style>

{{--    <style>--}}
{{--        .tab-button.active {--}}
{{--          text-decoration: underline;--}}
{{--          color: #819A53;--}}
{{--          border-bottom: 4px solid #819A53;--}}
{{--          margin-bottom: 2rem;--}}
{{--        }--}}
{{--      </style>--}}
@endpush

@section('page-detail-wrapper')
    <div class="account-head mb-0">
        <span class="account-heading">
                        {{ __('velocity::app-static.user-profile.account-info') }}
            {{-- Hesap Bilgilerim --}}
        </span>

        {{-- <span class="account-action">
            <a href="{{ route('customer.profile.edit') }}" class="theme-btn light unset float-right">
                {{ __('shop::app.customer.account.profile.index.edit') }}
            </a>
        </span> --}}
    </div>




    <div class="relative w-full mt-[40px]">
        <div class="flex flex-col lg:flex-row space-y-3 lg:space-y-0 justify-around w-full relative z-10">
            <button class="transition-all duration-300 font-bold text-xl text-center pb-4 tab-button active text-terra-khaki-green border-b-[5px] border-terra-khaki-green" onclick="showTab(1)" id="show-tab-1">
                {{-- Kullanıcı bilgilerim --}}
                {{ __('velocity::app-static.user-profile.user-info') }}

            </button>
            <button class="transition-all duration-300 font-bold text-xl text-center pb-4 tab-button" onclick="showTab(2)" id="show-tab-2">
                {{-- Şifre değişikliği --}}
                {{ __('velocity::app-static.user-profile.pass-change') }}
            </button>
        </div>
        <div class=" mx-auto border-t-2 border-terra-bg-light-gray-2 absolute bottom-[1px] w-full left-0"></div>
    </div>


    {!! view_render_event('bagisto.shop.customers.account.profile.view.before', ['customer' => $customer]) !!}


    <div class="w-full lg:w-7/12 mt-8 lg:ml-[170px]">
        <div class="" id="tab-content">
        <div class="w-full hidden" id="tab-1">
            <div class=" text-xl font-bold relative z-40">
                {{-- Profil Bilgileri --}}
                {{ __('velocity::app-static.user-profile.profile') }}

            </div>
            <div class="flex justify-end">
                <form method="POST" @submit.prevent="onSubmit" action="{{ route('customer.profile.update') }}" enctype="multipart/form-data" class="w-full">
                    @csrf

                    <div class="flex w-full ">
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit_form_controls.before', [
                            'customer' => $customer,
                        ]) !!}
                        <div class="w-full md:w-1/2 pr-3 md:pr-5 mt-6">
                            <div :class="`${errors.has('first_name') ? 'has-error' : ''}`">
                                {{-- <label class="col-12 mandatory">
                            {{ __('shop::app.customer.account.profile.fname') }}
                        </label> --}}

                                <input value="{{ $customer->first_name }}" id="first_name" name="first_name" placeholder="Ad" type="text"
                                    class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 rounded-[10px] w-full border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    v-validate="'required'"
                                    data-vv-as="&quot;{{ __('shop::app.customer.account.profile.fname') }}&quot;" />
                                <span class="control-error" v-if="errors.has('first_name')"
                                    v-text="errors.first('first_name')"></span>
                            </div>
                        </div>
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.first_name.after', ['customer' => $customer]) !!}

                        <div class="w-full md:w-1/2 mt-6">
                            <div :class="`${errors.has('last_name') ? 'has-error' : ''}`">
                                {{-- <label class="col-12 mandatory">
                        {{ __('shop::app.customer.account.profile.lname') }}
                    </label> --}}
                                <input value="{{ $customer->last_name }}" id="last_name" name="last_name" type="text"
                                    class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    v-validate="'required'"
                                    data-vv-as="&quot;{{ __('shop::app.customer.account.profile.lname') }}&quot;" />
                                <span class="control-error" v-if="errors.has('last_name')"
                                    v-text="errors.first('last_name')"></span>
                            </div>
                        </div>
                    </div>

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.last_name.after', ['customer' => $customer]) !!}

                    <div class="w-full mt-6">
                        <input value="{{ $customer->user_name }}" type="text" id="user_name" name="user_name"
                            placeholder="User name"
                            class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green">
                        <p class="text-xs text-terra-orange pl-3 mt-2">
                            {{-- *Kullanıcı adınızı en fazla 2 defa değiştirebilirsiniz. --}}
                            *{{ __('velocity::app-static.user-profile.info') }}

                        </p>
                    </div>

                        <button type="submit" class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right">
                        {{-- Güncelle --}}
                        {{ __('velocity::app.shop.general.update') }}
                    </button>
                </form>
            </div>


            <div class="flex justify-end">
                <form method="POST" @submit.prevent="onSubmit" action="{{ route('customer.profile.updateProfile.store') }}" enctype="multipart/form-data" class="w-full">
                    {{-- <div :class="`row ${errors.has('gender') ? 'has-error' : ''}`">
                        <label class="col-12 mandatory">
                            {{ __('shop::app.customer.account.profile.gender') }}
                        </label>

                        <div class="col-12">
                            <select
                                name="gender"
                                v-validate="'required'"
                                class="control styled-select"
                                data-vv-as="&quot;{{ __('shop::app.customer.account.profile.gender') }}&quot;">

                        <option value=""
                            @if ($customer->gender == "")
                                selected="selected"
                            @endif>
                            {{ __('admin::app.customers.customers.select-gender') }}
                        </option>

                        <option value="Other"
                            @if ($customer->gender == "Other")
                                selected="selected"
                            @endif>
                            {{ __('velocity::app.shop.gender.other') }}
                        </option>

                        <option
                            value="Male"
                            @if ($customer->gender == "Male")
                                selected="selected"
                            @endif>
                            {{ __('velocity::app.shop.gender.male') }}
                        </option>

                        <option
                            value="Female"
                            @if ($customer->gender == "Female")
                                selected="selected"
                            @endif>
                            {{ __('velocity::app.shop.gender.female') }}
                        </option>
                    </select>

                            <div class="select-icon-container">
                                <span class="select-icon rango-arrow-down"></span>
                            </div>

                            <span class="control-error" v-if="errors.has('gender')" v-text="errors.first('gender')"></span>
                        </div>
                    </div> --}}

                    {{-- {!! view_render_event('bagisto.shop.customers.account.profile.edit.gender.after', ['customer' => $customer]) !!} --}}

                    {{-- <div :class="`row ${errors.has('date_of_birth') ? 'has-error' : ''}`">
                        <label class="col-12">
                            {{ __('shop::app.customer.account.profile.dob') }}
                        </label>

                        <div class="col-12">
                            <date id="date-of-birth">
                                <input
                                    type="date"
                                    name="date_of_birth"
                                    placeholder="yyyy/mm/dd"
                                    value="{{ old('date_of_birth') ?? $customer->date_of_birth }}"
                                    v-validate="" data-vv-as="&quot;{{ __('shop::app.customer.account.profile.dob') }}&quot;" />
                            </date>

                            <span class="control-error" v-if="errors.has('date_of_birth')" v-text="errors.first('date_of_birth')"></span>
                        </div>
                    </div> --}}

                    {{-- {!! view_render_event('bagisto.shop.customers.account.profile.edit.date_of_birth.after', [
                        'customer' => $customer,
                    ]) !!} --}}
                    @csrf

                    <div class="row mt-6">
                        {{-- <label class="col-12">
                        {{ __('shop::app.customer.account.profile.phone') }}
                        </label> --}}
                        <div class="col-12">
                            <input value="{{ old('phone') ?? $customer->phone }}" placeholder="+90 (___) ___ __ __"
                                class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                name="phone" id="phone" type="text" />
                            <a
                                class="absolute right-5 top-3 border-1 border-terra-khaki-green  bg-white text-2xs font-bold rounded-lg py-1 px-2 self-center hover:opacity-90">
                                <span class="text-terra-khaki-green">
                                    {{-- Değiştir --}}
                                    {{ __('velocity::app-static.user-profile.change') }}

                                </span></a>
                            <span class="control-error" v-if="errors.has('phone')" v-text="errors.first('phone')"></span>
                        </div>
                    </div>

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.phone.after', ['customer' => $customer]) !!}

                    <div class="row mt-6">
                        {{-- <label class="col-12 mandatory">
                        {{ __('shop::app.customer.account.profile.email') }}
                        </label> --}}

                        <div class="col-12">
                            <input value="{{ old('email') ?? $customer->email }}" id="email" name="email" type="text"
                                class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                v-validate="'required'" />
                            <a
                                class="absolute right-5 top-3  border-1 border-terra-khaki-green  bg-white text-2xs font-bold rounded-lg py-1 px-2 self-center hover:opacity-90"><span
                                    class="text-terra-khaki-green">
                                    {{-- Değiştir --}}
                                    {{ __('velocity::app-static.user-profile.change') }}

                                </span></a>
                            <span class="control-error" v-if="errors.has('email')" v-text="errors.first('email')"></span>
                        </div>
                    </div>

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.email.after', ['customer' => $customer]) !!}

                    {{-- <div class="row image-container {!! $errors->has('image.*') ? 'has-error' : '' !!}">
                        <label class="col-12">
                            {{ __('admin::app.catalog.categories.image') }}
                        </label>

                        <div class="col-12">
                            <image-wrapper :button-label="'{{ __('admin::app.catalog.products.add-image-btn-title') }}'" input-name="image" :multiple="false" :images='"{{ $customer->image_url }}"'></image-wrapper>

                            <span class="control-error" v-if="{!! $errors->has('image.*') !!}">
                                @foreach ($errors->get('image.*') as $key => $message)
                                    @php echo str_replace($key, 'Image', $message[0]); @endphp
                                @endforeach
                            </span>
                        </div>
                    </div> --}}

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.image.after', ['customer' => $customer]) !!}

                    {{-- <div class="row">
                        <label class="col-12">
                            {{ __('velocity::app.shop.general.enter-current-password') }}
                        </label>

                        <div :class="`col-12 ${errors.has('oldpassword') ? 'has-error' : ''}`">
                            <input value="" name="oldpassword" type="password" />
                        </div>
                    </div> --}}

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.oldpassword.after', [
                        'customer' => $customer,
                    ]) !!}

                    {{-- <div class="row">
                        <label class="col-12">
                            {{ __('velocity::app.shop.general.new-password') }}
                        </label>

                        <div :class="`col-12 ${errors.has('password') ? 'has-error' : ''}`">
                            <input
                                value=""
                                name="password"
                                ref="password"
                                type="password"
                                v-validate="'min:6'" />

                            <span class="control-error" v-if="errors.has('password')" v-text="errors.first('password')"></span>
                        </div>
                    </div> --}}

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit.password.after', ['customer' => $customer]) !!}

                    {{-- <div class="row">
                        <label class="col-12">
                            {{ __('velocity::app.shop.general.confirm-new-password') }}
                        </label>

                        <div :class="`col-12 ${errors.has('password_confirmation') ? 'has-error' : ''}`">
                            <input value="" name="password_confirmation" type="password"
                            v-validate="'min:6|confirmed:password'" data-vv-as="confirm password" />

                            <span class="control-error" v-if="errors.has('password_confirmation')" v-text="errors.first('password_confirmation')"></span>
                        </div>
                    </div> --}}

                    @if (core()->getConfigData('customer.settings.newsletter.subscription'))
                        <div class="control-group">
                            <input type="checkbox" id="checkbox2" name="subscribed_to_news_letter"
                                @if (isset($customer->subscription)) value="{{ $customer->subscription->is_subscribed }}" {{ $customer->subscription->is_subscribed ? 'checked' : '' }} @endif
                                style="width: auto;">
                            <span>{{ __('shop::app.customer.signup-form.subscribe-to-newsletter') }}</span>
                        </div>
                    @endif

                    {!! view_render_event('bagisto.shop.customers.account.profile.edit_form_controls.after', [
                        'customer' => $customer,
                    ]) !!}


                    <button type="submit" class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right">
                        {{-- Güncelle --}}
                        {{ __('velocity::app.shop.general.update') }}
                    </button>
                </form>
            </div>

            {{-- <button
                type="submit"
                class="theme-btn mb20" onclick="window.showDeleteProfileModal();">
                {{ __('shop::app.customer.account.address.index.delete') }}
            </button> --}}

            {{-- <div id="deleteProfileForm" class="d-none">
                <form method="POST" action="{{ route('customer.profile.destroy') }}" @submit.prevent="onSubmit">
                    @csrf

                    <modal id="deleteProfile" :is-open="modalIds.deleteProfile">
                        <h3 slot="header">
                            {{ __('shop::app.customer.account.address.index.enter-password') }}
                        </h3>

                        <i class="rango-close"></i>

                        <div slot="body">
                            <div class="control-group" :class="[errors.has('password') ? 'has-error' : '']">
                                <label for="password" class="required">{{ __('admin::app.users.users.password') }}</label>

                                <input type="password" v-validate="'required|min:6'" class="control" id="password" name="password" data-vv-as="&quot;{{ __('admin::app.users.users.password') }}&quot;"/>

                                <span class="control-error" v-if="errors.has('password')" v-text="errors.first('password')"></span>
                            </div>

                            <div class="page-action">
                                <button type="submit"  class="theme-btn mb20">
                                    {{ __('shop::app.customer.account.address.index.delete') }}
                                </button>
                            </div>
                        </div>
                    </modal>
                </form>
            </div> --}}

            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-10">
                        <div class="card">
                            <div class="card-header flex items-center justify-center">
                                <img src="/deneme/google-auth.png" style="max-height: 40px; margin-right:10px;" alt="">

                               <span class=" font-bold text-xl text-center text-terra-khaki-green">
                                {{ __('Two Factor Authentication') }}
                                </span>
                            </div>

{{--                            <div class="card-body">--}}
{{--                                <p class="text-lg font-bold relative text-center text-red-700 mb-5">{{ __('Please enter your one-time password to complete your login.') }}</p>--}}

{{--                                <form method="POST" action="{{ route('2fa.verify') }}">--}}
{{--                                    @csrf--}}

{{--                                    <div class="form-group flex flex-col justify-center items-center">--}}
{{--                                        <label for="one_time_password" class="col-md-12 text-lg font-bold relative text-center">{{ __('One Time Password') }}</label>--}}

{{--                                        <div class="col-md-6">--}}
{{--                                            <input id="one_time_password" type="text" class="mt-3 bg-terra-searchbar-green rounded-xl px-4 py-2 w-full focus:outline-none focus:shadow-outline @error('one_time_password') is-invalid @enderror" name="one_time_password" required autofocus>--}}

{{--                                            @error('one_time_password')--}}
{{--                                            <span class="invalid-feedback" role="alert">--}}
{{--                                            <strong>{{ $message }}</strong>--}}
{{--                                        </span>--}}
{{--                                            @enderror--}}
{{--                                        </div>--}}
{{--                                        <div class="flex justify-center mt-5">--}}
{{--                                            <button type="submit" class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90">--}}
{{--                                                {{ __('Verify') }}--}}
{{--                                            </button>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}

{{--                                </form>--}}
{{--                            </div>--}}
                            @if( auth()->user()->google2fa_enabled )
                            <div class="card-body">
                                <div class="flex items-center justify-center">
                                    <img src="/deneme/tick.png" style="max-height: 40px; margin-right:10px;" alt="Okey">

                                    <p class="text-lg font-bold relative text-center text-terra-khaki-green my-10">{{ __('Google Two Factor Authentication is Verified') }}</p>
                                </div>
                            </div>
                            @else
                            <div class="card-body">
                                <form method="POST" action="{{ route('2fa.activate') }}">
                                    @csrf
                                    <input type="text" class="hidden" name="key" value="{{ $key }}">

                                    <div class="form-group row w-full mx-auto">
                                        <div class="col-md-6 flex justify-center">
                                            <div class="flex justify-center items mx-auto p-1 border-1p border-terra-khaki-green rounded">
                                            {!! Google2FA::getQRCodeInline( 'Terramirum', auth()->user()->email, $key ); !!}
                                            </div>
                                        </div>
                                        <div class="col-md-6 flex justify-center flex-col">
                                            <label for="one_time_password" class="text-lg font-bold relative text-center">{{ __('One Time Password') }}</label>
                                            <input id="one_time_password" type="number" maxlength="6" minlength="6" class="tracking-widest text-xl font-bold text-center mt-5 bg-terra-searchbar-green rounded-xl px-4 py-2 w-full focus:outline-none focus:shadow-outline @error('one_time_password') border-1p border-terra-red @enderror" name="one_time_password" required autofocus>
                                            @foreach($errors->all() as $message)
                                            <div class=" text-center invalid-feedback @error('one_time_password'){{'block'}}@enderror" role="alert">
                                            <strong>{{ $message }}</strong>
                                            </div>
                                            @endforeach
                                            <div class="flex justify-center mt-5">
                                                <button type="submit" class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90">
                                                    {{ __('Verify') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="tab-2" class="hidden">
            <div class=" text-xl font-bold relative z-40">
                {{-- Şifre değişikliği --}}
                {{ __('velocity::app-static.user-profile.pass-change') }}

            </div>
            <div class="w-full text-left text-terra-dark-gray font-terramirum text-base mb-4 mt-3 flex items-start">
                <img class="mr-2" src="/../deneme/svg/info.svg" alt="">
                <div>
                    {{-- Şifreniz en az bir harf, rakam veya özel karakter içermeli. Ayrıca şifreniz
                en az 8 karakterden oluşmalı. --}}
                {{ __('velocity::app-static.user-profile.pass-change-msg') }}
                </div>
            </div>

            <div class="flex justify-end">
                <form method="POST" @submit.prevent="onSubmit" action="{{ route('customer.profile.updatePassword.store') }}" enctype="multipart/form-data" class="w-full">
                    @csrf
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="mandatory">
                                {{-- Mevcut Şifre --}}
                                {{ __('velocity::app-static.user-profile.current-pass') }}

                            </div>
                            <input value="" id="password_old" name="oldpassword" type="password" class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green">
                            <a class="absolute mt-3 right-5" onclick="passToggle('password_old', 'pass_old')" id="shoPassword" :class="show-password">
                                <img id="pass_old" src="/../deneme/svg/eyes.svg" alt="">
                            </a>
                        </div>
                    </div>
                    <div class="row mt-6">
                        <div class="col-12">
                            <div class="mandatory">
                                {{-- Yeni Şifre --}}
                                {{ __('velocity::app-static.user-profile.new-pass') }}

                            </div>
                            <input value="" id="password_new" name="password" ref="password" type="password" v-validate="'min:8'" name="password" type="password" autocomplete="off" v-validate="'required|min:8'" class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green">
                            <a class="absolute mt-3 right-5" onclick="passToggle('password_new', 'pass_new')" id="shoPassword" :class="show-password">
                                <img id="pass_new" src="/../deneme/svg/eyes.svg" alt="">
                            </a>
                            <span class="control-error" v-if="errors.has('password')" v-text="errors.first('password')"></span>
                        </div>
                    </div>
                    <div class="row mt-6">
                        <div class="col-12">
                            <div class="mandatory">
                                {{-- Yeni Şifre Tekrar --}}
                                {{ __('velocity::app-static.user-profile.confirm-pass') }}

                            </div>
                            <input id="password_conf" value="" name="password_confirmation" type="password" v-validate="'min:6|confirmed:password'" data-vv-as="confirm password" class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green">
                            <a class="absolute mt-3 right-5" onclick="passToggle('password_conf', 'pass_conf')" id="shoPassword" :class="show-password">
                                <img id="pass_conf" src="/../deneme/svg/eyes.svg" alt="">
                            </a>
                            <span class="control-error" v-if="errors.has('password_confirmation')" v-text="errors.first('password_confirmation')"></span>
                        </div>
                    </div>
                        <button type="submit" class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right">
                        {{-- Güncelle --}}
                        {{ __('velocity::app.shop.general.update') }}
                    </button>
                </form>
            </div>

        </div>
       </div>
    </div>

    {!! view_render_event('bagisto.shop.customers.account.profile.view.after', ['customer' => $customer]) !!}
@endsection

@push('scripts')
    <script>
        /**
         * Show delete profile modal.
         */
        function showDeleteProfileModal() {
            document.getElementById('deleteProfileForm').classList.remove('d-none');

            window.app.showModal('deleteProfile');
        }

        function passToggle(inputId, iconId) {
            var input = document.getElementById(inputId);
            var icon = document.getElementById(iconId);

            if (input.type === "password") {
                input.type = "text";
                icon.src = "/../deneme/svg/eyes-open.svg";
            } else {
                input.type = "password";
                icon.src = "/../deneme/svg/eyes.svg";
            }
        }


    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
        showTab(1);
        });

        function showTab(tabIndex) {
        document.querySelectorAll('#tab-content > div').forEach(tabContent => {tabContent.classList.add('hidden');});

        const selectedTabContent = document.querySelector(`#tab-${tabIndex}`);
        if (selectedTabContent) {selectedTabContent.classList.remove('hidden');}
        document.querySelectorAll('.tab-button').forEach(tabButton => {tabButton.classList.remove('active', 'text-terra-khaki-green', 'border-b-[5px]', 'border-terra-khaki-green');});

        const selectedTabButton = document.getElementById(`show-tab-${tabIndex}`);
        if (selectedTabButton) {selectedTabButton.classList.add('active','text-terra-khaki-green', 'border-b-[5px]', 'border-terra-khaki-green');}
        }


        function copyValue() {
            var input = document.getElementById("wallet");
            var value = input.value.split(":")[1].trim();
            var tempInput = document.createElement("input");
            tempInput.value = value;
            document.body.appendChild(tempInput);
            tempInput.select();

            document.execCommand("copy");
            document.body.removeChild(tempInput);
            // alert("Copied!: " + value);

            var popup = document.getElementById("myPopup");
            var popupMessage = document.getElementById("popupMessage");
            popupMessage.textContent = "Wallet Address copied: " + value;
            popup.style.display = "block";

            setTimeout(function() {
                popup.style.display = "none";
            }, 3000);
        }

    </script>

@endpush
