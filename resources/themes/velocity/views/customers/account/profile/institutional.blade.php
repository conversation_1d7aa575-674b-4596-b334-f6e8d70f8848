@extends('shop::layouts.default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')

    <!-- main-area -->
    <main class="fix">
        <!-- breadcrumb-area -->
        <section class="breadcrumb-area breadcrumb-bg">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="breadcrumb-content">
                            <h2 class="title">{!! __('velocity::app-static.profilePage.profile-page') !!}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- breadcrumb-area-end -->
        <!-- blog-area -->
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <div class="row">
                    <div class="col-lg-3">
                        <aside class="blog-sidebar">
                            <div class="blog-widget">
                                <h2 class="bw-title">{!! __('velocity::app-static.profilePage.profile') !!}</h2>
                                <div class="category-list">
                                    <ul>
                                        @include('shop::customers.account.left-menu')
                                        @yield('left-menu')
                                    </ul>
                                </div>
                            </div>
                        </aside>
                    </div>
                    <div class="col-lg-9">
                        <section class="contact-aera">
                            <div class="container custom-container-four">
                                <div class="contact-info-wrap-two wow fadeInLeft"
                                     data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;"
                                >
                                    <h2 class="title">{!! __('velocity::app-static.profilePage.institutional') !!}</h2>
                                    <div class="post-comments-form">
                                        <div
                                            class="comment-form"
                                            id="forInstitutional"
                                            data-company-name="{!! __('velocity::app-static.profilePage.company-name') !!}"
                                            data-company-mail="{!! __('velocity::app-static.profilePage.company-mail') !!}"
                                            data-company-tax-number="{!! __('velocity::app-static.profilePage.company-tax-number') !!}"
                                            data-company-phone="{!! __('velocity::app-static.profilePage.company-phone') !!}"
                                            data-legal-form="{!! __('velocity::app-static.profilePage.legal-form') !!}"
                                            data-company-representative="{!! __('velocity::app-static.profilePage.company-representative') !!}"
                                            data-company-address="{!! __('velocity::app-static.profilePage.company-address') !!}"
                                            data-company-name-error="{!! __('velocity::app-static.profilePage.company-name-error') !!}"
                                            data-company-mail-error="{!! __('velocity::app-static.profilePage.company-mail-error') !!}"
                                            data-company-tax-number-error="{!! __('velocity::app-static.profilePage.company-tax-number-error') !!}"
                                            data-company-phone-error="{!! __('velocity::app-static.profilePage.company-phone-error') !!}"
                                            data-legal-form-error="{!! __('velocity::app-static.profilePage.legal-form-error') !!}"
                                            data-company-representative-error="{!! __('velocity::app-static.profilePage.company-representative-error') !!}"
                                            data-company-address-error="{!! __('velocity::app-static.profilePage.company-address-error') !!}"
                                            data-error-message="{!! __('velocity::app-static.profilePage.error-message') !!}"
                                            data-company-button="{!! __('velocity::app-static.profilePage.company-button') !!}"
                                        >
                                            <for-institutional-component></for-institutional-component>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </section>
        <!-- blog-area-end -->
    </main>
    <!-- main-area-end -->

@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush