@section('left-menu')
    <a href="/miligram" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-2 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
        <picture>
            <source srcset="/miligold/img/miligram-coin.png" type="image/png">
            <img src="/miligold/img/miligram-coin.png" class="mr-2 h-12 w-12 flex-shrink-0">
        </picture>
        <div>
            <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                {!! __('velocity::app-gold.buy_sell_milyem') !!}
            </h3>
        </div>
    </a>
        <a href="{!! route( 'customer.profile.index' ) !!}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-2 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/uyelik.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/uyelik.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-static.profilePage.profile-account') !!}
                </h3>
            </div>
        </a>
        <a href="{!! route( 'customer.wallet.index' ) !!}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-3 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/odeme.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/odeme.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-static.profilePage.profile-wallet') !!}
                </h3>
            </div>
        </a>
        <a href="{{ route( 'customer.orders.index' ) }}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-2 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/sepet.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/sepet.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/sepet.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-static.profilePage.profile-orders') !!}
                </h3>
            </div>
        </a>
        <a href="{{route('customer.convert-to-physical')}}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-3 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/cevir.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-gold.convert_to_physical') !!}
                </h3>
            </div>
        </a>
        <a href="{!! route( 'customer.address.index' ) !!}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-3 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/cevir.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/cevir.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-gold.address_list') !!}
                </h3>
            </div>
        </a>
        <a href="{{ route( 'customer.profile.password' ) }}" class="relative flex items-center rounded-2.5xl border border-jacarta-100 bg-white p-2 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <picture>
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/nasil-milyem/guvenlik.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/nasil-milyem/guvenlik.png" class="mr-2 h-12 w-12 flex-shrink-0">
            </picture>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-jacarta-700 dark:text-white">
                    {!! __('velocity::app-static.profilePage.profile-security') !!}
                </h3>
            </div>
        </a>

        <a href="javascript:;" onclick="document.getElementById('customerLogout').submit();" class="bg-red relative flex items-center rounded-2.5xl border border-jacarta-100 p-3 transition-shadow hover:shadow-lg dark:border-jacarta-700 dark:bg-jacarta-700">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="48" height="40" class="fill-white dark:fill-white">
                    <g>
                        <g>
                            <path d="M175.3,64V24.2c0-6.4-5.2-11.5-11.5-11.5H21.5c-6.3,0-11.5,5.2-11.5,11.5v163c0,4.2,2.5,8.3,6.1,10.2l87.7,45.4c3.9,1.9,8.5-0.8,8.5-5.2v-44.2h51.5c6.4,0,11.5-5.2,11.5-11.5v-63h-23.1v45.8c0,3.3-2.5,5.8-5.8,5.8h-34V72.4c0-4.2-2.5-8.3-6.2-10.2L54.6,35.7h91.9c3.3,0,5.8,2.5,5.8,5.8v22.7h23.1V64L175.3,64z"></path>
                            <path d="M204.9,45.1l37.5,37.5c4.8,4.8,4.8,11.9,0,16.7l-37.5,37.5c-4.8,4.8-12.1,5-16.9,0.2c-4.6-4.6-4-12.3,0.4-16.9l16.9-16.7h-65.5c-3.3,0-6.5-1.3-8.6-3.9c-5.4-5.8-4-16,2.9-19.8c1.7-1,3.9-1.5,5.8-1.5h65.5c0,0-16.7-16.7-16.9-16.7c-4.4-4.4-5-12.3-0.4-16.7C192.6,40.1,200.1,40.3,204.9,45.1"></path>
                        </g>
                    </g>
                </svg>
            </div>
            <div>
                <h3 class="mb-1 ml-3 font-display text-xl font-semibold text-white dark:text-white">
                    {!! __('velocity::app-static.profilePage.logout') !!}
                </h3>
            </div>
        </a>

@endsection