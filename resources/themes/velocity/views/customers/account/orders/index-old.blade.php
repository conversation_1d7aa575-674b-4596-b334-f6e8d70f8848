@extends('shop::customers.account.index')

@section('page_title')
    {{ __('velocity::app-static.user-profile.orders-menu-title') }}
@endsection
@push('css')
    <style type="text/css">
        .sort-by {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 16px 0;
        }

        .sort-by select {
            background: #f2f2f2;
            border-radius: 17px;
            padding: 8px 16px;
            margin-left: 8px;
            font-size: 18px;
        }

        .sort-by select {
            -moz-appearance: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .popup {
            display: none;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #79914e;
            padding: 10px;
            border: 1px solid #79914e;
            border-radius: 20px;
            box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .popup-content {
            font-size: 14px;
            color: #fff;
        }
        .hiddenx {
            display: none !important;
        }
    </style>
@endpush
@section('page-detail-wrapper')
    <div class="account-head mb-10 ml-5">
        <span class="account-heading">
            {{ __('velocity::app-static.user-profile.orders-menu-title') }}
        </span>
    </div>
    {!! view_render_event('bagisto.shop.customers.account.orders.list.before') !!}
    <wallettabbutton-component></wallettabbutton-component>
    <wallettabtable-component></wallettabtable-component>
    <walletdepositcash-component></walletdepositcash-component>
    <walletdepositcrypto-component></walletdepositcrypto-component>
    <walletwithdrawcash-component></walletwithdrawcash-component>
    <walletwithdrawcrypto-component></walletwithdrawcrypto-component>
@endsection
@push('scripts')
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.2/Chart.min.js"></script>
    {{ __(/** Date Filter Template **/) }}
    <script type="text/x-template" id="date-filter-template">
        <div>
            <div id="calender-destop" class="flex justify-end space-x-4">
                <div class=" date">
                    <date @onChange="applyFilter('start', $event)" hide-remove-button="1">
                        <input type="text" class="!bg-terra-searchbar-green control text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green " id="start_date" value="{{ $startDate->format('Y-m-d') }}" placeholder="{{ __('admin::app.dashboard.from') }}" v-model="start"/></date>
                </div>

                <div class=" date">
                    <date @onChange="applyFilter('end', $event)" hide-remove-button="1">
                        <input type="text" class="!bg-terra-searchbar-green control text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green" id="end_date" value="{{ $endDate->format('Y-m-d') }}" placeholder="{{ __('admin::app.dashboard.to') }}" v-model="end"/></date>
                </div>
            </div>
        </div>
    </script>
    {{ __(/** Date Mobile Filter Template **/) }}
    <script type="text/x-template" id="date-mobile-filter-template">
        <div>
            <div id="calender-mobile">
                <span  @click="openCalender()"></span>
            </div>
            <div v-if="toggleCalenderIcon">
                <div id="date-start" style="">
                    <div class="control-group start-date" style="margin-top:15px">
                        <label for="type">{{ __('admin::app.dashboard.from') }}</label>
                        <date @onChange="setDate('start', $event)" hide-remove-button="1">
                            <input type="text" class="control" id="start_date" value="{{ $startDate->format('Y-m-d') }}" placeholder="{{ __('admin::app.dashboard.from') }}" v-model="start"/>
                        </date>
                    </div>
                </div>

                <div id="date-end" style="">
                    <div class="control-group end-date" style="margin-top:15px">
                        <label for="type">{{ __('admin::app.dashboard.to') }}</label>
                        <date @onChange="setDate('end', $event)" hide-remove-button="1">
                            <input type="text" class="control" id="end_date" value="{{ $endDate->format('Y-m-d') }}" placeholder="{{ __('admin::app.dashboard.to') }}" v-model="end"/>
                        </date>
                    </div>
                </div>

                <div id="date-submit" style="">
                    <button class="btn btn-lg btn-primary" @click="applyFilter">Submit</button>
                </div>
            </div>
        </div>
    </script>
    {{ __(/** wallet Tab Button Template **/) }}
    <script type="text/x-template" id="wallettabbutton-template">
        <div>
            <div class="relative w-full mt-[40px]">
                <div class="flex justify-around w-full relative z-10">
                    <button class=" font-bold text-xl text-center pb-4 tab-button"
                            @click="showTab(1)"
                            id="show-tab-1"
                    >
                        {{-- Cüzdan --}}
                        {{ __('velocity::app-static.user-profile.wallet-tab') }}
                    </button>

                    <button class=" font-bold text-xl text-center pb-4 tab-button"
                            @click="showTab(4)"
                            id="show-tab-4"
                    >
                        {{-- Siparişler --}}
                        {{ __('Transfer Details') }}
                    </button>

                    <button class=" font-bold text-xl text-center pb-4 tab-button"
                            @click="showTab(2)"
                            id="show-tab-2"
                    >
                        {{-- Siparişler --}}
                        {{ __('velocity::app-static.user-profile.orders-tab') }}
                    </button>

                    <button class=" font-bold text-xl text-center pb-4 tab-button"
                            @click="showTab(3)"
                            id="show-tab-3"
                    >
                        {{-- My NFT --}}
                        {{ __('velocity::app-static.user-profile.nft-tab') }}
                    </button>
                </div>
                <div class=" mx-auto border-t-2 border-terra-bg-light-gray-2 absolute bottom-[1px] w-full left-0"></div>
            </div>
        </div>
    </script>
    {{ __(/** wallet Tab Table Template **/) }}
    <script type="text/x-template" id="wallettabtable-template">
        <div>
            <div class="max-w-full " id="tab-content">
                <div class="w-full account-content tab-table hiddenx" id="tab-1">
                    <div class=" col-12 mt-7">
                        <div class="sort-by flex flex-col lg:flex-row items-center justify-between ">
                            <div class="flex items-center w-full lg:w-1/3 mb-5 lg:mb-0">
                                <label for="chainId" class="text-base font-bold">Select Chain</label>
                                <select name="chainid"
                                        id="chainid"
                                        @change="functionWalletTableListRefreshButton"
                                        class="chainid !bg-terra-searchbar-green"
                                >
                                    <option value="terramirum-testnet" selected>Terramirum</option>
                                    <option value="sepolia">Etherium</option>
                                </select>
                            </div>
                            <div class="w-full relative w-full md:w-2/4">
                                <input type="text"
                                       name="wallet"
                                       id="wallet"
                                       v-model="walletBalancesAddress"
                                       disabled="disabled"
                                       class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-terra-searchbar-green focus:ring-terra-khaki-green"
                                />
                                <div id="myPopup" class="popup">
                                    <span id="popupMessage" class="popup-content"></span>
                                </div>
                                <a @click="copyValue()"
                                   class="bg-[#E8E8E8] z-10 cursor-pointer group absolute right-4 top-1/4 border-1 border-terra-khaki-green text-2xs font-bold rounded-lg self-center hover:opacity-90"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="27px" width="27px" version="1.1" id="Layer_1" viewBox="0 0 64 64"
                                         enable-background="new 0 0 64 64" xml:space="preserve" class="fill-black group-hover:fill-[#256130] cursor-pointer transition-all">
                                        <g id="Text-files">
                                            <path d="M53.9791489,9.1429005H50.010849c-0.0826988,0-0.1562004,0.0283995-0.2331009,0.0469999V5.0228   C49.7777481,2.253,47.4731483,0,44.6398468,0h-34.422596C7.3839517,0,5.0793519,2.253,5.0793519,5.0228v46.8432999   c0,2.7697983,2.3045998,5.0228004,5.1378999,5.0228004h6.0367002v2.2678986C16.253952,61.8274002,18.4702511,64,21.1954517,64   h32.783699c2.7252007,0,4.9414978-2.1725998,4.9414978-4.8432007V13.9861002   C58.9206467,11.3155003,56.7043495,9.1429005,53.9791489,9.1429005z M7.1110516,51.8661003V5.0228   c0-1.6487999,1.3938999-2.9909999,3.1062002-2.9909999h34.422596c1.7123032,0,3.1062012,1.3422,3.1062012,2.9909999v46.8432999   c0,1.6487999-1.393898,2.9911003-3.1062012,2.9911003h-34.422596C8.5049515,54.8572006,7.1110516,53.5149002,7.1110516,51.8661003z    M56.8888474,59.1567993c0,1.550602-1.3055,2.8115005-2.9096985,2.8115005h-32.783699   c-1.6042004,0-2.9097996-1.2608986-2.9097996-2.8115005v-2.2678986h26.3541946   c2.8333015,0,5.1379013-2.2530022,5.1379013-5.0228004V11.1275997c0.0769005,0.0186005,0.1504021,0.0469999,0.2331009,0.0469999   h3.9682999c1.6041985,0,2.9096985,1.2609005,2.9096985,2.8115005V59.1567993z"></path>
                                            <path d="M38.6031494,13.2063999H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0158005   c0,0.5615997,0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4542999,1.0158997-1.0158997   C39.6190491,13.6606998,39.16465,13.2063999,38.6031494,13.2063999z"></path>
                                            <path d="M38.6031494,21.3334007H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0157986   c0,0.5615005,0.4544001,1.0159016,1.0159006,1.0159016h22.3491974c0.5615005,0,1.0158997-0.454401,1.0158997-1.0159016   C39.6190491,21.7877007,39.16465,21.3334007,38.6031494,21.3334007z"></path>
                                            <path d="M38.6031494,29.4603004H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4543991,1.0158997-1.0158997   S39.16465,29.4603004,38.6031494,29.4603004z"></path>
                                            <path d="M28.4444485,37.5872993H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h12.1904964c0.5615025,0,1.0158005-0.4543991,1.0158005-1.0158997   S29.0059509,37.5872993,28.4444485,37.5872993z"></path>
                                        </g>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <div class="overflow-x-scroll lg:overflow-x-visible table">
                            <table class="table table-bordered ml-2">
                                <thead>
                                    <tr>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{-- Currency --}}
                                            {{ __('velocity::app-static.user-profile.wallet-currency') }}
                                        </th>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{-- Amount --}}
                                            {{ __('velocity::app-static.user-profile.balance') }}
                                        </th>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{-- Amount --}}
                                            {{ __('velocity::app-static.user-profile.locked-balance') }}
                                        </th>
                                        <th style="background-color: #f8f9fa; text-transform: capitalize;">
                                            {{-- Amount --}}
                                            {{ __('velocity::app-static.user-profile.transactions') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody v-for="balance in walletBalancesTableList">
                                    <tr>
                                        <td class="!w-2/6">@{{ balance.isoCurrency }}</td>
                                        <td class="!w-1/6">@{{ balance.balance / Math.pow(10, balance.exponent) }}</td>
                                        <td class="!w-1/6">@{{ balance.pendingBalance / Math.pow(10, balance.exponent) }}</td>
                                        <td class="!w-2/6">
                                            <div class="flex justify-center space-x-2 xl:space-x-4 relative">
                                                <div class="dropdown">
                                                    <button class="col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300 dropdown-toggle"
                                                            type="button"
                                                            data-toggle="dropdown"
                                                            aria-expanded="false"
                                                    >
                                                        Deposit
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <button @click="functionShowDepositCashModal(balance.isoCurrency, balance.chainId)" v-if="balance.isStableCoin == true">With Cash</button>
                                                        <button @click="functionShowDepositCryptoModal(balance.isoCurrency, balance.chainId)">With Crypto</button>
                                                    </div>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="col-auto text-left fs16 remove-decoration bg-terra-orange font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300 dropdown-toggle"
                                                            type="button"
                                                            data-toggle="dropdown"
                                                            aria-expanded="false"
                                                    >
                                                        Withdraw
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <button @click="functionShowWithDrawCashModal(balance.isoCurrency, balance.chainId)" v-if="balance.isStableCoin == true">With Cash</button>
                                                        <button @click="functionShowWithDrawCryptoModal(balance.isoCurrency, balance.chainId)">With Crypto</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody v-if="walletBalancesTableList == 0">
                                    <tr>
                                        <td colspan="4" class="text-center">
                                            {{ __('velocity::app-static.user-profile.empty-wallet') }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="w-full account-content tab-table hiddenx" id="tab-2">
                    <div class="account-items-list">
                        <div class="account-table-content lg:ml-5 lg:container">
                            <datagrid-plus src="{{ route('customer.orders.index') }}"></datagrid-plus>
                        </div>
                    </div>
                    {!! view_render_event('bagisto.shop.customers.account.orders.list.after') !!}
                </div>
                <div class="w-full account-content tab-table hiddenx" id="tab-3" >
                    <div class="">
                        @if(count($products) > 0)
                            <div class="container mt-12">
                                <div class="mx-auto w-10/12 md:w-full grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 gap-2 lg:gap-4 justify-items-center">
                                    @foreach($products as $product)
                                        @include('shop::products.list.card', ['product' => $product])
                                    @endforeach
                                </div>
                            </div>
                            @push('css')
                                <style>
                                    .add-to-cart-btn.pl0 {
                                        display: none !important;
                                    }
                                </style>
                            @endpush
                        @else
                            <div class="empty ml-5">
                                <div class="pt-10 pb-14 bg-off-white rounded-50p mt-7 mr-3 flex flex-col justify-center items-center space-y-7">
                                    <div class="p-5 bg-terra-khaki-green rounded-2xl">
                                        <img src="/deneme/svg/green-star.svg" alt="" />
                                    </div>
                                    <div class="font-terramirum font-bold text-2xl text-center">{{ __('velocity::app-static.user-profile.nft-empty-title') }}</div>
                                    <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">{{ __('velocity::app-static.user-profile.nft-empty-msg') }}</div>
                                    <div class="font-terramirum font-bold text-2xl text-center text-terra-khaki-green">
                                        <a href="/" class="flex unset">
                                            <u>{{ __('velocity::app-static.wishlist.btn-text') }}</u>
                                            <img src="/deneme/svg/arrow.svg" class="ml-2" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    {!! view_render_event('bagisto.shop.customers.account.orders.list.after') !!}
                </div>
                <div class="w-full account-content tab-table hiddenx" id="tab-4">
                    <div class="account-layout col-12 mt-7">
                        <div class="flex justify-between flex-col md:flex-row">
                            <div class="flex justify-start flex-col md:flex-row mb-5 items-center md:space-x-4 space-x-0 space-y-3 md:space-y-0">
                                <button @click="functionTableListFilterToday( '{{ now()->format('Y-m-d') }}', '{{ now()->format('Y-m-d') }}' )"
                                   class="ml-3 col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300"
                                >Today</button>
                                <button @click="functionTableListFilterThisWeek( '{{ now()->subWeek()->format('Y-m-d') }}', '{{ now()->format('Y-m-d') }}' )"
                                   class="ml-3 col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300"
                                >This Week</button>
                                <button @click="functionTableListFilterThisMonth( '{{ now()->subMonth()->format('Y-m-d') }}', '{{ now()->format('Y-m-d') }}' )"
                                   class="ml-3 col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300"
                                >This Month</button>
                                <button @click="functionTableListFilterLastThreeMonth( '{{ now()->subMonths(3)->format('Y-m-d')}}', '{{now()->format('Y-m-d')}}' )"
                                   class="ml-3 col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300"
                                >Last Three Month</button>
                            </div>
                            <div class="flex justify-end flex-col md:flex-row mb-5 items-center space-x-4">
                                <div class="text-base font-bold">Filter By Date</div>
                                <date-filter class="w-3/4 md:w-2/4"></date-filter>
                            </div>
                        </div>
                        <div class="overflow-x-scroll lg:overflow-x-visible table">
                            <table class="table table-bordered ml-2">
                                <thead>
                                    <tr>
                                        <th>sıra</th>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{ 'Title' }}
                                        </th>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{-- Currency --}}
                                            {{ __('velocity::app-static.user-profile.wallet-currency') }}
                                        </th>
                                        <th style="background-color: #f8f9fa;text-transform: capitalize;">
                                            {{-- Amount --}}
                                            {{ __('velocity::app-static.user-profile.balance') }}
                                        </th>
                                        <th style="background-color: #f8f9fa; text-transform: capitalize;">
                                            {{ 'Date' }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody v-for="results in walletTableTransferDetailList">
                                    <tr v-for="result in results.result">
                                        <td class="!w-2/6">@{{ result.status }}</td>
                                        <td class="!w-2/6">@{{ result.title }}</td>
                                        <td class="!w-1/6">@{{ result.amount }}</td>
                                        <td class="!w-1/6">@{{ result.currency }}</td>
                                        <td class="!w-1/6">@{{ result.reqDate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <modal-component :is-open="showDepositCashModal">
                    <div class="flex mx-auto w-full rounded-2lg" slot="body">
                        <div class="w-full">
                            <div class="font-terramirum font-bold text-lg text-black text-center lg:w-3/4 mx-auto tracking-wide pb-3">
                                To top up the balance, you must make a payment via <span class="text-red-700">Money Transfer/EFT</span> to one of the relevant bank addresses.
                            </div>
                            <div class="font-terramirum font-bold text-lg text-black text-center lg:w-3/4 mx-auto tracking-wide pb-3">
                                You must write <span class="text-red-700">@{{ walletModalAccountId }}</span> in the description section
                            </div>
                            <div class="font-terramirum font-bold text-2xl text-center tracking-wide text-terra-khaki-green pb-2">
                                Bank Account Information
                            </div>
                            <div class="flex flex-wrap mt-3 border-b-2 pb-2 border-terra-khaki-green">
                                <div class="w-full lg:w-1/3">
                                    <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                        Bank:
                                    </div>
                                    <div v-for="item in walletModalBankDetail">
                                        <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2">
                                            @{{  item.bankName }}
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full lg:w-1/3">
                                    <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                        Account Name:
                                    </div>
                                    <div v-for="item in walletModalBankDetail">
                                        <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2">
                                            @{{  item.accountName }}
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full lg:w-1/3">
                                    <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                        IBAN(TRY):
                                    </div>
                                    <div v-for="item in walletModalBankDetail">
                                        <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2">
                                            @{{ item.iban }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </modal-component>
            </div>
            <div>
                <modal-component :is-open="showDepositCryptoModal">
                    <div slot="body">
                        <div class="font-terramirum font-bold text-lg text-black text-center lg:w-3/4 mx-auto tracking-wide pb-3">
                            You must pay to the following Wallet
                        </div>
                        <div class="w-full relative">
                            <input type="text"
                                   name="wallet"
                                   value="{{ __('velocity::app-static.user-profile.wallet-address') }}: {{$balance['address'] ?? 'Boş'}}"
                                   id="walletModal"
                                   class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                   disabled="disabled"
                            />
                            <div id="myPopupModal" class="popup z-99999">
                                <span id="popupMessageModal" class="popup-content"></span>
                            </div>
                            <a @click="copyValueModal()" class="bg-[#E8E8E8] z-10 cursor-pointer group absolute right-4 top-1/4 border-1 border-terra-khaki-green text-2xs font-bold rounded-lg self-center hover:opacity-90">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="27px" width="27px" version="1.1" id="Layer_1" viewBox="0 0 64 64"
                                     enable-background="new 0 0 64 64" xml:space="preserve" class="fill-black group-hover:fill-[#256130] cursor-pointer transition-all">
                                <g id="Text-files">
                                    <path d="M53.9791489,9.1429005H50.010849c-0.0826988,0-0.1562004,0.0283995-0.2331009,0.0469999V5.0228   C49.7777481,2.253,47.4731483,0,44.6398468,0h-34.422596C7.3839517,0,5.0793519,2.253,5.0793519,5.0228v46.8432999   c0,2.7697983,2.3045998,5.0228004,5.1378999,5.0228004h6.0367002v2.2678986C16.253952,61.8274002,18.4702511,64,21.1954517,64   h32.783699c2.7252007,0,4.9414978-2.1725998,4.9414978-4.8432007V13.9861002   C58.9206467,11.3155003,56.7043495,9.1429005,53.9791489,9.1429005z M7.1110516,51.8661003V5.0228   c0-1.6487999,1.3938999-2.9909999,3.1062002-2.9909999h34.422596c1.7123032,0,3.1062012,1.3422,3.1062012,2.9909999v46.8432999   c0,1.6487999-1.393898,2.9911003-3.1062012,2.9911003h-34.422596C8.5049515,54.8572006,7.1110516,53.5149002,7.1110516,51.8661003z    M56.8888474,59.1567993c0,1.550602-1.3055,2.8115005-2.9096985,2.8115005h-32.783699   c-1.6042004,0-2.9097996-1.2608986-2.9097996-2.8115005v-2.2678986h26.3541946   c2.8333015,0,5.1379013-2.2530022,5.1379013-5.0228004V11.1275997c0.0769005,0.0186005,0.1504021,0.0469999,0.2331009,0.0469999   h3.9682999c1.6041985,0,2.9096985,1.2609005,2.9096985,2.8115005V59.1567993z"></path>
                                    <path d="M38.6031494,13.2063999H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0158005   c0,0.5615997,0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4542999,1.0158997-1.0158997   C39.6190491,13.6606998,39.16465,13.2063999,38.6031494,13.2063999z"></path>
                                    <path d="M38.6031494,21.3334007H16.253952c-0.5615005,0-1.0159006,0.4542999-1.0159006,1.0157986   c0,0.5615005,0.4544001,1.0159016,1.0159006,1.0159016h22.3491974c0.5615005,0,1.0158997-0.454401,1.0158997-1.0159016   C39.6190491,21.7877007,39.16465,21.3334007,38.6031494,21.3334007z"></path>
                                    <path d="M38.6031494,29.4603004H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h22.3491974c0.5615005,0,1.0158997-0.4543991,1.0158997-1.0158997   S39.16465,29.4603004,38.6031494,29.4603004z"></path>
                                    <path d="M28.4444485,37.5872993H16.253952c-0.5615005,0-1.0159006,0.4543991-1.0159006,1.0158997   s0.4544001,1.0158997,1.0159006,1.0158997h12.1904964c0.5615025,0,1.0158005-0.4543991,1.0158005-1.0158997   S29.0059509,37.5872993,28.4444485,37.5872993z"></path>
                                </g>
                            </svg>
                            </a>
                        </div>
                    </div>
                </modal-component>
            </div>
            <div>
                <modal-component :is-open="showWithDrawCashModal">
                    <div slot="body">
                        <div v-if="modalGoogle2fa.status">
                            <div>
                                <form v-if="modalGoogle2fa.password" method="POST"
                                      v-on:submit="functionWithCryptoGoogle2faActivate"
                                >
                                    <div v-if="modalGoogle2fa.panel" class="form-group row w-full mx-auto">
                                        <div v-if="modalGoogle2fa.qrkode" class="col-md-6 flex justify-center">
                                            <div class="flex justify-center items mx-auto p-1 border-1p border-terra-khaki-green rounded">
                                                <img v-bind:src="modalGoogle2fa.qrkode" />
                                            </div>
                                        </div>
                                        <div :class="[modalGoogle2fa.qrkode ? 'col-md-6 flex justify-center flex-col' : 'col-md-12 flex text-center justify-center flex-col']">
                                            <label for="one_time_password"
                                                   class="text-lg font-bold relative text-center"
                                            >{{ __('One Time Password') }}</label>
                                            <input type="number"
                                                   name="one_time_password"
                                                   maxlength="6"
                                                   minlength="6"
                                                   v-model="modalGoogle2fa.oneTimePassword"
                                                   id="one_time_password"
                                                   class="tracking-widest text-xl font-bold text-center mt-5 bg-terra-searchbar-green rounded-xl px-4 py-2 w-full focus:outline-none focus:shadow-outline"
                                                   required
                                                   autofocus
                                            />
                                            <input type="text"
                                                   name="google2faKey"
                                                   v-model="modalGoogle2fa.google2faKey"
                                                   class="hidden"
                                            />
                                            <div class="modalGoogle2faPasswordNot" style="display: none">
                                                <div class=" text-center invalid-feedback block" role="alert">
                                                    <strong class="error-message">Warning: @{{ modalGoogle2fa.one_time_password }}</strong>
                                                </div>
                                            </div>
                                            <div class="flex justify-center mt-5">
                                                <button type="submit"
                                                        class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90"
                                                >
                                                    {{ __('Verify') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                <div v-else>
                                    <div class="flex items-center justify-center">
                                        <img alt="Okey"
                                             src="/deneme/tick.png"
                                             style="max-height: 40px; margin-right:10px;"
                                        />
                                        <p class="text-lg font-bold relative text-center text-terra-khaki-green my-10">{{ __('Google Two Factor Authentication is Verified') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <div>
                                <div class="font-terramirum font-bold text-lg text-black text-center lg:w-3/4 mx-auto tracking-wide">
                                    You can perform transactions by filling in the required fields for the transfer.
                                </div>
                                <div class="font-terramirum font-bold text-lg text-red-700 text-center lg:w-3/4 mx-auto tracking-wide pb-3">You can only transfer to your own account</div>
                                <form class="w-full relative" method="post" id="withCashForm" v-on:submit="functionWithCashSubmitForm">
                                    @csrf
                                    <input type="text"
                                           name="iban"
                                           v-model="form.iban"
                                           id="iban"
                                           class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                           placeholder="IBAN"
                                           required
                                    />
                                    <input  type="text"
                                            name="full_name"
                                            value="{{ auth()->user()->name }}"
                                            id="full_name"
                                            placeholder="{{ auth()->user()->name }}"
                                            class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                            required
                                            disabled
                                    />
                                    <input type="number"
                                           name="amount"
                                           v-model="form.amount"
                                           id="amount"
                                           class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                           placeholder="Amount"
                                           required
                                    />
                                    <textarea name="message"
                                              v-model="form.message"
                                              cols="10"
                                              id="message"
                                              class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                              placeholder="Message"
                                              required
                                    ></textarea>
                                    <input type="hidden" name="currecy" value="isoCurrency" id="currency"/>
                                    <div id="myPopupModal" class="popup z-99999">
                                        <span id="popupMessageModal" class="popup-content"></span>
                                    </div>
                                    <button type="submit"
                                            class="col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300 dropdown-toggle"
                                    >
                                        Start Withdraw Action
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </modal-component>
            </div>
            <div>
                <modal-component :is-open="showWithDrawCryptoModal">
                    <div slot="body">
                        <div v-if="modalGoogle2fa.status">
                            <div>
                                <form v-if="modalGoogle2fa.password" method="POST"
                                      v-on:submit="functionWithCryptoGoogle2faActivate"
                                >
                                    <div v-if="modalGoogle2fa.panel" class="form-group row w-full mx-auto">
                                        <div v-if="modalGoogle2fa.qrkode" class="col-md-6 flex justify-center">
                                            <div class="flex justify-center items mx-auto p-1 border-1p border-terra-khaki-green rounded">
                                                <img v-bind:src="modalGoogle2fa.qrkode" />
                                            </div>
                                        </div>
                                        <div :class="[modalGoogle2fa.qrkode ? 'col-md-6 flex justify-center flex-col' : 'col-md-12 flex text-center justify-center flex-col']">
                                            <label for="one_time_password"
                                                   class="text-lg font-bold relative text-center"
                                            >{{ __('One Time Password') }}</label>
                                            <input type="number"
                                                   name="one_time_password"
                                                   maxlength="6"
                                                   minlength="6"
                                                   v-model="modalGoogle2fa.oneTimePassword"
                                                   id="one_time_password"
                                                   class="tracking-widest text-xl font-bold text-center mt-5 bg-terra-searchbar-green rounded-xl px-4 py-2 w-full focus:outline-none focus:shadow-outline"
                                                   required
                                                   autofocus
                                            />
                                            <input type="text"
                                                   name="google2faKey"
                                                   v-model="modalGoogle2fa.google2faKey"
                                                   class="hidden"
                                            />
                                            <div class="modalGoogle2faPasswordNot" style="display: none">
                                                <div class=" text-center invalid-feedback block" role="alert">
                                                    <strong class="error-message">Warning: @{{ modalGoogle2fa.one_time_password }}</strong>
                                                </div>
                                            </div>
                                            <div class="flex justify-center mt-5">
                                                <button type="submit"
                                                        class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90"
                                                >
                                                    {{ __('Verify') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                <div v-else>
                                    <div class="flex items-center justify-center">
                                        <img alt="Okey"
                                             src="/deneme/tick.png"
                                             style="max-height: 40px; margin-right:10px;"
                                        />
                                        <p class="text-lg font-bold relative text-center text-terra-khaki-green my-10">{{ __('Google Two Factor Authentication is Verified') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <div>
                                <div class="font-terramirum font-bold text-lg text-black text-center lg:w-3/4 mx-auto tracking-wide pb-3">
                                    You can perform transactions by filling in the required fields for the transfer.
                                </div>
                                <form class="w-full relative" method="post" id="withCryptoForm" v-on:submit="functionWithCryptoSubmitForm">
                                    @csrf
                                    <input type="hidden"
                                           name="currecy"
                                           v-model="form.currecy"
                                           id="currency"
                                    />
                                    <input type="text"
                                           name="wallet"
                                           v-model="form.wallet"
                                           id="wallet"
                                           class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                           placeholder="Wallet Address"
                                           required
                                    />
                                    <input type="number"
                                           name="amount"
                                           v-model="form.amount"
                                           id="amount"
                                           class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                           placeholder="Amount"
                                           required
                                    />
                                    <textarea name="message"
                                              id="message"
                                              cols="10"
                                              v-model="form.message"
                                              class="mb-3 text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-2 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                              placeholder="Message"
                                              required
                                    ></textarea>
                                    <div id="myPopupModal" class="popup z-99999">
                                        <span id="popupMessageModal" class="popup-content"></span>
                                    </div>
                                    <button type="submit"
                                            class="col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300 dropdown-toggle"
                                    >
                                        Start Withdraw Action
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </modal-component>
            </div>
        </div>
    </script>
    {{ __(/** javascript kodlarımız **/) }}
    <script>
        (() => {
            //-- Tarih Filtresi --//
            Vue.component('date-filter', {
                template: '#date-filter-template',
                data: function() {
                    return {
                        start: "{{ $startDate->format('Y-m-d') }}",
                        end: "{{ $endDate->format('Y-m-d') }}",
                    }
                },
                methods: {
                    applyFilter: function(field, date) {
                        this[field] = date;
                        this.$root.$refs.A.functionWalletTransferDetailTableList(this.start, this.end);
                    }
                }
            });
            //-- Mobil için Tarih Filtresi --//
            Vue.component('date-mobile-filter', {
                template: '#date-mobile-filter-template',
                data: function() {
                    return {
                        start: "{{ $startDate->format('Y-m-d') }}",
                        end: "{{ $endDate->format('Y-m-d') }}",
                        toggleCalenderIcon : 0
                    }
                },
                methods: {
                    openCalender: function(){
                        if(this.toggleCalenderIcon) {
                            this.toggleCalenderIcon = 0;
                            $('#calender-mobile span').css('top','0');
                        } else {
                            this.toggleCalenderIcon = 1;
                            $('#calender-mobile span').css('top','-40px');
                        }
                    },
                    setDate: function(field, date) {
                        this[field] = date;
                    },
                    applyFilter: function() {
                        this.$root.$refs.A.functionWalletTransferDetailTableList(this.start, this.end);
                    }
                }
            });
            //-- Tab Button Component --//
            Vue.component('wallettabbutton-component', {
                template: '#wallettabbutton-template',
                mounted: function() {
                    if(localStorage.getItem('showTab') == null) {
                        $('#show-tab-1').addClass('text-terra-khaki-green border-b-[5px] border-terra-khaki-green active');
                        $('#tab-1').removeClass('hiddenx');
                    }
                    $('#show-tab-' + localStorage.getItem('showTab')).addClass('text-terra-khaki-green border-b-[5px] border-terra-khaki-green active');
                    $('#tab-' + localStorage.getItem('showTab')).removeClass('hiddenx');
                },
                methods: {
                    showTab: function(value) {
                        localStorage.setItem('showTab', value);
                        $('.tab-table').addClass('hiddenx');
                        $('.tab-button').removeClass('text-terra-khaki-green border-b-[5px] border-terra-khaki-green active');
                        $('#show-tab-' + value).addClass('text-terra-khaki-green border-b-[5px] border-terra-khaki-green active');
                        $('#tab-' + value).removeClass('hiddenx');
                    }
                }
            });
            //-- Tab Table Component --//
            Vue.component('wallettabtable-component', {
                template: '#wallettabtable-template',
                data: function() {
                    return {
                        showDepositCashModal: false,
                        showDepositCryptoModal: false,
                        showWithDrawCashModal: false,
                        showWithDrawCryptoModal: false,

                        walletModalAccountId: null,
                        walletModalBankDetail: [],

                        walletBalancesAddress: null,
                        walletBalancesTableList: [],
                        walletBalancesChainId: null,

                        walletTableTransferDetailList: [],
                        modalGoogle2fa: {
                            panel: false,
                            status: true,
                            qrkode: null,
                            password: true,
                            google2faKey: null,
                            oneTimePassword: null,
                            one_time_password: null,
                        },
                        form: {
                            iban: null,
                            amount: null,
                            message: null,
                            isoCurrent: null,
                            wallet: null,
                        }
                    }
                },
                mounted: function() {
                    this.showDepositCashModal = false;
                    this.showDepositCryptoModal = false;
                    this.showWithDrawCashModal = false;
                    this.showWithDrawCryptoModal = false;
                    if(localStorage.getItem('showTab') == null) {
                        $('#tab-1').removeClass('hiddenx');
                    }
                    $('#tab-' + localStorage.getItem('showTab')).removeClass('hiddenx');
                },
                created: function() {
                    this.$root.$refs.A = this;
                    this.functionWalletTableList( null );
                    this.functionWalletTransferDetailTableList( null, null );
                },
                methods: {
                    //-- Tablo üstündeki cüzdan kodunu kopyalama işlemi --//
                    copyValue: function() {
                        var input = document.getElementById("wallet");
                        var value = input.value.split(":")[1].trim();
                        var tempInput = document.createElement("input");
                        tempInput.value = value;
                        document.body.appendChild(tempInput);
                        tempInput.select();
                        document.execCommand("copy");
                        document.body.removeChild(tempInput);
                        var popup = document.getElementById("myPopup");
                        var popupMessage = document.getElementById("popupMessage");
                        popupMessage.textContent = "Wallet Address copied: " + value;
                        popup.style.display = "block";
                        setTimeout(function () {
                            popup.style.display = "none";
                        }, 3000);
                    },
                    //-- Modal üstündeki cüzdan kodunu kopyalama işlemi --//
                    copyValueModal: function() {
                        var input = document.getElementById("walletModal");
                        var value = input.value.split(":")[1].trim();
                        var tempInput = document.createElement("input");
                        tempInput.value = value;
                        document.body.appendChild(tempInput);
                        tempInput.select();
                        document.execCommand("copy");
                        document.body.removeChild(tempInput);
                        var popup = document.getElementById("myPopupModal");
                        var popupMessage = document.getElementById("popupMessageModal");
                        popupMessage.textContent = "Wallet Address copied: " + value;
                        popup.style.display = "block";
                        setTimeout(function () {
                            popup.style.display = "none";
                        }, 3000);
                    },
                    //-- Deposit With Cash Modal --//
                    functionShowDepositCashModal: function( isoCurrency, chainId ) {
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route('customer.wallet.deposit.cash') }}',
                            {isoCurrency: isoCurrency, chainid: chainId}
                        ).then(response => {
                            this.$root.hideLoader();
                            this.walletModalAccountId = response.data.accountId;
                            this.walletModalBankDetail = response.data.bankDetail[0].bankDetailInfo;
                            this.showDepositCashModal = false;
                            this.showDepositCashModal = true;
                        }).catch(error => {

                        });
                        this.showDepositCryptoModal = false;
                        this.showWithDrawCashModal = false;
                        this.showWithDrawCryptoModal = false;
                    },
                    //-- Deposit With Crypto Modal --//
                    functionShowDepositCryptoModal: function() {
                        this.showDepositCryptoModal = false;
                        this.showDepositCryptoModal = true;

                        this.showDepositCashModal = false;
                        this.showWithDrawCashModal = false;
                        this.showWithDrawCryptoModal = false;
                    },
                    //-- Withdraw With Cash Modal --//
                    functionShowWithDrawCashModal: function() {
                        this.form.iban = null;
                        this.form.amount = null;
                        this.form.message = null;
                        this.modalGoogle2fa.oneTimePassword = null;
                        this.modalGoogle2fa.status = true;
                        this.modalGoogle2faEnabled = false;
                        this.showWithDrawCashModal = false;
                        this.showWithDrawCashModal = true;

                        this.showDepositCashModal = false;
                        this.modalGoogle2fa.password = true;
                        this.showDepositCryptoModal = false;
                        this.showWithDrawCryptoModal = false;
                        //-- --//
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route('customer.wallet.withdraw.google2fachecked') }}',
                            {Google2faChecked: 'Google2faChecked'}
                        ).then(response => {
                            this.$root.hideLoader();
                            this.modalGoogle2fa.panel = true;
                            this.modalGoogle2fa.status = response.data.status;
                            this.modalGoogle2fa.qrkode = response.data.qrkode;
                            this.modalGoogle2fa.google2faKey = response.data.google2faKey;
                        }).catch(error => {

                        });
                    },
                    //-- Withdraw With Crypto Modal --//
                    functionShowWithDrawCryptoModal: function( isoCurrent, chainId ) {
                        this.form.iban = null;
                        this.form.amount = null;
                        this.form.message = null;
                        this.form.isoCurrent = isoCurrent;
                        this.showWithDrawCryptoModal = false;
                        this.showWithDrawCryptoModal = true;
                        this.modalGoogle2fa.oneTimePassword = null;
                        this.modalGoogle2fa.status = true;
                        this.modalGoogle2faEnabled = false;
                        this.modalGoogle2fa.password = true;

                        this.showWithDrawCashModal = false;
                        this.showDepositCryptoModal = false;
                        this.showDepositCashModal = false;
                        //-- --//
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route('customer.wallet.withdraw.google2fachecked') }}',
                            {Google2faChecked: 'Google2faChecked'}
                        ).then(response => {
                            this.$root.hideLoader();
                            this.modalGoogle2fa.panel = true;
                            this.modalGoogle2fa.status = response.data.status;
                            this.modalGoogle2fa.qrkode = response.data.qrkode;
                            this.modalGoogle2fa.google2faKey = response.data.google2faKey;
                        }).catch(error => {

                        });
                    },
                    //-- Wallet Information Table List Refresh Button --//
                    functionWalletTableListRefreshButton: function( event ) {
                        this.functionWalletTableList(event.target.value);
                    },
                    //-- Wallet Information Table List --//
                    functionWalletTableList: function( chainId ) {
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route('customer.wallet.table.list') }}',
                            {chainId: chainId}
                        ).then(response => {
                            this.$root.hideLoader();
                            this.walletBalancesAddress = '{{ __('velocity::app-static.user-profile.wallet-address') }}: ' + response.data.address;
                            this.walletBalancesTableList = response.data.balances;
                            this.walletBalancesChainId = response.data.chainId
                        }).catch(error => {

                        });
                    },
                    //-- Wallet Transfer Detail Table List --//
                    functionWalletTransferDetailTableList: function( start, end ) {
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route('customer.wallet.table.transfer.detail.list') }}',
                            {start: start, end: end}
                        ).then(response => {
                            this.$root.hideLoader();
                            this.walletTableTransferDetailList = response.data;
                            console.log(this.walletTableTransferDetailList);
                        }).catch(error => {

                        });
                    },
                    //-- Wallet Transfer Detail Table List Filter Today --//
                    functionTableListFilterToday: function( start, end ) {
                        this.functionWalletTransferDetailTableList( start, end );
                    },
                    //-- Wallet Transfer Detail Table List Filter This Week --//
                    functionTableListFilterThisWeek: function( start, end ) {
                        this.functionWalletTransferDetailTableList( start, end );
                    },
                    //-- Wallet Transfer Detail Table List Filter This Month --//
                    functionTableListFilterThisMonth: function( start, end ) {
                        this.functionWalletTransferDetailTableList( start, end );
                    },
                    //-- Wallet Transfer Detail Table List Filter Three Month --//
                    functionTableListFilterLastThreeMonth: function( start, end ) {
                        this.functionWalletTransferDetailTableList( start, end );
                    },//-- WithDraw Cash Send Data --//
                    //-- WithCash Modal Panel Send Data --//
                    functionWithCashSubmitForm: function( event ) {
                        event.preventDefault(),
                        this.$root.showLoader();
                        axios
                            .post(
                                '{{route('customer.wallet.withdraw.cash.handler')}}',
                                {
                                    iban: this.form.iban,
                                    amount: this.form.amount,
                                    message: this.form.message
                                }
                            ).then(response => {
                            this.$root.hideLoader();
                            if(response.data.status == 'not') {
                                var popup = document.getElementById("myPopupModal");
                                var popupMessage = document.getElementById("popupMessageModal");
                                popupMessage.textContent = "Hata: " + response.data.error;
                                popup.style.display = "block";
                            } else {
                                var popup = document.getElementById("myPopupModal");
                                var popupMessage = document.getElementById("popupMessageModal");
                                popupMessage.textContent = "Başarılı: " + response.data.success;
                                popup.style.display = "block";
                                this.form.iban = null;
                                this.form.amount = null;
                                this.form.message = null;
                                event.target.reset();
                            }
                            setTimeout(function () {
                                popup.style.display = "none";
                            }, 3000);
                        }).catch(error => {

                        });
                    },
                    //-- WithCash Modal Panel Crypto Data --//
                    functionWithCryptoSubmitForm: function( event ) {
                        event.preventDefault();
                        this.$root.showLoader();
                        axios
                        .post(
                            '{{ route( 'customer.wallet.withdraw.crypto' ) }}',
                            {
                                currecy: this.form.isoCurrent,
                                wallet: this.form.wallet,
                                amount: this.amount,
                                message: this.form.message
                            }
                        ).then(response => {
                            this.$root.hideLoader();
                            if(response.data.isSuccessful == true) {
                                var popup = document.getElementById("myPopupModal");
                                var popupMessage = document.getElementById("popupMessageModal");
                                popupMessage.textContent = "Başarılı: " + response.data.errorMessage;
                                popup.style.display = "block";
                            } else {
                                var popup = document.getElementById("myPopupModal");
                                var popupMessage = document.getElementById("popupMessageModal");
                                popupMessage.textContent = "Hata: " + response.data.errorMessage;
                                popup.style.display = "block";
                                event.target.reset();
                            }
                            setTimeout(function () {
                                popup.style.display = "none";
                            }, 3000);
                        }).catch(error => {

                        });
                    },
                    //-- --//
                    functionWithCryptoGoogle2faActivate: function( event ) {
                        event.preventDefault();
                        event.target.reset();
                        axios
                        .post(
                            '{{ route( 'customer.wallet.withdraw.google2faactivate' ) }}',
                            {
                                key: this.modalGoogle2fa.google2faKey,
                                one_time_password: this.modalGoogle2fa.oneTimePassword,
                            }
                        ).then(response => {
                            this.modalGoogle2fa.password = response.data.password;
                            this.modalGoogle2fa.one_time_password = response.data.one_time_password;
                            setTimeout(() => this.modalGoogle2fa.status = response.data.status, 1500);
                            this.modalGoogle2fa.panel = false;
                        }).catch(error => {
                            this.modalGoogle2fa.one_time_password = error.response.data.message;
                        });
                    }
                }
            });
            $(document).on('click',function(e) {
                $('div.dropdown-menu').css('display', 'none');
                $('[data-toggle="dropdown"]').removeClass('active');
            });
            $('body').on('click', 'button[data-toggle="dropdown"]', function(event) {
                if($(this).hasClass('active') == true) {
                    $(this).parent('div').children('div.dropdown-menu').toggle('show');
                } else {
                    $(this).parent('div').children('div.dropdown-menu').toggle('hide');
                }
            });
        })();
    </script>
@endpush