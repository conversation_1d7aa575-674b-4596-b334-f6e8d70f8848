@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;
    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);
        $metaTitle = $homeSEO->meta_title;
        $metaDescription = $homeSEO->meta_description;
        $metaKeywords = $homeSEO->meta_keywords;
    }

    $applicationDataArray = [
        'walletTableRoute'        => route( 'customer.wallet.table.list' ),
        'deliveryListRoute'       => route( 'customer.delivery.point.list' ),
        'deliveryCreateRoute'     => route( 'customer.delivery.point.created' ),
        'convertPanel'            => __( 'velocity::app-gold.convert-to-physical.convertPanel' ),
        'convertModalPanel'       => __( 'velocity::app-gold.convert-to-physical.convertModalPanel' ),
        'deliveryPointErrorsText' => __( 'velocity::app-gold.convert-to-physical.deliveryPointErrorsText' ),
    ];
@endphp

@section('page_title')
    {{ __('velocity::app-gold.convert_to_physical') }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ __('velocity::app-gold.convert_to_physical') }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')
    <main>
        <!-- Activity -->
        <section class="relative py-24">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="container">
                <h1 class="py-16 text-center font-display text-4xl font-medium text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.convert_to_physical') !!}</h1>

                <div class="lg:flex">
                    <!-- Profile -->
                    <div class="mb-10 shrink-0 basis-4/12 space-y-3 lg:mb-0 lg:pr-10">
                        @include('shop::customers.account.left-menu')
                        @yield('left-menu')
                    </div>

                    <!-- Filters -->
                    <aside class="basis-8/12 lg:pl-5">
                        <convert-to-physical-component
                            application-data="{{ json_encode($applicationDataArray) }}"
                        ></convert-to-physical-component>
                    </aside>
                </div>
            </div>
        </section>
        <!-- end activity -->
    </main>
@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush