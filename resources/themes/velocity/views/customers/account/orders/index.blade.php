@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }

    $orderDataObject = [
        'orderError' => __('velocity::app-static.ordersPage.order-error'),
    ];
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')
    <main>
        <!-- Activity -->
        <section class="relative py-24">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="container">
                <h1 class="py-16 text-center font-display text-4xl font-medium text-jacarta-700 dark:text-white">{!! __('velocity::app-static.ordersPage.orders-page') !!}</h1>

                <div class="lg:flex">
                    <!-- Profile -->
                    <div class="mb-10 shrink-0 basis-4/12 space-y-3 lg:mb-0 lg:pr-10">
                        @include('shop::customers.account.left-menu')
                        @yield('left-menu')
                    </div>

                    <!-- Filters -->
                    <aside class="basis-8/12 lg:pl-5">
                        <orders-component
                            order-data-objects="{{ json_encode($orderDataObject) }}"
                            id="ordersComponent"
                            data-table-order-id="{!! __('velocity::app-static.ordersPage.table-order-id') !!}"
                            data-table-order-pcs="{!! __('velocity::app-static.ordersPage.table-order-pcs') !!}"
                            data-table-order-show="{!! __('velocity::app-static.ordersPage.table-order-show') !!}"
                            data-table-order-date="{!! __('velocity::app-static.ordersPage.table-order-date') !!}"
                            data-table-order-total="{!! __('velocity::app-static.ordersPage.table-order-total') !!}"
                            data-table-order-action="{!! __('velocity::app-static.ordersPage.table-order-action') !!}"
                            data-table-order-status="{!! __('velocity::app-static.ordersPage.table-order-status') !!}"
                            data-modal-content-tx-id="{!! __('velocity::app-static.ordersPage.modal-content-tx-id') !!}"
                            data-modal-content-table-pcs="{!! __('velocity::app-static.ordersPage.modal-content-table-pcs') !!}"
                            data-modal-content-table-sku="{!! __('velocity::app-static.ordersPage.modal-content-table-sku') !!}"
                            data-modal-content-title-text="{!! __('velocity::app-static.ordersPage.modal-content-title-text') !!}"
                            data-modal-content-table-name="{!! __('velocity::app-static.ordersPage.modal-content-table-name') !!}"
                            data-modal-content-payment-tax="{!! __('velocity::app-static.ordersPage.modal-content-payment-tax') !!}"
                            data-modal-content-table-price="{!! __('velocity::app-static.ordersPage.modal-content-table-price') !!}"
                            data-modal-content-table-total="{!! __('velocity::app-static.ordersPage.modal-content-table-total') !!}"
                            data-modal-content-payment-method="{!! __('velocity::app-static.ordersPage.modal-content-payment-method') !!}"
                            data-table-order-action-button-text="{!! __('velocity::app-static.ordersPage.table-order-action-button-text') !!}"
                            data-modal-content-payment-subtotal="{!! __('velocity::app-static.ordersPage.modal-content-payment-subtotal') !!}"
                            data-modal-content-payment-grand-total="{!! __('velocity::app-static.ordersPage.modal-content-payment-grand-total') !!}"
                            data-modal-content-cancel-text="{!! __('velocity::app-static.ordersPage.modal-content-cancel-text') !!}"
                            data-modal-button-cancel-text="{!! __('velocity::app-static.ordersPage.modal-button-cancel-text') !!}"
                            data-modal-button-success-text="{!! __('velocity::app-static.ordersPage.modal-button-success-text') !!}"
                        ></orders-component>

                    </aside>
                </div>
            </div>
        </section>
        <!-- end activity -->
    </main>


@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush