@extends('shop::layouts.default')

@section('page_title')
    {{ __('velocity::app-static.reset-password.page-title') }}
@endsection

@section('body')

    <!-- main-area -->
    <main class="fix">
        <!-- breadcrumb-area -->
        <section class="breadcrumb-area breadcrumb-bg">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="breadcrumb-content">
                            <h2 class="title">{!! __('velocity::app-static.reset-password.page-title') !!}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- breadcrumb-area-end -->

        <section id="loginForm" class="blog-area pt-50 pb-50">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class=" product-item">
                            <div class="d-flex align-items-center justify-content-center flex-column">
                                <div class="choose-icon ">
                                    <img style="width: 70%;" src="/deneme/svg/logo.svg" alt="" />
                                </div>
                                <div class="product-content">
                                    <h4 class="sub-title mb-0">
                                        {!! __('velocity::app-static.reset-password.section-title') !!}
                                    </h4>
                                </div>
                            </div>
                            <div id="form" class=" amount-section">
                                <form
                                    action="{{ route('customer.register.success.store') }}"
                                    method="POST"
                                    class="w-100 mt-5 row justify-content-center">
                                    @csrf
                                    @method('post')

                                    <input type="hidden" name="token" value="{{ $token }}">

                                    <div class="col-md-9 mb-3">
                                        <input
                                            type="email"
                                            name="email"
                                            id="email"
                                            class="w-100"
                                            placeholder="{{ __('velocity::app-static.reset-password.email') }}"
                                            required=""
                                            value="{{ old('email') }}"
                                        />
                                        @if($errors->has('email'))
                                            <span class="error">{{ $errors->first('email') }}</span>
                                        @endif
                                    </div>
                                    <div class="col-md-9 mb-3">
                                        <div class="input-group">
                                            <input
                                                id="password"
                                                type="password"
                                                name="password"
                                                class="w-100"
                                                placeholder="{{ __('velocity::app-static.reset-password.new-password') }}"
                                            />
{{--                                            <div class="input-group-append">--}}
{{--                                                <span class="input-group-text" @click="myFunction()" style="background-color: #495057; border: none;">--}}
{{--                                                    <i class="fa fa-eye-slash text-white" id="passIcon"></i>--}}
{{--                                                </span>--}}
{{--                                            </div>--}}
                                        </div>
                                        @if($errors->has('password'))
                                            <span class="error">{{ $errors->first('password') }}</span>
                                        @endif
                                    </div>
                                    <div class="col-md-9 mb-3">
                                        <div class="input-group">
                                            <input
                                                id="password_confirmation"
                                                type="password"
                                                name="password_confirmation"
                                                class="w-100"
                                                placeholder="{{ __('velocity::app-static.reset-password.confirm-password') }}"
                                            />
{{--                                            <div class="input-group-append">--}}
{{--                                                <span class="input-group-text" @click="myFunction()" style="background-color: #495057; border: none;">--}}
{{--                                                    <i class="fa fa-eye-slash text-white" id="passIcon"></i>--}}
{{--                                                </span>--}}
{{--                                            </div>--}}
                                        </div>
                                        @if($errors->has('password_confirmation'))
                                            <span class="error">{{ $errors->first('password_confirmation') }}</span>
                                        @endif
                                    </div>
                                    <div class="col-md-12 product-content">
                                        <div class="contract mt-4 d-flex justify-content-center w-100">
                                            <a href="{{ route('customer.session.index') }}"
                                               class="text-opacity-50 text-light">
                                                {!! __('velocity::app-static.forgot-password.remember-password') !!}
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-12 product-content">
                                        <div class="contract mt-4 d-flex justify-content-center w-100">
                                            <button type="submit" class="btn">{{ __('velocity::app-static.reset-password.submit-btn') }}</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <!-- main-area-end -->
@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
    {!! Captcha::renderJS() !!}
@endpush



