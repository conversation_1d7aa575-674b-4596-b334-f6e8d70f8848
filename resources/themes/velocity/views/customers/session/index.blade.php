@extends('shop::layouts.miligold-default')

@section('page_title')
    {{ __('velocity::app-static.login.page-title') }}
@endsection

@section('body')
    <main>
        <section class="relative bg-contain bg-right-top bg-no-repeat pt-32 pb-16">
            <div class="container relative z-10">
                <h1 class="text-center font-display text-4xl font-medium text-jacarta-700">{!! __('velocity::app-static.login.page-title') !!} </h1>
            </div>
        </section>

        <!-- Contact -->
        <section class="relative py-24 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="mx-auto max-w-5xl">
                <div class="lg:flex justify-center">
                    <!-- Contact Form -->
                    <div class="mb-12 lg:mb-0 lg:w-2/3 lg:pr-12">
                        <div id="form" class="rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700">
                            <loginform-component></loginform-component>
                            <div class="text-center mt-3">
                                <a href="{{route('customer.register.index')}}">{{__('velocity::app.customer.signup-form.page-title')}}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
    <script type="text/x-template" id="loginform-template">
        <div>
            <form
                @submit.prevent="loginFormSubmit"
                action="{{ route( 'customer.session.create' ) }}"
                method="POST"
                class="col-md-12 amount-content row"
            >
                {{ csrf_field() }}
                <div class="mb-6 w-full">
                    <label for="email" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                        {!! __('velocity::app-static.login.email-placeholder') !!}
                        <span class="text-red">*</span>
                    </label>
                    <input
                        name="email" id="email" type="email" required=""
                        v-model="email"
                        class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                        value="{{ old('email') }}"
                        :class="`form-control ${errors.email != null ? 'error' : ''}`"
                    />
                    <span class="error">@{{ errors.email }}</span>
                </div>
                <div class="mb-6 w-full">
                    <label for="email" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                        {!! __('velocity::app-static.login.password-placeholder') !!}
                        <span class="text-red">*</span>
                    </label>
                        <input
                            id="password"
                            type="password"
                            name="password"
                            v-model="password"
                            class="contact-form-input w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-[#D0AA49]/10 focus:ring-[#D0AA49] dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                            :class="`form-control ${errors.password != null ? 'error' : ''}`"
                        />
                        <div class="input-group-append">
                            <span class="input-group-text h-full" @click="myFunction()" style="background-color: #495057; border: none;">
                                <i class="fa fa-eye-slash text-white" id="passIcon"></i>
                            </span>
                        </div>
                    <span class="error">@{{ errors.password }}</span>
                </div>
                    <div class="mt-4 flex justify-center w-full">
                        <a href="{{ route('customer.forgot-password.create') }}" class="text-opacity-50 text-light">
                            {!! __('velocity::app-static.login.forgot-your-password') !!}
                        </a>
                    </div>
                <div class="flex justify-center w-full my-4">
                    <button type="submit" class="rounded-full bg-[#D0AA49] py-3 px-8 text-center font-semibold text-white shadow-milyem-volume transition-all hover:bg-[#d0aa49ad] hover:text-[#000000]">
                        {!! __('velocity::app-static.login.singin-button') !!}
                    </button>
                </div>
            </form>
        </div>
    </script>
    <script>
        // aoutoFocus işlemi
        $(function(){
            $(":input[name=email]").focus();
        });

        // login form
        Vue.component('loginform-component', {
            template: '#loginform-template',
            data: function() {
                return {
                    errors: {
                        email: null,
                        password: null
                    },
                    email: null,
                    password: null,
                }
            },
            mounted: function() {},
            created: function() {},
            methods: {
                loginFormSubmit: function( event ) {
                    if (this.email && this.password) {
                        event.target.submit();
                        return true;
                    }
                    this.errors = [];
                    if (!this.email) {
                        this.errors.email = "{!! __('velocity::app-static.login.email-required') !!}";
                    } else if(!this.validEmail(this.email)) {
                        this.errors.email = "{!! __('velocity::app-static.login.email-error-text') !!}";
                    }
                    if (!this.password) {
                        this.errors.password = "{!! __('velocity::app-static.login.password-error-text') !!}";
                    }
                    event.preventDefault();
                },
                myFunction: function() {
                    var x = document.getElementById('password');
                    if (x.type === 'password') {
                        x.type = 'text';
                        $('#passIcon').addClass('fa-eye');
                        $('#passIcon').removeClass('fa-eye-slash');
                    } else if (x.type === 'text') {
                        x.type = 'password';
                        $('#passIcon').removeClass('fa-eye');
                        $('#passIcon').addClass('fa-eye-slash');
                    }
                },
                validEmail: function (email) {
                    var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                    return re.test(email);
                }
            }
        });

        const app = new Vue({
            el: '#form'
        });
    </script>

    {!! Captcha::renderJS() !!}
@endpush



