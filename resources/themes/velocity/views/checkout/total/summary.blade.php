

<div class="order-summary fs16 bg-terra-light-wheat py-3 px-8 rounded-2xl mb-2">
    {{-- <h3 class="fw6">{{ __('velocity::app.checkout.cart.cart-summary') }}</h3> --}}
    <div class="font-terramirum font-bold text-xl tracking-wide text-terra-khaki-green pb-3 border-b-1 border-bordergray">
        {{ __('velocity::app.checkout.cart.cart-summary') }}
        {{-- Sepet Özeti --}}
    </div>
@if(!empty($catalog_rules) && empty($cart->applied_cart_rule_ids))

    {{-- <img src="/deneme/svg/announce.svg" alt=""> --}}

    {{-- <div class="row">
        <span class="col-6 col-lg-7 text-lg text-black font-terramirum">
            Amount benefiting from the campaign
        </span>
        <span class="col-6 col-lg-5 text-right text-lg text-black font-terramirum font-bold">{{ core()->currency($base_total) }}</span>
    </div> --}}

        @foreach ($catalog_rules as $key)
        <div class="rounded-2xl bg-terra-light-khaki-green px-4 py-2 w-full mb-2">
            <div class="flex justify-between items-center">
                <div class="flex">
                    <img src="/deneme/svg/dot.svg" class="mr-2">
                    <div>{{$cart->base_total}}</div>
                    <div class="text-sm text-black font-terramirum"> {{ $key->action_type == "by_percent" ? "%" : core()->getCurrentCurrency()->symbol }} {{ (int)$key->discount_amount }} {{__('velocity::app-static.cart.campaign-info')}} <b>{{$key->name}}</b> </div>
                </div>

            </div>
        </div>

        @endforeach


@endif


    <div class="row">
        <span class="col-6 col-lg-7 text-lg text-black font-terramirum">
            {{ __('velocity::app.checkout.total') }}
            {{-- Toplam Tutar --}}
        </span>
        <span class="col-6 col-lg-5 text-right text-lg text-black font-terramirum font-bold">{{ core()->currency($cart->base_sub_total) }}</span>
    </div>

    {{-- <div class="flex justify-between my-6">
        <div class="text-lg text-black font-terramirum">Toplam İndirim Tutarı</div>
        <div class="text-lg text-black font-terramirum font-bold">-100,00 ₺</div>
    </div>
    <div class="mb-2">
        <div class="text-sm text-black font-terramirum">Kampanya İndirim Tutarı</div>
    </div>
    <div class="rounded-2xl bg-terra-light-khaki-green px-2 py-2">
        <div class="flex justify-between items-center">
            <div class="flex">
                <img src="/deneme/svg/dot.svg" class="mr-2">
                <div class="text-sm text-black font-terramirum">2. Alışverişe özel %10 konut indirimi</div>
            </div>
            <div class="text-base text-black font-terramirum font-bold">-100,00 ₺</div>
        </div>
    </div> --}}


    {{-- @if ($cart->selected_shipping_rate)
        <div class="row">
            <span class="col-8">{{ __('shop::app.checkout.total.delivery-charges') }}</span>
            <span class="col-4 text-right">{{ core()->currency($cart->selected_shipping_rate->base_price) }}</span>
        </div>
    @endif --}}

    @if ($cart->base_tax_total)
        @foreach (Webkul\Tax\Helpers\Tax::getTaxRatesWithAmount($cart, true) as $taxRate => $baseTaxAmount )
            <div class="row">
                <span class="col-6 col-lg-7" id="taxrate-{{ core()->taxRateAsIdentifier($taxRate) }}">
                     {{ __('velocity::app.checkout.vat') }} %{{ $taxRate }} </span>
                <span class="col-6 col-lg-5 text-right text-base text-black font-terramirum font-bold" id="basetaxamount-{{ core()->taxRateAsIdentifier($taxRate) }}">{{ core()->currency($baseTaxAmount) }}</span>
            </div>
        @endforeach
    @endif

    @if (
        $cart->base_discount_amount
        && $cart->base_discount_amount > 0
    )
        <div
            id="discount-detail"
            class="row">

    {{--            <div class="mb-2">--}}
    {{--                <div class="text-sm text-black font-terramirum">{{ $coupon_name }}</div>--}}
    {{--            </div>--}}

            <div class="rounded-2xl bg-terra-light-khaki-green px-4 py-2 w-full">
                <div class="flex justify-between items-center">
                    <div class="flex">
                        <img src="/deneme/svg/dot.svg" class="mr-2">
                        <div class="text-sm text-black font-terramirum">{{ $coupon_desc }} </div>
                    </div>
                    <span class="text-right text-base text-black font-terramirum font-bold">
                        {{ core()->currency($cart->base_discount_amount) }}
                    </span>

                </div>
            </div>

            {{-- <span class="col-8">{{ __('shop::app.checkout.total.disc-amount') }}</span> --}}

        </div>
    @endif



    <div class="payable-amount row justify-content-between" id="grand-total-detail">
        <span class="col-6 col-lg-5 text-base text-black font-terramirum">
            {{ __('velocity::app.checkout.amount-to-paid') }}
            {{-- Ödenecek Tutar --}}
        </span>
        <span class="col-6 col-lg-5 text-right fw6 text-base text-black font-terramirum font-bold" id="grand-total-amount-detail">
            {{ core()->currency($cart->base_grand_total) }}
        </span>
    </div>

    <div class="row">
        @php
            $minimumOrderAmount = (float) core()->getConfigData('sales.orderSettings.minimum-order.minimum_order_amount') ?? 0;
        @endphp

        @if (Cart::hasError())
            <button class="theme-btn text-uppercase col-12 remove-decoration fw6 text-center" disabled>
                {{ __('velocity::app.checkout.proceed') }}
            </button>
        @else
            <proceed-to-checkout
                href="{{ route('shop.checkout.onepage.index') }}"
                add-class="font-terramirum font-bold bg-terra-orange text-white rounded-[18px] py-3 px-5 text-center text-xl w-full tracking-wider uppercase buybutton"
                text="{{ __('shop::app.checkout.cart.buy') }}"
                {{-- text="SATIN AL" --}}
                style="box-shadow: 0 0px 6px rgb(0 0 0 / 42%)"
                is-minimum-order-completed="{{ $cart->checkMinimumOrder() }}"
                minimum-order-message="{{ __('shop::app.checkout.cart.minimum-order-message', ['amount' => core()->currency($minimumOrderAmount)]) }}">
            </proceed-to-checkout>
        @endif
    </div>
</div>