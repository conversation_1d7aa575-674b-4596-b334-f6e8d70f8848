@component('shop::emails.layouts.master')
    @section('content')
        <div style="display: flex;justify-content: start;align-items: center;margin-top: 15px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="38.823" height="45.374" viewBox="0 0 38.823 45.374">
                <g id="Group_4695" data-name="Group 4695" transform="translate(-30 -74.313)">
                    <g id="Group_3446" data-name="Group 3446" transform="translate(30 74.313)">
                        <g id="Group_3447" data-name="Group 3447" transform="translate(0 0)">
                            <path id="Path_1970" data-name="Path 1970"
                                  d="M9.018,0H25.564c.023.017.044.044.07.051A3.236,3.236,0,0,1,28.3,3.586q0,9.185-.009,18.37a.773.773,0,0,0,.414.769,12.1,12.1,0,0,1,5.574,12.7,11.727,11.727,0,0,1-8.064,9.232,11.763,11.763,0,0,1-12.106-2.5,1.157,1.157,0,0,0-.823-.316q-4.888.016-9.777.006A3.258,3.258,0,0,1,0,38.356q0-5.29.011-10.58Q.011,18.79,0,9.8A4.379,4.379,0,0,1,1.123,6.73C2.794,4.88,4.45,3.017,6.111,1.159A2.916,2.916,0,0,1,7.98.166C8.327.123,8.672.057,9.018,0m3.141,40.048c-2.949-4.975-2.935-9.866.551-14.416,3.531-4.609,8.338-5.62,13.818-4.06v-.419q0-8.83,0-17.66a1.494,1.494,0,0,0-1.711-1.705H9.425c0,.211,0,.369,0,.527,0,1.062.014,2.125,0,3.187A3.75,3.75,0,0,1,6.3,9.335a5.609,5.609,0,0,1-1.013.052c-1.147.006-2.294,0-3.478,0-.009.2-.023.356-.023.514q0,14.23-.009,28.459a1.56,1.56,0,0,0,1.72,1.7c2.727-.044,5.455-.014,8.183-.014Zm20.575-6.921A10.44,10.44,0,1,0,22.281,43.562,10.476,10.476,0,0,0,32.733,33.128M2.751,7.595c1.113,0,2.158.07,3.188-.02A1.864,1.864,0,0,0,7.618,5.629c.007-.383,0-.766,0-1.149q0-1.071,0-2.141L7.5,2.278,2.751,7.595"
                                  transform="translate(0 -0.001)" fill="#ffe4c1" />
                            <path id="Path_1971" data-name="Path 1971"
                                  d="M30.4,58.41h-.479c-2.728,0-5.456-.03-8.183.014a1.56,1.56,0,0,1-1.72-1.7q.021-14.23.009-28.459c0-.158.014-.316.023-.514,1.185,0,2.332,0,3.478,0a5.6,5.6,0,0,0,1.013-.052,3.75,3.75,0,0,0,3.122-3.832c.017-1.062,0-2.124,0-3.187V20.15H43.061a1.494,1.494,0,0,1,1.711,1.705q0,8.83,0,17.66v.419c-5.479-1.56-10.286-.548-13.818,4.06-3.486,4.55-3.5,9.441-.551,14.416m2.014-21.275c2.123,0,4.246,0,6.369-.005a1.383,1.383,0,0,0,.636-.121.818.818,0,0,0,.406-.982.883.883,0,0,0-.969-.662q-6.436,0-12.871,0c-.654,0-1.04.327-1.048.874-.008.568.376.9,1.063.9q3.207,0,6.413,0m-.024-5.307c2.167,0,4.335.005,6.5,0a.881.881,0,0,0,.9-1.212A.955.955,0,0,0,38.8,30.05q-6.015.01-12.031,0c-.324,0-.65-.014-.973,0a.879.879,0,0,0-.827,1.111.889.889,0,0,0,.972.657q3.229.006,6.458,0M27.481,42.446c.574,0,1.148.01,1.722,0a.882.882,0,1,0-.014-1.763q-1.678-.014-3.356,0a.882.882,0,1,0,.014,1.763c.544.011,1.089,0,1.634,0"
                                  transform="translate(-18.245 -18.363)" fill="#ffe4c1" />
                            <path id="Path_1972" data-name="Path 1972"
                                  d="M154.5,265.965A10.44,10.44,0,1,1,144.031,255.5,10.482,10.482,0,0,1,154.5,265.965m-1.95-.016a8.487,8.487,0,0,0-4.492-7.507,8.291,8.291,0,0,0-3.5-.978.885.885,0,0,0-.146,1.764c.277.041.558.055.832.108a6.717,6.717,0,1,1-6.018,1.948,1.535,1.535,0,0,0,.388-.574.817.817,0,0,0-.389-.989.84.84,0,0,0-1.12.177,8.247,8.247,0,0,0-2.458,7.125,8.485,8.485,0,0,0,16.9-1.075m-9.147,2.245c-.721-.609-1.392-1.208-2.1-1.757a1.069,1.069,0,0,0-.792-.239,1.24,1.24,0,0,0-.674.6.771.771,0,0,0,.279.946c.82.7,1.636,1.4,2.463,2.088a1.488,1.488,0,0,0,2.512-.808,11.547,11.547,0,0,1,3.235-5.726.907.907,0,1,0-1.257-1.269,12.989,12.989,0,0,0-2.927,4.223c-.263.619-.479,1.259-.736,1.942m-2.882-9.395a.882.882,0,1,0,1.764.021.907.907,0,0,0-.852-.895.9.9,0,0,0-.912.874"
                                  transform="translate(-121.765 -232.838)" fill="#ffe4c1" />
                            <path id="Path_1973" data-name="Path 1973" d="M31.014,30.989l4.747-5.317.123.06q0,1.071,0,2.141c0,.383,0,.766,0,1.149A1.864,1.864,0,0,1,34.2,30.969c-1.031.09-2.075.02-3.188.02" transform="translate(-28.263 -23.395)" fill="#ffe4c1" />
                            <path id="Path_1974" data-name="Path 1974" d="M82.966,193.427q-3.207,0-6.413,0c-.687,0-1.071-.328-1.063-.9.008-.547.393-.874,1.048-.874q6.436,0,12.871,0a.883.883,0,0,1,.969.662.818.818,0,0,1-.406.982,1.383,1.383,0,0,1-.636.121c-2.123.008-4.246.005-6.369.005" transform="translate(-68.793 -174.654)" fill="#ffe4c1" />
                            <path id="Path_1975" data-name="Path 1975" d="M82.909,133.521q-3.229,0-6.458,0a.889.889,0,0,1-.972-.657.879.879,0,0,1,.827-1.111c.323-.019.649,0,.973,0q6.015,0,12.031,0a.955.955,0,0,1,1.005.563.881.881,0,0,1-.9,1.212c-2.167.009-4.335,0-6.5,0" transform="translate(-68.759 -120.055)" fill="#ffe4c1" />
                            <path id="Path_1976" data-name="Path 1976" d="M78.029,253.271c-.545,0-1.09.009-1.634,0a.882.882,0,1,1-.014-1.763q1.678-.016,3.356,0a.882.882,0,1,1,.014,1.763c-.574.013-1.148,0-1.722,0" transform="translate(-68.793 -229.188)" fill="#ffe4c1" />
                            <path id="Path_1977" data-name="Path 1977" d="M172.6,286.081a8.485,8.485,0,0,1-16.9,1.075,8.247,8.247,0,0,1,2.458-7.125.841.841,0,0,1,1.12-.177.818.818,0,0,1,.389.989,1.535,1.535,0,0,1-.388.573,6.708,6.708,0,1,0,6.018-1.948c-.274-.053-.556-.067-.832-.108a.885.885,0,0,1,.146-1.764,8.293,8.293,0,0,1,3.5.978,8.487,8.487,0,0,1,4.492,7.507" transform="translate(-141.82 -252.97)" fill="#ffe4c1" />
                            <path id="Path_1978" data-name="Path 1978" d="M206.507,331.3c.257-.683.473-1.323.736-1.942a12.991,12.991,0,0,1,2.927-4.223.907.907,0,1,1,1.257,1.269,11.547,11.547,0,0,0-3.235,5.726,1.488,1.488,0,0,1-2.512.808c-.827-.689-1.643-1.391-2.463-2.088a.771.771,0,0,1-.279-.946,1.24,1.24,0,0,1,.674-.6,1.069,1.069,0,0,1,.792.239c.71.548,1.381,1.147,2.1,1.757" transform="translate(-184.871 -295.947)" fill="#ffe4c1" />
                            <path id="Path_1979" data-name="Path 1979" d="M211.4,283.672a.9.9,0,0,1,.912-.874.907.907,0,0,1,.852.895.882.882,0,1,1-1.764-.021" transform="translate(-192.647 -257.71)" fill="#ffe4c1" />
                        </g>
                    </g>
                    <g id="Group_3444" data-name="Group 3444" transform="translate(35.282 76.366)">
                        <g id="Group_3443" data-name="Group 3443" clip-path="url(#clip-path)">
                            <path id="Path_1965" data-name="Path 1965"
                                  d="M8.76,0H24.832A.249.249,0,0,0,24.9.049a3.038,3.038,0,0,1,2.592,3.292q0,8.557-.009,17.114a.714.714,0,0,0,.4.717,11.185,11.185,0,0,1-2.418,20.436,11.789,11.789,0,0,1-11.759-2.325,1.151,1.151,0,0,0-.8-.294q-4.748.015-9.5.006A3.1,3.1,0,0,1,0,35.733q0-4.928.01-9.856Q.011,17.505,0,9.134A3.982,3.982,0,0,1,1.091,6.269C2.714,4.546,4.322,2.81,5.936,1.079A2.871,2.871,0,0,1,7.751.155C8.089.114,8.423.053,8.76,0M11.81,37.309c-2.865-4.635-2.851-9.192.536-13.43,3.43-4.293,8.1-5.235,13.422-3.782v-.39q0-8.226,0-16.452c0-1.079-.535-1.589-1.662-1.589H9.154c0,.2,0,.344,0,.491,0,.99.013,1.979,0,2.969A3.534,3.534,0,0,1,6.119,8.7a5.678,5.678,0,0,1-.984.049c-1.114.005-2.228,0-3.379,0-.009.184-.022.331-.022.479q0,13.256-.009,26.513A1.485,1.485,0,0,0,3.4,37.323c2.649-.041,5.3-.013,7.949-.013ZM31.8,30.862a9.975,9.975,0,0,0-10.168-9.746,9.985,9.985,0,0,0-10.114,9.719,9.988,9.988,0,0,0,10.129,9.748A9.981,9.981,0,0,0,31.8,30.862M2.672,7.075c1.081,0,2.1.065,3.1-.018A1.762,1.762,0,0,0,7.4,5.244c.007-.357,0-.714,0-1.07q0-1,0-1.995l-.119-.056L2.672,7.075"
                                  transform="translate(0 -0.001)" fill="#ff9201" />
                            <path id="Path_1966" data-name="Path 1966" d="M82.752,193.306q-3.115,0-6.23,0c-.668,0-1.041-.305-1.033-.835.008-.51.382-.814,1.018-.814q6.251,0,12.5,0a.856.856,0,0,1,.941.617.751.751,0,0,1-.394.915,1.393,1.393,0,0,1-.618.113c-2.062.008-4.124,0-6.187,0" transform="translate(-68.985 -175.817)" fill="#ff9201" />
                            <path id="Path_1967" data-name="Path 1967" d="M82.7,133.4q-3.136,0-6.273,0a.861.861,0,0,1-.944-.612.826.826,0,0,1,.8-1.035c.314-.017.63,0,.945,0q5.843,0,11.686,0a.935.935,0,0,1,.977.525c.248.569-.182,1.127-.878,1.129-2.105.008-4.21,0-6.316,0" transform="translate(-68.951 -120.854)" fill="#ff9201" />
                            <path id="Path_1968" data-name="Path 1968" d="M77.956,253.15c-.529,0-1.058.008-1.587,0a.823.823,0,1,1-.014-1.643q1.63-.015,3.26,0a.823.823,0,1,1,.014,1.642c-.557.012-1.115,0-1.673,0" transform="translate(-68.985 -230.713)" fill="#ff9201" />
                            <path id="Path_1969" data-name="Path 1969" d="M206.4,330.856c.25-.637.46-1.232.715-1.809a12.106,12.106,0,0,1,2.844-3.934.909.909,0,0,1,1.328-.12c.394.363.363.861-.107,1.3a10.654,10.654,0,0,0-3.142,5.334,1.463,1.463,0,0,1-2.44.752c-.8-.641-1.6-1.3-2.392-1.945a.7.7,0,0,1-.271-.881,1.189,1.189,0,0,1,.655-.559,1.068,1.068,0,0,1,.769.223c.689.511,1.341,1.069,2.042,1.637" transform="translate(-185.387 -297.918)" fill="#ff9201" />
                        </g>
                    </g>
                </g>
            </svg>
            <div style="font-weight: 600;color: #407D38;font-size: 18px;margin-left: 6px;">
                {{ __('mail.subscription.title', ['app_name' => config('app.name')]) }}
            </div>
        </div>
        <div style="display: flex;">
            <div style="width: 100%; margin-top: 20px;padding-bottom: 10px;background-color: #E2E9DD; padding: 10px;border-radius: 8px;display: flex;flex-wrap: wrap; ">
                <div style="width: 100%;">
                    <div style="font-weight: 600;color: #000000;font-size: 12px;margin-left: 6px;">
                        {{ __('mail.subscription.summary', ['email' => $email]) }}
                    </div>
                </div>
            </div>
        </div>
    @endsection
@endcomponent