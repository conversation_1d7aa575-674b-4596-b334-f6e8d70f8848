@extends('shop::emails.layouts.miligold-mail')

@section('content')
    <div style="margin-bottom: 20px; display: flex; align-items: center; gap: 20px; padding: 0 20px;">
        <img src="{{ asset('miligold/img/icons/change_password.svg') }}" width="48">
        <h2 style="margin: 0; font-weight: bold; color: #D09800; font-size: 24px;">{!! __('mail.customer.auth.forgot_password.greeting') !!}</h2>
    </div>

    <div style="background-color: #ffffff; border-radius: 35px; box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1); padding: 20px; background-image: url('{{ asset('miligold/img/pattern.png') }}'); background-repeat: repeat; background-position: center;">
        <div>
            <p style="font-size: 14px; color: #333;">{!! __('mail.customer.auth.forgot_password.dear', ['name' => $customer_name]) !!}</p>

            <div style="margin: 20px 0;">
                <a href="{{ route('customer.reset-password.create', $token) }}"
                   style="color: #ffffff;font-size: 14px;font-weight: 600; background-color:#D09800;padding:8px 30px; border-radius: 6px;text-decoration: none;">
                    {{ __('mail.customer.auth.forgot_password.reset-password') }}
                </a>
            </div>

            <p style="font-size: 14px; color: #555;">{!! __('mail.customer.auth.forgot_password.summary') !!}</p>
            <p style="font-size: 14px; color: #555;">{!! __('mail.customer.auth.forgot_password.thanks') !!}</p>
        </div>
    </div>
@endsection
