<template>
    <div class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
        <div
            class="modal__container"
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-1-title"
            style="box-shadow:0 34px 35px rgba(160, 171, 191, 0.21);
            width: 100%;"
        >
            <header class="modal__header" style="height:20px;">
                <h2
                    class="modal__title"
                    :class="isModalAddressId === 0 ? 'modal-h2-title-80': 'modal-h2-title-70'"
                    id="modal-1-title"
                >
                    {{ isModalAddressId !== 0 ? myModalAddressText.modalEditTitle: myModalAddressText.modalAddedTitle }}
                </h2>
                <button
                    class="modal__close"
                    aria-label="Close modal"
                    @click="functionCloseMyAddresshModal"
                ></button>
            </header>
            <main
                id="modal-1-content"
                style="margin-top:0;"
                class="modal__content popup-info-wrap"
            >
                <div v-if="isModalTypeStatus === 'added' || isModalTypeStatus === 'edit'">
                    <div v-if="isInModal" class="myAddressEditModal">
                        <form
                            method="post"
                            @submit.prevent="functionOnSubmitAddress"
                        >
                            <div class="account-table-content mb-8 w-full mt-8">
                                <div class="control-group mb-5">
                                    <label for="company_name" class="col-12 modal-input-text-left">
                                        {{ myModalAddressText.modalCompanyTitle }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="company_name"
                                            v-model="myAddressData.company_name"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error"></span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="first_name" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalFirstName }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="first_name"
                                            v-model="myAddressData.first_name"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textFirstName )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textFirstName) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textFirstName }}</span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="last_name" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalLastName }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="last_name"
                                            v-model="myAddressData.last_name"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textLastName )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textLastName) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textLastName }}</span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="vat_id" class="col-12 modal-input-text-left">
                                        {{ myModalAddressText.modalVatID }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="vat_id"
                                            v-model="myAddressData.vat_id"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error"></span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="address1" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalStreetAddress }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="address1"
                                            v-model="myAddressData.address1"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textStreetAddress )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textStreetAddress) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textStreetAddress }}</span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="country" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalCountry }}
                                    </label>
                                    <select
                                        id="country"
                                        type="text"
                                        name="country"
                                        v-model="myAddressData.country"
                                        @change="functionInputValidateControl( myModalAddressText.errorText.textCountry )"
                                        class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                    >
                                        <option value="">{{ myModalAddressText.modalCountry }}</option>
                                        <option
                                            v-for="item in myAddressData.countryArray"
                                            :value="item?.code"
                                            :selected="item?.code === myAddressData.country"
                                        >{{ item?.name }}</option>
                                    </select>
                                    <div class="select-icon-container">
                                        <span class="select-icon rango-arrow-down"></span>
                                    </div>
                                    <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textCountry) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textCountry }}</span>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="city" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalCity }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="city"
                                            v-model="myAddressData.city"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textCity )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textCity) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textCity }}</span>
                                    </div>
                                </div>

                                <div class="control-group mb-5">
                                    <label for="state" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalState }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="state"
                                            v-model="myAddressData.state"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textState )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textState) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textState }}</span>
                                    </div>
                                </div>

                                <div class="control-group mb-5">
                                    <label for="postcode" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalPostalCode }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="postcode"
                                            maxlength="5"
                                            v-model="myAddressData.postcode"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textPostalCode ); functionPostCodeCheck( myAddressData.postcode )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textPostalCode) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textPostalCode }}</span>
                                    </div>
                                </div>
                                <div class="control-group mb-5">
                                    <label for="phone" class="col-12 modal-input-text-left required">
                                        {{ myModalAddressText.modalPhone }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            type="text"
                                            id="phone"
                                            v-model="myAddressData.phone"
                                            placeholder="(*************"
                                            @keyup="functionInputValidateControl( myModalAddressText.errorText.textPhone ); functionPhoneCodeCheck( myAddressData.phone )"
                                            class="w-full rounded-lg border-jacarta-100 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                                        />
                                        <span class="control-error" :style="`${errors.includes(myModalAddressText.errorText.textPhone) ? 'display:block!important;': ''}`">{{ myModalAddressText.errorText.textPhone }}</span>
                                    </div>
                                </div>
                                <div class="control-group mb-5 row">
                                    <label for="default_address" class="col-12 modal-input-text-left">
                                        {{ myModalAddressText.modalDefaultAddress }}
                                    </label>
                                    <div class="col-12">
                                        <input
                                            id="default_address"
                                            type="checkbox"
                                            checked="checked"
                                            v-model="myAddressData.default_address"
                                        />
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="rounded-full bg-green py-3 px-8 text-center font-semibold text-white hover:bg-green-600 transition-all ease-in-out">
                                {{ isModalAddressId !== 0 ? myModalAddressText.modalSendButtonText: myModalAddressText.modalAddedTitle }}
                            </button>
                        </form>
                    </div>
                    <div v-else class="myAddressEditStatus">
                        <img :src="modalAlertStatus === true ? '/images/checked.png': '/images/warning.png'" :class="modalAlertStatus === true ? 'success-icon':'warning-icon'"  alt=""/>
                        <h1 class="py-16 text-center font-display text-4xl font-medium text-jacarta-700 dark:text-white">{{ modalAlertMessage }}</h1>
                    </div>
                </div>
                <div v-if="isModalTypeStatus === 'delete'">
                    <div>
                        <img src="/images/warning.png" class="warning-icon"  alt=""/>
                        <h4 class="py-16 text-center font-display text-xl font-medium text-jacarta-700 dark:text-white">
                            {{ modalAlertMessage === '' ? myModalAddressText.modalAddressDeleteText: modalAlertMessage }}
                        </h4>
                    </div>
                    <div v-if="modalAlertMessage === ''" class="space-x-4">
                        <button
                            type="button"
                            @click="functionDeleteMyAddresshModal"
                            class="rounded-full bg-green py-3 px-8 text-center font-semibold text-white hover:bg-green-600 transition-all ease-in-out"
                        >
                            evet
                        </button>
                        <button
                            type="button"
                            @click="functionCloseMyAddresshModal"
                            class="rounded-full bg-red py-3 px-8 text-center font-semibold text-white hover:bg-red-400 transition-all ease-in-out"
                        >
                            Hayır
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>
</template>

<script>
    import {EventBus} from "../../app";
    export default {
        name: 'modalMyAddressComponent',
        data() {
            return {
                errors: [],
                isInModal: true,
                myAddressData: {
                    city: '',
                    state: '',
                    vat_id: '',
                    phone: '',
                    country: '',
                    last_name: '',
                    address1: '',
                    address2: '',
                    postcode: '',
                    first_name: '',
                    company_name: '',
                    countryArray: [],
                    default_address: ''
                },
                isModalAddressId: '',
                modalAlertMessage: '',
                modalAlertStatus: true,
                myModalAddressText: [],
                isModalTypeStatus: null,
            }
        },
        mounted() {},
        created() {
            this.isModalAddressId = this.$parent.modalMyAddress.isModalAddressId;
            this.isModalTypeStatus = this.$parent.modalMyAddress.isModalTypeStatus;
            if(this.isModalAddressId !== 0) {
                this.functionMyAddressEdit();
            } else {
                this.functionCountries();
            }
            this.myModalAddressText = this.$parent.propsDataObject.addressModal;
        },
        methods: {
            /**
             * Bu function ile Countries Listesini çekiyoruz.
             */
            functionCountries: function() {

                try {
                    axios
                        .get(
                            this.$parent.propsDataObject.countriesListRoute
                        )
                        .then(response => {
                            this.myAddressData.countryArray = response?.data?.countyArray;
                        });
                } catch (err) {

                }

            }, /** end functionCountries: function() **/
            /**
             * Bu function ile Formu tetikliyoruz.
             * Function içinde ise yeni ekleme veya Güncelleme
             * işlemi için ilgili function tetikleniyor.
             */
            functionOnSubmitAddress: function() {

                if(this.isModalAddressId === 0) {
                    this.functionOnSubmitAddedAddress();
                } else {
                    this.functionOnSubmitUpdateAddress();
                }

            }, /** end functionOnSubmitAddress: function() **/
            /**
             * Bu function ile Yeni Adres Ekliyoruz.
             */
            functionOnSubmitAddedAddress: function() {

                try {
                    this.errors = [];
                    if(this.myAddressData.first_name &&
                       this.myAddressData.last_name &&
                       this.myAddressData.postcode &&
                       this.myAddressData.address1 &&
                       this.myAddressData.country &&
                       this.myAddressData.state &&
                       this.myAddressData.city &&
                       this.myAddressData.phone
                    ) {
                        const keys = {};
                        const formData = this.myAddressData;
                        for(const [key, value] of Object.entries(formData)) {
                            if (key !== 'countryArray') {
                                if (key === 'address1') {
                                    keys[key] = value.split(' ');
                                } else {
                                    keys[key] = value;
                                }
                            }
                        }
                        axios
                            .post(
                                '/customer/account/addresses/createjs',
                                keys
                            )
                            .then(response => {
                                this.isInModal = false;
                                if (response?.data.status === true) {
                                    this.modalAlertStatus = response?.data.status;
                                    this.modalAlertMessage = response?.data.success;
                                    this.$parent.functionAddressList();
                                    setTimeout(() => this.functionCloseMyAddresshModal(), 2000);
                                } else {
                                    this.modalAlertStatus = response?.data.status;
                                    this.modalAlertMessage = response?.data.warning;
                                }
                            }).catch(function (response) {
                            console.log("catchErrResp::", response);
                        });
                    }
                    if(!this.myAddressData.first_name) {
                        this.errors.push(this.myModalAddressText.errorText.textFirstName);
                    }
                    if(!this.myAddressData.last_name) {
                        this.errors.push(this.myModalAddressText.errorText.textLastName);
                    }
                    if(!this.myAddressData.postcode) {
                        this.errors.push(this.myModalAddressText.errorText.textPostalCode);
                    }
                    if(!this.myAddressData.country) {
                        this.errors.push(this.myModalAddressText.errorText.textCountry);
                    }
                    if(!this.myAddressData.city) {
                        this.errors.push(this.myModalAddressText.errorText.textCity);
                    }
                    if(!this.myAddressData.state) {
                        this.errors.push(this.myModalAddressText.errorText.textState);
                    }
                    if(!this.myAddressData.phone) {
                        this.errors.push(this.myModalAddressText.errorText.textPhone);
                    }
                    if(!this.myAddressData.address1) {
                        this.errors.push(this.myModalAddressText.errorText.textStreetAddress);
                    }
                } catch (err) {
                    console.log(err);
                }

            }, /** end functionOnSubmitAddedAddress: function() **/
            /**
             * Bu function ile Adres Güncelleme işlemini yapıyoruz.
             */
            functionOnSubmitUpdateAddress: function() {

                try {
                    this.errors = [];
                    if(this.myAddressData.first_name &&
                        this.myAddressData.last_name &&
                        this.myAddressData.postcode &&
                        this.myAddressData.address1 &&
                        this.myAddressData.country &&
                        this.myAddressData.city &&
                        this.myAddressData.phone) {
                        const keys = {};
                        const formData = this.myAddressData;
                        for(const [key, value] of Object.entries(formData)) {
                            if(key !== 'countryArray') {
                                if(key === 'address1') {
                                    keys[key] = value.split(' ');
                                } else {
                                    keys[key] = value;
                                }
                            }
                        }
                        axios
                            .put(
                                '/customer/account/addresses/updatejs/' + this.$parent.modalMyAddress.isModalAddressId,
                                keys
                            )
                            .then(response => {
                                this.isInModal = false;
                                if(response?.data.status === true) {
                                    this.modalAlertStatus = response?.data.status;
                                    this.modalAlertMessage = response?.data.success;
                                    this.$parent.functionAddressList();
                                    setTimeout(() => this.functionCloseMyAddresshModal(), 2000);
                                } else {
                                    this.modalAlertStatus = response?.data.status;
                                    this.modalAlertMessage = response?.data.warning;
                                }
                            }).catch(function (response) {
                                console.log("catchErrResp::",response);
                            });
                    }
                    if(!this.myAddressData.first_name) {
                        this.errors.push(this.myModalAddressText.errorText.textFirstName);
                    }
                    if(!this.myAddressData.last_name) {
                        this.errors.push(this.myModalAddressText.errorText.textLastName);
                    }
                    if(!this.myAddressData.postcode) {
                        this.errors.push(this.myModalAddressText.errorText.textPostalCode);
                    }
                    if(!this.myAddressData.country) {
                        this.errors.push(this.myModalAddressText.errorText.textCountry);
                    }
                    if(!this.myAddressData.city) {
                        this.errors.push(this.myModalAddressText.errorText.textCity);
                    }
                    if(!this.myAddressData.phone) {
                        this.errors.push(this.myModalAddressText.errorText.textPhone);
                    }
                    if(!this.myAddressData.address1) {
                        this.errors.push(this.myModalAddressText.errorText.textStreetAddress);
                    }
                } catch (err) {

                }

            }, /** end functionOnSubmitAddress: function() **/
            /**
             * Bu function ile Modal kapatıyoruz.
             */
            functionCloseMyAddresshModal: function() {

                this.$parent.modalMyAddress.isModalAddressModal = false;

            }, /** end functionCloseMyAddresshModal: function() **/
            /**
             * Bu function ile Adres Düzenleme Alanlarını Dolduruyoruz.
             */
            functionMyAddressEdit: function() {

                try {
                    axios
                        .get(
                            '/customer/account/addresses/editjs/' + this.$parent.modalMyAddress.isModalAddressId
                        )
                        .then(response => {
                            this.myAddressData.countryArray   = response?.data?.countyArray
                            this.myAddressData.city           = response?.data?.address?.city;
                            this.myAddressData.state           = response?.data?.address?.state;
                            this.myAddressData.vat_id          = response?.data?.address?.vat_id;
                            this.myAddressData.phone          = response?.data?.address?.phone;
                            this.myAddressData.country        = response?.data?.address?.country;
                            this.myAddressData.last_name       = response?.data?.address?.last_name;
                            this.myAddressData.address1       = response?.data?.address?.address1.replace(/\n/g, ' ');
                            this.myAddressData.address2       = response?.data?.address?.address2;
                            this.myAddressData.postcode       = response?.data?.address?.postcode;
                            this.myAddressData.first_name      = response?.data?.address?.first_name;
                            this.myAddressData.company_name    = response?.data?.address?.company_name;
                            this.myAddressData.default_address = response?.data?.address?.default_address;
                        });
                } catch (err) {

                }

            }, /** end functionMyAddressEdit: function() **/
            /**
             * Bu function ile Mevcut Adresi Siliyoruz.
             */
            functionDeleteMyAddresshModal: function() {

                try {
                    axios
                        .delete(
                            '/customer/account/addresses/deletejs/' + this.$parent.modalMyAddress.isModalAddressId,
                        )
                        .then(response => {
                            this.isInModal = false;
                            if(response?.data.status === true) {
                                this.modalAlertStatus = response?.data.status;
                                this.modalAlertMessage = response?.data.success;
                                this.$parent.functionAddressList();
                                //setTimeout(() => this.functionCloseMyAddresshModal(), 2000);
                            } else {
                                this.modalAlertStatus = response?.data.status;
                                this.modalAlertMessage = response?.data.warning;
                            }
                        }).catch(function (response) {
                        console.log("catchErrResp::",response);
                    });
                } catch (err) {

                }

            }, /** end functionDeleteMyAddresshModal: function() **/
            /**
             * Bu function ile Modal Panelide Bulunan INPUT, SELECT Validate alanını temizliyoruz.
             * @param value
             */
            functionInputValidateControl: function( value ) {

                if(this.errors.indexOf(value) !== -1) {
                    this.errors.splice(this.errors.indexOf(value), 1);
                }

            }, /** end functionInputValidateControl: function( value ) **/
            /**
             * Bu function ile PostaKodu Giriş Alanını Filtreliyoruz.
             * @param input
             */
            functionPostCodeCheck: function( input ) {

                if (!/^\d{1,5}$/.test(input)) {
                    this.errors.push(this.myModalAddressText.errorText.textPostalCode);
                }

            }, /** end functionPostCodeCheck: function( input ) **/
            /**
             * Bu function ile Telefon Girişini Formatlıyoruz.
             * @param input
             */
            functionPhoneCodeCheck: function( input ) {

                document.getElementById('phone').addEventListener('input', function (e) {
                    var x = e.target.value.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/);
                    e.target.value = !x[2] ? x[1] : '(' + x[1] + ') ' + x[2] + (x[3] ? '-' + x[3] : '');
                });

            } /** end functionPhoneCodeCheck: function( input ) **/

        }
    }
</script>

<style scoped>
    .modal-h2-title-70 {
        position: relative;
        left: -50%;
        margin-left: 10px !important;
    }
    .modal-h2-title-80 {
        position: relative;
        left: -65%;
        margin-left: 10px !important;
    }
    .modal-input-text-left {
        display: block;
        text-align: start!important;
        font-family: CalSans-SemiBold, sans-serif;
    }
    .success-icon {
        display: block;
        width: 80px;
        padding: 15px;
        margin: 0 auto;
        margin-top: 40px;
        margin-bottom: -30px;
        border-radius: 50%;
        background: green;
    }
    .warning-icon {
        display: block;
        width: 100px;
        margin: 0 auto;
        margin-top: 40px;
        margin-bottom: -30px;
    }
    .modal__container {
        background-color: #ffffff;
        padding: 22px 15px;
        max-width: 600px;
        max-height: 80vh;
        border-radius: 4px;
        overflow-y: auto;
        box-sizing: border-box;
    }
    .required::after {
        content: "*";
        font-size: 16px;
        margin-left: -1px;
        color: #F05153;
    }
    .control-error {
        display: none;
        color: #F05153;
        text-align: left;
    }
</style>