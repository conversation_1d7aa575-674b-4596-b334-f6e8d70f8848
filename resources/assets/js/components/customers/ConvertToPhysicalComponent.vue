<template>
    <div class="row">
        <div class="flex-wrap flex justify-center">
            <form
                class="mb-5"
                method="POST"
                enctype="multipart/form-data"
                @submit.prevent="functionDeliveryAppointmentSubmit"
            >
                <div class="flex flex-wrap justify-center items-end rounded-2.5xl border border-[#D0AA49] bg-white p-10 dark:border-jacarta-600 dark:bg-jacarta-700">
                    <div class="w-1/2 px-3 mb-6">
                        <div class="font-display text-sm text-jacarta-700 dark:text-white text-left whitespace-nowrap mb-1">{{ deliveryPoint?.applicationData?.convertPanel?.isoCurrencyText }} :</div>
                        <div class="w-full text-xl rounded-lg border-2 border-jacarta-200 py-2.5 px-4 focus:ring-milyem text-center flex items-center justify-center space-x-3">
                            <div class="inline-flex h-[2.5rem] w-[2.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                                <picture>
                                    <source srcset="/miligold/img/miligram-coin.png" type="image/webp">
                                    <source srcset="/miligold/img/miligram-coin.png" type="image/png">
                                    <img src="/miligold/img/miligram-coin.png" alt="MiliGold" class="rounded-2lg" loading="lazy">
                                </picture>
                            </div>
                            <h3 class="font-display text-xl text-jacarta-700 dark:text-white text-left whitespace-nowrap">
                                <span v-if="convertibleStatus === true">Loading...</span>
                                <span v-else>{{ (convertible?.balance  / Math.pow(10, convertible?.exponent)).toFixed(convertible?.exponent === 0 ? 2: 2) }} {{ convertible?.chainCurrency }}</span>
                            </h3>
                        </div>
                        <span class="control-error" :style="`${deliveryPoint.errors.includes('axx') ? 'display:block!important;': ''}`">{{ 'axx' }}</span>
                    </div>
                    <div class="mb-6 w-1/2 px-3">
                        <label for="amount" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                            {{ deliveryPoint?.applicationData?.convertPanel?.isoCurrencyAmount }}
                            <span class="text-red">*</span>
                        </label>
                        <div class="relative">
                            <span
                                @click="functionDecrementCount"
                                :class="deliveryPoint.form.amount === 1 ?'pointer-events-none': 'pointer-events-auto'"
                                class=" absolute top-2 left-2 rounded-lg bg-milyem px-6 py-2 font-display text-2xl text-white hover:opacity-90 cursor-pointer"
                            >
                                -
                            </span>
                            <input
                                name="count"
                                type="number"
                                v-model="deliveryPoint.form.pointAmount"
                                @keyup="functionInputValidateControl( deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryAmountError )"
                                :placeholder="deliveryPoint?.applicationData?.convertPanel?.isoCurrencyAmountPlaceholder"
                                class="w-full text-xl rounded-lg border-2 border-jacarta-200 py-4 px-4 focus:ring-milyem text-center"
                            />
                            <span
                                @click="functionIncrementCount"
                                class=" absolute top-2 right-2 rounded-lg bg-milyem px-6 py-2 font-display text-2xl text-white hover:opacity-90 cursor-pointer pointer-events-auto"
                            >
                                +
                            </span>
                        </div>
                    </div>
                    <div class="mb-6 w-1/2 px-3">
                        <label
                            for="deliveryPoint"
                            class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white"
                        >
                            {{ deliveryPoint?.applicationData?.convertPanel?.deliveryPointText }}
                            <span class="text-red">*</span>
                        </label>
                        <select
                            id="deliveryPoint"
                            name="deliveryPoint"
                            v-model="deliveryPoint.form.deliveryPoint"
                            @change="functionInputValidateControl( deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointOptionError )"
                            class="w-full rounded-lg border-jacarta-100 py-3 pl-3 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                        >
                            <option disabled value="">{{ deliveryPoint?.applicationData?.convertPanel?.deliveryPointOptionText }}</option>
                            <option v-for="item in deliveryPoint.deliveryPointSelectArray" :value="item?.id">{{ item?.title }}</option>
                        </select>
                        <span class="control-error" :style="`${deliveryPoint.errors.includes(deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointOptionError) ? 'display:block!important;': ''}`">{{ deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointOptionError }}</span>
                    </div>
                    <div class="mb-6 w-1/2 px-3">
                        <label for="deliveryDateTime" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                            {{ deliveryPoint?.applicationData?.convertPanel?.deliveryPointDateTimeText }}
                            <span class="text-red">*</span>
                        </label>
                        <datetime
                            type="datetime"
                            id="deliveryDateTime"
                            zone="Europe/Istanbul"
                            name="deliveryDateTime"
                            value-zone="Europe/Istanbul"
                            format="dd-MM-yyyy HH:mm:ss"
                            :phrases="{ok: 'OK', cancel: 'Cancel'}"
                            v-model="deliveryPoint.form.deliveryDateTime"
                            @blur="functionInputValidateControl( deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointDateTimeError )"
                            input-class="w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300">
                        </datetime>
                        <span class="control-error" :style="`${deliveryPoint.errors.includes(deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointDateTimeError) ? 'display:block!important;': ''}`">{{ deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointDateTimeError }}</span>
                    </div>
                    <div class="mb-6 w-1/2 px-3">
                        <label for="pointPhone" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                            {{ deliveryPoint?.applicationData?.convertPanel?.deliveryPointPhoneText }}
                            <span class="text-red">*</span>
                        </label>
                        <input
                            type="text"
                            id="pointPhone"
                            v-mask="'(###) ### ## ##'"
                            v-model="deliveryPoint?.form.pointPhone"
                            @keyup="functionInputValidateControl( deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointPhoneError ); functionPhoneCodeCheck( deliveryPoint?.form.pointPhone )"
                            class="w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                        />
                        <span class="control-error" :style="`${deliveryPoint.errors.includes(deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointPhoneError) ? 'display:block!important;': ''}`">{{ deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointPhoneError }}</span>
                    </div>
                    <div class="mb-6 w-1/2 px-3">
                        <label for="pointEmail" class="mb-1 block font-display text-sm text-jacarta-700 dark:text-white">
                            {{ deliveryPoint?.applicationData?.convertPanel?.deliveryPointEmailText }}
                            <span class="text-red">*</span>
                        </label>
                        <input
                            type="email"
                            id="pointEmail"
                            v-model="deliveryPoint?.form.pointEmail"
                            @blur="validEmail( deliveryPoint?.form.pointEmail )"
                            @keyup="functionInputValidateControl( deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointEmailError )"
                            class="w-full rounded-lg border-jacarta-100 py-3 hover:ring-2 hover:ring-milyem/10 focus:ring-milyem dark:border-jacarta-600 dark:bg-jacarta-700 dark:text-white dark:placeholder:text-jacarta-300"
                        />
                        <span class="control-error" :style="`${deliveryPoint.errors.includes(deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointEmailError) ? 'display:block!important;': ''}`">{{ deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointEmailError }}</span>
                    </div>
                    <div class="flex justify-center w-full">
                        <button class="rounded-full bg-green py-3 px-8 text-center font-semibold text-white hover:bg-green-600 transition-all ease-in-out">
                            {{ deliveryPoint?.applicationData?.convertPanel?.physicalGoldAppointment }}
                        </button>
                    </div>
                    <p class="text-sm text-red text-center mt-4">
                        {{ deliveryPoint?.applicationData?.convertPanel?.deliveryPontWaringnText }}
                    </p>
                </div>
            </form>
        </div>
        <div v-if="modal.statusModal" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
            <div
                class="modal__container"
                role="dialog"
                aria-modal="true"
                aria-labelledby="modal-1-title"
                style="box-shadow:0 34px 35px rgba(160, 171, 191, 0.21);width: 100%;"
            >
                <header class="modal__header" style="height:20px;">
                    <h2
                        class="modal__title"
                        id="modal-1-title"
                    ></h2>
                    <button
                        class="modal__close"
                        aria-label="Close modal"
                        @click="functionCloseModal"
                    ></button>
                </header>
                <main
                    id="modal-1-content"
                    style="margin-top:0;"
                    class="modal__content popup-info-wrap"
                >
                    <div>
                        <img :src="modal.alertStatus === true ? '/images/checked.png': '/images/warning.png'" :class="modal.alertStatus === true ? 'success-icon':'warning-icon'"  alt=""/>
                        <h1 class="py-16 text-center font-display text-4xl font-medium text-jacarta-700 dark:text-white">{{ modal.alertMessage }}</h1>
                    </div>
                </main>
            </div>
        </div>
    </div>
</template>

<script>
    import { Datetime } from 'vue-datetime';
    import 'vue-datetime/dist/vue-datetime.css';
    import {EventBus} from "../../app";

    Vue.use(Datetime);
    export default {
        components: {
            datetime: Datetime
        },
        props: {
            applicationData:Array|Object,
        },
        data() {
            return {
                modal: {
                    statusModal: false,
                    alertStatus: false,
                    alertMessage: '',
                },
                convertible: [],
                convertibleStatus: true,
                deliveryPoint: {
                    errors: [],
                    form: {
                        pointAmount: 1,
                        pointPhone: '',
                        pointEmail: '',
                        deliveryPoint: '',
                        deliveryDateTime: ''
                    },
                    deliveryPointSelectArray: [],
                    applicationData: JSON.parse(this.applicationData),
                }
            }
        },
        mounted() {
            this.functionDeliveryPointList();
            this.functionSelectCoinList();
            console.log(this.deliveryPoint.applicationData.deliveryListRoute)
        },
        created() {},
        methods: {
            /**
             * Bu function ile Adet Artıyor.
             */
            functionIncrementCount: function() {

                this.deliveryPoint.form.pointAmount++;

            }, /** end functionIncrementCount: function() **/
            /**
             * Bu function ile Adet Düşüyor.
             */
            functionDecrementCount: function() {

                if(this.deliveryPoint.form.pointAmount > 1) {
                    this.deliveryPoint.form.pointAmount--;
                }

            }, /** end functionDecrementCount: function() **/
            /**
             * Bu function ile Teslimat Noktası Listeleniyor.
             */
            functionDeliveryPointList: function() {

                try {
                    axios
                        .get(
                            this.deliveryPoint.applicationData.deliveryListRoute
                        )
                        .then(response => {
                            this.deliveryPoint.deliveryPointSelectArray = response?.data?.data?.data;
                        });
                } catch (err) {

                }

            }, /** end functionDeliveryPointList: function() **/
            /**
             * Bu function ile Teslimat Randevusunu Kayıt Altına Alıyoruz.
             */
            functionDeliveryAppointmentSubmit: function() {

                try {
                    if(this.deliveryPoint.form.pointPhone &&
                       this.deliveryPoint.form.pointEmail &&
                       this.deliveryPoint.form.deliveryPoint &&
                       this.deliveryPoint.form.deliveryDateTime
                    ) {
                        axios
                            .post(
                                this.deliveryPoint.applicationData.deliveryCreateRoute,
                                this.deliveryPoint.form
                            )
                            .then(response => {
                                this.modal.alertStatus = true;
                                this.modal.statusModal = true;
                                this.modal.alertMessage = this.deliveryPoint?.applicationData?.convertModalPanel?.transactionSuccessful;
                            });
                    }
                    if(!this.deliveryPoint.form.pointPhone) {
                        this.deliveryPoint.errors.push(this.deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointPhoneError);
                    }
                    if(!this.deliveryPoint.form.pointEmail) {
                        this.deliveryPoint.errors.push(this.deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointEmailError);
                    }
                    if(!this.deliveryPoint.form.deliveryPoint) {
                        this.deliveryPoint.errors.push(this.deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointOptionError);
                    }
                    if(!this.deliveryPoint.form.deliveryDateTime) {
                        this.deliveryPoint.errors.push(this.deliveryPoint?.applicationData?.deliveryPointErrorsText?.deliveryPointDateTimeError);
                    }
                } catch (err) {

                }

            }, /** end functionDeliveryAppointmentSubmit: function() **/
            /**
             * Bu function ile Modal Panelide Bulunan INPUT, SELECT Validate alanını temizliyoruz.
             * @param value
             */
            functionInputValidateControl: function( value ) {

                if(this.deliveryPoint.errors.indexOf(value) !== -1) {
                    this.deliveryPoint.errors.splice(this.deliveryPoint.errors.indexOf(value), 1);
                }

            }, /** end functionInputValidateControl: function( value ) **/
            /**
             * Bu function ile Telefon Girişini Formatlıyoruz.
             * @param input
             */
            functionPhoneCodeCheck: function( input ) {

                document.getElementById('pointPhone').addEventListener('input', function (e) {
                    var x = e.target.value.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/);
                    e.target.value = !x[2] ? x[1] : '(' + x[1] + ') ' + x[2] + (x[3] ? '-' + x[3] : '');
                });

            }, /** end functionPhoneCodeCheck: function( input ) **/
            /**
             *
             * @param email
             * @returns {boolean}
             */
            validEmail: function (email) {

                var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);

            }, /** end validEmail: function (email) **/
            /**
             *
             */
            functionCloseModal: function () {
                this.modal.statusModal = false;
            }, /** end functionCloseModal: function () **/
            /**
             *
             */
            functionSelectCoinList: function() {

                axios
                    .post(
                        this.deliveryPoint.applicationData.walletTableRoute,
                        {
                            chainId: 'mirum-testnet'
                        }
                    )
                    .then(response => {
                        this.convertible = response?.data?.data?.balances[0];
                        if(this.convertible === undefined) {
                            this.convertibleStatus = true;
                        } else {
                            this.convertibleStatus = false;
                        }
                    }).catch(error => {

                });

            }, /** end functionSelectCoinList: function() **/
        }
    };
</script>

<style scoped>
    .success-icon {
        display: block;
        width: 80px;
        padding: 15px;
        margin: 0 auto;
        margin-top: 40px;
        margin-bottom: -30px;
        border-radius: 50%;
        background: green;
    }
    .warning-icon {
        display: block;
        width: 100px;
        margin: 0 auto;
        margin-top: 40px;
        margin-bottom: -30px;
    }
    .modal__container {
        background-color: #ffffff;
        padding: 22px 15px;
        max-width: 600px;
        max-height: 80vh;
        border-radius: 4px;
        overflow-y: auto;
        box-sizing: border-box;
    }
    .required::after {
        content: "*";
        font-size: 16px;
        margin-left: -1px;
        color: #F05153;
    }
    .control-error {
        display: none;
        color: #F05153;
        text-align: left;
    }
</style>