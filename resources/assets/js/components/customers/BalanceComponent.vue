<template>
    <div class="row">
        <div v-if="walletError.status" class="w-full md:w-1/2 my-12">
            <div class="flex flex-col space-y-2 rounded-2lg border border-jacarta-100 bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                <span class="text-xl font-bold uppercase text-milyem">Oops</span>
                <span class="text-base text-jacarta-700 dark:text-white" v-html="walletError.message"></span>
            </div>
        </div>
        <div v-else class="flex-wrap flex justify-center my-12">
            <div
                v-for="item in walletTable.walletBalanceList"
                class="w-1/2 p-3 pt-0 dark:border-jacarta-600 dark:bg-jacarta-700 mb-12"
                v-if="item.isoCurrency !== 'MIRUM'"
            >
                <div class=" rounded-2.5xl border border-jacarta-100 bg-white text-center transition-shadow hover:shadow-xl">
                    <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                        <picture>
                            <source srcset="/miligold/img/miligram-coin.png" type="image/webp">
                            <source srcset="/miligold/img/miligram-coin.png" type="image/png">
                            <img src="/miligold/img/miligram-coin.png" alt="MiliGold" class="rounded-2lg" loading="lazy">
                        </picture>
                    </div>
                    <h3 class="mb-4 font-display text-xl text-jacarta-700 dark:text-white">{{ (item.balance / Math.pow(10, item.exponent)).toFixed(item.exponent === 0 ? 2: 2) }} {{ item.isoCurrency }}</h3>
                    <div v-if="item.isoCurrency === 'MLGR'" class="flex space-x-4 justify-around w-full mb-4">
                        <div class="relative group-dropdown cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-shadow group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" >
                            <a
                                href="/miligram"
                                class="dropdown-toggle group font-bold text-base text-jacarta-700 group-hover:text-[#caa754] dark:text-white whitespace-nowrap "
                                id="depositDropdown"
                            >
                                {{ item.isoCurrency }} {{ balanceCart.panelDropButtonDepositBuyText }}
                            </a>
                        </div>
                        <div class="relative group-dropdown cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-shadow group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" >
                            <a
                                href="/miligram"
                                class="dropdown-toggle group font-bold text-base text-jacarta-700 group-hover:text-[#caa754] dark:text-white whitespace-nowrap "
                                id="withdrawDropdown"
                            >
                                {{ item.isoCurrency }} {{ balanceCart.panelDropButtonWithdrawSellText }}
                            </a>
                        </div>
                    </div>
                    <div v-else class="flex space-x-4 justify-center w-full pt-4 mb-4">
                        <template>
                            <div ref="dropdownWrapper" class="relative inline-block text-left">
                                <button
                                    @click="toggleDropdown"
                                    class="inline-flex items-center justify-center font-medium mb-auto p-2 px-4 rounded-full border transition focus:outline-none focus:ring-2 bg-terra-gold text-white border-terra-gold-dark focus:ring-terra-gold-light">
                                    {{ item.isoCurrency }} {{ balanceCart.panelDropButtonDepositText }}
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="ml-2 h-4 w-4">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                    </svg>
                                </button>

                                <div v-if="showDropdown" class="absolute z-20 mt-2 w-56 overflow-hidden rounded-xl border border-gray-100 bg-white shadow-xl transition-all duration-200">
                                    <a
                                        v-for="method in walletSources"
                                        :key="method.code"
                                        :href="method.route"
                                        class="block w-full px-4 py-2 text-left text-sm text-gray-700 transition hover:bg-gray-100">
                                        {{ method.title }}
                                    </a>
                                </div>
                            </div>
                        </template>

                        <template>
                            <a
                                :href="walletWithdrawRoute"
                                class="inline-flex items-center justify-center font-medium mb-auto p-2 px-4 rounded-full border transition focus:outline-none focus:ring-2 bg-terra-gold text-white border-terra-gold-dark focus:ring-terra-gold-light">
                                {{ item.isoCurrency }} {{ balanceCart.panelDropButtonWithdrawText }}
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="ml-2 h-4 w-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
                                </svg>
                            </a>
                        </template>

                    </div>
                </div>
            </div>
        </div>
        <modalWithDrawCashComponent v-if="modalWithDrawCashTransfer.isModalWithDrawCashModal">
            <template v-slot:selectChainText>
                {{ modalWithDrawCashTransfer.walletChainText }}
            </template>
            <template v-slot:panelCurrency>
                {{ modalWithDrawCashTransfer.chainCurrency }}
            </template>
            <template v-slot:panelBalance>
                {{ modalWithDrawCashTransfer.walletBalance }}
            </template>
            <template v-slot:panelPendingBalance>
                {{ modalWithDrawCashTransfer.walletPedding }}
            </template>
            <template v-slot:panelWalletAddress>
                {{ modalWithDrawCashTransfer.walletAddress }}
            </template>
        </modalWithDrawCashComponent>
        <modalWithDrawCryptoComponent v-if="modalWithDrawCryptoTransfer.isModalWithDrawCryptoModal">
            <template v-slot:selectChainText>
                {{ modalWithDrawCryptoTransfer.walletChainText }}
            </template>
            <template v-slot:panelCurrency>
                {{ modalWithDrawCryptoTransfer.chainCurrency }}
            </template>
            <template v-slot:panelBalance>
                {{ modalWithDrawCryptoTransfer.walletBalance }}
            </template>
            <template v-slot:panelPendingBalance>
                {{ modalWithDrawCryptoTransfer.walletPedding }}
            </template>
            <template v-slot:panelWalletAddress>
                {{ modalWithDrawCryptoTransfer.walletAddress }}
            </template>
        </modalWithDrawCryptoComponent>
        <modalWithDrawCryptoDepositComponent v-if="modalWithDrawCryptoDepositInformation.isModalWithDrawCryptoDepositModal"></modalWithDrawCryptoDepositComponent>
        <modalWithDrawCashDepositComponent v-if="modalWithDrawCashDepositInformation.isModalWithDrawCashDepositModal"></modalWithDrawCashDepositComponent>
    </div>
</template>

<script>
    import {EventBus} from "../../app";
    export default {
        data() {
            return {
                showDropdown: false,
                walletSources: [],
                walletSourcesRoute: document.getElementById('balanceComponent').dataset.walletSourcesRoute,
                walletWithdrawRoute: document.getElementById('balanceComponent').dataset.walletWithdrawRoute,
                walletError: {
                    status: false,
                    message: null,
                },
                walletTable: {
                    walletAddress: null,
                    walletChainId: null,
                    walletChainText: null,
                    walletBalanceList: [],
                },
                modalWithDrawCashTransfer: {
                    isModalWithDrawCashModal: false,
                },
                modalWithDrawCryptoTransfer: {
                    walletBalance: null,
                    chainCurrency: null,
                    walletAddress: null,
                    walletPedding: null,
                    walletChainText: null,
                    isModalWithDrawCryptoModal: false,
                },
                modalWithDrawCashDepositInformation: {
                    chainCurrency: null,
                    walletChainId: null,
                    isModalWithDrawCashDepositModal: false,
                },
                modalWithDrawCryptoDepositInformation: {
                    walletAddress: null,
                    isModalWithDrawCryptoDepositModal: false,
                },
                balanceCart: {
                    panelBalance: document.getElementById('balanceComponent').dataset.panelBalance,
                    panelCurrency: document.getElementById('balanceComponent').dataset.panelCurrency,
                    panelPendingBalance: document.getElementById('balanceComponent').dataset.panelPendingBalance,
                    panelDropButtonDepositText: document.getElementById('balanceComponent').dataset.panelDropButtonDepositText,
                    panelDropButtonWithdrawText: document.getElementById('balanceComponent').dataset.panelDropButtonWithdrawText,
                    panelDropMenuButtonWithcashText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonWithcashText,
                    panelDropMenuButtonWithcryptoText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonWithcryptoText,

                    panelDropButtonDepositBuyText: document.getElementById('balanceComponent').dataset.panelDropButtonDepositBuyText,
                    panelDropButtonWithdrawSellText: document.getElementById('balanceComponent').dataset.panelDropButtonWithdrawSellText,
                    panelDropMenuButtonWithcryptoMLGRText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonWithcryptoMlgrText,

                    panelDropMenuButtonWithdrawWithcashText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonWithdrawWithcashText,
                    panelDropMenuButtonDepositWithcashText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonDepositWithcashText,
                    panelDropMenuButtonWithdrawWithcryptoText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonWithdrawWithcryptoText,
                    panelDropMenuButtonDepositWithcryptoText: document.getElementById('balanceComponent').dataset.panelDropMenuButtonDepositWithcryptoText,
                }
            }
        },
        created() {
            /** Wallet Address Table Information **/
            EventBus.$on('balances', (value) => {
                if (value === null) {
                    this.walletError.status = true;
                } else {
                    this.walletError.status = false;
                    this.walletTable.walletBalanceList = value;
                }
            });
            /** Wallet empty control **/
            EventBus.$on('errorText', (value) => {
                this.walletError.message = value.message;
            });
            /** Select ChainId Selected **/
            EventBus.$on('selectChainId', (value) => {
                this.walletTable.walletChainId = value;
            });
            /** Select ChainText Selected **/
            EventBus.$on('selectChainText', (value) => {
                this.walletTable.walletChainText = value;
            });
            /** Wallet Address Information **/
            EventBus.$on('walletaAddress', (value) => {
                this.walletTable.walletAddress = value;
            });
        },
        methods: {
            toggleDropdown() {
                this.showDropdown = !this.showDropdown;
            },
            async fetchWalletSources() {
                try {
                    const response = await axios.get(this.walletSourcesRoute);
                    this.walletSources = response.data.data
                        .filter(method => method.status === true)
                        .sort((a, b) => a.sort - b.sort);

                    console.log(response.data);
                } catch (error) {
                    console.error('Wallet sources fetch error:', error);
                }
            },
            handleOutsideClick(event) {
                let ref = this.$refs.dropdownWrapper;

                if (Array.isArray(ref)) {
                    ref = ref[0];
                }

                if (ref && ref.$el) {
                    ref = ref.$el;
                }

                if (!(ref instanceof HTMLElement) || typeof ref.contains !== 'function') {
                    console.warn('Not a valid DOM element:', ref);
                    return;
                }

                if (!ref.contains(event.target)) {
                    this.showDropdown = false;
                }
            },
            /** TRM Bank information list for TRM **/
            functionShowDepositCashModal: function( isoCurrency, chainCurrency, walletChainId ) {
                this.modalWithDrawCashDepositInformation.isoCurrency = isoCurrency;
                this.modalWithDrawCashDepositInformation.walletChainId = walletChainId;
                this.modalWithDrawCashDepositInformation.chainCurrency = chainCurrency;
                this.modalWithDrawCashDepositInformation.isModalWithDrawCashDepositModal = true;
            },
            /** TRM Coin information **/
            functionShowDepositCryptoModal: function(  ) {
                this.modalWithDrawCryptoDepositInformation.isModalWithDrawCryptoDepositModal = true;
                this.modalWithDrawCryptoDepositInformation.walletAddress = this.walletTable.walletAddress;
            },
            functionShowWithDrawCashModal: function( walletAddress, chainCurrency, chainText, balance, pendingBalance ) {
                this.modalWithDrawCashTransfer.walletBalance = balance;
                this.modalWithDrawCashTransfer.walletChainText = chainText;
                this.modalWithDrawCashTransfer.chainCurrency = chainCurrency;
                this.modalWithDrawCashTransfer.walletAddress = walletAddress;
                this.modalWithDrawCashTransfer.walletPedding = pendingBalance;
                this.modalWithDrawCashTransfer.isModalWithDrawCashModal = true;
            },
            /** Wallet Crypto Transfer Modal Information **/
            functionShowWithDrawCryptoModal: function( walletAddress, chainCurrency, chainText, balance, pendingBalance ) {
                //EventBus.$emit('chainCurrency', chainCurrency);
                this.modalWithDrawCryptoTransfer.walletBalance = balance;
                this.modalWithDrawCryptoTransfer.walletChainText = chainText;
                this.modalWithDrawCryptoTransfer.chainCurrency = chainCurrency;
                this.modalWithDrawCryptoTransfer.walletAddress = walletAddress;
                this.modalWithDrawCryptoTransfer.walletPedding = pendingBalance;
                this.modalWithDrawCryptoTransfer.isModalWithDrawCryptoModal = true;
            }
        },
        mounted() {
            this.fetchWalletSources();
            document.addEventListener('click', this.handleOutsideClick);
        },
        beforeDestroy() {
            document.removeEventListener('click', this.handleOutsideClick);
        }
    };
</script>