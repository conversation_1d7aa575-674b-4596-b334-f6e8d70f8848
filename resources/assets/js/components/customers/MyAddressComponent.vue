<template>
    <div class="row">
        <div class="block float-right -mt-9-5-rem">
            <button
                type="button"
                @click="functionModalOpenMyAddress( 0, 'added' )"
                class="rounded-full bg-green py-3 px-8 text-center font-semibold text-white hover:bg-green-600 transition-all ease-in-out">
                {{ propsDataObject.addressModal.modalAddedTitle }}
            </button>
        </div>
        <div v-if="statusError.status" class="flex justify-center w-full my-12">
            <div class="flex flex-col space-y-2 rounded-2lg border border-jacarta-100 bg-light-base p-5 text-center transition-shadow hover:shadow-lg dark:border-jacarta-600 dark:bg-jacarta-800">
                <span class="text-xl font-bold uppercase text-milyem" v-html="propsDataObject.addressError.title"></span>
                <span class="text-base text-jacarta-700 dark:text-white" v-html="propsDataObject.addressError.message"></span>
            </div>
        </div>
        <div v-else class="flex-wrap flex justify-center my-12">
            <div v-for="item in addressTable.addressList" class="w-1/2 p-3 pt-0 dark:border-jacarta-600 dark:bg-jacarta-700 mb-12">
                <div class=" rounded-2.5xl border border-jacarta-100 bg-white text-center transition-shadow hover:shadow-xl" :style="item?.default_address === true ? 'border-width: 3px;border-color: green;': ''">
                    <!-- <div class="mb-9 -mt-8 inline-flex h-[5.5rem] w-[5.5rem] items-center justify-center rounded-full border border-jacarta-100 bg-white dark:border-jacarta-600 dark:bg-jacarta-700">
                        <picture>
                            <source srcset="/miligold/img/miligram-coin.png" type="image/webp">
                            <source srcset="/miligold/img/miligram-coin.png" type="image/png">
                            <img src="/miligold/img/miligram-coin.png" alt="MiliGold" class="rounded-2lg" loading="lazy">
                        </picture>
                    </div> -->
                    <h3 class="mb-4 font-display text-xl text-jacarta-700 dark:text-white">
                        {{ item?.first_name }} {{ item?.last_name }}
                    </h3>
                    <div class="flex-row w-full justify-start space-y-1 mb-4">
                        <div class="w-full">
                            {{ item?.address1 }}
                        </div>
                        <div class="w-full">
                            {{ item?.address2 }}
                        </div>
                        <div class="w-full">
                            {{ item?.city }}
                        </div>
                        <div class="w-full">
                            {{ item?.country }} {{ item?.postcode }}
                        </div>
                        <div class="w-full">
                            {{ propsDataObject.addressTable.contact }} {{ item?.phone }}
                        </div>
                    </div>
                    <div class="flex space-x-4 justify-around w-full mb-4">
                        <div class="relative group-dropdown cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-shadow group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" >
                            <button
                                type="button"
                                @click="functionModalOpenMyAddress( item?.id, 'edit' )"
                                class="dropdown-toggle group font-bold text-base text-jacarta-700 group-hover:text-[#caa754] dark:text-white whitespace-nowrap "
                            >
                                {{ propsDataObject.addressTable.updateButtonText }}
                            </button>
                        </div>
                        <div class="relative group-dropdown cursor-pointer group flex items-center rounded-2.5xl border border-[#caa754] bg-white py-4 px-3 2xl:px-5 transition-shadow group-hover:shadow-lg dark:border-transparent dark:bg-jacarta-700" >
                            <button
                                type="button"
                                @click="functionModalOpenMyAddress( item?.id, 'delete' )"
                                class="dropdown-toggle group font-bold text-base text-jacarta-700 group-hover:text-[#caa754] dark:text-white whitespace-nowrap "
                            >
                                {{ propsDataObject.addressTable.deleteButtonText }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <modal-my-address-component v-if="modalMyAddress.isModalAddressModal"></modal-my-address-component>
    </div>
</template>

<script>
import {EventBus} from "../../app";
export default {
    props: {
        addressDataObject: Array|Object
    },
    data() {
        return {
            statusError: {
                status: true,
            },
            addressTable: {
                addressList: [],
            },
            modalMyAddress: {
                isModalAddressId: null,
                isModalTypeStatus: null,
                isModalAddressModal: false,
            },
            propsDataObject: JSON.parse(this.addressDataObject),
        }
    },
    created() {
        this.functionAddressList();
    },
    methods: {
        /**
         * Bu function ile Adres Düzenleme Modalını Açıyoruz.
         */
        functionModalOpenMyAddress: function( value, type ) {

            this.modalMyAddress.isModalAddressId = value;
            this.modalMyAddress.isModalTypeStatus = type;
            this.modalMyAddress.isModalAddressModal = true;

        }, /** end functionModalOpenMyAddress: function( value ) **/
        /**
         * Bu function ile Kullanıcıya Ait Adresleri çekiyoruz ve Listeliyoruz.
         */
        functionAddressList: function() {

            try {
                axios
                    .get(
                        this.propsDataObject.addressListRoute
                    )
                    .then(response => {
                        if(response?.data?.status === true) {
                            this.statusError.status = false;
                            this.addressTable.addressList = response?.data?.addresses;
                        } else {
                            this.statusError.status = true;
                        }
                    });
            } catch (err) {

            }

        }, /** end functionAddressList: function() **/
    }
};
</script>

<style scoped>
    .-mt-9-5-rem {
        margin-top: -9.5rem;
    }
</style>