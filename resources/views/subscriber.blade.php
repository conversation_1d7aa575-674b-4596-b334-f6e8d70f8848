<section class="bulten container">
    <div class="border-1 border-borderflat-gray2 px-5 lg:px-10 rounded-34p lg:rounded-60p py-5 mt-4 md:max-w-2/3 lg:max-w-1/2 mx-auto" style="border:1px solid rgb(207, 207, 207);">
        <div class="font-terramirum font-semibold text-2xl md:text-2xl lg:text-3xl text-center">{{ __('velocity::app-static.homepage.subscribe-newsletter')}}</div>
        <form action="{{url('subscriber-form')}}" method="post" class="relative">
            @csrf
            <div class="self-center mt-46p relative group">
                <input
                    id="email"
                    type="email"
                    name="email"
                    placeholder="E-mail *"
                    autocomplete="off"
                    autofill="off"
                    class="pl-2 peer placeholder-transparent font-bold text-lg !border-b-2 border-bordermid-gray w-full group-hover:placeholder-white focus:outline-none focus:ring-transparent focus:border-black @error('email'){{'!border-terra-red'}}@enderror"
                    required
                />
                @error('email')
                <div class="text-terra-red">{{ $message }}</div>
                @enderror
                <label for="email" class="font-terramirum font-regular absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-xs peer-placeholder-shown:text-lg peer-placeholder-shown:top-0 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-xs @error('email'){{'!text-terra-red'}}@enderror">E-mail *</label>
                <div class="relative md:absolute md:right-0 md:-top-3 mt-3 md:mt-0">
                    <button class="bg-terra-light-soft-gray font-bold text-white rounded-full py-2 px-4 self-center text-sm w-full max-w-324p" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 / 16%)">{{ __('velocity::app-static.homepage.sign-up')}}</button>
                </div>
            </div>
            <div class="self-center mt-3 relative group flex items-center">
                <input name="kvkk" id="kvkk" class="cursor-pointer !w-6 !h-6 border-2 rounded-md focus:outline-none !checked:bg-black !focus:bg-black" type="checkbox" required />
                <label  for="kvkk" class="underline font-terramirum text-xs md:text-base text-center text-terra-dark-antrasit"><span>{{ __('velocity::app-static.homepage.kvvk-agree-text')}}</label>
                @error('kvkk')
                <div class="text-terra-red">{{ $message }}</div>
                @enderror
            </div>


        </form>
    </div>
    <modal-component :is-open="true">
        <template v-slot:body>
            <div class="col-12 col-lg-12 py-3">
                <p>&nbsp;</p>

                <p><strong>NETELSAN ELEKTRİK ELEKTRONİK SİS.TİC.SAN.AŞ KİŞİSEL VERİLERİN İŞLENMESİNE İLİŞKİN AYDINLATMA METNİ VE MUVAFAKATNAME</strong></p>

                <p><strong>1. Konu</strong></p>

                <p>İşbu aydınlatma formu, 6698 sayılı Kişisel Verilerin Korunması Kanun("KVKK")'un aydınlatma yükümlülüğüne ilişkin 10. maddesi uyarınca kişisel verilerin işlenmesi faaliyetlerimiz ile ilgili olarak ilgili kişileri bilgilendirmek ve aydınlatmak amacıyla hazırlanmıştır. Bu kapsamda, iş sözleşmelerinin imzalanmasından önceki hazırlık süreci ile sözleşmenin imzalanması, devamı ve sona ermesi süreçlerinde elde edilecek kişisel verilerin işlenmesi bakımından; veri sorumlusunun kimliği, kişisel verilerin hangi amaçla işleneceği, işlenen kişisel verilerin kimlere ve hangi amaçla aktarılabileceği, kişisel veri toplamanın yöntemi ve hukuki sebebi ile ilgili kişinin KVKK'nın 11.maddesi kapsamında sayılan diğer haklarına aşağıda yer verilecektir.</p>

                <p><strong>2. Kişisel Veri Sorumlusu</strong></p>

                <p>KVKK' nın ilgili hükümleri uyarınca şirketimiz Netelsan Elektrik Elektronik Sis.Tic.San.A.Ş. veri sorumlusu sıfatını haiz olup kişisel verilerinizi korumakla yükümlüdür. Bu kapsamda şirketimiz, aşağıda belirtilecek amaçlar dâhilinde, size ait her türlü kişisel verinin, hukuk ve dürüstlük kurallarına uygun olacak şekilde işlenmesi, kaydedilmesi, saklanması ve korunması, yalnızca kanunla belirlenen mevzuat sınırlar çerçevesinde işlendikleri amaç ile sınırlı olarak 3. kişilere aktarılması ve açıklanmasını sağlamakla sorumludur.</p>

                <p><strong>3. Kişisel Verilerin İşlenme Amacı</strong></p>

                <ul>
                    <li>Tarafınızla imzalanan/imzalanması planlanan iş sözleşmesi kapsamındaki kişisel veriler,</li>
                    <li>Başvurulan pozisyon ve/veya açık pozisyonlara uygunluğun tespiti,&nbsp;</li>
                    <li>Kurum kültürü ve teamüllerine uyum sağlama eğiliminin,</li>
                    <li>İş tecrübesinin belirlenmesi,</li>
                    <li>Tarafınızla iletişim sağlanması,</li>
                    <li>İşyeri ve iş süreçleri güvenliğinin teyidi,</li>
                    <li>Ücret ve diğer hakların belirlenmesi,&nbsp;</li>
                    <li>Sağlık durumunun işin niteliğine uygunluğunun belirlenmesi ve acil bir durumda asgari düzeyde sağlık durumuna ilişkin bilgi sahibi olunması,</li>
                    <li>İleride doğacak işgücü ihtiyacının personel aday havuzu sistemi vasıtası ile karşılanması,</li>
                    <li>Acil durumlarda iletişim kurulacak kişilerin bilgilerine sahip olunması,</li>
                </ul>

                <p>Tarafınızla imzalanan/imzalanması planlanan iş sözleşmesi kapsamındaki karşılıklı yükümlülüklerinin yerine getirilebilmesi amaçlarıyla doğrudan veya dolaylı olarak ilgili olan kimlik, adres, iletişim bilgileri ile sair kişisel veriler, şirketimiz tarafından kanunun ilgili hükümlerine uygun şekilde elde edilir. Elde edilen bu veriler, yine bu amaçlarla kanunun ilgili hükümlerine uygun şekilde kaydedilir, işlenme amacına uygun bir süre boyunca fiziksel veya elektronik ortamda güvenli bir şekilde depolanır, muhafaza edilir, değiştirilir yeniden düzenlenir, açıklanır, aktarılır, devralınır, sınıflandırılır, işlenir ya da bunların kullanılması engellenir.</p>

                <p><strong>4. İşlenen Kişisel Verilerin Aktarılabileceği Kişiler ve Aktarma Amacı</strong></p>

                <p>Elde edilen ve işlenen kişisel veriler, yukarıda açıklanan amaçlar kapsamında mevzuat hükümlerine uygun olarak şirketimiz çalışanlarına, görevlilerine, denetçi ve danışmanlarına, bağımsız denetim şirketlerine, iştiraklerine ve grup şirketlerine, tarafınıza verilecek olan hizmetlerin veya faaliyetlerin yürütülmesi için hizmet alınan veya birlikte çalışılan iş ortaklarına ve hizmet sağlayıcılarına, kamu kurum ve kuruluşlarına; tarafınızla akdedilen iş sözleşmesinin gereklerinin yerine getirilmesi, istatistiklerin tutulması, şirket içi planlamalar yapılması, marketing ve bilgi araştırmaları faaliyetlerinin gerçekleştirilmesi amaçlarıyla aktarılabilir. Ayrıca ilgili mevzuat hükümleri uyarınca kamu otoriteleri tarafından istenen bilgiler kanunda belirlenen sınırlar çerçevesinde ilgili kamu kurum ve kuruluşlarıyla paylaşılır. Bu madde kapsamında aktarılan verileriniz, aktarımın gerçekleştiği üçüncü kişiler tarafından kişisel verilerin korunması mevzuatına uygun olarak elde edilebilir, kaydedilebilir, depolanabilir, muhafaza edilebilir, değiştirilebilir, yeniden düzenlenebilir, açıklanabilir, aktarılabilir, devralınabilir, sınıflandırılabilir, işlenebilir ya da verilerin kullanımı engellenebilir.</p>

                <p><strong>5. Kişisel Veri Toplamanın Yöntemi ve Hukuki Sebebi</strong></p>

                <p>Kişisel veriler, şirketimize ait çağrı merkezleri, internet sayfaları, hizmet verilen şirket birimleri aracılığıyla, sözlü ve/veya yazılı olarak ya da güvenlikli elektronik ortamlar aracılığıyla ve tarafınızla akdedilen/akdedilmesi planlana iş sözleşmesi uyarınca toplanabilir</p>
            </div>
        </template>
    </modal-component>

</section>
