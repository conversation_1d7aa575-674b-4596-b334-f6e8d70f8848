
            <div class="w-2/12 pr-2">
                <div class="flex justify-between items-center w-full">
                    <div class="font-terramirum font-bold text-xl tracking-wide">Tü<PERSON></div>
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22.175" height="12.262" viewBox="0 0 22.175 12.262">
                        <defs>
                            <clipPath id="clip-path">
                                <rect id="Rectangle_121" data-name="Rectangle 121" width="22.175" height="12.262" fill="#79914e" stroke="#79914e" stroke-width="1" />
                            </clipPath>
                        </defs>
                        <g id="Group_142" data-name="Group 142" clip-path="url(#clip-path)">
                            <path id="Path_262" data-name="Path 262" d="M11.082,9.938,21.021,0l1.155,1.172L11.085,12.262,0,1.178,1.166.021l9.917,9.917" fill="#79914e" stroke="#79914e" stroke-width="1" />
                        </g>
                    </svg>

                </div>
                <form action="#" class="mb-3 pb-4 border-b-1 border-terra-flat-gray">
                    <div class="self-center mt-6 relative group w-full">
                        <input
                            id="category_name"
                            type="text"
                            placeholder=""
                            class="peer placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white"
                            autocomplete="off"
                            required
                            name="category_name"
                        />
                        <label for="category_name"
                               class="font-terramirum transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-3 group-focus-within:px-1 pl-0 text-xs group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-terra-text-gray group-focus-within:text-black peer-valid:text-black"
                        >Kategori ara</label>
                    </div>
                    <div class="mt-5 max-h-310p overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Villa" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Villa">Villa</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Daire" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Daire">Daire</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Konut" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Konut">Konut</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Taş ev" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Taş ev">Taş ev</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Bungalov" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Bungalov">Bungalov</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="Rezidans" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                            <label class="font-terramirum font-semibold text-sm text-black pl-2" for="Rezidans">Rezidans</label>
                        </div>
                    </div>

                </form>
                <div class="flex items-center w-full mb-5">
                    <div class="font-terramirum font-bold text-xl tracking-wide">Konum</div>
                </div>

                <form action="#" class="mb-3 pb-4 border-b-1 border-terra-flat-gray">
                    <div class="self-center mt-4 relative group w-full">
                        <input id="country" type="text" placeholder="" autocomplete="off" required name="category_name"
                               class="peer placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white" />
                        <label for="country" class="font-terramirum transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-3 group-focus-within:px-1 pl-0 text-xs group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-terra-text-gray group-focus-within:text-black peer-valid:text-black">Ülke</label>
                    </div>
                    <div class="self-center mt-4 relative group w-full">
                        <input id="state" type="text" placeholder="" autocomplete="off" required name="category_name"
                               class="peer placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white" />
                        <label for="state" class="font-terramirum transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-3 group-focus-within:px-1 pl-0 text-xs group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-terra-text-gray group-focus-within:text-black peer-valid:text-black">Şehir</label>
                    </div>
                    <div class="self-center mt-4 relative group w-full">
                        <input id="city" type="text" placeholder="" autocomplete="off" required name="category_name"
                               class="peer placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white" />
                        <label for="city" class="font-terramirum transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-3 group-focus-within:px-1 pl-0 text-xs group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-terra-text-gray group-focus-within:text-black peer-valid:text-black">Semt,Mahalle</label>
                    </div>
                </form>
                <div class="flex items-center w-full mb-5">
                    <div class="font-terramirum font-bold text-xl tracking-wide">Fiyat aralığı</div>
                </div>
                <form action="#" class=" w-full flex justify-center items-center flex-col mb-3 pb-4 border-b-1 border-terra-flat-gray">
                    <div class="flex w-full items-center">
                        <input class="p-2 mr-1 px-1 w-1/2 placeholder:text-terra-text-gray text-xs font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white"
                               id="min" type="text" placeholder="Min" name="min" />
                        <svg class="mx-1" xmlns="http://www.w3.org/2000/svg" width="7.971" height="28.739" viewBox="0 0 7.971 28.739">
                            <line id="Line_137" data-name="Line 137" y1="28.5" x2="7" transform="translate(0.486 0.119)" fill="none" stroke="#676767" stroke-width="1" />
                        </svg>
                        <input class="p-2 mr-1 px-1 w-1/2 placeholder:text-terra-text-gray text-xs font-terramirum bg-off-via-white border-1 rounded-lg border-terra-flat-gray-2 focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white"
                               id="max" type="text" placeholder="Max" name="max" />
                    </div>
                    <button
                        class="mt-2 font-terramirum w-1/2 items-center bg-white text-terra-khaki-green rounded-lg py-2 px-1 self-center text-xs font-semibold" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%)">
                        Güncelle
                    </button>
                </form>
                <div class="mt-5">
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="item1" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                        <label class="font-terramirum font-semibold text-sm text-black pl-2" for="item1">Kaçırılmayacak Fırsatlar</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="item2" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                        <label class="font-terramirum font-semibold text-sm text-black pl-2" for="item2">En Lüks İlanlar</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="item3" class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray" type="checkbox" />
                        <label class="font-terramirum font-semibold text-sm text-black pl-2" for="item3">En Çok Aranan İlanlar</label>
                    </div>
                </div>

            </div>
