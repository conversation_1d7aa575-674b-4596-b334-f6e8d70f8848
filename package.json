{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "admin-npm": "cd packages/Webkul/Admin/ && npm install && npm run production"}, "devDependencies": {"autoprefixer": "^9.8.8", "axios": "^0.21.4", "bootstrap": "^4.0.0", "cross-env": "^5.2.1", "jquery": "^3.2", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "popper.js": "^1.12", "postcss": "^8.4.38", "resolve-url-loader": "^3.1.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue": "^2.7.16", "vue-template-compiler": "^2.7.16", "webpack-cli": "^3.3.12"}, "dependencies": {"@sumsub/websdk": "^2.3.10", "luxon": "^1.28.1", "v-mask": "^2.3.0", "vee-validate": "^3.3.0", "vue-currency-input": "1.22.4", "vue-datepicker": "^1.3.0", "vue-datetime": "^1.0.0-beta.14", "weekstart": "^2.0.0"}}