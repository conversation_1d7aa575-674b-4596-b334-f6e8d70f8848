/*
  Theme Name: Bigtech - ICO & Crypto Landing Page Template
  Support: <EMAIL>
  Description: Bigtech - ICO & Crypto Landing Page Template.
  Version: 1.0
*/

/* CSS Index
-----------------------------------

1. Theme default css
2. Header
3. Mobile-menu
4. <PERSON>
5. Breadcrumb
6. Countdown
7. About
8. Choose
9. Chart
10. Counter
11. RoadMap
12. Document
13. Team
14. Download
15. Faq
16. Blog
17. Pagination
18. Contact
19. Newsletter
20. Footer
21. Preloader

*/

/* 1. Theme default css */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@200;300;400;500;600;700&family=Poppins:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

body {
    font-family: 'Outfit', sans-serif;
    font-weight: normal;
    font-size: 16px;
    color: #A4B4C3;
    font-style: normal;
    background-color: #030B15;
    line-height: 1.75;
}

body.white-background {
    background-color: #FFFFFF;
}

img,
.img {
    max-width: 100%;
    transition: all 0.3s ease-out 0s;
}

.f-left {
    float: left
}

.f-right {
    float: right
}

.fix {
    overflow: hidden
}

a,
button {
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    text-decoration: none;
}

a:focus,
.btn:focus,
button:focus {
    text-decoration: none;
    outline: none;
    box-shadow: none;
}

a:hover,
.portfolio-cat a:hover,
.footer -menu li a:hover {
    color: #ff9700;
    text-decoration: none;
}

a,
button {
    color: #ff9700;
    outline: medium none;
    text-decoration: none;
}

.btn:focus,
button:focus,
input:focus,
input:focus,
textarea,
textarea:focus {
    outline: 0
}

.uppercase {
    text-transform: uppercase;
}

.capitalize {
    text-transform: capitalize;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Poppins', sans-serif;
    color: #FFFFFF;
    margin-top: 0px;
    font-style: normal;
    font-weight: 600;
    text-transform: capitalize;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    color: inherit;
}

h1 {
    font-size: 40px;
}

h2 {
    font-size: 35px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 22px;
}

h5 {
    font-size: 18px;
}

h6 {
    font-size: 16px;
}

ul {
    margin: 0px;
    padding: 0px;
}

li {
    list-style: none
}

p {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.75;
    color: #727885;
    margin-bottom: 15px;
}

hr {
    border-bottom: 1px solid #eceff8;
    border-top: 0 none;
    margin: 30px 0;
    padding: 0;
}

label {
    color: #7e7e7e;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
}

*::-moz-selection {
    background: #ff9700;
    color: #fff;
    text-shadow: none;
}

::-moz-selection {
    background: #ff9700;
    color: #fff;
    text-shadow: none;
}

::selection {
    background: #ff9700;
    color: #fff;
    text-shadow: none;
}

*::-moz-placeholder {
    color: #ff9700;
    font-size: 14px;
    opacity: 1;
}

*::placeholder {
    color: #555555;
    font-size: 14px;
    opacity: 1;
}

.theme-overlay {
    position: relative
}

.theme-overlay::before {
    background: #1696e7 none repeat scroll 0 0;
    content: "";
    height: 100%;
    left: 0;
    opacity: 0.6;
    position: absolute;
    top: 0;
    width: 100%;
}

.separator {
    border-top: 1px solid #f2f2f2
}

/* Bootstrap 5 */
.container {
    padding-left: 15px;
    padding-right: 15px;
}

.row {
    --bs-gutter-x: 30px;
}

.row.g-0 {
    --bs-gutter-x: 0;
}

.gutter-y-30 {
    --bs-gutter-y: 30px;
}

/* button style */
.btn {
    user-select: none;
    -moz-user-select: none;
    background: #040E18;
    border-radius: 70px;
    border: 2px solid transparent;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-size: 15px;
    font-weight: 700;
    letter-spacing: .5px;
    line-height: 1;
    margin-bottom: 0;
    padding: 25px 50px;
    text-align: center;
    text-transform: uppercase;
    touch-action: manipulation;
    transition: all 0.4s 0s;
    vertical-align: middle;
    white-space: nowrap;
    box-shadow: 0px 6px 22px rgba(6, 34, 51, 0.22);
    background-clip: padding-box;
    position: relative;
}

.btn::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: -2px;
    background: linear-gradient(90deg, #FF9700 0%, rgba(4, 12, 21, 1) 49%, #FF9700 100%);
    border-radius: 70px;
    transition: all 0.4s ease 0s;
    z-index: -1;
}

.btn:hover {
    border-color: #FF9700;
    color: #FF9700;
}

.btn.btn-two {
    background: #564DCA;
    border-radius: 5px;
    padding: 25px 42px;
    border: none;
}

.btn.btn-two::after {
    display: none;
}

.btn.btn-two:hover {
    color: #fff;
    background: #ff9700;
}

.breadcrumb > .active {
    color: #888;
}

/* scrollUp */
.scroll-top {
    width: 50px;
    height: 50px;
    line-height: 50px;
    position: fixed;
    bottom: 105%;
    right: 50px;
    font-size: 16px;
    border-radius: 50%;
    z-index: 99;
    color: #3d3d3d;
    text-align: center;
    cursor: pointer;
    background: #dcbc95;
    transition: 1s ease;
    border: none;
}

.scroll-top.open {
    bottom: 30px;
}

.scroll-top::after {
    position: absolute;
    z-index: -1;
    content: '';
    top: 100%;
    left: 5%;
    height: 10px;
    width: 90%;
    opacity: 1;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.scroll-top:hover {
    background: #a3a3a3;
}


/* 2. Header */
.custom-container {
    max-width: 1630px;
}

.custom-container-two {
    max-width: 1700px;
}

.custom-container-three {
    max-width: 1520px;
}

.custom-container-four {
    max-width: 1200px;
}

.transparent-header {
    position: absolute;
    left: 0;
    top: 0px;
    width: 100%;
    z-index: 9;
    height: auto;
}

.menu-area {
    border-bottom: 1px solid rgb(255 255 255 / 10%);
}

.menu-nav {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.navbar-wrap {
    display: flex;
    flex-grow: 1;
}

.navbar-wrap ul {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-left: 140px;
}

.navbar-wrap > ul > li {
    display: block;
    position: relative;
    margin-right: 25px;
}

.navbar-wrap > ul > li > a {
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    color: #FFFFFF;
    padding: 45px 0;
    display: block;
    line-height: 1;
    position: relative;
    z-index: 1;
    letter-spacing: 1px;
}

.navbar-wrap > ul > li:last-child {
    margin-right: 0;
}

.navbar-wrap > ul > li > a::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3px;
    background: #ff9700;
    -webkit-transform-origin: right top;
    -ms-transform-origin: right top;
    transform-origin: right top;
    -webkit-transform: scale(0, 1);
    -ms-transform: scale(0, 1);
    transform: scale(0, 1);
    transition: transform 0.4s cubic-bezier(.74, .72, .27, .24);
}

.navbar-wrap > ul > li.active > a::before,
.navbar-wrap > ul > li > a:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}

.navbar-wrap > ul > li.active > a,
.navbar-wrap > ul > li:hover > a {
    color: #fff;
}

.main-menu .navigation li.menu-item-has-children .dropdown-btn {
    display: none;
}

.header-action > ul {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.header-action > ul > li {
    position: relative;
    margin-left: 10px;
    padding-left: 5px;
}

.header-action > ul > li:first-child {
    margin-left: 0;
    padding-left: 0;
}

.header-action > ul::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 28px;
    background: #FFFFFF;
    opacity: .2;
}

.header-action > ul > li:first-child::before {
    display: none;
}

.header-lang {
    position: relative;
}

.header-lang .selected-lang {
    font-size: 16px;
    color: #ffffff;
    display: flex;
    align-items: center;
    cursor: pointer;
    letter-spacing: 1px;
}

.header-lang .selected-lang::after {
    content: "\f107";
    display: block;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    color: #ff9700;
    margin-left: 10px;
}

.header-lang .lang-list {
    position: absolute;
    left: -10px;
    top: calc(100% + 25px);
    background: #0B1D33;
    z-index: 3;
    padding: 10px 0;
    border-radius: 6px;
    box-shadow: 0px 30px 70px 0px rgba(40, 44, 49, 0.15);
    min-width: 100px;
    visibility: hidden;
    opacity: 0;
    transform-origin: top center;
    transform: perspective(400px) rotateX(-45deg);
    transition: all 0.3s ease-out 0s;
}

.header-lang:hover .lang-list {
    visibility: visible;
    opacity: 1;
    transform: perspective(400px) rotateX(0deg);
}

.header-lang .lang-list li {
    margin-bottom: 5px;
    line-height: 1;
}

.header-lang .lang-list li:last-child {
    margin-bottom: 0;
}

.header-lang .lang-list li a {
    display: block;
    font-size: 16px;
    color: #fff;
    padding: 5px 15px 5px 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.header-lang .lang-list li a:hover {
    color: #ff9700;
}

.header-btn .btn {
    font-size: 14px;
    padding: 16px 25px;
}

.navbar-wrap ul li .sub-menu {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    min-width: 230px;
    border: 1px solid rgba(255, 255, 255, 0.07);
    background: #0B1D33;
    margin: 0;
    transform: scale(1, 0);
    transform-origin: 0 0;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    -webkit-box-shadow: 0px 30px 70px 0px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0px 30px 70px 0px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 30px 70px 0px rgba(0, 0, 0, 0.15);
    border-radius: 0;
    padding: 18px 0;
    display: block;
    visibility: hidden;
    opacity: 0;
    z-index: 9;
}

.navbar-wrap ul li .sub-menu .sub-menu {
    right: auto;
    left: 100%;
    top: 0;
}

.navbar-wrap ul li .sub-menu li {
    margin-left: 0;
    text-align: left;
    display: block;
}

.navbar-wrap ul li .sub-menu li a {
    padding: 9px 15px 9px 25px;
    line-height: 1.4;
    font-weight: 500;
    color: #fff;
    text-transform: uppercase;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    display: block;
    font-size: 14px;
}

.navbar-wrap ul li .sub-menu li a:hover,
.navbar-wrap ul li .sub-menu li.active a {
    color: #ff9700;
}

.navbar-wrap ul li:hover > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.sticky-menu {
    position: fixed;
    left: 0;
    margin: auto;
    top: 0;
    width: 100%;
    z-index: 99;
    background: #030B15;
    -webkit-animation: 1000ms ease-in-out 0s normal none 1 running fadeInDown;
    animation: 1000ms ease-in-out 0s normal none 1 running fadeInDown;
    -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
    box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
    border-radius: 0;
}

.sticky-menu .navbar-wrap > ul > li > a {
    padding: 45px 0;
}

#header-fixed-height.active-height {
    display: block;
    height: 105px;
}

/* header-two */
.menu-area.menu-style-two {
    border-bottom: none;
    padding: 30px 0;
}

.menu-style-two .navbar-wrap > ul > li > a {
    padding: 40px 0;
}

.menu-style-two .navbar-wrap > ul > li > a::before {
    display: none;
}

.menu-style-two .header-btn .btn {
    background: #FF9700;
    border-radius: 15px;
}

.menu-style-two .header-btn .btn2 {
    user-select: none;
    -moz-user-select: none;
    background: transparent;
    border-radius: 15px;
    border: 2px solid #FF9700;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 700;
    letter-spacing: .5px;
    line-height: 1;
    margin-bottom: 0;
    padding: 15px 30px;
    text-align: center;
    text-transform: uppercase;
    touch-action: manipulation;
    transition: all 0.4s 0s;
    vertical-align: middle;
    white-space: nowrap;
    box-shadow: 0px 6px 22px rgba(6, 34, 51, 0.22);
    background-clip: padding-box;
    position: relative;
}

.menu-style-two .header-btn .btn2:hover {
    border-color: #FF9700;
    color: #FFFFFF;
    background: #FF9700;
}

.menu-style-two .header-btn .btn::after {
    display: none;
}

.menu-style-two .header-btn .btn:hover {
    background: transparent;
    border-color: #FF9700;
    color: #fff;
}

.menu-area.menu-style-two.sticky-menu {
    padding: 0 0;
}

.menu-style-two .navbar-wrap > ul > li.active > a,
.menu-style-two .navbar-wrap > ul > li:hover > a {
    color: #ff9700;
}

/* 3. Mobile-menu */
.nav-outer .mobile-nav-toggler {
    position: relative;
    float: right;
    font-size: 40px;
    line-height: 50px;
    cursor: pointer;
    display: none;
    color: #fff;
    margin-right: 30px;
    top: 15px;
}

.mobile-menu {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    padding-right: 30px;
    max-width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    z-index: 99;
    border-radius: 0px;
    transition: all 700ms ease;
    -moz-transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
    -o-transition: all 700ms ease;
    -webkit-transform: translateX(101%);
    -ms-transform: translateX(101%);
    transform: translateX(101%);
}

.mobile-menu .navbar-collapse {
    display: block !important;
}

.mobile-menu .nav-logo {
    position: relative;
    padding: 30px 25px;
    text-align: left;
}

.mobile-menu-visible {
    overflow: hidden;
}

.mobile-menu-visible .mobile-menu {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
}

.mobile-menu .navigation li.current > a:before {
    height: 100%;
}

.menu-backdrop {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    transition: all 700ms ease;
    -moz-transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
    -o-transition: all 700ms ease;
    opacity: 0;
    visibility: hidden;
    background: #0B1D33;
}

.mobile-menu-visible .menu-backdrop {
    opacity: 0.80;
    visibility: visible;
}

.mobile-menu .menu-box {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background: #0B1D33;
    padding: 0px 0px;
    z-index: 5;
    box-shadow: -9px 0 14px 0px rgb(0 0 0 / 6%);
}

.mobile-menu-visible .mobile-menu .menu-box {
    opacity: 1;
    visibility: visible;
}

.mobile-menu .close-btn {
    position: absolute;
    right: 15px;
    top: 28px;
    line-height: 30px;
    width: 35px;
    text-align: center;
    font-size: 20px;
    color: #ff9700;
    cursor: pointer;
    z-index: 10;
    -webkit-transition: all 0.9s ease;
    -o-transition: all 0.9s ease;
    transition: all 0.9s ease;
}

.mobile-menu-visible .mobile-menu .close-btn {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
}

.mobile-menu .navigation {
    position: relative;
    display: block;
    width: 100%;
    float: none;
}

.mobile-menu .navigation li {
    position: relative;
    display: block;
    border-top: 1px solid rgb(255 255 255 / 6%);
}

.mobile-menu .navigation:last-child {
    border-bottom: 1px solid rgb(255 255 255 / 6%);
}

.mobile-menu .navigation li > ul > li:first-child {
    border-top: 1px solid rgb(255 255 255 / 6%);
}

.mobile-menu .navigation li > a {
    position: relative;
    display: block;
    line-height: 24px;
    padding: 10px 60px 10px 25px;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
    border: none;
    letter-spacing: 0.08em;
}

.mobile-menu .navigation li ul li > a {
    font-size: 15px;
    margin-left: 20px;
    text-transform: capitalize;
}

.mobile-menu .navigation li ul li ul li a {
    margin-left: 40px;
}

.mobile-menu .navigation li ul li ul li ul li a {
    margin-left: 60px;
}

.mobile-menu .navigation li > a:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 0;
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
}

.mobile-menu .navigation li.menu-item-has-children .dropdown-btn {
    position: absolute;
    right: 15px;
    top: 6px;
    width: 32px;
    height: 32px;
    text-align: center;
    font-size: 16px;
    line-height: 32px;
    color: #fff;
    background: #ff9700;
    cursor: pointer;
    border-radius: 2px;
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 5;
}

.mobile-menu .navigation li.menu-item-has-children .dropdown-btn.open {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.mobile-menu .navigation li > ul,
.mobile-menu .navigation li > ul > li > ul {
    display: none;
}

.mobile-menu .social-links ul {
    display: flex;
    position: relative;
    text-align: center;
    padding: 30px 20px 20px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.mobile-menu .social-links li {
    position: relative;
    display: inline-block;
    margin: 0px 6px 10px;
}

.mobile-menu .social-links li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    position: relative;
    line-height: 32px;
    font-size: 16px;
    color: #fff;
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
    border: 1px solid rgb(255 255 255 / 10%);
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}

.mobile-menu .social-links li a:hover {
    border-color: #ff9700;
    background: #ff9700;
    color: #fff;
}

.menu-area .mobile-nav-toggler {
    position: relative;
    float: right;
    font-size: 30px;
    cursor: pointer;
    line-height: 1;
    color: #000;
    display: none;
    margin-top: 3px;
}

/* 4. Banner */
.banner-bg {
    background-image: url(../img/banner/banner_bg.jpg);
    background-position: center;
    background-size: cover;
    position: relative;
    z-index: 1;
    padding: 120px 0 0;
    overflow: hidden;
}

.banner-bg::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(0.23deg, #030B15 5.68%, rgba(3, 11, 21, 0.42) 81.9%);
    z-index: -1;
}

.banner-shape-wrap img {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    max-height: 90px;
}

.banner-shape-wrap img.img-one {
    top: 28%;
    left: -3%;
    animation: leftToRight 5s infinite linear;
}

.banner-shape-wrap img.img-two {
    left: auto;
    right: 8%;
    top: 20%;
}

.banner-shape-wrap img.img-three {
    left: auto;
    right: -2%;
    top: auto;
    bottom: 14%;
    animation: alltuchtopdown 3s infinite linear;
}

.banner-content img {
    margin-bottom: 25px;
}

.banner-content .title {
    font-size: 55px;
    margin-bottom: 45px;
    line-height: 1.4;
    letter-spacing: -0.01em;
}

.banner-content .title span {
    color: #ff9700;
}

.banner-progress-wrap ul {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 0px 0 120px;
}

.banner-progress-wrap ul li {
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    color: #574dca;
    display: inline-block;
    position: relative;
    padding-bottom: 35px;
}

.banner-progress-wrap ul li::before {
    content: "";
    position: absolute;
    bottom: 6px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 24px;
    background: #574dca;
}

.banner-progress-wrap ul li:nth-child(2) {
    color: #FF9700;
}

.banner-progress-wrap ul li:nth-child(3) {
    color: #12D176;
}

.banner-progress-wrap ul li:nth-child(2)::before {
    background: #FF9700;
}

.banner-progress-wrap ul li:nth-child(3)::before {
    background: #12D176;
}

.banner-progress-wrap {
    margin: 0 150px 70px;
}

.banner-progress-wrap .progress {
    height: 16px;
    background-color: #F2F2F2;
    border-radius: 0;
    overflow: inherit;
    margin-bottom: 25px;
    margin-top: 8px;
    border-radius: 30px;
}

.banner-progress-wrap .progress .progress-bar {
    background-color: #ff9700;
    position: relative;
    overflow: inherit;
    border-radius: 30px
}

.banner-progress-wrap .progress .progress-bar::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #fff;
    z-index: 5;
    border: 6px solid #ff9700;
    animation: shine 1s linear infinite, indexprogress 1s linear 1 1s;
    transition: linear 1s linear 1s;
}

.banner-progress-wrap .title {
    font-size: 15px;
    text-transform: uppercase;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.banner-progress-wrap .title span {
    margin-left: auto;
}

.banner-countdown-wrap .title {
    font-size: 26px;
    margin-bottom: 15px;
    letter-spacing: -0.01em;
}

.banner-countdown-wrap .coming-time {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.banner-countdown-wrap .coming-time .time-count {
    min-width: 162px;
    min-height: 96px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    position: relative;
    margin: 20px 17.5px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    background: #0B1D33;
    border: 1px solid rgba(255, 255, 255, 0.04);
    border-radius: 5px;
    color: #A4B4C3;
    text-transform: capitalize;
}

.banner-countdown-wrap .coming-time .time-count span {
    font-size: 35px;
    font-weight: 600;
    color: #574dca;
    margin-bottom: 12px;
    display: block;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 1px;
}

.banner-countdown-wrap .coming-time .time-count.hour span {
    color: #FF9700;
}

.banner-countdown-wrap .coming-time .time-count.min span {
    color: #FF1D45;
}

.banner-countdown-wrap .coming-time .time-count.sec span {
    color: #12D176;
}

@keyframes alltuchtopdown {
    0% {
        transform: rotateX(0deg) translateY(0px);
    }
    50% {
        transform: rotateX(0deg) translateY(-30px);
    }
    100% {
        transform: rotateX(0deg) translateY(0px);
    }
}

@keyframes leftToRight {
    0% {
        transform: rotateX(0deg) translateX(0px);
    }
    50% {
        transform: rotateX(0deg) translateX(50px);
    }
    100% {
        transform: rotateX(0deg) translateX(0px);
    }
}

/* banner-two */
.banner-area-two {
    position: relative;
    padding: 225px 0 0;
    z-index: 1;
}

.banner-bg-two {
    background-image: url(../img/banner/banner_bg02.jpg);
    background-size: cover;
    background-position: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: calc(100% - 164px);
    z-index: -1;
}

.banner-bg-two::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -40px;
    background-image: url(../img/banner/banner_shape.svg);
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 722px;
    z-index: -1;
}

.banner-area-two .banner-content img {
    margin-bottom: 0;
}

.banner-area-two .banner-content {
    margin-bottom: 0;
}

.banner-area-two .banner-content .title {
    margin-bottom: 65px;
    line-height: 1.36;
    text-transform: none;
}

.banner-social-wrap {
    position: absolute;
    left: 100px;
    bottom: 90px;
}

.banner-social-wrap ul li {
    margin-bottom: 25px;
    border: 1px solid #F3F3F3;
    border-radius: 50px;
    width: 42px;
    display: flex;
    align-items: center;
}

.banner-social-wrap ul li.is-active {
    width: auto;
}

.banner-social-wrap ul li:last-child {
    margin-bottom: 0;
}

.banner-social-wrap ul li a {
    font-size: 15px;
    color: #081A39;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    transition: 0s;
}

.banner-social-wrap ul li span {
    color: #B5BAC4;
    font-size: 14px;
    font-weight: 500;
    padding-right: 14px;
    display: none;
}

.banner-scroll {
    display: flex;
    align-items: center;
    flex-direction: column;
    position: absolute;
    right: 40px;
    bottom: 90px;
}

.banner-scroll span {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.515em;
    color: #081A39;
    transform: rotate(-90deg);
    margin-bottom: 120px;
    opacity: 0.4;
}


/* 5. Breadcrumb */
.breadcrumb-bg {
    background-image: url('../img/doc/in-page-bg.jpeg');
    background-size: cover;
    background-position: center;
    position: relative;
    padding: 200px 0 80px 0;
}

.breadcrumb-bg .container {
    position: relative;
    z-index: 7;
}

.breadcrumb-bg::before {
    position: absolute;
    content: "";
    background: rgba(0, 0, 0, 0.6);
    left: 0;
    bottom: 0px;
    width: 100%;
    height: 100%;
}

.breadcrumb-content {
    text-align: center;
}

.breadcrumb-content .title {
    font-size: 65px;
    /*margin-bottom: 30px;*/
    letter-spacing: -0.01em;
    line-height: 1.15;
}

.breadcrumb-content .breadcrumb {
    display: flex;
    margin-bottom: 0;
    justify-content: center;
    align-items: center;
}

.breadcrumb-content .breadcrumb .breadcrumb-item {
    color: #fff;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 700;
    line-height: 1.27;
    text-decoration: underline;
}

.breadcrumb-content .breadcrumb .breadcrumb-item:first-child {
    text-decoration: none;
}

.breadcrumb-content .breadcrumb .breadcrumb-item a {
    color: #fff;
}

.breadcrumb-content .breadcrumb-item + .breadcrumb-item::before {
    float: left;
    padding-right: 15px;
    color: #FFFFFF;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-size: 17px;
    font-weight: 700;
    line-height: 1;
    opacity: .5;
    padding-top: 3px;
}

.breadcrumb-content .breadcrumb-item + .breadcrumb-item {
    padding-left: 15px;
}


/* 6. Countdown */
.countdown-area-two .countdown-wrap {
    background: #FFFFFF;
    box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21);
    border-radius: 20px;
    position: relative;
    text-align: center;
    padding: 55px 50px 60px;
}

.countdown-area-two .countdown-wrap::before {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 80%;
    background: #ff9700;
    opacity: 0.08;
    border-radius: 20px;
    z-index: -1;
}

.countdown-area-two .countdown-wrap .title {
    color: #030B15;
    letter-spacing: -0.01em;
    font-size: 26px;
    margin-bottom: 35px;
    line-height: 1.88;
}

.ClassyCountdown-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 0;
    column-gap: 115px;
    row-gap: 30px;
}

.ClassyCountdown-wrapper > div > div:not(:last-child) {
    display: block !important;
}

.ClassyCountdown-wrapper > div > div canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}

.ClassyCountdown-value {
    display: block;
    line-height: 0;
}

.ClassyCountdown-value > div {
    display: block;
    text-align: center;
    line-height: 1;
    margin-bottom: 5px;
    font-size: 35px;
    font-weight: 600;
    color: #030B15;
    font-family: 'Poppins', sans-serif;
    margin-top: -95px;
}

.ClassyCountdown-value > span {
    font-size: 14px;
    display: block;
    text-align: center;
    font-family: 'Outfit';
    color: #030B15;
    font-weight: 500;
    line-height: 1;
}


/* 7. About */
.about-img {
    position: relative;
    padding-left: 120px;
}

.about-img img.img-two {
    position: absolute;
    left: 60px;
    top: 40px;
}

.about-content {
    margin-left: 70px;
}

.section-title .sub-title {
    text-transform: uppercase;
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    position: relative;
    letter-spacing: 0.09em;
    display: inline-block;
    padding: 0 15px;
    margin-bottom: 25px;
}

.section-title .sub-title::after,
.section-title .sub-title::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    background: #ff9700;
    border-radius: 50%;
}

.section-title .sub-title::after {
    left: auto;
    right: 0;
}

.section-title h3.title {
    position: relative;
    display: inline-block;
    padding: 0 30px;
}

.section-title h3.title::after,
.section-title h3.title::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: #FF9700;
    border-radius: 50%;
}

.section-title h3.title::after {
    left: auto;
    right: 0;
}

.section-title .title {
    font-size: 42px;
    margin-bottom: 0;
    letter-spacing: -0.01em;
}

.section-title .title span {
    color: #ff9700;
}

.about-content p {
    margin-bottom: 20px;
    width: 70%;
    color: #ffffff;
}

.partner-wrap ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.partner-wrap ul li {
    width: 20%;
    height: 123px;
    border: 1px solid rgba(255, 255, 255, 0.06);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -1px;
    margin-top: -1px;
    cursor: pointer;
}

.partner-wrap ul li img {
    opacity: .5;
    transition: .3s linear;
}

.partner-wrap ul li:hover img {
    opacity: 1;
}

/* about-two */
.about-area-two {
    padding: 130px 0;
    position: relative;
}

.about-area-mailing {
    position: relative;
}

.about-area-mailing:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 37, 62, 0.86);
}

.about-area-mailing .about-area-content {
    z-index: 2;
    position: relative;
}

.about-area-mailing .about-area-content h4, .about-area-mailing .about-area-content h2, .about-area-mailing .about-area-content p {
    filter: drop-shadow(2px 2px 2px #000000);
}

.about-area-mailing .about-area-content h4 {
    font-size: 24px;
}

.about-area-mailing .about-area-content p {
    width: 100%;
    font-size: 18px;
    color: #ffffff;
}

.mailing-area-2 {
    position: relative;
}

.mailing-area-2:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(39, 39, 39, 0.16), rgba(24, 27, 30, 1));
}

.mailing-area-2 .about-area-content {
    z-index: 2;
    position: relative;
}

.mailing-area-2 .about-area-content p, .mailing-area-3 .about-area-content p, .mailing-area-5 .about-area-content p {
    width: 100%;
    font-size: 16px;
    color: #ffffff;
}

.mailing-area-2 .section-title .sub-title, .mailing-area-3 .section-title .sub-title, .mailing-area-5 .section-title .sub-title, .mailing-area-6 .section-title .sub-title {
    font-size: 42px;
    line-height: 35px;
    margin-bottom: 20px;
    text-transform: unset;
}

.mailing-area-3 .section-title .sub-title {
    color: #FF9700;
}

.section-title .sub-title .yellow, .section-title .title .yellow {
    color: #FF9700 !important;
}

.mailing-area-5 {
    background-color: #000000;
    position: relative;
}

.mailing-area-5:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(10, 21, 28, 1), rgba(44, 63, 77, 1));
}

.mailing-area-6 {
    position: relative;
}

.mailing-area-6:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 21, 28, 0.6);
}

.section-title.section-title-two .sub-title {
    color: #564DCA;
    letter-spacing: 0.02em;
    padding: 0;
    text-decoration: underline;
    margin-bottom: 18px;
}

.section-title.section-title-two .sub-title::before,
.section-title.section-title-two .sub-title::after {
    display: none;
}

.section-title.section-title-two .title {
    font-size: 45px;
    color: #030B15;
    line-height: 1.26;
}

.about-content-two p {
    color: #727885;
    margin-bottom: 20px;
    width: 80%;
}

.about-content-two .about-list {
    margin-bottom: 45px;
}

.about-content-two .about-list ul li {
    display: flex;
    align-items: baseline;
    color: #030B15;
    margin-bottom: 10px;
}

.about-content-two .about-list ul li:last-child {
    margin-bottom: 0;
}

.about-content-two .about-list ul li i {
    color: #564DCA;
    margin-right: 10px;
}

.about-shape-wrap img {
    position: absolute;
}

.about-shape-wrap img.shape-one {
    left: 9%;
    bottom: 90px;
    animation: leftToRight 5s infinite linear;
}

.about-shape-wrap img.shape-two {
    right: 7%;
    bottom: 22%;
}

.rotateme {
    -webkit-animation-name: teamRotate;
    animation-name: teamRotate;
    -webkit-animation-duration: 10s;
    animation-duration: 10s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
}

@keyframes teamRotate {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes teamRotate {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}

/* 8. Choose */
.choose-area .row {
    margin: 0 -10px;
}

.choose-area .row [class*="col-"] {
    padding: 0 10px;
}

.choose-item {
    background: #030B15;
    border: 1px solid #121A23;
    border-radius: 15px;
    padding: 25px 20px;
    transition: .3s ease-in-out;
}


.choose-item:hover {
    border-color: transparent;
}

.choose-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.07);
    border: 4px solid rgba(255, 255, 255, 0.06);
    border-radius: 50%;
    margin-bottom: 30px;
    transition: .3s ease-in-out;
}

.choose-item:hover .choose-icon {
    border-color: #12D176;
}


.choose-item:hover .choose-content a {
    color: #12D176;
}

.choose-item:hover .choose-content p {
    color: #ffffff;
}


.choose-content .title {
    font-size: 22px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
}

.choose-content p {
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 22px;
    color: #A4B4C3;
}

.choose-area .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.choose-area .slide-progress {
    display: block;
    width: 100%;
    height: 3px;
    border-radius: 0;
    background: rgba(255, 255, 255, 0.10);
    overflow: hidden;
    background-image: linear-gradient(to right, #ff9700, #ff9700);
    background-repeat: no-repeat;
    background-size: 0 100%;
    transition: background-size .4s ease-in-out;
    margin-top: 50px;
}

/* choose-two */
.choose-bg {
    background-image: url(../img/bg/choose_bg.jpg);
    background-size: cover;
    background-position: center;
    padding: 130px 0 218px;
    position: relative;
}

.choose-bg::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    background-image: url(../img/bg/choose_shape01.png);
    background-position: center;
    background-repeat: repeat;
    width: 100%;
    height: 25px;
}

.choose-bg::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    background-image: url(../img/bg/choose_shape02.png);
    background-position: center;
    background-repeat: repeat;
    width: 100%;
    height: 19px;
}

.choose-item-two {
    margin-bottom: 30px;
}

.choose-icon-two {
    margin-bottom: 35px;
}

.choose-icon-two img {
    height: 48px;
}

.choose-item-two .choose-content {
    width: 77%;
}

/* 9. Chart */
.chart-inner {
    padding: 0 110px;
}

.chart-bg {
    background-image: url(../img/bg/chart_bg.jpg);
    background-size: cover;
    background-position: center;
    padding: 130px 0;
}

.chart-content .nav-tabs {
    border-bottom: none;
    margin: 0 -20px;
    margin-bottom: 50px;
}

.chart-content .nav-tabs .nav-item {
    padding: 0 20px;
}

.chart-content .nav-tabs .nav-link {
    margin-bottom: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    text-transform: uppercase;
    font-size: 18px;
    color: #fff;
    font-weight: 700;
    padding: 0;
    position: relative;
    letter-spacing: 1px;
}

.chart-content .nav-tabs .nav-link span {
    color: #84c348;
}

.chart-content .nav-tabs .nav-link::before {
    background: #ffffff !important;
}

.chart-content .nav-tabs .nav-link span.yellow {
    color: #FF9700;
}

.chart-content .nav-tabs .nav-link span.purple {
    color: #574dca;
}

.chart-content .nav-tabs .nav-link::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3px;
    background: #ff9700;
    transition: .3s linear;
    opacity: 0;
}

.chart-content .nav-tabs .nav-link.active::before {
    opacity: 1;
}

.chart-content {
    width: 72%;
}

.chart-content-inner .title {
    font-size: 55px;
    margin-bottom: 35px;
    letter-spacing: -0.01em;
    line-height: 1.2;
}

.chart-content-inner p {
    color: #A4B4C3;
    margin-bottom: 45px;
}

.chart-content-inner .btn {
    background: #ff9700;
    min-width: 200px;
    padding: 21px 50px;
}

.chart-content-inner .btn:hover {
    color: #fff;
}

.chart-content-inner .btn::after {
    display: none;
}

.chart-wrap {
    background: #0B1D33;
    border-radius: 20px;
    padding: 60px 45px 60px;
    text-align: center;
}

.chart-wrap.general-wrap {
    border-radius: 10px;
    padding: 30px 30px 30px;
}

/*.chart-wrap img {*/
/*    margin-bottom: 75px;*/
/*}*/

/*.chart-wrap ul {*/
/*    display: flex;*/
/*    align-items: center;*/
/*    flex-wrap: wrap;*/
/*}*/

/*.chart-wrap ul li {*/
/*    width: 50%;*/
/*    text-align: left;*/
/*    font-size: 14px;*/
/*    color: #fff;*/
/*    font-weight: 500;*/
/*    position: relative;*/
/*    padding-left: 30px;*/
/*    margin-bottom: 8px;*/
/*}*/

/*.chart-wrap ul li::before {*/
/*    content: "";*/
/*    position: absolute;*/
/*    left: 0;*/
/*    top: 50%;*/
/*    transform: translateY(-50%);*/
/*    width: 20px;*/
/*    height: 20px;*/
/*    border-radius: 50%;*/
/*    background: #005F73;*/
/*}*/

/*.chart-wrap ul li:nth-child(2):before {*/
/*    background: #F72585;*/
/*}*/

/*.chart-wrap ul li:nth-child(3):before {*/
/*    background: #5DD400;*/
/*}*/

/*.chart-wrap ul li:nth-child(4):before {*/
/*    background: #FF9700;*/
/*}*/

/*.chart-wrap ul li:nth-child(5):before {*/
/*    background: #ff9700;*/
/*}*/

/*.chart-wrap ul li:nth-child(6):before {*/
/*    background: #007FF4;*/
/*}*/

/* 10. Counter */
.counter-inner {
    background: #FFFFFF;
    box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21);
    border-radius: 20px;
    padding: 64px 75px 33px;
    position: relative;
    margin-top: -120px;
    margin-bottom: 10px;
}

.counter-inner::before {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -10px;
    width: 80%;
    height: 80%;
    border-radius: 20px;
    background: #ff9700;
    opacity: 0.08;
    z-index: -1;
}

.counter-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.counter-icon {
    width: 71px;
    flex: 0 0 71px;
    margin-right: 30px;
}

.counter-content .count {
    display: flex;
    align-items: center;
    color: #030B15;
    letter-spacing: -0.02em;
    font-size: 48px;
    line-height: 0.85;
    margin-bottom: 8px;
}

.counter-content p {
    margin-bottom: 0;
    color: #646580;
    line-height: 1.6;
}


/* 11. RoadMap */
.roadmap-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.roadmap-area::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(360deg, #0B1D33 0%, rgba(11, 29, 51, 0) 97.51%);
    transform: matrix(1, 0, 0, -1, 0, 0);
    opacity: .8;
}

.roadmap-wrap {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 1670px;
    overflow-x: auto;
}

.roadmap-wrap::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1670px;
    height: 8px;
    background: linear-gradient(294.72deg, #FF4581 9.05%, #4388DD 79.28%);
    opacity: 0.1;
}

.roadmap-item {
    width: 345px;
    display: flex;
    flex-direction: column;
    flex: 0 0 345px;
}

.roadmap-title {
    display: inline-block;
    color: #ff9700;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 22px;
    letter-spacing: 0.1em;
}

.roadmap-content .dot {
    content: "";
    position: absolute;
    left: -8px;
    top: 0;
    width: 16px;
    height: 16px;
    background: #ff9700;
    border-radius: 50%;
}

.roadmap-content .dot::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    background: #ff9700;
    opacity: 0.1;
    border-radius: 50%;
}

.roadmap-content {
    position: relative;
    margin-left: 57px;
    padding-left: 17px;
    padding-top: 50px;
}

.roadmap-content::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #ff9700;
}

.roadmap-content .title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 10px;
}

.roadmap-content p {
    margin-bottom: 0;
    color: #A4B4C3;
}

.roadmap-item:nth-child(even) {
    flex-direction: column-reverse;
    margin-bottom: 165px;
    width: 400px;
    flex: 0 0 400px;
}

.roadmap-item:nth-child(even) .roadmap-content .dot {
    top: auto;
    bottom: 0;
}

.roadmap-item:nth-child(even) .roadmap-title {
    margin-top: 22px;
    margin-bottom: 0;
}

.roadmap-item:nth-child(even) .roadmap-content {
    padding-bottom: 110px;
    padding-top: 0;
}

.roadmap-item:not(:first-child) {
    margin-left: -120px;
}

.roadmap-item:nth-child(odd) {
    margin-top: 165px;
    margin-left: -180px;
}

.roadmap-item:nth-child(1) {
    margin-left: 100px;
    width: 288px;
    flex: 0 0 auto;
}

.roadmap-item:last-child {
    width: 290px;
    flex: 0 0 auto;
}

.bt-roadmap-item:nth-child(2) .roadmap-content::before,
.bt-roadmap-item:nth-child(2) .dot,
.bt-roadmap-item:nth-child(2) .dot::before {
    background: #FF4581;
}

.bt-roadmap-item:nth-child(3) .roadmap-content::before,
.bt-roadmap-item:nth-child(3) .dot,
.bt-roadmap-item:nth-child(3) .dot::before {
    background: #5DD400;
}

.bt-roadmap-item:nth-child(4) .roadmap-content::before,
.bt-roadmap-item:nth-child(4) .dot,
.bt-roadmap-item:nth-child(4) .dot::before {
    background: #007FF4;
}

.bt-roadmap-item:nth-child(5) .roadmap-content::before,
.bt-roadmap-item:nth-child(5) .dot,
.bt-roadmap-item:nth-child(5) .dot::before {
    background: #FF9700;
}

.bt-roadmap-item:nth-child(6) .roadmap-content::before,
.bt-roadmap-item:nth-child(6) .dot,
.bt-roadmap-item:nth-child(6) .dot::before {
    background: #FF4581;
}

.bt-roadmap-item:nth-child(7) .roadmap-content::before,
.bt-roadmap-item:nth-child(7) .dot,
.bt-roadmap-item:nth-child(7) .dot::before {
    background: #5DD400;
}

.bt-roadmap-item:nth-child(8) .roadmap-content::before,
.bt-roadmap-item:nth-child(8) .dot,
.bt-roadmap-item:nth-child(8) .dot::before {
    background: #007FF4;
}

.bt-roadmap-item:nth-child(9) .roadmap-content::before,
.bt-roadmap-item:nth-child(9) .dot,
.bt-roadmap-item:nth-child(9) .dot::before {
    background: #FF9700;
}

.bt-roadmap-item:nth-child(10) .roadmap-content::before,
.bt-roadmap-item:nth-child(10) .dot,
.bt-roadmap-item:nth-child(10) .dot::before {
    background: #FF4581;
}

.bt-roadmap-item:nth-child(11) .roadmap-content::before,
.bt-roadmap-item:nth-child(11) .dot,
.bt-roadmap-item:nth-child(11) .dot::before {
    background: #5DD400;
}

.bt-roadmap-item:nth-child(12) .roadmap-content::before,
.bt-roadmap-item:nth-child(12) .dot,
.bt-roadmap-item:nth-child(12) .dot::before {
    background: #007FF4;
}

.bt-roadmap-item:nth-child(2) .roadmap-title {
    color: #FF4581;
}

.bt-roadmap-item:nth-child(3) .roadmap-title {
    color: #5DD400;
}

.bt-roadmap-item:nth-child(4) .roadmap-title {
    color: #007FF4;
}

.bt-roadmap-item:nth-child(5) .roadmap-title {
    color: #FF9700;
}

.bt-roadmap-item:nth-child(6) .roadmap-title {
    color: #FF4581;
}

.bt-roadmap-item:nth-child(7) .roadmap-title {
    color: #5DD400;
}

.bt-roadmap-item:nth-child(8) .roadmap-title {
    color: #007FF4;
}

.bt-roadmap-item:nth-child(9) .roadmap-title {
    color: #FF9700;
}

.bt-roadmap-item:nth-child(10) .roadmap-title {
    color: #FF4581;
}

.bt-roadmap-item:nth-child(11) .roadmap-title {
    color: #5DD400;
}

.bt-roadmap-item:nth-child(12) .roadmap-title {
    color: #007FF4;
}

/* roadmap-two */
.roadmap-wrap-two {
    display: flex;
    align-items: flex-start;
    position: relative;
    margin-bottom: 50px;
}

.roadmap-wrap-two:last-child {
    margin-bottom: 0;
}

.roadmap-wrap-two::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50px;
    width: 100%;
    height: 8px;
    background: linear-gradient(294.72deg, #FF4581 9.05%, #4388DD 79.28%);
    opacity: 0.1;
}

.roadmap-wrap-two .roadmap-content .title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #030B15;
    line-height: 1.62;
}

.roadmap-wrap-two .roadmap-content p {
    color: #727885;
}

.roadmap-wrap-two .roadmap-content {
    padding-top: 75px;
}

.roadmap-wrap-two .roadmap-item:nth-child(odd) {
    margin-top: 0;
    margin-left: 0;
}

.roadmap-wrap-two .roadmap-item:not(:first-child) {
    margin-left: 0;
}

.roadmap-wrap-two .roadmap-item:nth-child(even) .roadmap-content {
    padding-bottom: 0;
    padding-top: 75px;
}

.roadmap-wrap-two .roadmap-item:nth-child(even) {
    flex-direction: column;
    margin-bottom: 30px;
    flex: 0 0 400px;
    width: 400px;
}

.roadmap-wrap-two .roadmap-item:nth-child(even) .roadmap-content .dot {
    top: 0;
    bottom: auto;
}

.roadmap-wrap-two .roadmap-item:nth-child(even) .roadmap-title {
    margin-top: 0;
    margin-bottom: 22px;
}

.roadmap-wrap-two .roadmap-item:nth-child(2),
.roadmap-wrap-two .roadmap-item:nth-child(1) {
    margin-left: 130px;
}

.roadmap-wrap-two .roadmap-item:nth-child(4) {
    width: 260px;
    flex: 0 0 260px;
}

.roadmap-wrap-two .roadmap-item {
    margin-bottom: 30px;
}

.roadmap-wrap-two .roadmap-item:nth-child(2) .roadmap-content::before,
.roadmap-wrap-two .roadmap-item:nth-child(2) .dot,
.roadmap-wrap-two .roadmap-item:nth-child(2) .dot::before {
    background: #FF9700;
}

.roadmap-wrap-two .roadmap-item:nth-child(3) .roadmap-content::before,
.roadmap-wrap-two .roadmap-item:nth-child(3) .dot,
.roadmap-wrap-two .roadmap-item:nth-child(3) .dot::before {
    background: #007FF4;
}

.roadmap-wrap-two .roadmap-item:nth-child(4) .roadmap-content::before,
.roadmap-wrap-two .roadmap-item:nth-child(4) .dot,
.roadmap-wrap-two .roadmap-item:nth-child(4) .dot::before {
    background: #FF4581;
}

.roadmap-wrap-two .roadmap-item:nth-child(2) .roadmap-title {
    color: #FF9700;
}

.roadmap-wrap-two .roadmap-item:nth-child(3) .roadmap-title {
    color: #007FF4;
}

.roadmap-wrap-two .roadmap-item:nth-child(4) .roadmap-title {
    color: #FF4581;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(1) .roadmap-content::before,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(1) .dot,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(1) .dot::before {
    background: #FF4581;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(2) .roadmap-content::before,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(2) .dot,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(2) .dot::before {
    background: #007FF4;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(3) .roadmap-content::before,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(3) .dot,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(3) .dot::before {
    background: #FF9700;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(4) .roadmap-content::before,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(4) .dot,
.roadmap-wrap-two.bottom .roadmap-item:nth-child(4) .dot::before {
    background: #ff9700;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(1) .roadmap-title {
    color: #FF4581;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(2) .roadmap-title {
    color: #007FF4;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(3) .roadmap-title {
    color: #FF9700;
}

.roadmap-wrap-two.bottom .roadmap-item:nth-child(4) .roadmap-title {
    color: #ff9700;
}


/* 12. Document */
.area-bg {
    background-image: url(../img/bg/area_bg.png);
    background-size: cover;
    background-position: center;
}

.document-content .document-list {
    margin-bottom: 40px;
}

.document-content .document-list li {
    font-size: 18px;
    color: #fff;
    position: relative;
    margin-bottom: 15px;
    padding-left: 15px;
}

.document-content .document-list li:last-child {
    margin-bottom: 0;
}

.document-content .document-list li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    background: #fff;
    border-radius: 50%;
}

/* 13. Team */
.team-area {
    position: relative;
    z-index: 1;
}

.team-area::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(360deg, #0B1D33 0%, rgba(11, 29, 51, 0) 97.51%);
    transform: matrix(1, 0, 0, -1, 0, 0);
    z-index: -1;
}

.team-item {
    text-align: center;
    margin-bottom: 20px;
}

.team-item .team-thumb {
    position: relative;
    border-radius: 50%;
    padding: 15px;
    margin-bottom: 5px;
    display: inline-block;
}

.team-item .team-thumb::before {
    content: "";
    left: 0;
    top: 0;
    width: 226px;
    height: 226px;
    background: transparent;
    border: 2px dashed #ff9700;
    border-radius: 50%;
    position: absolute;
    transition: opacity .3s linear;
    animation: teamRotate 10s linear infinite;
    opacity: 0;
}

.team-item:hover .team-thumb::before {
    opacity: 1;
}

.team-item .team-thumb img {
    border-radius: 50%;
    filter: grayscale(100%);
    transition: all .3s ease-in-out;
    max-width: 197px;
}

.team-item:hover .team-thumb img {
    filter: grayscale(0);
}

.team-content .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 500;
    transition: .3s linear;
}

.team-content span {
    display: block;
}

.team-content .team-social {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 -10px;
}

.team-content .team-social li {
    padding: 0 10px;
}

.team-content .team-social li a {
    color: #fff;
    font-size: 13px;
}

.team-content .team-social li a:hover {
    color: #ff9700;
}

/* team-two */
.team-bg {
    background-image: url(../img/bg/team_bg.jpg);
    background-size: cover;
    background-position: center;
    padding: 130px 0 100px;
    position: relative;
}

.team-bg::after,
.team-bg::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    background-image: url(../img/bg/team_shape01.png);
    background-position: center;
    background-repeat: repeat;
    width: 100%;
    height: 29px;
}

.team-bg::after {
    background-image: url(../img/bg/team_shape02.png);
    top: auto;
    bottom: 0;
    height: 23px;
}

.team-item.team-item-two .team-thumb img {
    border-radius: 0;
    filter: grayscale(100%);
    transition: all .3s ease-in-out;
}

.team-item.team-item-two:hover .team-thumb img {
    filter: grayscale(0);
}

.team-item.team-item-two .team-thumb {
    border-radius: 0;
    padding: 0;
    margin-bottom: 35px;
}

.team-item.team-item-two .team-thumb::before {
    display: none;
}

.team-item.team-item-two {
    margin-bottom: 30px;
}

/* 14. Download */
.download-content p {
    color: #727885;
    margin-bottom: 30px;
}

.download-btn {
    display: flex;
    align-items: center;
}

.download-btn a {
    margin-right: 20px;
    margin-top: 10px;
    display: block;
}

/* 15. Faq */
.faq-area {
    background: #F3F6FC;
    padding: 120px 0 130px;
    position: relative;
    z-index: 1;
}

.faq-area.dark {
    background: #020b15;
    padding: 0 0 0 0;
    position: relative;
    z-index: 1;
}

.dark .faq-wrap .accordion-body p {
    color: #ffffff;
}

.faq-wrap .accordion-item {
    background-color: transparent;
    border: none;
    margin-bottom: 20px;
    position: relative;
}

.faq-wrap .accordion-item:last-child {
    margin-bottom: 0;
}

.faq-wrap .accordion-item:first-of-type .accordion-button {
    border-top-left-radius: 100px;
    border-top-right-radius: 100px;
}

.faq-wrap .accordion-item:first-of-type .accordion-button:not(.collapsed) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.faq-wrap .accordion-button {
    font-size: 24px;
    font-weight: 500;
    font-family: 'Poppins';
    line-height: 1.25;
    padding: 37px 100px 37px 55px;
    border-radius: 100px;
    position: relative;
}

.dark .faq-wrap .accordion-button {
    color: #ffffff;
    background: #0f151d;
}

.faq-wrap .accordion-button:not(.collapsed) {
    color: #030B15;
    background-color: #fff;
    box-shadow: none;
    border-radius: 0;
}

.dark .faq-wrap .accordion-button:not(.collapsed) {
    color: #ff9700;
    background-color: #0f151d;
    box-shadow: none;
    border-radius: 0;
}

.dark .faq-wrap .accordion-button::after {
    color: white;
}

.faq-wrap .accordion-body {
    padding: 0 40px 45px 55px;
    background: #fff;
    position: relative;
}

.dark .faq-wrap .accordion-body {
    background: #0f151d;
    color: white;
}

.faq-wrap .accordion-body::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4px;
    background: #ff9700;
}

.faq-wrap .accordion-body p {
    margin-bottom: 0;
    color: #727885;
    font-size: 18px;
    line-height: 1.55;
}

.faq-wrap .accordion-item:last-of-type .accordion-button.collapsed {
    border-bottom-right-radius: 100px;
    border-bottom-left-radius: 100px;
}

.accordion-button:focus {
    border-color: transparent;
    box-shadow: none;
}

.faq-wrap .accordion-button::after {
    width: auto;
    height: auto;
    content: "\f063";
    background-image: none;
    background-size: auto;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    font-size: 25px;
    position: absolute;
    right: 50px;
    top: 36px;
    color: #132047;
    opacity: .3;
}

.faq-wrap .accordion-button:not(.collapsed)::after {
    background-image: none;
    transform: rotate(-180deg);
    opacity: 1;
}

.faq-shape-wrap img {
    position: absolute;
    z-index: -1;
}

.faq-shape-wrap img.img-one {
    left: 7%;
    bottom: 15%;
    animation: leftToRight 5s infinite linear;
}

.faq-shape-wrap img.img-two {
    right: 6%;
    top: 15%;
}

.faq-shape-wrap img.img-three {
    left: 50%;
    transform: translateX(-50%);
    bottom: 5%;
}

/* 16. Blog */
.blog-post-item {
    margin-bottom: 60px;
}

.blog-post-thumb {
    margin-bottom: 30px;
}

.blog-post-thumb img {
    border-radius: 15px;
    width: 100%;
}

.blog-meta ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 25px;
}

.blog-meta ul li {
    color: #030B15;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    margin-right: 40px;
    margin-top: 5px;
}

.blog-meta ul li:last-child {
    margin-right: 0;
}

.blog-meta ul li i {
    margin-right: 10px;
    color: #564DCA;
}

.blog-meta ul li a {
    color: #030B15;
}

.blog-meta ul li a:hover {
    color: #564DCA;
}

.blog-post-content .title {
    color: #030B15;
    letter-spacing: -0.02em;
    font-size: 36px;
    margin-bottom: 20px;
    line-height: 1.27;
}

.blog-post-content .title a:hover {
    color: #564DCA;
}

.blog-post-content p {
    margin-bottom: 30px;
}

.blog-post-content .btn.btn-two i {
    margin-left: 20px;
}

/*.blog-sidebar {*/
/*    margin-left: 25px;*/
/*}*/

.blog-widget {
    background: #030B15;
    padding: 40px 30px;
    margin-bottom: 40px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(246, 246, 246, 0.06);
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.25);
    border-radius: 11px;
}

.blog-widget .bw-title {
    font-size: 22px;
    margin-bottom: 20px;
    color: #ffffff;
    letter-spacing: -0.02em;
}

.blog-widget .sidebar-search {
    position: relative;
}

.blog-widget .sidebar-search input {
    background: #F3F6FC;
    border: 1px solid #F2F4F6;
    color: #030A39;
    width: 100%;
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    padding: 19px 50px 19px 20px;
    height: 60px;
}

.blog-widget .sidebar-search input::placeholder {
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #727885;
}

.blog-widget .sidebar-search button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 20px;
    border: none;
    background: transparent;
    padding: 0;
    font-size: 14px;
    color: #030A39;
}

.blog-widget .category-list ul li {
    margin-bottom: 15px;
}

.blog-widget .category-list ul li:last-child {
    margin-bottom: 0;
}

.blog-widget .category-list ul li a {
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #ffffff;
    font-weight: 600;
    font-size: 15px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.04);
    background: #0B1D33;
    padding: 11px 18px;
    border-radius: 5px;
}

.blog-widget .category-list ul li a span {
    margin-left: auto;
}

.blog-widget .category-list ul li a:hover {
    color: #ffffff;
    box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.04);
    border-color: #ffffff;
    background-color: #0f2846;
    opacity: 0.9;
}

.blog-widget .page-list ul li {
    margin-bottom: 20px;
}

.blog-widget .page-list ul li:last-child {
    margin-bottom: 0;
}

.blog-widget .page-list ul li a {
    display: flex;
    align-items: center;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: #727885;
}

.blog-widget .page-list ul li a i {
    margin-left: auto;
}

.blog-widget .page-list ul li a:hover {
    color: #171717;
}

.rc-post-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.rc-post-item:last-child {
    margin-bottom: 0;
}

.rc-post-thumb {
    width: 97px;
    flex: 0 0 97px;
    margin-right: 25px;
}

.rc-post-thumb img {
    border-radius: 10px;
}

.rc-post-content span {
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #727885;
    display: block;
    margin-bottom: 5px;
}

.rc-post-content .title {
    font-size: 15px;
    color: #141515;
    margin-bottom: 0;
    line-height: 1.42;
}

.rc-post-content .title a:hover {
    color: #564DCA;
}

.blog-widget .tag-list ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 -5px -10px;
}

.blog-widget .tag-list ul li {
    padding: 0 5px 10px;
}

.blog-widget .tag-list ul li a {
    color: #b6b7ba;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 12px;
    border: 1px solid #F2F4F6;
    padding: 6px 20px;
    display: block;
}

.blog-widget .tag-list ul li a:hover {
    opacity: 1;
    color: #fff;
    background: #564DCA;
    border-color: #564DCA;
}

.blog-widget:last-child {
    margin: 0 0;
}


/* blog-details */
.blog-details-wrap .blog-post-content p {
    margin-bottom: 15px;
}

.bd-approach-wrap {
    margin: 50px 0 65px;
}

.bd-approach-wrap .row .col-46 {
    width: 46%;
    flex: 0 0 46%;
}

.bd-approach-wrap .row .col-54 {
    width: 54%;
    flex: 0 0 54%;
}

.bd-approach-content {
    width: 93%;
}

.bd-approach-content .title {
    font-size: 24px;
    margin-bottom: 20px;
}

.bd-approach-content ul li {
    font-weight: 500;
    font-size: 15px;
    color: #171151;
    margin-bottom: 5px;
}

.bd-approach-content ul li:last-child {
    margin-bottom: 0;
}

.bd-approach-content ul li i {
    color: #564DCA;
    margin-right: 15px;
}

.bd-approach-img img {
    border-radius: 10px;
}

.bd-technology-content .title {
    letter-spacing: -0.03em;
    font-size: 24px;
    margin-bottom: 25px;
}

.blog-details-img {
    margin: 35px 0 40px;
}

.blog-details-img img {
    border-radius: 10px;
    margin-bottom: 20px;
}

.blog-details-bottom {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 36px 0;
    border-top: 1px solid #ECEEF3;
    border-bottom: 1px solid #ECEEF3;
}

.blog-details-tags {
    width: 70%;
    flex: 0 0 70%;
}

.blog-details-tags ul {
    display: flex;
    align-items: center;
}

.blog-details-tags ul li {
    margin-top: 5px;
    margin-bottom: 5px;
}

.blog-details-social ul li.social-title,
.blog-details-tags ul li.tag-title {
    font-weight: 600;
    font-size: 20px;
    font-family: 'Poppins';
    letter-spacing: -0.02em;
    color: #030B15;
    margin-right: 20px;
}

.blog-details-tags ul li a {
    font-weight: 700;
    font-size: 12px;
    text-transform: uppercase;
    color: #C1C1C1;
    display: block;
    padding: 5px 18px;
}

.blog-details-tags ul li a:hover {
    background: #564DCA;
    color: #fff;
}

.blog-details-social {
    width: 30%;
    flex: 0 0 30%;
}

.blog-details-social ul {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.blog-details-social ul li {
    margin-right: 20px;
}

.blog-details-social ul li:last-child {
    margin-right: 0;
}

.blog-details-social ul li a {
    font-size: 15px;
    color: #0072AC
}

.blog-details-social ul li:nth-child(3) a {
    color: #DA0021;
}

.blog-details-social ul li:nth-child(4) a {
    color: #0072AC;
}

.blog-details-social ul li:nth-child(5) a {
    color: #3AC4FF;
}

.comment-wrap {
    border-bottom: 1px solid #ECEEF3;
}

.comment-wrap .title {
    font-size: 20px;
    color: #030B15;
    margin: 0 0 30px;
}

.latest-comments ul li .comments-box {
    display: flex;
    align-items: flex-start;
    width: 74%;
    margin-bottom: 60px;
}

.latest-comments ul li .comments-box .comments-avatar {
    width: 80px;
    flex: 0 0 80px;
    margin-right: 30px;
}

.latest-comments ul li .comments-box .comments-avatar img {
    border-radius: 50%;
}

.latest-comments ul li .comments-box .avatar-name {
    margin-bottom: 10px;
}

.latest-comments ul li .comments-box .avatar-name .title {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    color: #030B15;
    font-size: 16px;
    margin-bottom: 0;
}

.latest-comments ul li .comments-box .avatar-name .title a {
    font-size: 20px;
    opacity: .5;
}

.latest-comments ul li .comments-box .avatar-name .title a:hover {
    opacity: 1;
}

.latest-comments ul li .comments-box .avatar-name span {
    letter-spacing: 0.18em;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 10px;
    color: #727885;
}

.latest-comments ul li .comments-box .comment-text p {
    margin-bottom: 0;
    font-size: 14px;
}

.latest-comments ul li .children {
    margin-left: 100px;
}

.latest-comments ul li .children .comments-box {
    width: 84%;
}

.post-comments-form {
    margin-top: 50px;
}

.post-comment-content {
    margin-bottom: 45px;
}

.post-comment-content .title {
    color: #030B15;
    margin-bottom: 10px;
    letter-spacing: -0.02em;
    font-size: 20px;
}

.post-comment-content p {
    margin-bottom: 0;
}

.comment-form .form-grp {
    margin-bottom: 30px;
}

.comment-form .form-grp textarea,
.comment-form .form-grp input {
    width: 100%;
    border: none;
    background: rgb(118 118 130 / 10%);
    border-radius: 10px;
    font-weight: 400;
    font-size: 15px;
    padding: 23px 80px 23px 25px;
    color: #fff;
    height: 72px;
}

.comment-form .form-grp textarea::placeholder,
.comment-form .form-grp input::placeholder {
    color: #B2B0C1;
    font-weight: 400;
    font-size: 15px;
}

.comment-form .form-grp textarea {
    min-height: 172px;
    max-height: 172px;
}
.comment-form .form-grp.institutional-form textarea {
    min-height: 152px;
    max-height: 152px;
}

.comment-form .btn.btn-two:hover {
    background: #ff9700;
}


/* 17. Pagination */
.pagination-wrap ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 10px;
    margin-bottom: 0 !important;
}

.pagination-wrap ul li {
    display: block;
    margin: 10px 3.7px 0;
}

.pagination-wrap ul li a {
    width: 49px;
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #F2F5FA;
    border-radius: 5px;
    background: transparent;
    font-weight: 500;
    font-size: 14px;
    color: #8A879F;
}

.pagination-wrap ul li a:hover,
.pagination-wrap ul li .current {
    color: #fff;
    background: #564DCA;
    border-color: #564DCA;
}


/* 18. Contact */
.contact-info-wrap {
    margin-bottom: 50px;
}

.contact-info-item {
    text-align: center;
    position: relative;
    margin-bottom: 30px;
}

.contact-info-item::before {
    content: "";
    position: absolute;
    right: -61px;
    top: 44px;
    background-image: url(../img/images/line.png);
    width: 122px;
    height: 9px;
}

.contact-info-wrap .row [class*="col-"]:last-child .contact-info-item::before {
    display: none;
}

.contact-info-item .icon {
    width: 107px;
    height: 107px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent;
    font-size: 28px;
    color: #12D176;
    margin: 0 auto;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.07);
    z-index: 1;
    margin-bottom: 10px;
}

.contact-info-item .icon-background {
    position: absolute;
    width: 83px;
    height: 83px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #0B1D33;
    border-radius: 50%;
    z-index: -1;
}

.contact-info-item .icon-background::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 50%;
    width: 83px;
    height: 83px;
    background: transparent;
    border: 3px solid #12D176;
}

.contact-info-item .content p {
    color: #fff;
    font-size: 22px;
    margin-bottom: 0;
    font-weight: 500;
    line-height: 30px;
}

.contact-info-wrap .row [class*="col-"]:nth-child(2) .icon {
    color: #574dca;
}

.contact-info-wrap .row [class*="col-"]:nth-child(3) .icon {
    color: #ff9700;
}

.contact-info-wrap .row [class*="col-"]:nth-child(2) .icon-background::before {
    border-color: #574dca;
}

.contact-info-wrap .row [class*="col-"]:nth-child(3) .icon-background::before {
    border-color: #ff9700;
}

.contact-form-wrap {
    background-image: url(../img/bg/contact_bg.png);
    background-position: center;
    background-size: cover;
    padding: 80px;
}

.contact-form-wrap .form-grp {
    margin-bottom: 30px;
}

.contact-form-wrap .form-grp textarea,
.contact-form-wrap .form-grp input {
    width: 100%;
    background: #0B1D33;
    border: 1px solid rgba(255, 255, 255, 0.07);
    border-radius: 5px;
    font-size: 16px;
    color: #fff;
    font-weight: 400;
    padding: 18px 20px;
    height: 65px;
}

.contact-form-wrap .form-grp textarea::placeholder,
.contact-form-wrap .form-grp input::placeholder {
    font-size: 16px;
    color: #fff;
    font-weight: 400;
    opacity: .7;
}

.contact-form-wrap .form-grp textarea {
    min-height: 184px;
    max-height: 184px;
}

.contact-form-wrap .submit-btn .btn {
    background: #0B1D33;
    border-color: #ff9700;
}

/* contact-two */
.contact-bg {
    background-image: url(../img/bg/contact_bg02.jpg);
    background-size: cover;
    background-position: center;
    padding: 130px 0 250px;
    position: relative;
}

/*.contact-bg::after,*/
/*.contact-bg::before {*/
/*	content: "";*/
/*	position: absolute;*/
/*	left: 0;*/
/*	top: 0;*/
/*	background-image: url(../img/bg/contact_shape01.png);*/
/*	background-position: center;*/
/*	background-repeat: repeat;*/
/*	width: 100%;*/
/*	height: 29px;*/
/*}*/
/*.contact-bg::after {*/
/*	background-image: url(../img/bg/contact_shape02.png);*/
/*	top: auto;*/
/*	bottom: 0;*/
/*	height: 18px;*/
/*}*/
.contact-inner {
    padding: 0 40px;
}

.contact-info-wrap-two .title {
    font-size: 50px;
    margin-bottom: 35px;
    letter-spacing: -0.03em;
    line-height: 1.2;
}

.contact-list-item {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(246, 246, 246, 0.06);
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.25);
    border-radius: 11px;
    display: flex;
    align-items: center;
    padding: 22px 32px;
    width: 69%;
    margin-bottom: 15px;
}

.contact-list-item:last-child {
    margin-bottom: 0;
}

.contact-list-item .icon {
    width: 35px;
    flex: 0 0 35px;
    margin-right: 22px;
}

.contact-list-item .content p {
    margin-bottom: 0;
    font-size: 15px;
    line-height: 146.49%;
    color: #FFFFFF;
}

.wallet-list-item {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(255, 255, 255, 0.06);
    border: 1px solid #ffffff5c;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.25);
    border-radius: 11px;
    display: flex;
    align-items: center;
    padding: 22px 32px;
    width: 100%;
    margin-bottom: 15px;
    transition: all 0.4s 0s;
}

.ml-0 {
    margin-left: 0 !important;
}

.wallet-list-item:hover {
    border: 1px solid #ffffff;
    box-shadow: 2px 2px 9px 2px rgb(255 255 255 / 18%)
}

.wallet-list-item:last-child {
    margin-bottom: 0;
}

.wallet-list-item .icon {
    width: 35px;
    flex: 0 0 35px;
    margin-right: 22px;
}

.wallet-list-item .content p {
    margin-bottom: 0;
    font-size: 15px;
    line-height: 146.49%;
    color: #FFFFFF;
}

.order-items div {
    border-right: 1px solid #ffffff;
    text-align: center;
}

.order-items div:nth-last-child(1) {
    border-right: none;
}

.order-list-item {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(246, 246, 246, 0.06);
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.25);
    border-radius: 11px;
    display: flex;
    align-items: center;
    padding: 10px 15px;
    width: 100%;
    margin-bottom: 15px;
}

.order-list-item .content {
    width: 100%;
}

.order-list-item:last-child {
    margin-bottom: 0;
}

.order-list-item .icon {
    width: 35px;
    flex: 0 0 35px;
    margin-right: 22px;
}

.order-list-item .content p {
    margin-bottom: 0;
    font-size: 15px;
    color: #FFFFFF;
}

.order-list-item .content h3 {
    margin-bottom: 0;
    font-size: 16px;
    color: #ff9700;
    font-weight: bold;
}

.order-list-item .content .status {
    text-transform: uppercase;
    font-weight: 600;
    font-size: 12px;
    border: 1px solid #564DCA;
    padding: 3px 7px;
    display: inline-block;
    opacity: 1;
    color: #fff;
    background: #564DCA;
    border-radius: 10px;
}

.order-list-item .content .status.pending {
    border: 1px solid #FF9700;
    background: #FF9700;
}

.order-list-item .content .status.completed {
    border: 1px solid #12D176;
    background: #12D176;
}

.order-list-item .content .status.failed {
    border: 1px solid #FF1D45;
    background: #FF1D45;
}

.order-list-item .content .status.processing {
    border: 1px solid #ff9700;
    background: #ff9700;
}


.contact-form-wrap-two .title {
    font-size: 30px;
    text-transform: none;
    letter-spacing: -0.03em;
    line-height: 2;
    margin-bottom: 35px;
}

.contact-form-wrap-two textarea,
.contact-form-wrap-two input {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(246, 246, 246, 0.06);
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.25);
    border-radius: 11px;
    width: 100%;
    letter-spacing: -0.02em;
    color: #030B15;
    font-weight: 400;
    font-size: 20px;
    padding: 17px 22px;
    line-height: 1.5;
    height: 66px;
    margin-bottom: 20px;
    display: block;
    transition: .3s linear;
}

.contact-form-wrap-two textarea::placeholder,
.contact-form-wrap-two input::placeholder {
    letter-spacing: -0.02em;
    color: rgba(255, 255, 255, 0.38);
    font-weight: 400;
    font-size: 20px;
    line-height: 1.5;
}

.contact-form-wrap-two textarea:focus,
.contact-form-wrap-two input:focus {
    background: #fff;
    border-color: #fff;
}

.contact-form-wrap-two textarea {
    min-height: 165px;
    max-height: 165px;
}

.contact-form-wrap-two .btn.btn-two {
    background: #ff9700;
}

.contact-form-wrap-two .btn.btn-two:hover {
    background: #564DCA;
}

/* 19. Newsletter */
.newsletter-wrap {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0px 26px 23px rgba(81, 98, 132, 0.06);
    border-radius: 10px;
    padding: 64px 50px;
    margin-top: -120px;
    z-index: 1;
    position: relative;
}

.newsletter-content {
    width: 42.5%;
    flex: 0 0 42.5%;
}

.newsletter-content .title {
    color: #030B15;
    font-size: 32px;
    line-height: 1.5;
    margin-bottom: 0;
    letter-spacing: -1px;
}

.newsletter-form {
    width: 57.5%;
    flex: 0 0 57.5%;
}

.newsletter-form form {
    position: relative;
}

.newsletter-form input {
    width: 100%;
    border: 1px solid #ECEEF3;
    border-radius: 4px;
    background: #F3F6FC;
    font-size: 16px;
    font-weight: 400;
    color: #030B15;
    padding: 26px 215px 26px 25px;
    line-height: 1.25;
    height: 72px;
}

.newsletter-form input::placeholder {
    font-size: 16px;
    font-weight: 400;
    color: #667279;
}

.newsletter-form form button {
    position: absolute;
    right: 12px;
    top: 8px;
    bottom: 8px;
}

.newsletter-form .btn.btn-two {
    padding: 21px 47px;
}


/* 20. Footer */
.footer-area {
    position: relative;
    z-index: 1;
}

.footer-area::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(359.43deg, #0B1D33 11.06%, rgba(11, 29, 51, 0) 99.43%);
    z-index: -1;
}

.footer-scroll-wrap {
    position: relative;
    z-index: 1;
}

.footer-scroll-wrap .scroll-to-target {
    width: 66px;
    height: 66px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #030B15;
    border: 1px solid #1F262F;
    border-radius: 50%;
    font-size: 18px;
    padding: 0 0;
    color: #fff;
    margin: 0 auto;
}

.footer-scroll-wrap .scroll-to-target:hover {
    background: #ff9700;
    border-color: #ff9700;
}

.footer-scroll-wrap::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: 1px;
    background: #1F262F;
    z-index: -1;
}

.footer-top {
    padding: 55px 0 50px;
}

.footer-widget {
    margin-bottom: 30px;
}

.footer-widget .f-logo {
    margin-bottom: 20px;
    display: block;
}

.footer-content p {
    margin-bottom: 25px;
    font-size: 15px;
    line-height: 26px;
    color: #ffffff;
}

.footer-content .footer-social {
    display: flex;
    align-items: center;
    margin: 0 -7px;
}

.footer-content .footer-social li {
    padding: 0 7px;
    display: flex;
    align-items: center;
}

.footer-content .footer-social li a {
    color: #fff;
    margin-bottom: 15px;
    position: relative;
}

.footer-content .footer-social li a.round {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 2px solid #719ED6;
    font-size: 15px;
    margin-right: 15px;
    font-weight: 400;
}

.footer-content .footer-social li a.round:hover {
    background: #ff9700;
    border-color: #ff9700;
}

.footer-content .footer-social li a.title::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: #fff;
    -webkit-transform-origin: right top;
    -ms-transform-origin: right top;
    transform-origin: right top;
    -webkit-transform: scale(0, 1);
    -ms-transform: scale(0, 1);
    transform: scale(0, 1);
    transition: transform 0.4s cubic-bezier(.74, .72, .27, .24);
}

.footer-content .footer-social li a.title:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}

.transfer-details .select-btns a {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    border: 2px solid #719ED6;
    font-size: 15px;
    color: #fff;
    font-weight: 600;
    padding: 7px 12px;
    margin-right: 10px;
}

.transfer-details input, .transfer-details select {

    border: none;
    /*background: rgb(118 118 130 / 10%);*/
    background: #212a32;
    border-radius: 10px;
    font-weight: 600;
    font-size: 17px;
    padding: 7px 12px;
    color: #fff;
    margin-left: 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.transfer-details input[type="date"] {
    width: 130px;
}

.transfer-details .select-btns a:hover {
    background: #564DCA;
    border-color: #564DCA;
}

.footer-widget .fw-title {
    font-size: 18px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    margin: 25px 0 27px;
}

.footer-link ul li {
    margin-bottom: 10px;
}

.footer-link ul li:last-child {
    margin-bottom: 0;
}

.footer-link ul li a {
    font-size: 15px;
    line-height: 20px;
    color: #ffffff;
    position: relative;
}

.footer-link ul li a::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: #fff;
    -webkit-transform-origin: right top;
    -ms-transform-origin: right top;
    transform-origin: right top;
    -webkit-transform: scale(0, 1);
    -ms-transform: scale(0, 1);
    transform: scale(0, 1);
    transition: transform 0.4s cubic-bezier(.74, .72, .27, .24);
}

.footer-link ul li a:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}

.footer-link ul li a:hover {
    color: #fff;
}

.footer-top .row [class*="col-"]:nth-child(2) .footer-widget {
    margin-left: 80px;
}

.footer-top .row [class*="col-"]:nth-child(4) .footer-widget {
    margin-left: 75px;
}

.footer-newsletter p {
    font-size: 15px;
    line-height: 26px;
    margin-bottom: 60px;
    color: #A4B4C3;
}

.footer-newsletter form {
    position: relative;
}

.footer-newsletter form input {
    width: 100%;
    border: none;
    background: rgb(118 118 130 / 10%);
    border-radius: 10px;
    font-weight: 400;
    font-size: 15px;
    padding: 23px 80px 23px 25px;
    color: #fff;
    height: 72px;
}

.footer-newsletter form input::placeholder {
    font-weight: 400;
    font-size: 15px;
    color: #fff;
}

.footer-newsletter form button {
    position: absolute;
    right: 0;
    top: 0;
    width: 65px;
    height: 72px;
    background: #ff9700;
    border-radius: 10px;
    font-size: 20px;
    color: #fff;
    border: none;
}

.footer-bottom {
    border-top: 1px solid rgb(255 255 255 / 6%);
    padding: 27px 0;
}

.copyright-text p {
    color: #fff;
    margin-bottom: 0;
    font-size: 15px;
}

.footer-menu ul {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin: 0 -30px;
}

.footer-menu ul li {
    padding: 0 30px;
}

.footer-menu ul li a {
    font-size: 15px;
    color: #A4B4C3;
    font-weight: 500;
}

.footer-menu ul li a:hover {
    color: #fff;
}

/* footer-two */
.footer-area-two .footer-top {
    padding: 70px 40px 65px;
}

.footer-menu-two .navigation {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.footer-menu-two .navigation li {
    margin-right: 60px;
}

.footer-menu-two .navigation li:last-child {
    margin-right: 0;
}

.footer-menu-two .navigation li a {
    color: #030B15;
    font-weight: 500;
    font-size: 16px;
}

.footer-menu-two .navigation li a:hover {
    color: #ff9700;
}

.footer-area-two .footer-social ul {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.footer-area-two .footer-social ul li {
    margin-right: 40px;
}

.footer-area-two .footer-social ul li:last-child {
    margin-right: 0;
}

.footer-area-two .footer-social ul li a {
    color: #0A142F;
    opacity: .3;
    font-size: 24px;
    line-height: 1;
}

.footer-area-two .footer-social ul li a:hover {
    color: #ff9700;
    opacity: 1;
}

.footer-area-two .footer-bottom {
    border-top: 1px solid #F0F3F9;
    padding: 40px 40px;
}

.footer-area-two .copyright-text p {
    color: #282F3B;
    margin-bottom: 0;
    font-size: 15px;
    font-weight: 500;
}

.footer-area-two .scroll-up .scroll-to-target {
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #564DCA;
    padding: 0 0;
    border: none;
    color: #fff;
    font-size: 17px;
    margin: -60px auto 15px;
}

.footer-area-two .scroll-up .scroll-to-target:hover {
    background: #ff9700;
}

.footer-area-two .scroll-up span {
    display: block;
    color: #0A142F;
    font-size: 14px;
    font-weight: 500;
}

.footer-bottom-menu ul {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.footer-bottom-menu ul li {
    margin-right: 30px;
}

.footer-bottom-menu ul li:last-child {
    margin-right: 0;
}

.footer-bottom-menu ul li a {
    font-size: 15px;
    color: #0A142F;
    font-weight: 500;
}

.footer-bottom-menu ul li a:hover {
    color: #ff9700;
}

/* footer-three */
.footer-bg {
    background-image: url(../img/bg/breadcrumb-bg.jpg);
    background-size: cover;
    background-position: center;
    position: relative;
    padding-top: 120px;
}

.footer-bg::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    background-image: url(../img/bg/footer_shape.png);
    background-repeat: repeat;
    background-position: center;
    width: 100%;
    height: 29px;
}

.footer-area-three .footer-top {
    padding: 0 0 50px;
}


.bt-roadmap-wrap {
    display: flex;
    padding: 0;
    margin: 10px 0 0;
    justify-content: flex-end;
    min-height: 735px;
}

.bt-roadmap_x {
    position: relative;
}

.bt-roadmap_x::before {
    content: "";
    position: absolute;
    left: 0;
    top: calc(50% - 0px);
    transform: translateY(-50%);
    width: 1670px;
    height: 8px;
    background: linear-gradient(294.72deg, #FF4581 9.05%, #4388DD 79.28%);
    opacity: 0.1;
}

.bt-roadmap-item {
    display: flex;
    width: 550px;
    flex: 0 0 auto;
    height: 430px;
    align-self: flex-end;
    flex-direction: column;
    justify-content: flex-start;
}

.bt-roadmap-item:nth-child(even) {
    align-self: flex-start;
    flex-direction: column-reverse;
    margin-top: -10px;
}

.bt-roadmap-item:nth-child(even) .roadmap-title {
    margin: 22px 0 0;
}

.roadmap-content span {
    display: block;
    color: #A4B4C3;
}

.bt-roadmap-item:nth-child(even) .roadmap-content {
    padding: 0 0 80px 17px;
}

.bt-roadmap-item:nth-child(even) .roadmap-content .dot {
    top: auto;
    bottom: 0;
}

.bt-roadmap-item:not(:first-child) {
    margin-left: 0px;
}

/*.bt-roadmap-item:last-child {*/
/*    width: 280px;*/
/*}*/

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_draggerRail {
    display: none;
}

.bt-roadmap_x._mCS_1.mCS_no_scrollbar {
    padding-bottom: 0;
}

.bt-roadmap_x.mCustomScrollbar {
    padding: 0 0 30px;
}

.mCustomScrollBox + .mCSB_scrollTools + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
    bottom: 0;
}

.bt-roadmap_x:not(.mCS_no_scrollbar):before {
    top: calc(50% - 15px);
}

.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
    background-color: #00c2f2;
}

/* 21. Preloader */
#preloader {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #030B15;
    z-index: 999;
    width: 100%;
    height: 100%;
}

.spinner {
    margin: 100px auto;
    width: 50px;
    height: 40px;
    text-align: center;
    font-size: 10px;
}

.spinner > div {
    background-color: #00c2f2;
    height: 100%;
    width: 6px;
    display: inline-block;
    -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    animation: sk-stretchdelay 1.2s infinite ease-in-out;
}

.spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
}

.spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
}

.spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
}

.spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
}

@-webkit-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.4);
    }

    20% {
        -webkit-transform: scaleY(1.0);
    }

;
}
@keyframes sk-stretchdelay {
    0%, 40%, 100% {
        transform: scaleY(0.4);
        -webkit-transform: scaleY(0.4);
    }

    20% {
        transform: scaleY(1.0);
        -webkit-transform: scaleY(1.0);
    }

;
}

.cart-item {
    background: #030B15;
    border: 1px solid #121A23;
    border-radius: 15px;
    padding: 40px 20px;
    transition: .3s ease-in-out;
}

.cart-item:hover {
    border-color: transparent;
    background: #061527;
}

.cart-icon {
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.07);
    border: 4px solid rgba(255, 255, 255, 0.06);
    border-radius: 50%;
    margin-bottom: 30px;
    transition: .3s ease-in-out;
}

.cart-icon img {
    max-width: 50%;
}

.cart-item:hover .cart-icon {
    border-color: #ff9700;
}

.cart-content .title {
    font-size: 24px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
}

.cart-content .amount {
    font-size: 22px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
}

.cart-content .total {
    font-size: 22px;
    margin-bottom: 4px;
    margin-top: 10px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
    color: #12D176;
}

.cart-content .price {
    font-size: 19px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
    color: #ff9700;
}

.cart-content .amount a {
    color: #12D176;
}

.cart-content p {
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 22px;
    color: #A4B4C3;
}

.cart-content label {
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 22px;
    color: #A4B4C3;
}

.pay-detail {
    border-bottom: 2px solid #212a32;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.pay-detail:nth-last-child(-n + 1) {
    border-bottom: none;
}

.pay-detail h5 {
    color: #574dca;
}

.pay-detail h5.total {
    color: #5DD400;
}

.pay-detail h5.wallet {
    color: #ff9700;
}

.pay-detail h5.chain {
    color: #FF9700;
}

.popup-info-item {
    text-align: center;
    position: relative;
    margin-bottom: 30px;
}

.popup-info-item .icon {
    width: 75px;
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent;
    font-size: 28px;
    color: #FF9700;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.07);
    z-index: 1;
}

.popup-info-item .icon-background {
    position: absolute;
    width: 73px;
    height: 73px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #0B1D33;
    border-radius: 50%;
    z-index: -1;
}

.popup-info-item .icon-background::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 50%;
    width: 73px;
    height: 73px;
    background: transparent;
    border: 3px solid #FF9700;
}

.popup-info-item .content h4 {
    color: #fff;
    font-size: 25px;
    margin-bottom: 0;
    font-weight: 600;
    line-height: 30px;
    margin-left: 20px;
}

.popup-info-item .content p {
    color: #fff;
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 500;
    line-height: 25px;
}

.popup-info-item .content .bank p {
    color: #fff;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 500;
    line-height: 25px;
}

.popup-info-item .content p span {
    color: #F72585;
}

.popup-input {
    border: none;
    /*background: rgb(118 118 130 / 10%);*/
    background: #212a32;
    border-radius: 10px;
    font-weight: 600;
    font-size: 17px;
    padding: 7px 12px;
    color: #fff;
    margin-left: 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

@supports (-webkit-appearance: none) or (-moz-appearance: none) {
    .checkbox-wrapper-14 input[type=checkbox] {
        --active: #275EFE;
        --active-inner: #fff;
        --focus: 2px rgba(39, 94, 254, .3);
        --border: #BBC1E1;
        --border-hover: #275EFE;
        --background: #fff;
        --disabled: #F6F8FF;
        --disabled-inner: #E1E6F9;
        -webkit-appearance: none;
        -moz-appearance: none;
        height: 21px;
        outline: none;
        display: inline-block;
        vertical-align: top;
        position: relative;
        margin: 0;
        cursor: pointer;
        border: 1px solid var(--bc, var(--border));
        background: var(--b, var(--background));
        transition: background 0.3s, border-color 0.3s, box-shadow 0.2s;
    }

    .checkbox-wrapper-14 input[type=checkbox]:after {
        content: "";
        display: block;
        left: 0;
        top: 0;
        position: absolute;
        transition: transform var(--d-t, 0.3s) var(--d-t-e, ease), opacity var(--d-o, 0.2s);
    }

    .checkbox-wrapper-14 input[type=checkbox]:checked {
        --b: var(--active);
        --bc: var(--active);
        --d-o: .3s;
        --d-t: .6s;
        --d-t-e: cubic-bezier(.2, .85, .32, 1.2);
    }

    .checkbox-wrapper-14 input[type=checkbox]:disabled {
        --b: var(--disabled);
        cursor: not-allowed;
        opacity: 0.9;
    }

    .checkbox-wrapper-14 input[type=checkbox]:disabled:checked {
        --b: var(--disabled-inner);
        --bc: var(--border);
    }

    .checkbox-wrapper-14 input[type=checkbox]:disabled + label {
        cursor: not-allowed;
    }

    .checkbox-wrapper-14 input[type=checkbox]:hover:not(:checked):not(:disabled) {
        --bc: var(--border-hover);
    }

    .checkbox-wrapper-14 input[type=checkbox]:focus {
        box-shadow: 0 0 0 var(--focus);
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch) {
        width: 21px;
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch):after {
        opacity: var(--o, 0);
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch):checked {
        --o: 1;
    }

    .checkbox-wrapper-14 input[type=checkbox] + label {
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        margin-left: 4px;
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch) {
        border-radius: 7px;
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch):after {
        width: 5px;
        height: 9px;
        border: 2px solid var(--active-inner);
        border-top: 0;
        border-left: 0;
        left: 7px;
        top: 4px;
        transform: rotate(var(--r, 20deg));
    }

    .checkbox-wrapper-14 input[type=checkbox]:not(.switch):checked {
        --r: 43deg;
    }

    .checkbox-wrapper-14 input[type=checkbox].switch {
        width: 38px;
        border-radius: 11px;
    }

    .checkbox-wrapper-14 input[type=checkbox].switch:after {
        left: 2px;
        top: 2px;
        border-radius: 50%;
        width: 17px;
        height: 17px;
        background: var(--ab, var(--border));
        transform: translateX(var(--x, 0));
    }

    .checkbox-wrapper-14 input[type=checkbox].switch:checked {
        --ab: var(--active-inner);
        --x: 17px;
    }

    .checkbox-wrapper-14 input[type=checkbox].switch:disabled:not(:checked):after {
        opacity: 0.6;
    }
}

.checkbox-wrapper-14 * {
    box-sizing: inherit;
}

.checkbox-wrapper-14 *:before,
.checkbox-wrapper-14 *:after {
    box-sizing: inherit;
}


.product-item {
    background: #030B15;
    border: 1px solid #ffffff5c;
    border-radius: 15px;
    padding: 40px 20px;
    transition: .3s ease-in-out;
}

.product-item:hover {
    border-color: #ffffff;
    background: #061527;
}

.product-icon {
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.07);
    border: 4px solid rgba(255, 255, 255, 0.06);
    border-radius: 50%;
    transition: .3s ease-in-out;
}

.product-icon img {
    max-width: 50%;
}

.product-item:hover .product-icon {
    border-color: #ff9700;
}

.product-content .title {
    font-size: 30px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
}

.product-content .amount {
    font-size: 22px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
}

.product-content .total {
    font-size: 22px;
    margin-bottom: 24px;
    font-weight: 500;
    text-transform: none;
    line-height: 1.27;
    color: #12D176;
}

.product-content .amount a {
    color: #12D176;
}

.product-content p {
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 22px;
    color: #ffffff;
}

.product-content label {
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 22px;
    color: #A4B4C3;
}

.product-content .contract .btn {
    font-size: 14px;
    padding: 20px 45px;
    background: #FF9700;
    border-radius: 5px;
}

.product-content .contract .btn:after {
    background: none;
}

.product-content .contract .btn:disabled {
    background-color: #a76617;
    opacity: 1;
}


.product-content .contract .btn:hover {
    background: #564DCA;
    border-color: #564DCA;
    color: #fff;
}

.hexagon {
    width: 90px;
    height: 50px;
    background-color: #141c26;
    background: #141c26;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    position: relative;
    z-index: 1;
    border-left: 3px solid #212a32;
    border-right: 3px solid #212a32;
    border-radius: 5px;
    transition: .3s ease-in-out;
}

.hexagon img {
    width: 60%;
    z-index: 999;
}

.hexagon:before,
.hexagon:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    transition: background-color 0.25s;
    border-left: 3px solid #212a32;
    border-right: 3px solid #212a32;
}

.hexagon:before {
    transform: rotate(120deg);
    border-left: 3px solid #212a32;
    border-right: 3px solid #212a32;

}

.hexagon:after {
    transform: rotate(60deg);
    border-left: 3px solid #212a32;
    border-right: 3px solid #212a32;
}

.hexagon:hover, .hexagon:hover:after, .hexagon:hover:before {
    border-left: 3px solid #ff9700;
    border-right: 3px solid #ff9700;
}

.hexagon-main {
    width: 100%;
    height: 100%;
    background-color: #141c26;
    background: #141c26;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    position: relative;
    z-index: 1;
    border-left: 8px solid #ff9700;
    border-right: 8px solid #ff9700;
    border-radius: 10px;
    transition: .3s ease-in-out;
}

.hexagon-main img {
    width: 60%;
    z-index: 999;
}

.hexagon-main:before,
.hexagon-main:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    transition: .6s ease-in-out;
    border-left: 8px solid #ff9700;
    border-right: 8px solid #ff9700;
    border-radius: 10px;
}

.hexagon-main:before {
    transform: rotate(120deg);
    border-left: 8px solid #ff9700;
    border-right: 8px solid #ff9700;

}

.hexagon-main:after {
    transform: rotate(60deg);
    border-left: 8px solid #ff9700;
    border-right: 8px solid #ff9700;
    transition: .8s ease-in-out;
}

.hexagon-main:hover, .hexagon-main:hover:after, .hexagon-main:hover:before {
    border-left: 8px solid #84c348;
    border-right: 8px solid #84c348;
    background: #0c1d32;
}

.order-list-item .content .yellow {
    color: #FF9700;
}

.order-list-item .content .green {
    color: #12D176;
}

.order-list-item .content .red {
    color: #FF1D45;
}

.order-list-item .content .purple {
    color: #574dca;
}

.order-list-item .content .blue {
    color: #ff9700;
}

.product-progress {
    margin: 0 0;
}

.product-progress .progress {
    height: 12px;
    background-color: #F2F2F2;
    border-radius: 0;
    overflow: inherit;
    margin-bottom: 25px;
    margin-top: 8px;
    border-radius: 30px;;
}

.product-progress .progress .progress-bar {
    background-color: #15d176;
    position: relative;
    overflow: inherit;
    border-radius: 30px;
}

.product-progress .progress .progress-bar::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #ffffff;
    z-index: 5;
    border: 2px solid #15d176;
    background-image: url('/docs/logo.png');
    background-repeat: no-repeat;
    background-size: contain;
    animation: shine 4s linear infinite, end 1s linear 1 7s;
    transition: linear 3s linear 3s;
}

.product-progress .title {
    font-size: 15px;
    text-transform: uppercase;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.product-progress .title span {
    margin-left: auto;
}

@keyframes shine {
    0% {
        background-position: 0 -50px;
    }
    50% {
        background-position: 0 0;
    }
    100% {
        background-position: 0 50px;
    }
}

@keyframes indexprogress {
    0%, 100% {
        box-shadow: 0 0 10px 0px #ff9700;
    }
    50% {
        box-shadow: 0 0 15px 5px #ff9700;
    }
}

@keyframes end {
    0%, 100% {
        box-shadow: 0 0 10px 0px orange;
    }
    50% {
        box-shadow: 0 0 15px 5px orange;
    }
}

.amount-section .amount-content {
    display: flex;
    align-items: center;
    margin-top: 40px;
    margin-bottom: 40px;

}

.amount-section .amount-content li {
    padding: 0 7px;
}

.amount-section .amount-content li a {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 2px solid #719ED6;
    font-size: 15px;
    color: #fff;
    font-weight: 400;
}
.amount-section .amount-content li a.contact-text-area {
    width: auto;
    border-radius: 0;
    border: none;
    color: #ffffff;
    margin-left: 15px;
}

.amount-section .amount-content li a:hover {
    background: #ff9700;
    border-color: #ff9700;
}
.amount-section .amount-content li a.contact-text-area:hover {
    background: transparent;
    border-color: transparent;
    color: #ff9700;
}

.amount-section form input, .amount-section form textarea {
    width: auto;
    border: none;
    background: rgb(118 118 130 / 10%);
    border-radius: 10px;
    font-weight: 400;
    font-size: 14px;
    padding: 13px 10px 13px 10px;
    color: #fff;
    height: auto;
}

.amount-section form input.error {
    border: 2px solid #FF1D45;
    color: #FF1D45 !important;
}

/*.error {*/
/*    color: #FF1D45 !important;*/
/*}*/


.amount-section form input::placeholder, .amount-section form textarea::placeholder {
    font-weight: 400;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.63);
}

.chart-widget {
    margin: 0 auto;
    width: 100%;
    background-color: transparent;
    border-radius: 5px;
    box-shadow: 0px 0px 1px 0px #06060d;
}

/*.chart-shadow {*/
/*    -webkit-filter: drop-shadow(0px 3px 3px rgba(0, 0, 0, 0.5));*/
/*    filter: drop-shadow(0px 10px 10px rgba(30, 202, 211, 0.5));*/
/*}*/

.chart-item li {
    padding: 12px 10px;
    font-size: 20px;
    color: #ffffff;
    line-height: 1.1;
}

.chart-item.chart-general {
    padding: 0px;
    margin: 0px;
}

.chart-item.chart-general li {
    padding: 12px 10px;
    font-size: 16px;
    color: #ffffff;
    line-height: 1.1;
}

.chart-item li span {
    margin-right: 10px;
}

.chart-item li.yellow span {
    color: #FF9700;
}

.chart-item li.green span {
    color: #12D176;
}

.chart-item li.red span {
    color: #FF1D45;
}

.chart-item li.purple span {
    color: #574dca;
}

.chart-item li.blue span {
    color: #ff9700;
}

.mCustomScrollBox {
    height: 100%;
}

.snowflake {
    color: #fff;
    font-size: 1em;
    font-family: Arial;
    text-shadow: 0 0 1px #000;
}

.snowflake img {
    max-width: 25px;
    opacity: 0.5;
}

@-webkit-keyframes snowflakes-fall {
    0% {
        top: -10%
    }
    100% {
        top: 100%
    }
}

@-webkit-keyframes snowflakes-shake {
    0% {
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
    50% {
        -webkit-transform: translateX(80px);
        transform: translateX(80px)
    }
    100% {
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
}

@keyframes snowflakes-fall {
    0% {
        top: 100%
    }
    100% {
        top: -10%
    }
}

@keyframes snowflakes-shake {
    0% {
        transform: translateX(0px)
    }
    50% {
        transform: translateX(80px)
    }
    100% {
        transform: translateX(0px)
    }
}

.snowflake {
    position: absolute;
    top: -10%;
    z-index: 9999;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: default;
    -webkit-animation-name: snowflakes-fall, snowflakes-shake;
    -webkit-animation-duration: 10s, 3s;
    -webkit-animation-timing-function: linear, ease-in-out;
    -webkit-animation-iteration-count: infinite, infinite;
    -webkit-animation-play-state: running, running;
    animation-name: snowflakes-fall, snowflakes-shake;
    animation-duration: 10s, 3s;
    animation-timing-function: linear, ease-in-out;
    animation-iteration-count: infinite, infinite;
    animation-play-state: running, running
}

.snowflake:nth-of-type(0) {
    left: 1%;
    -webkit-animation-delay: 0s, 0s;
    animation-delay: 0s, 0s
}

.snowflake:nth-of-type(1) {
    left: 10%;
    -webkit-animation-delay: 1s, 1s;
    animation-delay: 1s, 1s
}

.snowflake:nth-of-type(2) {
    left: 20%;
    -webkit-animation-delay: 6s, .5s;
    animation-delay: 6s, .5s
}

.snowflake:nth-of-type(3) {
    left: 30%;
    -webkit-animation-delay: 4s, 2s;
    animation-delay: 4s, 2s
}

.snowflake:nth-of-type(4) {
    left: 40%;
    -webkit-animation-delay: 2s, 2s;
    animation-delay: 2s, 2s
}

.snowflake:nth-of-type(5) {
    left: 50%;
    -webkit-animation-delay: 8s, 3s;
    animation-delay: 8s, 3s
}

.snowflake:nth-of-type(6) {
    left: 60%;
    -webkit-animation-delay: 6s, 2s;
    animation-delay: 6s, 2s
}

.snowflake:nth-of-type(7) {
    left: 70%;
    -webkit-animation-delay: 2.5s, 1s;
    animation-delay: 2.5s, 1s
}

.snowflake:nth-of-type(8) {
    left: 80%;
    -webkit-animation-delay: 1s, 0s;
    animation-delay: 1s, 0s
}

.snowflake:nth-of-type(9) {
    left: 90%;
    -webkit-animation-delay: 3s, 1.5s;
    animation-delay: 3s, 1.5s
}


/*input[type="date"]::-webkit-inner-spin-button,*/
/*input[type="date"]::-webkit-calendar-picker-indicator {*/
/*    display: none;*/
/*    -webkit-appearance: none;*/
/*}*/

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}

.type-radio {
    display: inline-flex;

    align-items: center;
    justify-content: space-evenly;
    padding-bottom: 20px;
}

.type-radio .option {
    background: transparent;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    margin: 0 10px;
    border-radius: 5px;
    cursor: pointer;
    padding: 15px 15px;
    border: 1px solid rgba(246, 246, 246, 0.06);
    transition: all 0.3s ease;
    font-size: 20px;
}

.type-radio .option .dot {
    height: 20px;
    width: 20px;
    background: #d9d9d9;
    border-radius: 50%;
    position: relative;
}

.type-radio .option .dot::before {
    position: absolute;
    content: "";
    top: 4px;
    left: 4px;
    width: 12px;
    height: 12px;
    background: #051527;
    border-radius: 50%;
    opacity: 0;
    transform: scale(1.5);
    transition: all 0.3s ease;
}

input[type="radio"] {
    display: none;
}

#option-1:checked:checked ~ .option-1,
#option-2:checked:checked ~ .option-2 {
    border-color: #051527;
    background: #051527;
}

#option-1:checked:checked ~ .option-1 .dot,
#option-2:checked:checked ~ .option-2 .dot {
    background: #fff;
}

#option-1:checked:checked ~ .option-1 .dot::before,
#option-2:checked:checked ~ .option-2 .dot::before {
    opacity: 1;
    transform: scale(1);
}

.wrapper .option span {
    font-size: 20px;
    color: #808080;
}

#option-1:checked:checked ~ .option-1 span,
#option-2:checked:checked ~ .option-2 span {
    color: #fff;
}

.success-icon ~ i {
    color: #12D176 !important;
}

.success-icon::before {
    border-color: #12D176 !important;
    color: #12D176 !important;
}

.cs-swiper_parallax_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: cover;
    z-index: -10;
}

.cs-swiper_parallax_bg::before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 1;
}

.cs-swiper_parallax_bg video {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.table thead th {
    border: none;
    padding: 20px 30px;
    font-size: 14px;
    color: #fff;
    border-bottom: 4px solid #2b3035;
}

.table tbody th, .table tbody td {
    border: none;
    padding: 20px 30px;
    border-bottom: 3px solid #2b3035;
    font-size: 14px;
}

.block-hidden {
    display: none;
}

.hidden-block {
    display: block;
}

.width-100 {
    width: 100%;
}

.hidden-flex {
    display: flex;
}

ul.slick-dots {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

ul.slick-dots li {
    margin-right: 5px;
    margin-left: 5px;
    padding: 10px;
}

ul.slick-dots li i {
    font-size: 20px;
}

ul.slick-dots li.slick-active i {
    color: #FF1D45;
    font-size: 26px;
}

ul.slick-dots li.slick-active:nth-child(1) i {
    color: #FF1D45;
}

ul.slick-dots li.slick-active:nth-child(2) i {
    color: #423FAA;
}

ul.slick-dots li.slick-active:nth-child(3) i {
    color: #00FFD7;
}

ul.slick-dots li.slick-active:nth-child(4) i {
    color: #F271FF;
}

.z-999 {
    z-index: 999;
}


footer {
    position: relative;
}

footer #can {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 1;
    z-index: -1;
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
}


.service-carousel .slick-slide {
    margin-right: 8px;
    margin-left: 8px;
}

.slick-navigation {
    position: relative;
    width: 100%;
    height: 50px; /* Yüksekliği isteğinize göre ayarlayabilirsiniz */
}

.slick-prev, .slick-next {
    cursor: pointer;
    font-size: 0;
    line-height: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 999;
    width: 40px;
    height: 40px;
    background: rgb(26, 34, 43);
    border: 4px solid rgb(43, 50, 57);
    border-radius: 50%;
    transition: .3s ease-in-out;
}

.slick-prev:hover, .slick-next:hover {
    border-color: #12D176;
}

.slick-prev {
    left: -40px; /* Sol tarafa dayalı */
}

.slick-next {
    right: -40px; /* Sağ tarafa dayalı */
}

.slick-next:before, .slick-prev:before {
    color: #fff;
    font-size: 20px;
    line-height: 1;
}

.slick-prev:before {
    content: "\f053";
    display: block;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    color: #ffffff;
}

.slick-next:before {
    content: "\f054";
    display: block;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    color: #ffffff;
}

@media screen and (max-width: 767px) {
    .hexagon-main {
        width: 40%;
        height: 40%;
    }

    .chart-content .nav-tabs .nav-item {
        padding: 10px 10px 10px 10px !important;
    }

    .chart-content .nav-tabs .nav-link {
        font-size: 15px;
    }

    .chart-content-inner .title {
        font-size: 30px !important;
    }

    .bt-roadmap-wrap {
        min-height: 450px;
    }

    .bt-roadmap-item:nth-child(even) {
        align-self: flex-end;
        flex-direction: column;
        margin-top: 0px;
    }

    .bt-roadmap-item:nth-child(even) .roadmap-content .dot {
        top: 0;
        bottom: auto;
    }

    .bt-roadmap-item:nth-child(even) .roadmap-title {
        margin: 0px 0 22px 0;
    }

    .col-md-4.product-content {
        margin-top: 70px;
    }


    .block-hidden {
        display: block;
    }

    .hidden-block {
        display: none;
    }

    .fs-sm-16 {
        font-size: 18px !important;
    }

    .fs-sm-14 {
        font-size: 14px !important;
    }

    .fs-sm-13 {
        font-size: 13px !important;
        line-height: 1.4;
    }

    .fs-sm-12 {
        font-size: 12px !important;
        line-height: 1.3;
    }

    .mailing-area-2 .section-title .sub-title,
    .mailing-area-3 .section-title .sub-title,
    .mailing-area-5 .section-title .sub-title,
    .mailing-area-6 .section-title .sub-title {
        font-size: 22px;
        margin-bottom: 0px;
    }

    .width-sm-75 {
        width: 75%;
    }

    .about-area-mailing .about-area-content p {
        font-size: 13px;
    }

    .mt-sm-30 {
        margin-top: 30px;
    }

    .chart-wrap.general-wrap {
        padding: 30px 10px 30px 10px;
    }

    .text-sm-right {
        text-align: right;
    }

    .sm-size-100 {
        background-size: 100% !important;
    }

    .sm-img-absolute {
        position: absolute;
        top: 40%;
        z-index: 1;
        opacity: 0.4;
        right: -30%;
    }

    .mailing-area-2 .about-area-content p,
    .mailing-area-3 .about-area-content p,
    .mailing-area-5 .about-area-content p {
        font-size: 13px !important;
    }

    .text-red {
        color: #FF1D45 !important;
    }

    .text-blue {
        color: #423FAA !important;
    }

    .text-green {
        color: #00FFD7 !important;
    }

    .text-purple {
        color: #F271FF !important;
    }

    .area-3-carousel {
        background-image: url(/docs/mailing/carousel-bg.png);
        background-size: cover;
        background-position-x: left;
        background-position-y: center;
        min-height: 750px;
    }

    .area-3-carousel.bg-right {
        background-position-x: right;
    }

    .area-3-carousel img {
        z-index: 999;
    }

    .service-seed {
        border: 1px solid #FF1D45;
        padding: 7px;
        padding-top: 20px;
        margin-top: -25px;
        z-index: 1;
    }

    .service-private {
        border: 1px solid #423FAA;
        padding: 7px;
        padding-top: 20px;
        margin-top: -30px;
        z-index: 1;
    }

    .service-public {
        border: 1px solid #00FFD7;
        padding: 7px;
        padding-top: 20px;
        margin-top: -96px;
        z-index: 1;
    }

    .service-listing {
        border: 1px solid #F271FF;
        padding: 7px;
        padding-top: 20px;
        margin-top: -50px;
        z-index: 1;
    }

    .hidden-flex {
        display: none;
    }

    .slick-prev {
        left: 0px; /* Sol tarafa dayalı */
    }

    .slick-next {
        right: 0px; /* Sağ tarafa dayalı */
    }
}

.cms-page-container, .cms-page-container * {
    color: #ffffff;
}
.form-grp.institutional-form .error{
    border:1px solid red;
    color: #ffffff;
}



