<?php

return [
    'order' => [
        'subject'           => 'Yeni Sipariş Onayı',
        'heading'           => 'Sipariş Onayı!',
        'dear'              => 'Sayın :customer_name',
        'dear-admin'        => 'Sayın :admin_name',
        'greeting'          => ':created_at tarihinde oluşturulan :order_id numaralı siparişiniz için teşekkür ederiz',
        'greeting-admin'    => ':created_at tarihinde oluşturulan :order_id numaralı sipariş',
        'summary'           => 'Sipariş Özeti',
        'shipping-address'  => 'Teslimat Adresi',
        'billing-address'   => 'Fatura Adresi',
        'comment-content'   => 'Yorum',
        'contact'           => 'İletişim',
        'shipping'          => 'Kargo Yöntemi',
        'payment'           => 'Ödeme Yöntemi',
        'product-name'      => 'Ürün Adı',
        'price'             => 'Fiyat',
        'quantity'          => 'Adet',
        'subtotal'          => 'Ara Toplam',
        'shipping-handling' => 'Kargo ve İşlem Ücreti',
        'tax'               => 'Vergi',
        'discount'          => 'İndirim',
        'grand-total'       => 'Genel Toplam',
        'final-summary'     => 'Mağazamıza gösterdiğiniz ilgi için teşekkür ederiz. Siparişiniz kargoya verildiğinde takip numarasını ileteceğiz.',
        'follow-us'         => 'Bizi Takip Edin',
        'help'              => 'Herhangi bir yardıma ihtiyacınız olursa lütfen bizimle :support_email adresinden iletişime geçin.',
        'thanks'            => 'Teşekkürler!',

        'address' => [
            'company_name' => 'Firma Adı',
            'first_name'   => 'Ad',
            'last_name'    => 'Soyad',
            'email'        => 'E-Mail',
            'address1'     => 'Adres',
            'address2'     => 'Adres 2',
            'city'         => 'İlçe',
            'state'        => 'Şehir',
            'country'      => 'Ülke',
            'postcode'     => 'Posta Kodu',
            'phone'        => 'Telefon',
            'vat_id'       => 'Vergi Numarası',
        ],

        'comment' => [
            'subject'       => 'Sipariş #:order_id için yeni yorum eklendi',
            'dear'          => 'Sayın :customer_name',
            'final-summary' => 'Mağazamıza gösterdiğiniz ilgi için teşekkür ederiz',
            'help'          => 'Herhangi bir yardıma ihtiyacınız olursa lütfen bizimle :support_email adresinden iletişime geçin.',
            'thanks'        => 'Teşekkürler!',
        ],

        'cancel' => [
            'subject'           => 'Sipariş İptal Onayı',
            'heading'           => 'Sipariş İptal Edildi',
            'dear'              => 'Sayın :customer_name',
            'greeting'          => ':created_at tarihinde oluşturulan :order_id numaralı siparişiniz iptal edilmiştir.',
            'summary'           => 'Sipariş Özeti',
            'shipping-address'  => 'Teslimat Adresi',
            'billing-address'   => 'Fatura Adresi',
            'contact'           => 'İletişim',
            'shipping'          => 'Kargo Yöntemi',
            'payment'           => 'Ödeme Yöntemi',
            'subtotal'          => 'Ara Toplam',
            'shipping-handling' => 'Kargo ve İşlem Ücreti',
            'tax'               => 'Vergi',
            'discount'          => 'İndirim',
            'grand-total'       => 'Genel Toplam',
            'final-summary'     => 'Mağazamıza gösterdiğiniz ilgi için teşekkür ederiz',
            'help'              => 'Herhangi bir yardıma ihtiyacınız olursa lütfen bizimle :support_email adresinden iletişime geçin.',
            'thanks'            => 'Teşekkürler!',
        ],
    ],

    'invoice' => [
        'heading'  => 'Sipariş #:order_id için faturanız #:invoice_id',
        'subject'  => 'Sipariş #:order_id için Fatura',
        'summary'  => 'Fatura Özeti',
        'reminder' => [
            'subject'                                          => 'Fatura Hatırlatması',
            'your-invoice-is-overdue'                          => ':invoice numaralı faturanız :time süredir ödenmemiştir.',
            'please-make-your-payment-as-soon-as-possible'     => 'Lütfen ödemenizi en kısa sürede tamamlayın.',
            'if-you-ve-already-paid-just-disregard-this-email' => 'Eğer zaten ödeme yaptıysanız bu e-postayı dikkate almayın.',
        ],
    ],

    'shipment' => [
        'heading'           => 'Sipariş #:order_id için #:shipment_id numaralı kargo oluşturuldu',
        'inventory-heading' => 'Sipariş #:order_id için yeni kargo #:shipment_id oluşturuldu',
        'subject'           => 'Sipariş #:order_id için Kargo Bilgisi',
        'inventory-subject' => 'Sipariş #:order_id için yeni kargo oluşturuldu',
        'summary'           => 'Kargo Özeti',
        'carrier'           => 'Kargo Firması',
        'tracking-number'   => 'Takip Numarası',
        'greeting'          => ':created_at tarihinde verilen :order_id numaralı siparişinizin kargosu oluşturuldu',
    ],

    'refund' => [
        'heading'           => 'Sipariş #:order_id için İade #:refund_id',
        'subject'           => 'Sipariş #:order_id için İade',
        'summary'           => 'İade Özeti',
        'adjustment-refund' => 'İade Tutarı Düzeltmesi',
        'adjustment-fee'    => 'İşlem Ücreti Düzeltmesi',
    ],

    'forget-password' => [
        'subject'        => 'Şifre Sıfırlama Talebi',
        'dear'           => 'Sayın :name',
        'info'           => 'Bu e-postayı, hesabınız için şifre sıfırlama talebinde bulunduğunuz için alıyorsunuz.',
        'reset-password' => 'Şifreyi Sıfırla',
        'final-summary'  => 'Eğer şifre sıfırlama talebinde bulunmadıysanız başka bir işlem yapmanıza gerek yoktur.',
        'thanks'         => 'Teşekkürler!',
    ],

    'update-password' => [
        'subject' => 'Şifre Güncellendi',
        'dear'    => 'Sayın :name',
        'info'    => 'Bu e-postayı şifrenizi güncellediğiniz için alıyorsunuz.',
        'thanks'  => 'Teşekkürler!',
    ],

    'customer' => [
        'auth' => [
            'register' => [
                'subject'  => 'Hesap Oluşturuldu Onayı',
                'greeting' => 'Hesap Oluşturuldu Onayı',
                'dear'     => 'Merhaba :customer_name, <br><br> Hesabınız başarıyla oluşturuldu! Şimdi :company_name ailesine resmen katıldınız. İşte hesap detaylarınız: <br><br> Kayıtlı E-Posta: :email<br> Hesabınıza erişim sağlamak için lütfen aşağıdaki bağlantıyı kullanarak e-posta adresinizi doğrulayın:',
                'summary'  => 'Bağlantıya tıklamak istemiyorsanız, aşağıdaki onay kodunu kullanarak hesabınızı doğrulayabilirsiniz:<br><br>Onay Kodu: :code<br><br> Unutmayın, hesabınızın güvenliği için şifrenizi kimseyle paylaşmamanız önemlidir. Eğer bu hesabı siz oluşturmadıysanız veya herhangi bir sorunla karşılaşırsanız, lütfen bize bildirin. <br><br> İyi günler dileriz ve :company_name\'da keyifli zaman geçirmenizi bekleriz!',
                'verify'   => 'E-Posta Adresini Doğrula',
                'thanks'   => 'Saygılarımızla,<br><br>Miligold<br>Müşteri Destek Ekibi',
            ],

            'forgot_password' => [
                'subject'        => 'Şifre Değişimi İşlemi',
                'greeting'       => 'Şifre Değişimi İşlemi',
                'dear'           => 'Merhaba :name,<br><br>Hesabınızın güvenliği bizim için önemlidir. Şifrenizi değiştirmek üzere bir işlem başlatılmıştır. Eğer bu değişikliği siz yapmadıysanız, lütfen derhal müşteri destek ekibimizle iletişime geçin. <br><br> Eğer siz bu işlemi başlattıysanız, lütfen aşağıdaki talimatları takip ederek şifrenizi değiştirin:',
                'summary'        => 'Bu bağlantıyı tıkladığınızda sizi şifre değiştirme sayfamıza yönlendirecektir. <br><br> Yeni Şifrenizi Belirleyin <br> Açılan sayfada yeni şifrenizi belirleyin ve güçlü bir şifre kullanmaya özen gösterin. <br><br> Şifreyi Onaylayın <br> Belirlediğiniz yeni şifreyi tekrar girerek onaylayın. <br><br> Unutmayın, şifreler kişisel bilgilerinizin güvenliğini sağlamak için düzenli olarak değiştirilmelidir. Eğer herhangi bir sorunla karşılaşırsanız, lütfen bize bildirin.',
                'reset-password' => 'Şifreyi Değiştir',
                'thanks'         => 'İyi günler dileriz.<br><br>Miligold<br>Müşteri Destek Ekibi',
            ],

            'updated_password' => [
                'subject'  => 'Şifre Değişimi',
                'greeting' => 'Şifre Değişimi',
                'dear'     => 'Merhaba :name, <br><br> Hesabınızın şifresi başarıyla değiştirildi.',
                'summary'  => 'Eğer bu değişikliği siz yapmadıysanız, lütfen derhal müşteri destek ekibimizle iletişime geçin.',
                'thanks'   => 'İyi günler dileriz.<br><br>Miligold<br>Müşteri Destek Ekibi',
            ],

            'verification' => [
                'email' => [
                    'subject'           => 'Hesap Doğrulama',
                    'greeting'          => 'Hesap Doğrulama',
                    'dear'              => 'Merhaba :customer_name,<br><br>Hesabınızı oluşturduğunuz için teşekkür ederiz. Hesabınızın güvenliği için lütfen aşağıdaki bağlantıya tıklayarak e-posta adresinizi doğrulayın:',
                    'alternate_summary' => 'Bağlantıya tıklayamıyorsanız, aşağıdaki doğrulama kodunu kullanarak hesabınızı doğrulayabilirsiniz:',
                    'verify'            => 'E-Postayı Doğrula',
                    'verification_code' => 'Doğrulama Kodu: :code',
                    'summary'           => 'Not: Hesabınızı doğrulamadan önce, bu e-postayı hesabınızı oluştururken kullandığınız e-posta adresine aldığınızdan emin olun. Eğer bu e-postayı siz talep etmediyseniz, lütfen dikkate almayın.',
                    'thanks'            => 'Saygılarımızla,<br><br>Miligold<br>Müşteri Destek Ekibi',
                ],
            ],
            'verified' => [
                'email' => [
                    'subject'  => 'E-Posta Doğrulandı',
                    'greeting' => 'E-Posta Doğrulandı',
                    'dear'     => 'Merhaba :customer_name,<br><br>E-posta adresiniz başarıyla doğrulandı.',
                    'summary'  => 'Not: Bu e-postayı siz talep etmediyseniz, lütfen hemen müşteri destek ekibimizle iletişime geçin.',
                    'thanks'   => 'Saygılarımızla,<br><br>Miligold<br>Müşteri Destek Ekibi',
                ],
            ],
        ],
        'invitation' => [
            'subject'  => 'Miligold\'a Hoş Geldiniz!',
            'greeting' => 'Merhaba, Miligold\'a hoş geldiniz!',
            'dear'     => 'Müşteri desteğimize ulaştığınız için teşekkür ederiz. <br><br> Sisteme kayıt olmanız için aşağıdaki bağlantıya tıklayarak kayıt işlemini tamamlayabilirsiniz:',
            'summary'  => 'Kayıt işlemi tamamlandıktan sonra hesabınızla ilgili detaylı bilgilere erişebilecek ve sunduğumuz hizmetlerden yararlanabileceksiniz.',
            'accept'   => 'Miligold\'a Katıl',
            'thanks'   => 'İyi günler diler, bize katıldığınız için teşekkür ederiz.<br><br> Saygılarımla,<br> Miligold Müşteri Hizmetleri',
        ],
        'account' => [
            'order' => [
                'index' => [
                    'page-title'      => 'Siparişler',
                    'title'           => 'Siparişler',
                    'order_id'        => 'Sipariş No',
                    'date'            => 'Tarih',
                    'status'          => 'Durum',
                    'total'           => 'Toplam',
                    'order_number'    => 'Sipariş No',
                    'processing'      => 'İşleniyor',
                    'completed'       => 'Tamamlandı',
                    'canceled'        => 'İptal Edildi',
                    'closed'          => 'Kapalı',
                    'pending'         => 'Bekliyor',
                    'pending-payment' => 'Ödeme Bekliyor',
                    'fraud'           => 'Geçersiz',
                ],

                'view' => [
                    'page-tile'           => 'Sipariş #:order_id',
                    'info'                => 'Bilgi',
                    'placed-on'           => 'Sipariş Tarihi',
                    'products-ordered'    => 'Sipariş Edilen Ürünler',
                    'invoices'            => 'Faturalar',
                    'shipments'           => 'Teslimatlar',
                    'SKU'                 => 'Barkod',
                    'product-name'        => 'Ürün Adı',
                    'qty'                 => 'Miktar',
                    'item-status'         => 'Ürün Durumu',
                    'item-ordered'        => 'Sipariş Verildi (:qty_ordered)',
                    'item-invoice'        => 'Fatura Oluşturuldu (:qty_invoiced)',
                    'item-shipped'        => 'Kargoya Verildi (:qty_shipped)',
                    'item-canceled'       => 'İptal Edildi (:qty_canceled)',
                    'item-refunded'       => 'İade Edildi (:qty_refunded)',
                    'price'               => 'Fiyat',
                    'total'               => 'Toplam',
                    'subtotal'            => 'Ara Toplam',
                    'shipping-handling'   => 'Teslimat & Dağıtım',
                    'tax'                 => 'Vergi',
                    'discount'            => 'İndirim',
                    'tax-percent'         => 'Vergi Yüzdesi',
                    'tax-amount'          => 'Vergi Miktarı',
                    'discount-amount'     => 'İndirim Miktarı',
                    'grand-total'         => 'Genel Toplam',
                    'total-paid'          => 'Toplam Ödenen',
                    'total-refunded'      => 'Toplam İade',
                    'total-due'           => 'Toplam Kalan',
                    'shipping-address'    => 'Teslimat Adresi',
                    'billing-address'     => 'Fatura Adresi',
                    'shipping-method'     => 'Teslimat Şekli',
                    'payment-method'      => 'Ödeme Şekli',
                    'individual-invoice'  => 'Fatura #:invoice_id',
                    'individual-shipment' => 'Teslimat #:shipment_id',
                    'print'               => 'Yazdır',
                    'invoice-id'          => 'Fatura No',
                    'order-id'            => 'Sipariş No',
                    'order-date'          => 'Sipariş Tarihi',
                    'invoice-date'        => 'Fatura tarihi',
                    'payment-terms'       => 'Ödeme şartları',
                    'bill-to'             => 'Fatura Edilen',
                    'ship-to'             => 'Teslim Edilen',
                    'contact'             => 'İletişim',
                    'refunds'             => 'İadeler',
                    'individual-refund'   => 'İade #:refund_id',
                    'adjustment-refund'   => 'İade Düzenlemesi',
                    'adjustment-fee'      => 'Düzenleme Bedeli',
                    'cancel-btn-title'    => 'İptal',
                    'tracking-number'     => 'Takip No',
                    'cancel-confirm-msg'  => 'Bu siparişi silmek istediğinizden emin misiniz?',
                ],
            ],
        ],
    ],

    'sales' => [
        'order' => [
            'payment-excessive-retry' => [
                'subject'         => 'Ödeme Doğrulama Sorunu - Sipariş Akışı Uyarısı [:order_id]',
                'greeting'        => 'Sipariş Akışı Bildirimi: Ödeme Doğrulama Sorunu',
                'summary'         => 'Sayın :name, <br><br> Ödemenizin doğrulanması sırasında birden fazla tekrar denemesi tespit ettik. Bu durum ödeme sisteminde bir sorun olabileceğini göstermektedir. Lütfen sipariş ve ödeme durumunuzu kontrol edin. <br><br> Ekibimiz sorunu çözmek için çalışıyor. Sorun giderildiğinde size bilgi verilecektir.<br><br>Anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımızla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'deposit-excessive-retry' => [
                'subject'         => 'Para Transferi Sorunu - Sipariş Akışı Uyarısı [:order_id]',
                'greeting'        => 'Sipariş Akışı Bildirimi: Para Transferi Sorunu',
                'summary'         => 'Sayın :name, <br><br> Cüzdanınıza yapılan para transferi sırasında birden fazla tekrar denemesi tespit ettik. Bu durum transfer işleminde bir sorun olduğunu gösterebilir. Lütfen sipariş ve cüzdan bakiyenizi kontrol edin. <br><br> Ekibimiz durumu incelemektedir. Sorun çözüldüğünde size bilgi verilecektir.<br><br>İlginiz için teşekkür ederiz.',
                'dear'            => 'Saygılarımızla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'admin-payment-notification' => [
                'subject'         => 'Hata Bildirimi - Sipariş Akışı Tamamlandı [:order_id]',
                'greeting'        => 'Hata Bildirimi - Sipariş Akışı Tamamlandı',
                'summary'         => 'Sayın :name,<br><br>Önemli bilgi: Sipariş akışında bir hata oluştu. Arka planda çalışan servisler, ödemeyi alsalar bile doğrulama durumunu hemen göstermeyebilir. Ayrıca coin’lerin cüzdana aktarımı sırasında servisten kaynaklı bir hata da oluşmuş olabilir.<br><br>Süreç şu anda tamamlanmış gibi görünse de detaylı bir kontrol önerilir. Hata düzeltildiğinde sizinle paylaşılacaktır.<br><br>İyi çalışmalar dileriz.',
                'dear'            => 'Saygılarımızla,<br>Terramirum Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'admin-order-notification' => [
                'subject'         => 'Yeni Sipariş - Sipariş Akışı Başlatıldı [:order_id]',
                'greeting'        => 'Yeni Sipariş - Sipariş Akışı Başlatıldı',
                'summary'         => 'Sayın :name, <br> <br> Yeni bir sipariş alındı. Sipariş akışı başlatıldı. <br> <br> İyi çalışmalar dileriz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'completed' => [
                'subject'         => 'Sipariş Tamamlandı - Coin Transferi Gerçekleştirildi [:order_id]',
                'greeting'        => 'Sipariş Tamamlandı - Coin Transferi Gerçekleştirildi',
                'summary'         => 'Sayın :customer_name, <br> <br> Sevindirici bir haber! Siparişiniz tamamlandı. Ödemeniz zaten onaylanmıştı ve coin transferi başarıyla cüzdanınıza yapıldı. Artık işleminiz tamamen tamamlandı. <br> <br> Keyifli kullanımlar dileriz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'processing' => [
                'subject'         => 'Sipariş İşleniyor - Ödemeniz Doğrulandı [:order_id]',
                'greeting'        => 'Sipariş İşleniyor - Ödemeniz Doğrulandı',
                'summary'         => 'Sayın :customer_name, <br> <br> Siparişiniz başarıyla alındı ve ödemeniz doğrulandı. Şu anda satın aldığınız coinlerin transfer işlemi gerçekleştiriliyor. Transfer tamamlandığında size bilgi vereceğiz. <br> <br> Anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'pending' => [
                'subject'         => 'Siparişiniz Bekleme Aşamasında [:order_id]',
                'greeting'        => 'Siparişiniz Bekleme Aşamasında',
                'summary'         => 'Sayın :customer_name, <br> <br> Siparişiniz alınmış olup, ödeme henüz doğrulanmamıştır. İşleminiz tamamlanana kadar beklemeniz gerekebilir. Ödemenin doğrulanması sürecinde size bilgi sağlayacağız. <br> <br> Teşekkür eder, anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
            ],
            'token-error' => [
                'subject'        => 'Error Reporting - Token Transfer Error [:order_id]',
                'greeting'       => 'Error Reporting - Token Transfer Error',
                'summary'        => 'Dear :name, <br> <br> Important information: An error occurred in the token transfer process. The error will be shared with you when it is corrected. <br> <br> We wish you good work.',
                'dear'           => 'Kind regards,<br>Miligold Customer Services',
                'order-id'       => 'Order ID',
                'wallet-address' => 'Wallet Address',
                'error-message'  => 'Error Message',
                'amount'         => 'Amount',
            ],
            'token-completed' => [
                'subject'         => 'Token Transfer Completed [:order_id]',
                'greeting'        => 'Token Transfer Completed',
                'summary'         => 'Dear :name, <br> <br> Good news! The token transfer process has been successfully completed. Your transaction is now completely completed. <br> <br> We wish you pleasant use.',
                'dear'            => 'Kind regards,<br>Miligold Customer Services',
                'summary-invoice' => 'Invoice Summary',
            ],
            'new' => [
                'customer' => [
                    'subject'         => 'Yeni Sipariş - Sipariş İşleme Başladı [:order_id]',
                    'greeting'        => 'Yeni Sipariş - Sipariş İşleme Başladı',
                    'summary'         => 'Sayın :customer_name,<br><br>Yeni bir sipariş alındı. Sipariş işleme alınmıştır.<br><br>İyi çalışmalar dileriz.',
                    'dear'            => 'Saygılarımızla,<br>Miligold Müşteri Hizmetleri',
                    'summary-invoice' => 'Fatura Özeti',
                ],
                'admin' => [
                    'subject'         => 'Yeni Sipariş Uyarısı [:order_id]',
                    'greeting'        => 'Yeni Bir Sipariş Alındı',
                    'summary'         => 'Sayın :name,<br><br>Yeni bir müşteri siparişi alındı ve şu anda işleniyor.<br>Lütfen sipariş detaylarını inceleyin ve gerekli adımların atıldığından emin olun.',
                    'dear'            => 'Saygılarımızla,<br>Miligold Sistem Bildirimi',
                    'summary-invoice' => 'Sipariş Özeti',
                ],
            ],
            'cancel' => [
                'subject'         => 'Sipariş İptal Edildi [:order_id]',
                'greeting'        => 'Sipariş İptal Edildi',
                'summary'         => 'Sayın :customer_name, <br> <br> Siparişiniz iptal edildi. Eğer herhangi bir sorunla karşılaşırsanız veya yardıma ihtiyacınız olursa, lütfen bizimle iletişime geçin. <br> <br> Anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
                'payment-method'  => 'Ödeme Şekli',
                'billing-address' => 'Fatura Adresi',
            ],
            'comment' => [
                'subject'         => 'Yeni Yorum - Sipariş [:order_id]',
                'greeting'        => 'Yeni Yorum',
                'summary'         => 'Sayın :customer_name, <br> <br> Siparişinize yeni bir yorum eklendi. <br> <br> Anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımızla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
                'billing-address' => 'Fatura Adresi',
                'payment-method'  => 'Ödeme Yöntemi',
            ],
            'invoice' => [
                'subject'         => 'Yeni Fatura - Sipariş [:order_id]',
                'greeting'        => 'Yeni Fatura',
                'summary'         => 'Sayın :customer_name, <br> <br> Siparişiniz için yeni bir fatura oluşturuldu. <br> <br> Anlayışınız için teşekkür ederiz.',
                'dear'            => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
                'summary-invoice' => 'Fatura Özeti',
                'billing-address' => 'Fatura Adresi',
                'payment-method'  => 'Ödeme Yöntemi',
                'view-invoice'    => 'Faturayı Görüntüle',
            ],
        ],
        'deposit' => [
            'completed' => [
                'subject'  => 'Transfer Tamamlandı - Ödeme Onaylandı [:transaction_id]',
                'greeting' => 'Transfer Tamamlandı!',
                'summary'  => 'Sayın :customer_name, <br><br> Yapmış olduğunuz yatırım işlemi başarıyla tamamlandı ve ödeme sistemimiz tarafından onaylandı.',
                'dear'     => 'Saygılarımla,<br>Miligold Müşteri Hizmetleri',
            ],
        ],
    ],
];
