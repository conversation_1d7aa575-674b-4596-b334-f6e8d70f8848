<?php

return [
    'order' => [
        'subject'           => 'Neue Bestellbestätigung',
        'heading'           => 'Bestellbestätigung!',
        'dear'              => 'Sehr geehrte/r :customer_name',
        'dear-admin'        => 'Sehr geehrte/r :admin_name',
        'greeting'          => 'Vielen Dank für Ihre Bestellung :order_id, aufgegeben am :created_at',
        'greeting-admin'    => 'Bestellnummer :order_id, aufgegeben am :created_at',
        'summary'           => 'Zusammenfassung der Bestellung',
        'shipping-address'  => 'Lieferadresse',
        'billing-address'   => 'Rechnungsadresse',
        'comment-content'   => 'Kommentar',
        'contact'           => 'Kontakt',
        'shipping'          => 'Versandmethode',
        'payment'           => 'Zahlungsmethode',
        'product-name'      => 'Produktname',
        'price'             => 'Preis',
        'quantity'          => 'Menge',
        'subtotal'          => 'Zwischensumme',
        'shipping-handling' => 'Versand & Bearbeitung',
        'tax'               => 'Steuern',
        'discount'          => 'Rabatt',
        'grand-total'       => 'Gesamtsumme',
        'final-summary'     => 'Vielen Dank für Ihr Interesse an unserem Geschäft. Wir senden Ihnen die Sendungsverfolgungsnummer, sobald sie versendet wurde.',
        'follow-us'         => 'Folgen Sie uns',
        'help'              => 'Wenn Sie Hilfe benötigen, kontaktieren Sie uns bitte unter :support_email',
        'thanks'            => 'Vielen Dank!',

        'address' => [
            'company_name' => 'Firmenname',
            'first_name'   => 'Vorname',
            'last_name'    => 'Nachname',
            'email'        => 'E-Mail',
            'address1'     => 'Adresse',
            'address2'     => 'Adresse 2',
            'city'         => 'Stadt',
            'state'        => 'Bundesland',
            'country'      => 'Land',
            'postcode'     => 'Postleitzahl',
            'phone'        => 'Telefon',
            'vat_id'       => 'USt-IdNr.',
        ],

        'comment' => [
            'subject'       => 'Neuer Kommentar zu Ihrer Bestellung #:order_id hinzugefügt',
            'dear'          => 'Sehr geehrte/r :customer_name',
            'final-summary' => 'Vielen Dank für Ihr Interesse an unserem Geschäft',
            'help'          => 'Wenn Sie Hilfe benötigen, kontaktieren Sie uns bitte unter :support_email',
            'thanks'        => 'Vielen Dank!',
        ],

        'cancel' => [
            'subject'           => 'Bestellstornierungsbestätigung',
            'heading'           => 'Bestellung storniert',
            'dear'              => 'Sehr geehrte/r :customer_name',
            'greeting'          => 'Ihre Bestellung mit Bestellnummer :order_id, aufgegeben am :created_at, wurde storniert',
            'summary'           => 'Zusammenfassung der Bestellung',
            'shipping-address'  => 'Lieferadresse',
            'billing-address'   => 'Rechnungsadresse',
            'contact'           => 'Kontakt',
            'shipping'          => 'Versandmethode',
            'payment'           => 'Zahlungsmethode',
            'subtotal'          => 'Zwischensumme',
            'shipping-handling' => 'Versand & Bearbeitung',
            'tax'               => 'Steuern',
            'discount'          => 'Rabatt',
            'grand-total'       => 'Gesamtsumme',
            'final-summary'     => 'Vielen Dank für Ihr Interesse an unserem Geschäft',
            'help'              => 'Wenn Sie Hilfe benötigen, kontaktieren Sie uns bitte unter :support_email',
            'thanks'            => 'Vielen Dank!',
        ],
    ],

    'invoice' => [
        'heading'  => 'Ihre Rechnung #:invoice_id für Bestellung #:order_id',
        'subject'  => 'Rechnung für Ihre Bestellung #:order_id',
        'summary'  => 'Zusammenfassung der Rechnung',
        'reminder' => [
            'subject'                                          => 'Rechnungserinnerung',
            'your-invoice-is-overdue'                          => 'Ihre Rechnung :invoice ist um :time überfällig.',
            'please-make-your-payment-as-soon-as-possible'     => 'Bitte leisten Sie Ihre Zahlung so schnell wie möglich.',
            'if-you-ve-already-paid-just-disregard-this-email' => 'Wenn Sie bereits gezahlt haben, ignorieren Sie bitte diese E-Mail.',
        ],
    ],

    'shipment' => [
        'heading'           => 'Sendung #:shipment_id wurde für Bestellung #:order_id erstellt',
        'inventory-heading' => 'Für Bestellung #:order_id wurde eine neue Sendung #:shipment_id erstellt',
        'subject'           => 'Sendung für Ihre Bestellung #:order_id',
        'inventory-subject' => 'Für Bestellung #:order_id wurde eine neue Sendung erstellt',
        'summary'           => 'Zusammenfassung der Sendung',
        'carrier'           => 'Versandunternehmen',
        'tracking-number'   => 'Sendungsverfolgungsnummer',
        'greeting'          => 'Am :created_at wurde eine Bestellung :order_id aufgegeben',
    ],

    'refund' => [
        'heading'           => 'Ihre Rückerstattung #:refund_id für Bestellung #:order_id',
        'subject'           => 'Rückerstattung für Ihre Bestellung #:order_id',
        'summary'           => 'Zusammenfassung der Rückerstattung',
        'adjustment-refund' => 'Anpassungsrückerstattung',
        'adjustment-fee'    => 'Anpassungsgebühr',
    ],

    'forget-password' => [
        'subject'        => 'Kundenpasswort zurücksetzen',
        'dear'           => 'Sehr geehrte/r :name',
        'info'           => 'Sie erhalten diese E-Mail, weil wir eine Anfrage zum Zurücksetzen des Passworts für Ihr Konto erhalten haben',
        'reset-password' => 'Passwort zurücksetzen',
        'final-summary'  => 'Wenn Sie kein Passwort zurücksetzen angefordert haben, ist keine weitere Aktion erforderlich',
        'thanks'         => 'Vielen Dank!',
    ],

    'update-password' => [
        'subject' => 'Passwort aktualisiert',
        'dear'    => 'Sehr geehrte/r :name',
        'info'    => 'Sie erhalten diese E-Mail, weil Sie Ihr Passwort aktualisiert haben.',
        'thanks'  => 'Vielen Dank!',
    ],

    'customer' => [
        'auth' => [
            'register' => [
                'subject'  => 'Konto erstellt Bestätigung',
                'greeting' => 'Konto erstellt Bestätigung',
                'dear'     => 'Hallo :customer_name,<br><br>Ihr Konto wurde erfolgreich erstellt! Sie sind jetzt offiziell Teil der :company_name-Familie. Hier sind Ihre Kontodaten:<br><br>Registrierte E-Mail: :email<br>Um auf Ihr Konto zuzugreifen, bestätigen Sie bitte Ihre E-Mail-Adresse über den unten stehenden Link:',
                'summary'  => 'Wenn Sie nicht auf den Link klicken möchten, können Sie Ihr Konto mit dem unten stehenden Bestätigungscode verifizieren:<br><br>Bestätigungscode: :code<br><br>Denken Sie daran, aus Sicherheitsgründen ist es wichtig, Ihr Passwort niemandem mitzuteilen. Wenn Sie dieses Konto nicht erstellt haben oder auf Probleme stoßen, lassen Sie es uns bitte wissen.<br><br>Haben Sie einen schönen Tag und genießen Sie Ihre Zeit bei :company_name!',
                'verify'   => 'E-Mail bestätigen',
                'thanks'   => 'Mit freundlichen Grüßen,<br><br>Miligold<br>Kundensupport-Team',
            ],

            'forgot_password' => [
                'subject'        => 'Passwortänderungsprozess',
                'greeting'       => 'Passwortänderungsprozess',
                'dear'           => 'Hallo :name,<br><br>Die Sicherheit Ihres Kontos ist uns wichtig. Ein Prozess zur Änderung Ihres Passworts wurde eingeleitet. Wenn Sie diese Änderung nicht initiiert haben, kontaktieren Sie bitte sofort unser Kundensupport-Team.<br><br>Wenn Sie diesen Prozess gestartet haben, befolgen Sie bitte die unten stehenden Anweisungen, um Ihr Passwort zu ändern:',
                'summary'        => 'Durch Klicken auf diesen Link gelangen Sie zu unserer Passwortänderungsseite.<br><br>Setzen Sie Ihr neues Passwort fest<br>Legen Sie Ihr neues Passwort auf der geöffneten Seite fest und achten Sie darauf, ein starkes Passwort zu verwenden.<br><br>Passwort bestätigen<br>Bestätigen Sie, indem Sie das neue Passwort erneut eingeben, das Sie festgelegt haben.<br><br>Denken Sie daran, Passwörter sollten regelmäßig geändert werden, um die Sicherheit Ihrer persönlichen Informationen zu gewährleisten. Wenn Sie auf Probleme stoßen, lassen Sie es uns bitte wissen.',
                'reset-password' => 'Passwort ändern',
                'thanks'         => 'Wir wünschen Ihnen einen schönen Tag.<br><br>Miligold<br>Kundensupport-Team',
            ],

            'updated_password' => [
                'subject'  => 'Ihr Passwort wurde geändert',
                'greeting' => 'Ihr Passwort wurde geändert',
                'dear'     => 'Hallo, :name, <br><br> Die Sicherheit Ihres Kontos ist uns wichtig. Ihr Passwort wurde geändert.',
                'summary'  => 'Wenn Sie diese Änderung nicht initiiert haben, kontaktieren Sie bitte sofort unser Kundensupport-Team.',
                'thanks'   => 'Wir wünschen Ihnen einen schönen Tag.<br><br>Miligold<br>Kundensupport-Team',
            ],
            'verification' => [
                'email' => [
                    'subject'           => 'Konto Verifizierung',
                    'greeting'          => 'Konto Verifizierung',
                    'dear'              => 'Hallo, :customer_name, <br><br> danke, dass Sie ein Konto erstellt haben. Zum Schutz Ihres Kontos bestätigen Sie bitte Ihre E-Mail-Adresse, indem Sie auf den untenstehenden Link klicken:',
                    'alternate_summary' => 'Wenn Sie nicht auf den Link klicken können, können Sie Ihr Konto mit dem untenstehenden Bestätigungscode verifizieren:',
                    'verify'            => 'E-Mail verifizieren',
                    'verification_code' => 'Bestätigungscode: :code',
                    'summary'           => 'Hinweis: Vergewissern Sie sich, dass Sie diese E-Mail an die Adresse erhalten haben, mit der Sie Ihr Konto erstellt haben. Wenn Sie diese E-Mail nicht angefordert haben, ignorieren Sie bitte diese Nachricht.',
                    'thanks'            => 'Mit freundlichen Grüßen,<br><br>Miligold<br>Kundensupport-Team',
                ],
            ],
            'verified' => [
                'email' => [
                    'subject'  => 'E-Mail verifiziert',
                    'greeting' => 'E-Mail verifiziert',
                    'dear'     => 'Hallo, :customer_name, <br><br> Ihre E-Mail wurde erfolgreich verifiziert.',
                    'summary'  => 'Hinweis: Wenn Sie diese E-Mail nicht angefordert haben, wenden Sie sich bitte umgehend an unser Kundensupport-Team.',
                    'thanks'   => 'Mit freundlichen Grüßen,<br><br>Miligold<br>Kundensupport-Team',
                ],
            ],
        ],
        'invitation' => [
            'subject'  => 'Willkommen bei Miligold!',
            'greeting' => 'Hallo, Willkommen bei Miligold!',
            'dear'     => 'Vielen Dank, dass Sie sich an unseren Kundensupport gewandt haben. <br><br> Um sich im System zu registrieren, können Sie den Registrierungsprozess abschließen, indem Sie auf den unten stehenden Link klicken:',
            'summary'  => 'Nach Abschluss des Registrierungsprozesses können Sie auf detaillierte Informationen zu Ihrem Konto zugreifen und von den von uns angebotenen Dienstleistungen profitieren.',
            'accept'   => 'Miligold beitreten',
            'thanks'   => 'Wir wünschen Ihnen einen schönen Tag.<br><br> Freundliche Grüße,<br> Miligold Kundendienst',
        ],
        'account' => [
            'order' => [
                'index' => [
                    'page-title'      => 'Bestellungen',
                    'title'           => 'Bestellungen',
                    'order_id'        => 'Bestellnummer',
                    'date'            => 'Datum',
                    'status'          => 'Status',
                    'total'           => 'Gesamt',
                    'order_number'    => 'Bestellnummer',
                    'processing'      => 'In Bearbeitung',
                    'completed'       => 'Abgeschlossen',
                    'canceled'        => 'Storniert',
                    'closed'          => 'Geschlossen',
                    'pending'         => 'Ausstehend',
                    'pending-payment' => 'Ausstehende Zahlung',
                    'fraud'           => 'Betrug',
                ],

                'view' => [
                    'page-tile'           => 'Bestellung #:order_id',
                    'info'                => 'Information',
                    'placed-on'           => 'Platziert am',
                    'products-ordered'    => 'Bestellte Produkte',
                    'invoices'            => 'Rechnungen',
                    'shipments'           => 'Sendungen',
                    'SKU'                 => 'SKU',
                    'product-name'        => 'Name',
                    'qty'                 => 'Menge',
                    'item-status'         => 'Artikelstatus',
                    'item-ordered'        => 'Bestellt (:qty_ordered)',
                    'item-invoice'        => 'In Rechnung gestellt (:qty_invoiced)',
                    'item-shipped'        => 'Versendet (:qty_shipped)',
                    'item-minted'         => 'Geprägt (:qty_shipped)',
                    'item-canceled'       => 'Storniert (:qty_canceled)',
                    'item-refunded'       => 'Erstattet (:qty_refunded)',
                    'price'               => 'Preis',
                    'total'               => 'Gesamt',
                    'subtotal'            => 'Zwischensumme',
                    'shipping-handling'   => 'Versand & Bearbeitung',
                    'tax'                 => 'Steuer',
                    'discount'            => 'Rabatt',
                    'tax-percent'         => 'Steuerprozentsatz',
                    'tax-amount'          => 'Steuerbetrag',
                    'discount-amount'     => 'Rabattbetrag',
                    'grand-total'         => 'Gesamtsumme',
                    'total-paid'          => 'Gesamt bezahlt',
                    'total-refunded'      => 'Gesamt erstattet',
                    'total-due'           => 'Gesamt fällig',
                    'shipping-address'    => 'Lieferadresse',
                    'billing-address'     => 'Rechnungsadresse',
                    'shipping-method'     => 'Versandart',
                    'payment-method'      => 'Zahlungsmethode',
                    'individual-invoice'  => 'Rechnung #:invoice_id',
                    'individual-shipment' => 'Sendung #:shipment_id',
                    'print'               => 'Drucken',
                    'invoice-id'          => 'Rechnungsnummer',
                    'order-id'            => 'Bestellnummer',
                    'order-date'          => 'Bestelldatum',
                    'invoice-date'        => 'Rechnungsdatum',
                    'payment-terms'       => 'Zahlungsbedingungen',
                    'bill-to'             => 'Rechnung an',
                    'ship-to'             => 'Versand an',
                    'contact'             => 'Kontakt',
                    'refunds'             => 'Rückerstattungen',
                    'individual-refund'   => 'Rückerstattung #:refund_id',
                    'adjustment-refund'   => 'Anpassungsrückerstattung',
                    'adjustment-fee'      => 'Anpassungsgebühr',
                    'cancel-btn-title'    => 'abbrechen',
                    'tracking-number'     => 'Sendungsverfolgungsnummer',
                    'cancel-confirm-msg'  => 'Möchten Sie diese Bestellung wirklich abbrechen?',
                ],
            ],
        ],
    ],

    'sales' => [
        'order' => [
            'payment-excessive-retry' => [
                'subject'         => 'Zahlungsüberprüfungsproblem – Bestellablauf-Warnung [:order_id]',
                'greeting'        => 'Benachrichtigung zum Bestellablauf: Problem bei der Zahlungsüberprüfung',
                'summary'         => 'Sehr geehrte/r :name, <br><br> Wir haben mehrere Wiederholungsversuche während der Zahlungsüberprüfung festgestellt. Dies könnte auf ein Problem bei der Zahlungsbestätigung hinweisen. Bitte überprüfen Sie den Bestell- und Zahlungsstatus. <br><br> Unser Team arbeitet an der Lösung des Problems. Sie werden benachrichtigt, sobald es behoben ist.<br><br>Vielen Dank für Ihre Geduld.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundenservice',
                'summary-invoice' => 'Rechnungsdetails Zusammenfassung',
            ],
            'deposit-excessive-retry' => [
                'subject'         => 'Problem beim Transfer erkannt – Bestellablauf-Warnung [:order_id]',
                'greeting'        => 'Benachrichtigung zum Bestellablauf: Transferproblem',
                'summary'         => 'Sehr geehrte/r :name, <br><br> Wir haben mehrere Wiederholungsversuche während des Coin-Transfers festgestellt. Dies könnte auf ein Problem beim Übertragen von Geldern in Ihre Wallet hinweisen. Bitte überprüfen Sie die Bestellung und Ihr Wallet-Guthaben. <br><br> Unser Team untersucht das Problem und benachrichtigt Sie, sobald es gelöst ist.<br><br>Vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundenservice',
                'summary-invoice' => 'Rechnungsdetails Zusammenfassung',
            ],
            'admin-payment-notification' => [
                'subject'         => 'Fehlerbericht - Bestellablauf abgeschlossen [:order_id]',
                'greeting'        => 'Fehlerbericht - Bestellablauf abgeschlossen',
                'summary'         => 'Sehr geehrte/r :name,<br><br> Wichtige Information: Im Bestellablauf ist ein Fehler aufgetreten. Dienste im Hintergrund zeigen möglicherweise die Zahlung nicht sofort als verifiziert an, auch wenn sie diese erhalten haben. Darüber hinaus kann es zu einem Fehler von Seiten des Dienstes während der Übertragung der Münzen an die Brieftasche kommen. <br><br> Obwohl der Prozess im Moment abgeschlossen erscheint, wird eine detaillierte Überprüfung empfohlen. Der Fehler wird Ihnen mitgeteilt, sobald er behoben ist. <br><br> Wir wünschen Ihnen gute Arbeit.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'admin-order-notification' => [
                'subject'         => 'Neue Bestellung - Bestellablauf gestartet [:order_id]',
                'greeting'        => 'Neue Bestellung - Bestellablauf gestartet',
                'summary'         => 'Sehr geehrte/r :name,<br><br> Eine neue Bestellung wurde aufgegeben. Der Bestellablauf wurde gestartet. <br><br> Wir wünschen Ihnen gute Arbeit.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'completed' => [
                'subject'         => 'Bestellung abgeschlossen - Münztransfer abgeschlossen [:order_id]',
                'greeting'        => 'Bestellung abgeschlossen - Münztransfer abgeschlossen',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Gute Nachrichten! Ihre Bestellung ist abgeschlossen. Ihre Zahlung wurde bereits bestätigt und der Münztransfer wurde erfolgreich auf Ihre Brieftasche durchgeführt. Jetzt ist Ihre Transaktion vollständig abgeschlossen. <br><br> Wir wünschen Ihnen viel Freude beim Gebrauch.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'processing' => [
                'subject'         => 'Bestellverarbeitung - Ihre Zahlung wurde verifiziert [:order_id]',
                'greeting'        => 'Bestellverarbeitung - Ihre Zahlung wurde verifiziert',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Ihre Bestellung wurde erfolgreich empfangen und Ihre Zahlung wurde verifiziert. Die von Ihnen gekauften Münzen werden derzeit übertragen. Wir werden Sie benachrichtigen, wenn der Transfer abgeschlossen ist. <br><br> Vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'pending' => [
                'subject'         => 'Ihre Bestellung befindet sich in der ausstehenden Phase [:order_id]',
                'greeting'        => 'Ihre Bestellung befindet sich in der ausstehenden Phase',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Ihre Bestellung wurde empfangen, aber die Zahlung wurde noch nicht bestätigt. Möglicherweise müssen Sie warten, bis Ihre Transaktion abgeschlossen ist. Wir werden Sie während des Zahlungsüberprüfungsprozesses mit Informationen versorgen. <br><br> Vielen Dank und vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'token-error' => [
                'subject'        => 'Fehlermeldung - Token-Transferfehler [:order_id]',
                'greeting'       => 'Fehlermeldung - Token-Transferfehler',
                'summary'        => 'Sehr geehrte/r :name,<br><br> Wichtige Information: Ein Fehler ist im Token-Übertragungsprozess aufgetreten. Der Fehler wird mit Ihnen geteilt, sobald er behoben ist. <br><br> Wir wünschen Ihnen gute Arbeit.',
                'dear'           => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'order-id'       => 'Bestellnummer',
                'wallet-address' => 'Brieftaschenadresse',
                'error-message'  => 'Fehlermeldung',
                'amount'         => 'Betrag',
            ],
            'token-completed' => [
                'subject'         => 'Token Transfer Abgeschlossen [:order_id]',
                'greeting'        => 'Token Transfer Abgeschlossen',
                'summary'         => 'Sehr geehrte/r :name,<br><br> Gute Nachrichten! Der Token-Übertragungsprozess wurde erfolgreich abgeschlossen. Ihre Transaktion ist jetzt vollständig abgeschlossen. <br><br> Wir wünschen Ihnen viel Freude damit.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
            ],
            'new' => [
                'customer' => [
                    'subject'         => 'Neue Bestellung – Auftragsverarbeitung gestartet [:order_id]',
                    'greeting'        => 'Neue Bestellung – Auftragsverarbeitung gestartet',
                    'summary'         => 'Sehr geehrte/r :customer_name,<br><br>Eine neue Bestellung wurde erhalten. Die Auftragsverarbeitung hat begonnen.<br><br>Wir wünschen Ihnen gutes Gelingen.',
                    'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundenservice',
                    'summary-invoice' => 'Rechnungsübersicht',
                ],
                'admin' => [
                    'subject'         => 'Neue Bestellbenachrichtigung [:order_id]',
                    'greeting'        => 'Eine neue Bestellung wurde aufgegeben',
                    'summary'         => 'Sehr geehrte/r :name,<br><br>Eine neue Kundenbestellung wurde erhalten und befindet sich nun in Bearbeitung.<br>Bitte prüfen Sie die Bestelldetails und stellen Sie sicher, dass alle notwendigen Schritte zur Erfüllung eingeleitet werden.',
                    'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Systembenachrichtigung',
                    'summary-invoice' => 'Bestellübersicht',
                ],
            ],
            'cancel' => [
                'subject'         => 'Bestellung storniert [:order_id]',
                'greeting'        => 'Bestellung storniert',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Ihre Bestellung wurde storniert. Wenn Sie Fragen haben oder Hilfe benötigen, kontaktieren Sie uns bitte. <br><br> Vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
                'payment-method'  => 'Zahlungsmethode',
                'billing-address' => 'Rechnungsadresse',
            ],
            'comment' => [
                'subject'         => 'Neuer Kommentar - Bestellung [:order_id]',
                'greeting'        => 'Neuer Kommentar',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Ein neuer Kommentar wurde zu Ihrer Bestellung hinzugefügt. <br><br> Vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
                'payment-method'  => 'Zahlungsmethode',
                'billing-address' => 'Rechnungsadresse',
            ],
            'invoice' => [
                'subject'         => 'Rechnung für Bestellung #:order_id generiert',
                'greeting'        => 'Rechnung generiert',
                'summary'         => 'Sehr geehrte/r :customer_name,<br><br> Eine neue Rechnung wurde für Ihre Bestellung generiert. Sie können die Details der Rechnung anzeigen, indem Sie auf die Schaltfläche unten klicken. <br><br> Vielen Dank für Ihr Verständnis.',
                'dear'            => 'Mit freundlichen Grüßen,<br>Miligold Kundendienst',
                'summary-invoice' => 'Rechnungszusammenfassung',
                'billing-address' => 'Rechnungsadresse',
                'payment-method'  => 'Zahlungsmethode',
                'view-invoice'    => 'Rechnung anzeigen',
            ],
        ],
        'deposit' => [
            'completed' => [
                'subject'  => 'Übertragung abgeschlossen – Zahlung Bestätigt [:transaction_id]',
                'greeting' => 'Übertragung abgeschlossen!',
                'summary'  => 'Sehr geehrte/r :customer_name, <br><br> Ihre Einzahlung wurde erfolgreich abgeschlossen und von unserem Zahlungssystem bestätigt.',
                'dear'     => 'Mit freundlichen Grüßen,<br>Miligold Kundenservice',
            ],
        ],
    ],
];
