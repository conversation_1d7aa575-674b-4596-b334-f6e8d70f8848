# CHANGELOG for v1.x.x

This changelog consists of the bug & security fixes and new features being included in the releases listed below.

## **v1.4.5 (21th of September 2022)** - *Release*

* #6690 [enhancement] - Mass update option should be available for the categories section.

* #6689 [enhancement] - There should be a proper UI for categories in the shop if there are sub0category of any category.

* #6685 [enhancement] - There should be an option to filter the suspended customer. in the admin panel.

* #6646 [enhancement] - The actual and special prices both should be visible if there is any special price for a configurable product.

* #6345 [enhancement] - Invoice Design Can Be Enhanced

* #6394 [enhancement] - The page should focus on the warning message if the customer is trying book the product without selecting the required fields.  

* #1929 [enhancement] - Actual product amount and discounted amount both should display.

* #6732  [fixed] -  Getting exception if the customer is trying to move product from cart to wishlist

* #6725  [fixed] - The category should not be updated for all locales if the admin is updating the category for any specific locale. 

* #6705  [fixed] -  The filter value should be correct if the admin is trying to filter products by the status filter.

* #6699  [fixed] -  The sale icon should be visible if there is any special price for a configurable product.

* #6697  [fixed] -  The add button should be hidden from view page if the user has no permission to create.

* #6687  [fixed] -  There is an exception while the admin is updating the Velocity meta data

* #6686  [fixed] -  The notification should be visible in the admin panel if the order status is pending payment.

* #6679  [fixed] -  The admin is not able to create the catalog rule for the configurable product, with SKU condition type

* #6668  [fixed] -  The back button should work if the customer is adding a new address on the checkout page.

* #6366  [fixed] -  The product's image is adjusted when the user is trying to view any product.


## **v1.4.4 (30th of August 2022)** - *Release*

* #5584 [feature] -  there should be an feature so that when we click on mark as read , notification box should close.

* #5463 [feature] -  there should be a button so that we should clear all filters.

* [enhancement] - Optimized product

* [enhancement] - Optimized cart

* [enhancement] - Refactored code

* #6556 [enhancement] - Cart Rule -. Validation should be applied in Coupon Code uses per customer and used per coupon section.

* #6555 [enhancement] - Cart Rule - Auto Generate Coupon Code details at bottom must not visible when we switch to Manual Coupon Code.

* #6501 [enhancement] - Cart item prices rounded with precision of 2 resulting in rounding errors after applying taxes. 

* #6494 [enhancement] - The product's URL_KEY should not be changed if the admin is editing the product's name for another locale.

* #6464 [enhancement] - UI - In Table Booking Product, Special Request/Notes must be in Bold for Main Cart.

* #6458 [enhancement] - There should be the validations for the input fields if the admin is trying to create catalog rules.

* #6452 [enhancement] - The page should be open on the next tab when the admin is trying to view the CMS pages.

* #6427 [enhancement] - The mouse property should be not-allowed if the cart is empty. in default theme.

* #6426 [enhancement] - The warning message should be correct if the admin is trying to ship the invalid product quantity. 

* #6417 [enhancement] - There should be a flash message [ Coupan is already applied ] if the user is trying to apply the same coupon multiple times.

* #6408 [enhancement] - The name of the applied filters should be meaningful if the customer is trying to filter the orders.

* #6393 [enhancement] - The page should not be refreshed if the customer is trying to apply the same coupon multiple times.

* #6371 [enhancement] - There should be a flash message for customers when the cart rule is applied on the checkout page.

* #6358 [enhancement] - Unable to see any notification on Coupon Applied on the Checkout Page.

* #6355 [enhancement] - There should be a preview option in the CMS pages table.

* #6327 [enhancement] - Add Missing Language Folder In All Package

* #6273 [enhancement] - There should be a tooltip message, after copying the wishlist share link 

* #5801 [enhancement] - Maximum video uploading size should be mentioned when we create product. 

* #5464 [enhancement] - there should be an image icon of user profile image.

* #6545  [fixed] -  The admin should be able to create transactions only with the Grand Total amount of the order.

* #6406  [fixed] -  Discount should be shown only for the product for which we created a catalog rule.

* #6312  [fixed] -  The billing address should not be saved multiple times, if the customer is clicking on save address checkbox in multiple times.

* #6654  [fixed] -  The size of the image card should be fixed. on the home page.

* #6644  [fixed] -  Trimming should be added for the search string.

* #6640  [fixed] -  There should not be any alert box if the admin is trying to filter the products.

* #6635  [fixed] -  The page should not be redirected to the wishlist page when the customer removes any product from the wishlist.

* #6632  [fixed] -  There add to cart and the wishlist button are adjusting if the customer is trying to view any product.

* #6609  [fixed] -  The wishlist should not get empty after deleting the all product of the second-page wishlist.

* #6606  [fixed] -  The admin should be able to upload and remove the product's image.

* #6558  [fixed] -  The cross icon should be just before the calendar icon if the customer is trying to select the appointment date for booking the product.

* #6554  [fixed] -  The discount amount cannot be greater than 100 if the action type is the Percentage of the product. 

* #6547  [fixed] -  The product video should be played properly if any.

* #6518  [fixed] -  The admin should be able to update the product's status by mass action.

* #6514  [fixed] -  The save as category button should not be clicked automatically if the admin is pressing the enter key for the searching product.

* #6509  [fixed] -  There is an exception if the admin is trying to search products from the category page.

* #6507  [fixed] -  Notification url is broken when admin_url is changed.

* #6505  [fixed] -  The model box should be in the center of the page if the user is clicking on Quick view for any produt.

* #6492  [fixed] -  There is a UI issue if the user is trying to view the compared products in Arabic locale. 

* #6485  [fixed] -  The trash icon should be on the right side of the text if the user is trying to view the shopping cart in the Arabic locale.

* #6484  [fixed] -  The admin is not able to logout on the mobile view.

* #6479  [fixed] -  The position of the top bar should be fixed if the customer is trying to view any order in mobile view.

* #6471  [fixed] -  UI Issue - In Customer Profile Edit option, there we can see the Calendar Logo at DOB is inappropriate.

* #6469  [fixed] -  Translation Issue in the Configure Section related to Number.

* #6463  [fixed] -  There should not be an exception if the admin is trying to add a new variant for the configurable product.

* #6461  [fixed] -  There should not be any warning message for the default empty option if the admin is trying to create/edit any attribute.

* #6459  [fixed] -  Only the review should be deleted which the user wants to delete.

* #6453  [fixed] -  The new customer is not able to login properly when the another user is logging out.

* #6447  [fixed] -  There should not be an exception if the customer is trying to add that product to the wishlist which is deleted by the admin.

* #6444  [fixed] -  The page should be redirected to the login page if the guest user is trying to move products from compare to wishlist.

* #6443  [fixed] -  The height of Add to cart button should be a little less on compare page.

* #6442  [fixed] -  The products should be in proper alignment if there are multiple products in the compare list.

* #6441  [fixed] -  The customer should be able to share the wishlist product if the share link is already generated.

* #6434  [fixed] -  There should not be any translation issue in the checkout page, if the customer is trying to place an order.

* #6430  [fixed] -  The admin should not be able to create a transaction for an invoice with the incorrect Transaction amount

* #6429  [fixed] -  The user should not be able to add products into the cart with 0 quantity.

* #6425  [fixed] -  The sidebar submenu dropdown icon should be shown properly if the admin is changing the English from LTR to RTL. 

* #6423  [fixed] -  The order status should be shown properly in the admin panel. if the status of the order is payment pending. 

* #6419  [fixed] -  There is an exception if the admin is trying to add transactions for any invoice.

* #6407  [fixed] -  The user should not be able to place the order if the user is inactive. 

* #6402  [fixed] -  Only the address should be deleted which the user wants to delete.

* #6401  [fixed] -  There is no option to view compare items for guests.

* #6400  [fixed] -  The success message should not be shown multiple times if the user is trying to remove the applied coupon.

* #6397  [fixed] - The should not be an exception if the guest is trying to log in after deleting all the cart items.

* #6390  [fixed] -  The radio button should be toggle only after clicking on the radio button's in admin panel 

* #6389  [fixed] -  The share wishlist and delete button should not overlap the sidebar in mobile view.

* #6386  [fixed] -  The Products Ordered label should be highlighted if there is an error, while the admin is trying to create a ship for the order in dark mode.

* #6385  [fixed] -  There should be a search icon if the customer is trying to search the orders.

* #6384  [fixed] -  The product's image should not be out of the product card if the user is trying to view products in list mode.

* #6370  [fixed] -  At least one shipping and payment method should be available.

* #6369  [fixed] - The product images are overriding if there are multiple products in the cart.

* #6368  [fixed] - There should be an option to update the cart if the customer is adding downloadable and simple products into the cart.

* #6366  [fixed] -  The product's image is adjusted when the user is trying to view any variant of the configurable product

* #6365  [fixed] -  The bullet symbol should be visible if the user is trying to change the product's image in a quick view popup box. 

* #6364  [fixed] -  The warning message should be correct when the condition is not matching with cart rule while the user is applying a coupon. 

* #6362  [fixed] -  There should be some space between the share and delete buttons on the wishlist page.

* #6349  [fixed] -  The filter value should be correct if the admin is trying to filter the customers by the status filter

* #6346  [fixed] -  There should not be any extra space between the update cart and the delete all items button, on the shopping cart page.

* #6344  [fixed] -  The calendar icon is not showing when the admin is trying to filter the orders by date in dark mode.

* #6341  [fixed] -  There is some extra space between the product image and add to cart button in the mobile view

* #6340  [fixed] -  By default maximum price should be selected if the user is trying to filter a product by price filter.

* #6339  [fixed] -  The Add to cart button should not be disabled if any one product is inactive of the group product.

* #6335  [fixed] -  The translation is missing for slot dropdown if the user is trying to book any product 

* #6334  [fixed] -  The quantity field should not take a negative value if the admin is trying to edit the quantity

* #6333  [fixed] -  The country dropdown should be in the Ascending order when the customer is adding the delivery address on the checkout page.

* #6332  [fixed] -  The calendar Icon should be on the left side of the input box in the Arabic locale when the customer is editing the profile. 

* #6331  [fixed] -  The location and the calendar Icon should be slightly right side in the Arabic locale when the customer is trying to book any product.

* #6324  [fixed] -  The warning message should be correct if the admin is trying to upload an invalid image format for the profile image.

* #6319  [fixed] -  The calendar icon is in the center of the input box. if the customer is trying to book the product.

* #6318  [fixed] -  The add to cart button should be in proper alignment inside the compare page.

* #6317  [fixed] -  The page should be redirected to the product view page. if the user clicks on the product image inside the mini cart.

* #6313  [fixed] -  The warning message should be correct if the customer is selecting the PayPal Smart Button payment method with an invalid country code

* #6288  [fixed] -  The delete button is overriding on share button if the customer is trying to view the wishlist products on mobile view. 

* #6277  [fixed] -  Checkout redirects when a carrier has more than one shipping method 

* #6276  [fixed] -  The product's image should be visible properly in mobile view.

* #6271 [fixed] - There should not be any warning message if the user is trying to book a rental product for multiple hours.


## **v1.4.3 (20th of April 2022)** - *Release*

* #6213 [feature] -  Command For Bagisto Publish

* #6197 [feature] -  Link to frontend from admin product list or product detail page

* #6190 [feature] -  Give option to enable WYSIWYG editor in configuration

* #5376 [feature] -  There should be an option to remove all cart items at once

* #6294 [enhancement] - The mouse property should be a pointer when the admin is trying to mark all notifications as read.

* #6289 [enhancement] - Improvement on products listing

* #6285 [enhancement] - The dashboard cards are not clickable

* #6283 [enhancement] - When the user is trying to view the mini cart then Product Quantity Looks like as a minus value

* #6269 [enhancement] - The mouse property should be a pointer when the customer is trying to change the app currency & locale 

* #6259 [enhancement] - The color of View All Notifications should be blue on the mouseover in admin panel 

* #6243 [enhancement] - The mouse property should be a pointer when the admin is trying to drag the product image while editing any product 

* #6241 [enhancement] - There should be checkboxes inside the status dropdown if the user is trying to filter orders with multiple statuses

* #6229 [enhancement] - The mouse property should be a pointer when the user is trying to sort the table by tables header.

* #6217 [enhancement] - There should be an asterisk icon for the required fields if the user is trying to book the product. 

* #6213 [enhancement] - Command For Bagisto Publish 

* #6208 [enhancement] - The product status should be Highlighted in product list page

* #5967 [enhancement] - There should be an option to copy the product's share link.

* #5960 [enhancement] - There should be an option for a sitemap generator. .

* #5559 [enhancement] - There should be the currency icon inside the currency dropdown 

* #5530 [enhancement] - there should be an cross icon in blisss theme so that customer can delete product from cart 

* #5397 [enhancement] - The filter dropdown should be according to the selected value. 

* #5354 [enhancement] - The Input validation's dropdown should be according to attribute type.

* #2718 [enhancement] - Show error message "No booking available" if booking is not available for selected date. Booking 

* #2308 [enhancement] - Existing groups in the attribute families are not translatable yet.

* #6290  [fixed] -  Cancel already shipped order results in incorrect pending inventory amount

* #6279  [fixed] -  The currency icon is overriding on the currency dropdown when the app locale is Arabic.  

* #6270  [fixed] -  The enter key is not working if the admin is trying to apply the filter by pressing the enter key  

* #6267  [fixed] -  There should not be an exception if the admin is trying to view order after deleting the customer of same order Admin  
  
* #6266  [fixed] -  The admin is not able to view the bookings in the calendar view.   
 
* #6261  [fixed] -  The filter options should be under the filter button. when the customer is trying to filter order in Arabic locale  

* #6260  [fixed] -  The upload image option should be slightly on the right side. when the customer is adding a review for any product.  
 
* #6256  [fixed] -  There should not be any extra space between add to cart and the by now button.  
 
* #6253  [fixed] -  The page should be redirected to the particular order's page if the customer is clicking on the order id after placing the order.
 
* #6250  [fixed] -  The admin is not able to view the customer's review if the customer is creating a review with the moustache syntax Like: {{ url(' ') }}  
 
* #6246  [fixed] -  The products are not listing when url key is null  

* #6244  [fixed] -  There is an exception when the customer is changing the app currency on the product view page.
 
* #6228  [fixed] -  There should not be a blank value in the items per page dropdown. on customer orders page
 
* #6206  [fixed] -  The border of calendar icon is not showing in dark mode, when the admin is trying to view booking in calendar view.

* #6205  [fixed] -  The flash message should be according to the selected locale if the admin trying to create a Shipment.
 
* #6204  [fixed] -  The carousel arrow icons [ previous and next ] should work properly in the Arabic locale.

* #6202  [fixed] -  There should be some space between the input box and labels if the user is trying to book an appointment for any booking product.

* #6196  [fixed] -  The admin should be able to delete the users.

* #6195  [fixed] -  Mini Cart sub  total is not updating after removing the cart's item.

* #6192  [fixed] -  The shopping cart goes empty after removing an item from the mini cart.

* #5772  [fixed] -  issue in french lanugage in navigation bar

* #5067  [fixed] -  Products is_wishlisted attribute is always false on API   


## **v1.4.2 (31st of March 2022)** - *Release*

* #6164 [enhancement] - UI Enhancement - Please Add the separation Lines in the Heading section also in Orders and Downloadable Product Details. 

* #6162 [enhancement] -UI Enhancement - Please Make the Information Text Bold in the Compare Product Page. 

* #6119 [enhancement] -The locale dropdown should be in ascending order 

* #6117 ['enhancement] -The locale name should be shown instead of the locale code on the orders page 

* #5964 [enhancement] - The process to checkout button should be disabled if the product is out of stock. 

* #5399 [enhancement] - There should be a Back to Top Button in the user panel.

* #5355 [enhancement] -There should be an option to upload user's picture 


* #6186 [fixed] - The page should be redirected to the product-review page after submitting the review  

* #6181 [fixed] - There should not be any UI issue on the product-view page in the quantity field. 

* #6177 [fixed] - There is a UI issue on the product-view page in the Arabic locale 

* #6174 [fixed] - The sidebar should be disabled if the admin is trying to send the duplicate invoice. 

* #6167 [fixed] - In Reviews Section - Grammatical Mistake Found. 

* #6165 [fixed] - In Downloadable Product Section Unnecessary Column is present. 

* #6161 [fixed] - There should not be any UI issue on the orders page.if the user is trying to filter the orders  

* #6158 [fixed] - The quantity option should not be visible for booking products. 

* #6157 [fixed] - There is a UI issue on the product-edit page 

* #6156 [fixed] -  LINT ERROR IN EDIT PRODUCTS BLADE 

* #6153 [fixed] - There should be an asterisk icon for the required fields. 

* #6144 [fixed] - Payment methods page is Blank if admin_locale=fr 

* #6134 [fixed] - The total amount of cart items should be correct. 

* #6132 [fixed] - The edit channel page should not be blank in the Chinese locale. 

* #6126 [fixed] - There is a UI issue on the cart section with Arabic locale 

* #6124 [fixed] - There is a UI issue on the header section 

* #6121 [fixed] - The warning message should be correct if the user is trying to delete the account

* #6115 [fixed] - The Back to top button should be disabled when the user is placing the order 

* #6114 [fixed] - Edit channel page is Blank if admin_locale=fr 

* #6112 [fixed] - There should not be Error 500 if the admin is trying to edit the product. 

* #6109 [fixed] - The add to cart button should be blurred if the item is out of stock 

* #6103 [fixed] - The header section should be responsive in mobile view 

* #4959 [fixed] - getting exception when merge guest cart with customer cart with configurable item 


## **v1.4.1 (17th of March 2022)** - *Release*

* #6040 [enhancement] - The locale dropdown should be in ascending order

* #6007 [enhancement] - There should be an option to export selected product

* #5923 [enhancement] - There should not be option for mass delete if there is no record in table

* #6039 [fixed] - The from date should be less than to date

* #6038 [fixed] - The color of checkboxes should be according to the theme in the admin panel


## **v1.4.0 (16th of March 2022)** - *Release*

* #5654 [feature] -Only unread notification should be shown in the notification modal box. 

* #4787 [feature] - Unable to upload profile picture

* #4653 [feature] - Making billing address informations optional

* #4356 [feature] - Guest Checkout sucess add register

* #5921 [enhancement] -Notification should be received without page refresh.

* #5817 [enhancement] -The wishlist and the compare option should be inside the account section

* #5671 [enhancement] -By default the first submenu should be open, when the admin is clicking on any tab

* #5537 [enhancement] -New admin theme - RTL menu

* #5525 [enhancement] -Send a duplicate of the invoice to the customer

* #5490 [enhancement] -The address should be horizontal format

* #5404 [enhancement] -The Add image button should not be present when the user is trying to edit the profile

* #5396 [enhancement] -The currency icon should be shown properly

* #5395 [enhancement] -The user panel should be responsive on phone-view

* #5384 [enhancement] -There should be a confirmation alert before deleting all items from Wishlist and Compare

* #5300 [enhancement] -Suspend customers

* #5292 [enhancement] -Some data grids are missing ProvideDataGridPlus trait

* #5267 [enhancement] -Send reminders for overdue invoices

* #5219 [enhancement] -Issue in multi select data in every datagrid

* #5165 [enhancement] -Write All Factories In Laravel 8 Pattern

* #5158 [enhancement] -Breadcrumb Feature

* #5143 [enhancement] -Rest API Enhancement In Sanctum

* #5064 [enhancement] -Multi factor authentication

* #4834 [enhancement] -Requesting to load attributes via ajax

* #4772 [enhancement] -Ability to drag product image to reorder them in bagisto admin dashboard

* #4653 [enhancement] -Making billing address informations optional

* #2918 [enhancement] -Overview of sold booking products in Admin panel

* #2009 [enhancement] -Using AWS S3 for storage

* #1243 [enhancement] -Implement a feature to share Wishlist

* #5968 [enhancement] -The wishlist and compare option should not be in multiple times

* #1238 [enhancement] -Give configuration for converting textarea to wysiwyg editor or not for browser compatibility.

* #6019 [fixed] -There should not any UI issue if the admin is trying to update products by mass action. 

* #6010 [fixed] -There should not be any UI issue when the user is trying to write a review for the product in mobile view. 

* #6008 [fixed] -There is a UI issue on the Bills theme when the user is trying to log in.

* #6006 [fixed] -There is a UI issue if the admin is trying to update products by mass action.

* #5995 [fixed] -The filters should work properly. if the user is trying to filter the products by the color filter

* #5985 [fixed] -There should be a warning message on the checkout page, for the required field if the user is clicking outside of the required input field 

* #5980 [fixed] - There is a UI issue if the user is trying to filter products by category

* #5966 [fixed] - The product quantity should be visible on the cart page

* #5965 [fixed] - Maximum Password length is 18 characters in velocity theme, which is way too short to be secure

* #5941 [fixed] - UI Issue at the Profile Section -> Empty space below address

* #5939 [fixed] - The selected date and time should be visible if the admin trying to add Tickets for a booking product

* #5938 [fixed] - There is a UI issue on forget password page

* #5932 [fixed] - There should not be any extra space on the product view page

* #5931 [fixed] - The product must be added to the wishlist which the user wants to add

* #5929 [fixed] - The notification text should be clickable

* #5926 [fixed] - There is a UI in the header section on mobile view

* #5919 [fixed] - There is a UI issue on the orders page 

* #5915 [fixed] - There is a translation issue in notification pages.

* #5913 [fixed] - The user is able to edit the product without permission for the same

* #5908 [fixed] - The Success message should be according to the selected locale

* #5907 [fixed] - There is a UI issue on the checkout page

* #5904 [fixed] - There is a UI issue on the Tax Rates page

* #5903 [fixed] - There is a UI issue on the notification page

* #5899 [fixed] - The text color of the count should be white in mobile view

* #5898 [fixed] - The success/warning messages should be shown properly

* #5885 [fixed] - There are many translation issues on the Arabic locale 

* #5865 [fixed] - There is a translation issue when the admin is trying to create a product.

* #5855 [fixed] - The categories name should be shown in all locales

* #5841 [fixed] - There is a translation issue on the Notification page.

* #5839 [fixed] - There should not be any UI issue while adding the product.

* #5830 [fixed] - There should be no exception if the user is trying to log in

* #5828 [fixed] - The Channel filter is not working properly on the product list page.

* #5820 [fixed] - The user is not able to update the review by mass action.

* #5809 [fixed] - Ui issue when admin try to click on search order in notification in mobile view

* #5808 [fixed] - proper allignment of images when admin try to upload in arabic language

* #5805 [fixed] - Navigation alligment when we try to edit checkbox and select attribute in mobile view

* #5804 [fixed] - When I create product bullets in the edit I am experienced those UL get dropped and they are left out in the customer view

* #5803 [fixed] - Alligment issue when we make a configurable product in delete and edit button.

* #5802 [fixed] - Ui issue comes when we click on add link in downloadable product

* #5799 [fixed] - Ui issues in arabic language when we try to add booking product.

* #5797 [fixed] - There should be proper space when customer is try to add address.

* #5791 [fixed] - There should be gab between edit and delete option in addresss page in velocity.

* #5790 [fixed] - Issue in orders page in velocity in arabic language

* #5789 [fixed] - Alligment should be done properly in arabic in wishlist page

* #5788 [fixed] - When we are try to delete the sliders wrong notifications show

* #5785 [fixed] - Calender icon is not properly visible in arabic

* #5778 [fixed] - issue in arbaic language of alligment in mobile view

* #5775 [fixed] - translations not working in navigation in admin panel

* #5774 [fixed] - Admin - Address index view not found 

* #5772 [fixed] - issue in french lanugage in navigation bar

* #5770 [fixed] - Layout shifting in admin ui

* #5768 [fixed] - Zoomlens in product page overlap sidebar category menu 

* #5764 [fixed] - not able to select options in LTR ENGLISH

* #5762 [fixed] - not able to add slider images in velocity theme

* #5761 [fixed] - email spelling wrong in notifications emails

* #5755 [fixed] - there should be a space between update quantities in mobile view in admin panel

* #5754 [fixed] - not get any mail when we make new intventory sources in admin panel

* #5752 [fixed] - in mobile view wishlist option is not there in checkout page

* #5746 [fixed] - update button alligment should done in velocity theme.

* #5736 [fixed] - remove button should be with wishlist in mobile view in simple product

* #5735 [fixed] - alligment should be done proper in velocity in mobile view of remove icon

* #5730 [fixed] - there is an option to increase quanity when user is try to booked product in both themes.

* #5729 [fixed] - ui issue when we are try to book only booking product in bliss theme in checkout page (mobile view ).

* #5728 [fixed] - user is not able to update quanity in checkout cart page in veloicity theme

* #5727 [fixed] - ui issue when guest is place an order (mobile view ) in velocity

* #5722 [fixed] - when we disable status paypal from admin panel it show error in console

* #5719 [fixed] - ui issue when we try to make booking product in admin panel in mobile view 

* #5718 [fixed] - alligment issue in blisss theme in downloadable product section (mobile view)

* #5717 [fixed] - ui issue in admin panel when we try to edit cms pages (mobile view )

* #5709 [fixed] - Wrong error messages when uploading video 

* #5708 [fixed] - proper alligment should be done in order page (mobile view)

* #5707 [fixed] - ui issue in configuration part of admin (mobile view) 

* #5706 [fixed] - there should be an option of wishlist button in blisss theme in front panel 

* #5705 [fixed] - ui issue in compare similar items in compare page (mobile view) 

* #5704 [fixed] - alligment issue in velocity theme in mobile view 

* #5703 [fixed] - star ratings alligment should be in same line in mobile view 

* #5696 [fixed] - dropdown buttons is not working properly in admin panel

* #5695 [fixed] - ui issue when there is only 1 downloadable product in view shopping cart (mobile view)

* #5694 [fixed] - star ratings and reviews alligment should be in same line in blisss theme in mobile view

* #5687 [fixed] - there should be no option for downloadable product for increasing the quanity in velocity theme 

* #5684 [fixed] - reimporting exported tax rate csv results in state field missing error

* #5670 [fixed] - user not able to redirect to cms pages in blisss theme 

* #5663 [fixed] - The user should be able to download the downloadable product 

* #5661 [fixed] - ui issue in customer order view page in blisss theme in mobile view

* #5659 [fixed] - delete button in customer profile should be properly allign (bliss theme) in mobile view

* #5657 [fixed] - proper alligment should be done in bliss theme when we write reviews (mobile view)

* #5650 [fixed] - There is a UI issue in cart page on phone view 

* #5648 [fixed] - there should not be any blank space in payment method 

* #5636 [fixed] - ui issue when we search any product in bliss theme 

* #5635 [fixed] - issue when we click on search icon in blisss theme in mobile view 

* #5633 [fixed] - alligment issue after you place an order in mobile view 

* #5625 [fixed] - issue in side bar in mobile view when we open any cms pages in velocity 

* #5624 [fixed] - in mobile view order id and all other items is not visible when we have not done any order 

* #5621 [fixed] - There should not be any UI issue in the user panel. 

* #5620 [fixed] - there should be enhacement in button colour so that it should be according to mode

* #5619 [fixed] - images in review is not compatiable in mobile view

* #5617 [fixed] - there should be an enhacement so that languages is not with locale heading

* #5616 [fixed] - there should be dark colour in pending in reviews so that it should be more readable when we changes the mode 

* #5615 [fixed] - ui issue in order page in search bar in velocity theme

* #5613 [fixed] - ui issue in customer address edit in admin panel

* #5609 [fixed] - ui issue in catelog in mobile view

* #5608 [fixed] - slider should not overide in catelog rule and cart rule in condition part in mobile view

* #5603 [fixed] - notification box should be mobile compatible

* #5602 [fixed] - ui issue in tax rates in mobile view

* #5601 [fixed] - ui issue in exchange rate in mobile view

* #5591 [fixed] - there is a space between add category button and text box filter in mobile view

* #5585 [fixed] - filters should not attach with heading in category 

* #5581 [fixed] - there should be logo of admin in dekstop view like mobile view 

* #5579 [fixed] - velocity in list should be left alligment 

* #5571 [fixed] - there should be an icon of calender in velocity profile page of customer 

* #5569 [fixed] - There should be meaningful data inside the order dropdown 

* #5556 [fixed] - Deprecation Warning: Using / for division outside of calc() is deprecated and will be removed in Dart Sass 2.0.0. 

* #5555 [fixed] - darkmode dropdown list

* #5552 [fixed] - there should not be any blank space in attribute family 

* #5545 [fixed] - navigation sliders not work properly

* #5540 [fixed] - There is a UI issue in the admin panel 

* #5539 [fixed] - The Login user should not be able to update self status.

* #5538 [fixed] - There should not be any translation issue in the notification section

* #5531 [fixed] - there should be not any blank space in gender it may contain select gender

* #5529 [fixed] - when we hover over bell icon tooltip is not showing

* #5526 [fixed] - There are multiple flash messages if we are clicking on the mark as read inside the notification.

* #5524 [fixed] - Dashboard text is not readable when light mode is enabled

* #5523 [fixed] - Notifications dropdown size

* #5522 [fixed] - icons are missing in top right navbar

* #5521 [fixed] - Default logo is not visible

* #5516 [fixed] - The size of the product images should not increase when the user is removing items from compare list

* #5513 [fixed] - Admin package the manifest.php files are missing

* #5511 [fixed] - UI issue in product list page.

* #5510 [fixed] - The counting is not updating automatically when the user is trying to add or remove an item from comparison.

* #5505 [fixed] - maintance mode is not working in admin panel

* #5503 [fixed] - validation not working in inventory page

* #5501 [fixed] - There is a warning message if the user is trying to add the address.

* #5497 [fixed] - If the admin user is changing the permission of other user's then the user should be logging out with a message

* #5493 [fixed] - The page should be redirected to the product-view page if the user is clicking on the product image/name

* #5491 [fixed] - The user should be able to update quantity using the + icon.

* #5485 [fixed] - ui issue in address bar in mobile view

* #5484 [fixed] - The user should be able to add a new address.

* #5483 [fixed] - languages logo is not visible in mobile view

* #5478 [fixed] - alligment not fixed in mobile view in velocity order page

* #5474 [fixed] - The filters should work properly. if the admin is trying to filter the sliders

* #5473 [fixed] - There cart option should not multiple times in the account section.

* #5471 [fixed] - The user should be able to view the modules/plugins menu in mobile view also. if the user adds any module.

* #5470 [fixed] - there should be less spacing in compare section

* #5468 [fixed] - There is a UI issue in Compare Similar Items section

* #5466 [fixed] - text alligment in order page of velocity

* #5462 [fixed] - ui issue when we try to ship wrong quantity products

* #5461 [fixed] - ui issue when we add product in compare.

* #5460 [fixed] - UI issue when we add product in wishlist in blisss theme

* #5451 [fixed] - Possibility to order infinite configurable products (product_ordered_inventories and / or order_items not correctly updated at order)

* #5450 [fixed] - The CMS image should reflect on the shop if the admin is adding any image on CMS

* #5445 [fixed] - There should be a confirmation alert before deleting items from the Wishlist

* #5444 [fixed] - The admin is able to create Invoice/ship orders with 00 quantities.

* #5443 [fixed] - The message should be correct when the admin is trying to delete system attributes

* #5440 [fixed] - There should be an option if the user is trying to enter the product quantity before updating the cart.

* #5436 [fixed] - The discount Amount should be correct in the invoice.

* #5435 [fixed] - There should be a warning message if the user is trying to create an invoice with invalid quantity.

* #5430 [fixed] - ProductFlat getAttribute($key) throws Undefined property if value of $key is NULL in product_flat table

* #5429 [fixed] - php artisan route:list error target class onepagecontroller does not exist (current master branch)

* #5428 [fixed] - There should not be an exception if the admin is trying to refund the order.

* #5424 [fixed] - The item should not move to the wishlist if the user is canceling the action.

* #5423 [fixed] - The user should not be able to edit the record while the input field is giving a warning.

* #5409 [fixed] - The user should be able to view the reviews

* #5408 [fixed] - There should not be any exception if the user is trying to delete their reviews.

* #5406 [fixed] - The user should be able to edit the address.

* #5405 [fixed] - The success message should be correct when the user is adding items to the cart

* #5398 [fixed] - The User should not be able to add the same product to the wishlist multiple times.

* #5389 [fixed] - There should be the proper alignment of filter's

* #5381 [fixed] - Validation required for coupon code

* #5377 [fixed] - There should not be an exception When user is moving a product to a wishlist that is already in wishlist

* #5366 [fixed] - There should be a warning message when the user is applying the same filter again and again.

* #5365 [fixed] - Issue in price filter for configurable type product

* #5353 [fixed] - Shipping::rates protected variable is not cleared on Shipping::removeAllShippingRates()

* #5350 [fixed] - The product price should be correct after exporting to excel.

* #5347 [fixed] - Images are coming out to be same at the cross sell products.

* #5342 [fixed] - admin (RTL) - menu not apears

* #5328 [fixed] - SKU is still visible at the adding new product when it is disabled.

* #5325 [fixed] - Delete profile modal content data visible and then suddenly hide

* #5316 [fixed] - In Front end Order details, Order id is having a lot of space.

* #5315 [fixed] - Quantity issue in product shipment.

* #5312 [fixed] - Issue in sub-category for mobile view

* #5311 [fixed] - Total weight is null in shipment table

* #5307 [fixed] - Flat Rate calculation type in admin configurations 

* #5305 [fixed] - Cart Set Shipping API 

* #5289 [fixed] - Quantity should be updated on ' Shopping Cart ' page

* #5261 [fixed] - Side filter is getting detached from the category product listing in mobile 

* #5259 [fixed] - Responsive issue in user Profile - Velocity

* #5233 [fixed] - Issue if create product for multiple channel

* #5232 [fixed] - App config.php has bad reference to ProductGrid::class

* #5226 [fixed] - Not able to send the products to wishlist from compare page if the product is not on the first position

* #5210 [fixed] - UI Issue in search bar in order section at customer's end.

* #5209 [fixed] - There is no way to access Admin panel if there is only one admin and he try to modify the role of Administrator and allow limited access.

* #5208 [fixed] - UI Issue in bliss theme - Product View Page

* #5200 [fixed] - getting API error after opening categories named category

* #5198 [fixed] - Wrong phpdoc reference in Webkul\Core\Core to CustomerGroupRepository

* #5197 [fixed] - There is no way to log out for 2nd user of Admin if he himself modify his roles and remove some or all privileges.

* #5194 [fixed] - Getting exception when exporting a product from the admin panel

* #5191 [fixed] - Issue is in checkbox type attribute

* #5180 [fixed] - Some code appears in starting when clicking on the any product edit page

* #5179 [fixed] - Cart items get increased when navigating away from the buy now functionality

* #5178 [fixed] - API for customers to cancel order

* #5047 [fixed] - Order status gets complete when shipment is generated in case of pending invoice

## **v1.3.3 (27th of September 2021)** - *Release*

* #5008 [feature] - Image upload option with editor.

* #5005 [feature] - Ability to edit Bagisto product stock management without editing.

* #5163 [enhancement] - Datagrid enhancements.

* #5109 [enhancement] - Admin is not getting mail on customer registration.

* #5089 [enhancement] - On clicking the buy now button, cart items should not be carried in checkout.

* #5061 [enhancement] - Option to add DB_PREFIX in web installer.

* #5004 [enhancement] - Velocity checkout page changing address doesn't update select shipping method section.

* #5173 [fixed] - Typo in type hinting.

* #5170 [fixed] - Admin Orders page doesn't reload while clicking back.

* #5155 [fixed] - When product inventory is having the same quantity then the total qty on the listing page is not calculated properly.

* #5125 [fixed] - Wrong order status showing at admin and customer end.

* #5122 [fixed] - Issue in Mobile number field.

* #5115 [fixed] - Getting exception in case of admin change URL by passing 0 in place of `asc` or `desc`.

* #5113 [fixed] - Error in Admin->Configuration after added custom script.

* #5112 [fixed] - CMS pages content issue.

* #5111 [fixed] - Meta description error.

* #5110 [fixed] - If I change `APP_ADMIN_URL` in .env then the Admin Dashboard will not render properly.

* #5104 [fixed] - The last name should be a mandatory field even while editing a customer profile.

* #5101 [fixed] - Incorrect validation message for sort order and Download Allowed field in the downloadable information section.

* #5100 [fixed] - While adding address, first and last name should auto-populate at customer end.

* #5098 [fixed] - Delete customer address from admin panel - page not reload.

* #5091 [fixed] - Getting error while creating slider if db prefix has been added.

* #5088 [fixed] - Can not generate invoices.

* #5069 [fixed] - Category datagrid channel locale filter.

* #5067 [fixed] - Products is_wishlisted attribute is always false on API.

* #5065 [fixed] - Delete option does not work.

* #5057 [fixed] - Mysqli error on local by flywheeel install.

* #5056 [fixed] - Home banners sizes are missed up in the RTL layout.

* #5051 [fixed] - Mismatch in grand total and subtotal in HKD currency in the cart summary.

* #5041 [fixed] - Cancel button should not visible if the automatic invoice is generated with pending status.

* #5009 [fixed] - Need to optimize product attribute options.

* #4993 [fixed] - `parent_id` attribute is non-existent on `shipment_items` but there is an existing relationship in `ShipmentItem` model.

* #4990 [fixed] - Automatic scroller should be added to focus on required field while user trying to save catalog details.

## **v1.3.2 (7th of August 2021)** - *Release*

* #5020 [feature] - Webvital score improvement.

* #4855 [feature] - Autofilling of quantity.

* #4632 [feature] - Gooogle captcha integration.

* #4557 [feature] - Feature for sorting product review at customer's end.

* #4463 [feature] - Show prices with or without taxes.

* #4257 [feature] - Multi-lang support in admin.

* #4102 [feature] - Payment history for orders.

* #3477 [feature] - Image column in configurable products for variants.

* #3453 [feature] - Automatic invoice generation.

* #4927 [enhancement] - Allow the admin to set a payment term for invoices.

* #4926 [enhancement] - Allow admin to set an invoice prefix and the first invoice number.

* #4911 [enhancement] - Default variant selection for configurable product.

* #4901 [enhancement] - Configurable product addition issue.

* #4767 [enhancement] - PHP 8 Support.

* #4449 [enhancement] - Laravel 8 Support.

* #3834 [enhancement] - Ability to use multiple datagrids on single page.

* #3251 [enhancement] - Add orders list to the customers details view.

* #5053 [fixed] - showing validation on input fields while loading velocity

* #5046 [fixed] - Add to cart button is not working in compare page for default theme.

* #5045 [fixed] - Not getting product images where cache is exist.

* #5042 [fixed] - Update the alert message when edit and save customer address.

* #5033 [fixed] - Order status options after invoice generation on payment methods.

* #5029 [fixed] - Header menu content list layout need to fix.

* #5027 [fixed] - Fix customer account menu visibility in safari browser.

* #5018 [fixed] - Invoice ID is not recognized in transactions when invoice id contains custom prefix/suffix.

* #5013 [fixed] - No warning visible when user password doesn't match on the reset password page.

* #5011 [fixed] - Admin filter options are not working in Safari.

* #4992 [fixed] - Products image are getting stretch on search page.

* #4989 [fixed] - In sales, the module that creates refund subtotal is showing different.

* #4977 [fixed] - Configurable products are not visible on the category page if allow out of stock is disabled.
.
* #4969 [fixed] - Muliselect attribute options ID should not visible in compare product.

* #4965 [fixed] - Filter labels in datagrids are not translated.

* #4964 [fixed] - Search icon is missing in velocity theme Datagrid.

* #4963 [fixed] - French locale translations are missing.

* #4962 [fixed] - Able to access downloadable products even I have not purchased.

* #4960 [fixed] - The invoice date is missing on invoices.

* #4959 [fixed] - Getting exception when merging guest cart with customer cart with the configurable item.

* #4951 [fixed] - Real-time compare number is not decreased when deleting a product from compare page.

* #4948 [fixed] - Translation issue for the minimum order at cart checkout.

* #4947 [fixed] - Getting all invoices (that are not linked to customer or customer's order) in the customer section in the admin panel.

* #4941 [fixed] - The locale option should be visible with fields that are locale-based.

* #4935 [fixed] - Slider is not removed from the store if slider date gets expired.

* #4931 [fixed] - Root category name is missing when admin locale is not en.

* #4924 [fixed] - The coupon code button should not disable if one coupon code is already applied.

* #4921 [fixed] - Flag icons are missing when the locale is not en.

* #4903 [fixed] - Contribution guide is missing.

* #4896 [fixed] - Group product sort order functionality is not working.

* #4886 [fixed] - Make Sample optional for downloadable products.

* #4885 [fixed] - Hide shipping for virtual products

* #4854 [fixed] - Filtering customer address page is always redirect to the customer profile tab.

* #4848 [fixed] - Fix selected filter options alignment at customer panel.

* #4847 [fixed] - Add ACL for customer order list.

* #4840 [fixed] - Pagination alignment issue on customer end.

* #4827 [fixed] - Shop by category disappears on hover.

* #4823 [fixed] - UI bug at customer Invoice page if multiple invoices exist for same order id.

* #4818 [fixed] - Getting qty error when move item to cart from the wishlist.

* #4813 [fixed] - Compare product in velocity theme showing duplicated attribute after adding a new attribute family.

* #4811 [fixed] - Getting error when redirect on review page.

* #4807 [fixed] - Category logo icon is missing in API.

* #4806 [fixed] - Not able to see the configurable products in the API.

* #4804 [fixed] - The city field is missing in order confirmation emails.

* #4800 [fixed] - Filter is not working in customer grid.

* #4799 [fixed] - Product image resizing.

* #4794 [fixed] - Admin logo is getting hidden when scrolling down the menu bar.

* #4779 [fixed] - Image search is not working when out of stock is disabled.

* #4773 [fixed] - Category condition is not getting set in catalog rule.

* #4771 [fixed] - Datetime component should be enhanced as date component in UI package.

* #4758 [fixed] - Bug when adding an item twice in the cart from the API.

* #4752 [fixed] - Add endpoints for transactions API Done Enhancement.

* #4751 [fixed] - The sidebar menu icon should be removed from admin.

* #4748 [fixed] - Address is not saved on checkout.

* #4735 [fixed] - Price filter issue.

* #4730 [fixed] - All products are getting deleted when the associated category is deleted.

* #4729 [fixed] - Products do not exist inside a category, still they are visible on the category page.

* #4727 [fixed] - Error `addToCartHtml` is not defined on the compare page.

* #4722 [fixed] - Getting exception on wishlist when adding a configurable item on wishlist.

* #4715 [fixed] - Product name is breaking in invoice PDF.

* #4713 [fixed] - Paypal IPN Issue Fixed - SA6.

* #4710 [fixed] - Fixed currencies "value"s and "phone" fields formated for API.

* #4709 [fixed] - Getting exception when update inactive item on cart.

* #4708 [fixed] - Getting issue while selecting variant with no image.

* #4707 [fixed] - Fixed admin theme not loading with custom admin URL.

* #4702 [fixed] - Issue during exporting customers data.

* #4698 [fixed] - Category slug should not translation based.

* #4691 [fixed] - Distorted image issue.

* #4690 [fixed] - Variant images are not visible in a sorted manner as uploaded in the backend.

* #4686 [fixed] - Configurable item is still visible on the search page when out of stock is disabled.

* #4685 [fixed] - Blank space remains on the homepage when disabling out-of-stock items from the backend.

* #4682 [fixed] - Catalog rules and product price filter.

* #4680 [fixed] - Check out page extremely slow.

* #4677 [fixed] - API addresses, returns an error on create + not all fields are manipulated API.

* #4668 [fixed] - Customer profile header is missing in responsive view.

* #4564 [fixed] - Getting exception on admin interface when setup using webinstaller.

* #4519 [fixed] - Tax should not be state or Pincode-dependent. It should be product category-dependent.

* #3902 [fixed] - Duplicate order number getting generated when placed order simultaneously.

* #3196 [fixed] - L x W x H/D on product description.

## **v1.3.1 (22nd of February 2021)** - *Release*

* #4659 [fixed] - Only show defaults option in Shipping.

* #4654 [fixed] - Composer error with `khaled.alshamaa/ar-php` version.

* #4647 [fixed] - APP_VERSION returns the wrong version number.

* #4645 [fixed] - `/api/products` returns trying to get property 'product' of non-object.

* #4643 [fixed] - Getting exception when admin change the status of subscribed user to false.

* #4641 [fixed] - Getting data in client id "sb" by default.

* #4633 [fixed] - Incorrect validation message at admin end.

* #4344 [fixed] - Attribute option positioning not working (Front-end).

## **v1.3.0 (16th of February 2021)** - *Release*

* [feature] - PayPal Smart Button for quick and fast payment.

* [feature] - Email Marketing-Newsletter to send the newsletter to your valuable customers.

* [feature] - Control on Displaying of out of stock Product.

* [feature] - Minimum Order Amount by which it will necessary to meet their order subtotal to make a successful purchase.

* [feature] - Add Video of Your Product.

* [feature] - Maintenance Mode, let search engines know that your site is currently down.

* [feature] - Buy Now at Product Page, Admin can easily enable/disable the Buy Now Button for both themes.

* [feature] - laravel Debugger Mode, This will help the developer/users to see all the packages they are using inside the Webkul folder.

* [enhancement] - Duplicated queries removed.

* [enhancement] - Improved lighthouse score.

* [enhancement] - Slider path should also work in default theme.

* [enhancement] - Add configuration to enable/disable the image search feature.

* [enhancement] - Multi product images should be changed on mouse hover.

* [enhancement] - Add configuration to set admin url while install using bagisto:install command.

* [enhancement] - Add confirm box while moving an item to wishlist from the cart page.

* [enhancement] - Add option to export auto generated coupons in cart rule.

* [enhancement] - Add bulk category delete functionality.

* [enhancement] - Default shop content should be localized.

* [enhancement] - Schedule cron job to process newsletter campaigns mails.

* [enhancement] - Showing configurable product base on catalog rule price.

* [enhancement] - Wishlist and Cart key in Product API.

* [enhancement] - Add a option in the channel configuration to enable/disable wishlist.

* [enhancement] - Add product number.

* #4620 [fixed] -  value is not saved in fallback locale in configuration section.

* #4597 [fixed] -  Add a product to Wishlist twice issue.

* #4596 [fixed] -  Unable to view order at customer end.

* #4590 [fixed] -  getting exception on cart when remove minimum amount from backend.

* #4583 [fixed] -  inactive template are sending in newsletter mail to subscribed users.

* #4577 [fixed] -  error on minicart while adding configurable item.

* #4573 [fixed] -  out of stock items are visible on category & search page when configuration is disabled.

* #3572 [fixed] -  No info about other discounts in case there is multiple group pricing exist in same product.

* #4571 [fixed] -  video is not showing for variant product in product page.

* #4567 [fixed] -  configurable product variant attribute options are not visible in layered navigation.

* #4566 [fixed] -  wrong attribute are visible on layered navigation.

* #4506 [fixed] -  customer group pricing fixed discount should accept decimal values.

* #4498 [fixed] -  able to delete root category which is selected in channel.

* #4495 [fixed] -  Only load the locales that are enabled on the channel.

* #4486 [fixed] - getting exception when copy cart rule.

* #4481 [fixed] - Shipping costs are not calculated with grand total.

* #4459 [fixed] - disabled option should not accessible from routes in frontend.

* #4456 [fixed] - getting exception when filter category, attribute, customer datagrid based on status.

* #4457 [fixed] - cart rule status always set as draft.

* #4452 [fixed] - products are not visible on homepage if filter based on ar locale.

* #4447 [fixed] - Product URl key not accepting value for Chinese, Japnese, Arabic locale.

* #4440 [fixed] - getting exception when allow user role with marketing permission.

* #4435 [fixed] -  customer group condition doesn't match while sending newsletter email.

* #4434 [fixed] - missing date picker option in event filter #4434.

* #4433 [fixed] - getting exception when filter campaigns/email templates based on status.

* #4432 [fixed] - subscribed checkbox should be mark if customer is already subscribed.

* #4428 [fixed] - user roles permission option always save when trying to uncheck selected option.

* #4425 [fixed] - saved billing address details are not showing on onepage checkout.

* #4420 [fixed] -  Invoice PDF breaking.

* #4413 [fixed] - getting error when setup fresh bagisto from master.

* #4408 [fixed] - silder image not work.

* #4401 [fixed] - composer install --no-dev , gives error.

* #4399 [fixed] - Role-based security issue - User with no permission has acces to some admin pages/controls.

* #4392 [fixed] - Multiple cart rule is getting applied in case condition matches on applying only one coupon.

* #4386 [fixed] - Slow Category Query.

* #4372 [fixed] - The brand attribute should be deletable.

* #4369 [fixed] - Product filter [Shop By].

* #4335 [fixed] - Image search disable functionality is not working.

* #4325 [fixed] - address is not saving on onepage checkout.

* #4299 [fixed] - sort by newest first or oldest first doesn't work in category.

* #4287 [fixed] - Product locale not correctly selected since admin language select.

* #4277 [fixed] - subtotal is not updating when remove product from the minicart.

* #4248 [fixed] - PayPay scripts are being loaded even if inactive.

* #4234 [fixed] - default values are not working in system configuration.

* #4228 [fixed] - add a validation if user trying to upload greater size image in image search.

* #4212 [fixed] - Error uninstall developer dependencies going into production mode.

* #4192 [fixed] - db connection failed at first time when setup using bagisto:install.

* #4191 [fixed] - back date should be disabled in booking products.

* #4130 [fixed] - event ticket price field accepts negative values.

* #4124 [fixed] - Client Side validation issue in Special Price for ticket booking Product.

* #4119 [fixed] - Admin should not be able to create multiple cart rule using same coupon code.

* #4117 [fixed] - error in booking product while booking a slot.

* #4105 [fixed] - Issue with total due in cash on delivery.

* #4094 [fixed] - filter is not working properly in case product name contains any special character.

* #4085 [fixed] - Not able to fetch invoice by API.

* #4062 [fixed] - search icon in velocity theme is not clickable.

* #4061 [fixed] - variant image is not visible when item added in wishlist.

* #4030 [fixed] - featured or new label should not visible if product is not exist in default theme.

* #4024 [fixed] - customer group price discount is not applied when product price and discount amount is same.

* #3981 [fixed] - No validation message on creating addresses without required params.

* #3975 [fixed] - Filter is not working properly for status column in case product has not been name and other required details.

* #3964 [fixed] - pay with paypal smart button is missing in checkout.

* #3957 [fixed] - Configurable product parent image is visible in cart instead of variant image.

* #3954 [fixed] - Featured New Product Adding Special Price the 2 columns in mobile version is broke.

* #3940 [fixed] - bagisto:install defines APP_URL to localhost by default.

* #3932 [fixed] - When copying a product, replicating the image causes it to be volatile.

* #3886 [fixed] - Cart item not bound to the account when login with social login.

* #3879 [fixed] - Getting exception on uploading high size image in Invoice Slip Design logo.

* #3807 [fixed] - Can't locate path for migrations in bagisto/packages.

* #3785 [fixed] - Product filter is not working according to channel filter.

* #3638 [fixed] -  Promotion, combine "percentage" & "fixed amount to whole cart" cart rules get wrong disco * unt amount.

* #4394 [fixed] - maintenance mode status is not working in channel section.

* #4393 [fixed] - composer test in windows.

* #3374 [fixed] - Social Login Error.

## **v1.2.0 (8th of September 2020)** - *Release*

* [feature] - Config to add custom css and javascript (Eg. google anlytics)

* [feature] - Added some config for payment method additional information

* #3900 [fixed] - layout issue on cart page in ar (Default theme)

* #3894 [fixed] - Customer is not able to increase or decrease the qty of downloadable product from cart in default theme

* #3890 [fixed] - Add validation at admin end for bundle items qty

* #3889 [fixed] - default compare icon should be enabled in french also

* #3878 [fixed] - Search by name doesn't work when sending a request via API

* #3872 [fixed] - customer profile section having bug in mobile view for default theme

* #3871 [fixed] - downloadable sample link issue in RTL

* #3866 [fixed] - Compare icon is not coming in mobile view as well as layout issue on compare page in mobile view(default theme)

* #3862 [fixed] - Fix Payment Instruction view in checkout page

* #3861 [fixed] - Payment Instruction is not implemented for velocity theme

* #3860 [fixed] - Icon used for the customer note or copy item should not be same

* #3854 [fixed] - Fix reset password alert message

* #3852 [fixed] - Product copy functionality is not working properly for configurable products

* #3847 [fixed] - QTY not returned to inventory when canceling an order after shipping

* #3831 [fixed] - social icons are not visible in footer

* #3828 [fixed] - success alert is not visible while removing product from wishlist as guest

* #3822 [fixed] - Publish translations from Webkul\Admin

* #3819 [fixed] - Export Function not working in Firefox/Linux

* #3811 [fixed] - Velocity cms page url should be added by default

* #3804 [fixed] - uploading image on search is not resulting in any keywords

* #3802 [fixed] - Layout issue while adding linked products

* #3800 [fixed] - Header Content Count field should convert the value into 0 if given anything apart from valid data(numeric value)

* #3799 [fixed] - Featured Products and New Products should not display on front end if admin has set 0 in velocity meta data

* #3792 [fixed] - Recently viewed product is not working for Locales other than default.

* #3791 [fixed] - validation message showing for wrong field while editing user from admin end

* #3789 [fixed] - Filter conditions need to be change according to Boolean in newsletter subscription grid for subscribed column

* #3786 [fixed] - Default advertisement images should display in images section.

* #3784 [fixed] - Responsive issue on iPad

* #3783 [fixed] - Correct the product name alignment in compare list of default theme.

* #3782 [fixed] - Getting error on running php artisan migrate if db_prefix contains decimal value

* #3781 [fixed] - Getting exception on frontend after installation.

* #3772 [fixed] - Unable to add more than 4 products in related products

* #3643 [fixed] - Invoices should contain the company/store data

* #3002 [fixed] - Buggggggg iOS Safari images are stretched.

* #1257 [fixed] - Getting issue in install of v0.1.6

## **v1.2.0-BETA1 (18th of August 2020)** - *Release*

* [feature] - Customer group price for products implemented

* [feature] - Image search feature added with tensorflow.js

* [feature] - Migrated to Laravel 7

* [feature] - Search engine optimization with rich snippet

* [feature] - Blade file tracer

* [feature] - Search with Elastic and Algolia

* [feature] - Support for admin multi theme

* [feature] - One click upgrade

* [feature] - Social login (Facebook, Twitter, Google, Linkedin, Github)

* [feature] - Social share

* [feature] - Store configuration added

* [feature] - Feature to disable compare option

* [feature] - Store configuration added for product listing

* #3767 [fixed] - Header-nav on mobile view seem buggy on search item

* #3757 [fixed] - broken image on guest wishlist

* #3755 [fixed] - Webinstall - SMTP port is not set

* #3751 [fixed] - shipping tab is missing in order details for deleted customer

* #3747 [fixed] - velocity content header route throwing an exception in spanish locale

* #3745 [fixed] - getting exception in backend when having DB_PREFIX

* #3742 [fixed] - Invoice, ship and cancel buttons are missing in order placed by guest user

* #3740 [fixed] - translation of alert when remove compare item in default theme

* #3735 [fixed] - Make Velocity Meta Data section Channel wise.

* #3727 [fixed] - compare page layout issue for logged in user

* #3725 [fixed] - getting exception on comparison page of default theme

* #3723 [fixed] - getting exception when download uploaded file from backend

* #3720 [fixed] - Velocity theme option is missing in channel

* #3717 [fixed] - Layout issue in order grid at customer end

* #3704 [fixed] - No alert message while deleting customer with pending or processing order from admin end.

* #3702 [fixed] - On deleting customer their invoice and shipment records are getting disappeared.

* #3700 [fixed] - getting exception while creating refund of order placed by deleted customer

* #3693 [fixed] - There should not be any success message while trying to delete system attributes

* #3692 [fixed] - Channel filter is not working properly in product grid

* #3689 [fixed] - There should not be sale icon in shopping cart in velocity theme

* #3688 [fixed] - Select icons should be in right side in RTL on payment page in default theme

* #3678 [fixed] - Customer is able to access downloadable products even when invoice state is pending

* #3676 [fixed] - all cross selling products are not visible in cart page of velocity theme

* #3675 [fixed] - Address icon is overlapping on side bar menu in mobile view

* #3674 [fixed] - Bugs on category page for list mode

* #3657 [fixed] - Auth user can see all users info by id

* #3656 [fixed] - Product name gets blank each time we refresh the product page.

* #3649 [fixed] - product datagrid filter layout issue

* #3648 [fixed] - custom file type attribute is not visible in PDP

* #3642 [fixed] - getting exception when creating configurable product in case of DB_Prefix

* #3638 [fixed] - Promotion, combine "percentage" & "fixed amount to whole cart" cart rules get wrong discount amount

* #3637 [fixed] - No records founds text in downloadable product section of customer should display in centre, in mobile view.

* #3636 [fixed] - Correct the Ui of profile in mobile view,there is no difference in field name test and field data.

* #3631 [fixed] - Category slug should not accept values in capital letters while adding content for Header content

* #3629 [fixed] - Filter is not working properly in content list grid for content type column

* #3628 [fixed] - Correct the Success message after updating content in velocity meta data.

* #3622 [fixed] - channel filter is not working in cart rule

* #3621 [fixed] - Ui issue when applying filter in mobile view

* #3617 [fixed] - Add feature to set the category header content limit in velocity

* #3615 [fixed] - Getting exception while uploading favicon image if image is of high size

* #3611 [fixed] - The content of CMS page is cropped

* #3606 [fixed] - showing different number of star in velocity and default theme for same rating

* #3604 [fixed] - show percentage in place count number in review in velocity theme

* #3595 [fixed] - I would like like to change the admin route or url

* #3591 [fixed] - Getting exception while using layered navigation filters on category page

* #3580 [fixed] - Incorrect error message while adding bundle product in cart if no.of bundles contains more qty than available qty

* #3577 [fixed] - Customer is able to place order of more than available qty of any bundle option product

* #3575 [fixed] - Inactive child product shouldn't visible in grouped product

* #3574 [fixed] - One page Checkout loader hits on adding single digit in phone number

* #3570 [fixed] - Translation issue on uploading high size image

* #3564 [fixed] - getting exception if admin uploads higher size image in image swatch while editing/adding attribute

* #3562 [fixed] - getting exception when view category page in frontend

* #3561 [fixed] - Customer Revenue is not getting minus after refund

* #3558 [fixed] - Incorrect price showing for configurable product on front end

* #3554 [fixed] - Cart Rule Issue

* #3550 [fixed] - there should be tooltip text on mouse hover on compare icon on product

* #3548 [fixed] - filter is not working properly in attribute grid at admin end

* #3547 [fixed] - checkout country null issue

* #3546 [fixed] - Shipping charge is not getting calculated properly in case customer removes the product just before clicking on place order

* #3535 [fixed] - Sort By configuration from admin end is not working properly

* #3534 [fixed] - Add validation for products per page field otherwise if admin entered string value then customer gets exception on category page

* #3533 [fixed] - Products Per Page configuration is not working properly

* #3532 [fixed] - (Mobile view) getting product image issue when set grid as List type in category page

* #3531 [fixed] - In price filter, allow comma(,) for price in filter as in french locale float values used to be separated by , in stead of .

* #3530 [fixed] - mobile view sortBy functionality in category page is not working

* #3526 [fixed] - On changing current password admin/customer should get mail

* #3522 [fixed] - Admin is not getting mail when customer cancels order

* #3521 [fixed] - layout issue in order information in default theme at customer end

* #3520 [fixed] - Admin should get mail according to admin's default locale not customer's locale

* #3519 [fixed] - Customer is getting mails in Arabic for order information and rest mails in English for same order which was placed in ar locale.

* #3515 [fixed] - Order id is missing in mail which customer get when admin add any comment in order

* #3514 [fixed] - Use "has been" in place of "had been" in mail to warehouse

* #3508 [fixed] - Error on PL lang file

* #3507 [fixed] - locale filter is not working properly in product grid

* #3506 [fixed] - Uploaded image is not showing while editing Advertisement 4,3,2 Images for arabic locale

* #3505 [fixed] - No message on removing products or deleting all from compare list in default theme

* #3502 [fixed] - Side bar menu is getting removed while editing/adding customer address at admin end

* #3501 [fixed] - unable to download files while comparing products

* #3498 [fixed] - No.of items in compare should be displayed with compare tab in default theme

* #3496 [fixed] - showing values in compare list of those attribute which has been removed from attribute family

* #3495 [fixed] - image is not coming in compare list for any image type attribute

* #3494 [fixed] - compare feature is not working properly if admin creates a image type attribute and give attribute code and name "image"

* #3491 [fixed] - broken image for color image swatch type

* #3490 [fixed] - Back icon is not working in catalog and cart rule grid

* #3489 [fixed] - Sale icon is not showing even if catalog rule is applied for grouped and bundle type products

* #3488 [fixed] - color attribute default swatch value should be selected as dropdown swatch

* #3487 [fixed] - Velocity RTL product image zoom floats right - not showing

* #3486 [fixed] - Compare feature shows wrong attribute value

* #3485 [fixed] - layout issue on category page for filterable attributes

* #3484 [fixed] - missing product quick view icon on search page

* #3478 [fixed] - Getting exception on putting limit =0 in url

* #3472 [fixed] - layout issue in order and downloadable grid at customer end in mobile view

* #3469 [fixed] - Cannot remove a layered navigation attribute from product

* #3467 [fixed] - layout issue at shop end in ar

* #3465 [fixed] - When I update the folder name from bagisto to new name, the logo image doesn't appear, why?

* #3460 [fixed] - Add address option should come below in velocity theme

* #3458 [fixed] - not able to add (virtual,booking etc.) product to compare list by logged in user

* #3457 [fixed] - number indicator are hidden on wishlist or compare icon RTL format

* #3451 [fixed] - Change request regarding shipments

* #3447 [fixed] - By default social login should be enabled

* #3445 [fixed] - filter tag is out of box in search key term for long product name

* #3444 [fixed] - Layout issue on review page

* #3443 [fixed] - customer group price functionality is not working

* #3425 [fixed] - Impossible to connect, please check your Algolia Application Id.

* #3423 [fixed] - Cannot change account password

* #3422 [fixed] - getting exception when view order of deleted customer

* #3417 [fixed] - social login icons in RTL

* #3411 [fixed] - Sale level is displaying even if special price date has been already expired

* #3409 [fixed] - Wrong validation when remove variant(s) of configurable product

* #3400 [fixed] - fix UI for the compare page in default theme

* #3399 [fixed] - Remove from wishlist button is displaying as 1 when mouse not hover on product, this occur in all locale except English.

* #3396 [fixed] - getting error when admin view order placed by social customers

* #3395 [fixed] - default group should be set for the social login customer

* #3394 [fixed] - Not able to open menu in mobile view having locale arabic.

* #3393 [fixed] - Getting broken image link for locales in mobile view.

* #3392 [fixed] - Translation key is not added in order settings

* #3381 [fixed] - Customer city name does not allow hyphen

* #3380 [fixed] - Layout issues on mobile view in ar locale

* #3379 [fixed] - Getting error on migration command.

* #3377 [fixed] - error when click to twitter social login

* #3374 [fixed] - Social Login Error

* #3373 [fixed] - New Error migration Bagisto Install MySQL/MariaDB

* #3371 [fixed] - Easy bug: incorrect PL lang file

* #3369 [fixed] - getting exception when clicking on any social login icon

* #3365 [fixed] - On mobile responsive on ios sign up button is missing

* #3363 [fixed] - null value accepted in filter option at customer end

* #3360 [fixed] - filter and items per page is getting collapsed in ar

* #3358 [fixed] - New and sale icon lable on product issue in RTL

* #3357 [fixed] - Image search feature is not available in mobile view default theme

* #3356 [fixed] - search term removed from the search bar in default theme

* #3354 [fixed] - error when upload invalid image/file type in search

* #3341 [fixed] - filter option should be remove from the search page in mobile view

* #3340 [fixed] - mobile view not able to updated currency

* #3339 [fixed] - category display mode options are not working

* #3338 [fixed] - bundle option should be marked as mandatory if it's required

* #3335 [fixed] - New Label is missing in VelocityTheme

* #3331 [fixed] - layout issue while using filter at any grid(admin end) in ar locale

* #3330 [fixed] - Layout issue while adding configurable product to cart in velocity theme

* #3328 [fixed] - velocity logo and shop by category override

* #3323 [fixed] - text written in search page(for invalid search) should be properly aligned

* #3322 [fixed] - Title and url both are coming same on layered navigation page in velocity theme

* #3321 [fixed] - Comapre button should come between wishlist and cart in arabic also.

* #3320 [fixed] - Getting exception on frontend when opening a category in which brand is selected as filterable attribute.

* #3319 [fixed] - Issue in validation message while placing order of booking type product if customer did not fill select rent time

* #3317 [fixed] - Pagination layout should be implemented at customer end

* #3316 [fixed] - layout issues in checkout page while placing order of booking product

* #3315 [fixed] - compare option should be remove from customer profile options list if the compare is disable

* #3314 [fixed] - admin back to sign in link always redirects to same page after admin login

* #3313 [fixed] - In cart, in place of only qty, quantity and price both are coming in ar.

* #3311 [fixed] - Mobile Bug - Filters disappear when no products match filters

* #3310 [fixed] - Editing product title should not change URL if it has already been set

* #3309 [fixed] - 500 error when loading /search with "term" in query string

* #3307 [fixed] - Getting exception on changing locale when customer has opened order grid from his account

* #3304 [fixed] - Getting incorrect message on delete all from wishlist in case of guest user only

* #3303 [fixed] - Getting exception on changing locale when customer has opened downloadable products grid from his account

* #3301 [fixed] - fix search keys in search bar for analysed keywords in velocity

* #3298 [fixed] - Header content category always redirect to 404 error page

* #3297 [fixed] - getting exception when save booking product from edit page

* #3289 [fixed] - Main product is not showing in catalog grid if configurable product hasn't been created completely.

* #3286 [fixed] - fix calendar icon css at admin dashboard

* #3274 [fixed] - Installer Blank Page After Migration

* #3273 [fixed] - fix calendar icon present at dashboard in RTL

* #3272 [fixed] - getting exception when booking product type is not same as cart item for same product id

* #3270 [fixed] - fix icon design on catalog rule when select special price as condition

* #3265 [fixed] - recently view product heading is overlapped in RTL

* #3255 [fixed] - Appointment booking slot duration missing in UI for RTL

* #3254 [fixed] - exception on changing locale to Italian

* #3250 [fixed] - find product by image in search attempt to an error if app_url isn't define

* #3249 [fixed] - icons are overlapped in comparison page for RTL

* #3248 [fixed] - fix css for cancel icon on success alert RTL

* #3246 [fixed] - fix icon layout in edit booking product page for RTL

* #3243 [fixed] - Email settings are empty in backend

* #3241 [fixed] - login fields(email,passwords) are in the center when in RTL

* #3240 [fixed] - Payment methods in onepage checkout are not visible completely in RTL

* #3238 [fixed] - Trait 'Illuminate\Foundation\Auth\SendsPasswordResetEmails' not found

* #3237 [fixed] - Options of attribute not display as per its position

* #3236 [fixed] - selected category gets removed from the search in RTL

* #3235 [fixed] - need space b/w sign in & sign up box in mini login window for RTL

* #3234 [fixed] - UI Issue for cart, wishlist, compare icon number indicator in RTL

* #3232 [fixed] - homepage is showing 404 error page in both theme

* #3231 [fixed] - "error!options are missing alert" on home page shouldn't be shown

* #3222 [fixed] - UI issue in event ticket booking special price date field

* #3219 [fixed] - fix the date/time format in booking products

* #3218 [fixed] - virtual product not shipping step..

* #3215 [fixed] - when updating an attribute to 'use_in_flat', bagisto should update the product_flat table with the values of those products

* #3214 [fixed] - Getting exception on forgot password link.

* #3208 [fixed] - Customer group price functionality is not working.

* #3207 [fixed] - Issue in variant product of configurable, only one variant name display at a time and on refreshing it changes.

* #3205 [fixed] - Able to create the product without selecting required toggles button.

* #3204 [fixed] - Getting exception when changing currency from search page.

* #3203 [fixed] - Getting translation issue in price field of downloadable product.

* #3202 [fixed] - Getting exception in creating grouped product.

* #3199 [fixed] - Getting exception when click on product.

* #3197 [fixed] - Call to undefined function str_limit() when view product in velocity theme

* #3191 [fixed] - Bagisto v1.1.2 velocity responsive theme issue on iPhone and iPads

* #3190 [fixed] - Bagisto v1.1.2 velocity responsive theme issue on iPhone and iPads

* #3186 [fixed] - replace payment method text with an image on the checkout page

* #3184 [fixed] - Site showing blank page on 404

* #3183 [fixed] - ErrorException

* #3172 [fixed] - description or name is missing for comparable items if customer login

* #3171 [fixed] - fixed amount is applied on product for customer group price instead of apply in %

* #3164 [fixed] - getting exception when add/edit configurable product

* #3161 [fixed] - Trying to access array offset on value of type null

* #3160 [fixed] - Disabled products are not removed from bundles

* #3158 [fixed] - Column not found: 1054 Unknown column 'symbol' in 'field l

* #3153 [fixed] - Free Shipping and Flat Rate Shipping not desable

* #3150 [fixed] - Attribute not showing on the creating new configurable product page

* #3146 [fixed] - how to configure aws smtp server on bagisto

* #3144 [fixed] - error if selecting only one currency

* #3140 [fixed] - API for more than one locale !

* #3136 [fixed] - configurable product variant name gets removed from the catalog list

* #3135 [fixed] - How can I cad comment box in checkout form.

* #3131 [fixed] - Velocity theme responsiveness issue after changing the language to Arabic RTL

* #3120 [fixed] - admin panel multi locale

* #3118 [fixed] - Home page doesn't display categories and language bar doesn't work.

* #3115 [fixed] - minify the velocity.js for gtmetrix

* #3113 [fixed] - catalog storefront configuration for per product page is not working

* #3097 [fixed] - getting console error when remove cart item

* #3096 [fixed] - error when add product in compare list from the search product page

* #3095 [fixed] - pending orders detail page is blank when viewing in arabic locale

* #3090 [fixed] - error mysql8

* #3089 [fixed] - not getting price after changing configurable options

* #3087 [fixed] - after installation, first product registration does not open detailed page, I only opened from the second product

* #3079 [fixed] - Tracking Number in My Account

* #3077 [fixed] - How to change validation messages to spanish not working

* #3076 [fixed] - checkout disable when add new shipping address

* #3073 [fixed] - HTML entities are not being decoded when editing attribute options

* #3070 [fixed] - Edit Attribute -> Add Option or Swatch Item Error 404 for Indonesia(id) country code

* #3068 [fixed] - Inactive inventory source are get select in channel and products

* #3067 [fixed] - PHP Notice: date_default_timezone_set(): Timezone ID 'Asia/JakartaAsia/Kolkata' is invalid

* #3061 [fixed] - CORS errors

* #3054 [fixed] - customer is getting exception while cancel order

* #3053 [fixed] - [Velocity] Checkout: Shipping/Billing Address Name, email does not get updated

* #3051 [fixed] - error while migrate bagisto manually from console command

* #3050 [fixed] - Can't override models

* #3048 [fixed] - "nwidart/laravel-modules": "^3.2", is the wrong version for laravel 6.*

* #3047 [fixed] - The qty of configurable product is 0 when merging cart

* #3044 [fixed] - Getting exception when click on view shopping cart if adding group product in cart that contains variants of configurable product.

* #3040 [fixed] - Api logout not working..

* #3038 [fixed] - Trying to get property 'code' of non-object

* #3037 [fixed] - error mysql 8.0.20 bagisto v 1.1.2

* #3036 [fixed] - Shipping address options not shown

* #3035 [fixed] - Please update pwa for bagisto

* #3032 [fixed] - [Critical] Onecheckout preventing to continue to shipping method after selecting address

* #3030 [fixed] - Api for coupons..

* #3029 [fixed] - velocity theme not fully responsive

* #3026 [fixed] - Date validation error when editing Booking Products

* #3025 [fixed] - save address return error 500

* #3024 [fixed] - Blank order comment shouldn't added

* #3022 [fixed] - storage/ should not be included in .gitignore

* #3021 [fixed] - Deactivating the last category of level1 renders only level2 etc. from deactivated category

* #3020 [fixed] - Images can not add on velocity theme

* #3018 [fixed] - Icons are not showing on imac

* #3014 [fixed] - Support for Responsive Admin Panel

* #3011 [fixed] - The product is in cart or not

* #3009 [fixed] - Featured product slider and new product slider is not working

* #3005 [fixed] - One page checkout creating new address in profile every time.

* #3004 [fixed] - Category Deactivation not working

* #3001 [fixed] - Getting Error Exception when view order details

* #3000 [fixed] - arabic product in home page Not lined up in one format

* #2997 [fixed] - Category show sidebar

* #2996 [fixed] - Incomplete products JSON when type is grouped or bundled

* #2995 [fixed] - filter is not showing when search product from search bar

* #2994 [fixed] - Shipping method not getting updated after changing the zip code.

* #2991 [fixed] - Filters not showing in small devices

* #2990 [fixed] - Facebook Pixel integration for Laravel

* #2987 [fixed] - mult address

* #2985 [fixed] - Product category is not saving

* #2981 [fixed] - When paying with Paypal the user can change the amounts of the products

* #2974 [fixed] - Thumbnails are not generating on mobile ifproduct has more than 4 photos

* #2973 [fixed] - force the execution of the shipping methods trigger

* #2972 [fixed] - can add to homescreen on mobile device in velocity theme

* #2971 [fixed] - Need to add the possibility to translate velocity metadata

* #2969 [fixed] - Cancel icon is not visible in velocity theme for customer order detail

* #2964 [fixed] - Exception when buying non stockable item via API

* #2950 [fixed] - multiple error message on installer

* #2949 [fixed] - failed to migrate with new database using installer

* #2942 [fixed] - Randomize New and Featured Products

* #2937 [fixed] - Checkout old theme

* #2936 [fixed] - change the admin route for another

* #2931 [fixed] - Customer pays order in PayPal but there is no record in bagisto

* #2893 [fixed] - When creating a shipment, display items invoiced

* #2889 [fixed] - timezone drop down field is not visible in web installer

* #2888 [fixed] - Always Default locale should be selected when add new product for each channels

* #2886 [fixed] - Configuration option for Compare

* #2874 [fixed] - Order, payment process and payment metadata

* #2868 [fixed] - Cart (customer) address not persisted during the checkout

* #2863 [fixed] - Search Product Name and Description

* #2844 [fixed] - showing product image of each color in configurable product

* #2805 [fixed] - the sku should be all in caps

* #2804 [fixed] - There should be order review section instead of complete section on checkout page

* #2800 [fixed] - Add the ability to change Attribute Family for Products

* #2795 [fixed] - Cart error merging if you authenticate having items with low stock

* #2789 [fixed] - Product channel and locale dropdowns in the admin dashboard not working

* #2766 [fixed] - Needs User friendly UI for the event booking in product page

* #2762 [fixed] - Seeder: SQL Error: Duplicate entry for '1' key 'PRIMARY'

* #2725 [fixed] - Variants should not be created if cofigurable product created failed

* #2720 [fixed] - Error during migration on php artisan migrate

* #2702 [fixed] - Getting broken image for products and category.

* #2590 [fixed] - hi everyone i'm wondering if customers after register could give a referral code ??

* #2415 [fixed] - Add TO CART button should replace by “BOOk NOW” button for booking product.

* #2159 [fixed] - Taking more time to load product details in shopping cart.

* #2141 [fixed] - SQLSTATE[42S02]: Base table or view not found: 1146 Table '[DB_PREFIX].category_translations' doesn't exist

* #2060 [fixed] - auto generate coupon accordion not getting hidden while selecting no specific coupons

* #2009 [fixed] - Using AWS S3 for storage

* #1981 [fixed] - If customer update his address at time of checkout and save this address then address doesn't get save and also on checkout page old address displays.

* #1656 [fixed] - Validation error in Phone Field while Adding Address

* #1522 [fixed] - Quick Links broken

* #1370 [fixed] - install fails at the last step

* #1362 [fixed] - Site logo and Category Image are broken

* #1258 [fixed] - If payment is done through paypal then invoice should generate automatically and status of Order should be processing.

* #1246 [fixed] - Implement a feature to import product through csv file.

* #985 [fixed] - Dynamically insert products

* #838 [fixed] - Packages as composer dependency

* #824 [fixed] - Framework is not supporting I.E 11 browser.

* #343 [fixed] - Translation strings are missing from awful amount of controllers when returning responses with flash. And optimise translation strings for faster static translations.

## **v1.1.2 (28th of April 2020)** - *Release*

* [feature] - Now customer can cancel order.

* [feature] - Auto and manual currency exchange rates update feature added.

* #2954 [fixed] - The merging cart function does not work when already added all items of product into customer cart

* #2945 [fixed] - API product detail return empty array

* #2943 [fixed] - Scroll images is not working

* #2940 [fixed] - creating categories have error

* #2939 [fixed] - get product description for API without html tags

* #2938 [fixed] - Extend Model Class

* #2925 [fixed] - exception for php version 7.4

* #2919 [fixed] - Header Content not working on other languages

* #2915 [fixed] - filters are missing on mobile view.

* #2914 [fixed] - Filter not showing on mobile, also sorting not working on mobile

* #2908 [fixed] - A class is missing from the Velocity ProductRepositiry file

* #2901 [fixed] - Error when creating a category

* #2900 [fixed] - getting different variant of a configurable product in front end

* #2899 [fixed] - showing the configured products as radio button

* #2898 [fixed] - error when viewing a category and then wanting to change the language of the page in mobile view

* #2897 [fixed] - Inventory status field should be passed through validation for boolean in its backend controller.

* #2896 [fixed] - There are two fields with having same value of name attribute one is hidden and other is of its desired type - is this redundant code or its solving any purpose?

* #2895 [fixed] - The type hint of view in this blade file is 'address' - there is no tag in any provider which loads view with this type hint.

* #2890 [fixed] - cart rule condition (price in cart) always set to equal or less than when select greater than/less than

* #2884 [fixed] - Undefined Index slot: when add to cart rental booking

* #2875 [fixed] - Deleting brands that have been assigned to products causes checkout error

* #2871 [fixed] - Refund throws "Undefined index: shipping" error

* #2869 [fixed] - Updating "Velocity meta data" throws QueryException

* #2826 [fixed] - Not able to view cart icon

* #2793 [fixed] - Stock Check Incorrect for Configurable Items

* #2752 [fixed] - Error when you create or update a new catalog under root

* #2691 [fixed] - Shipping and Payment methods automatically selected on Checkout oage

* #2453 [fixed] - Velocity theme is not loading on fresh instance

* #797 [fixed] - Add new module

## **v1.1.1 (14th of April 2020)** - *Release*

* #2876 [fixed] - Place order is disable at checkout when select shipping address

* #2871 [fixed] - Refund throws "Undefined index: shipping" error

* #2866 [fixed] - ayout issue when customer save addresses form

* #2865 [fixed] - Save order taking so long time 30s

* #2856 [fixed] - Issue with Sort by functionality, when open any category it by defaults show Newest First but after changing sort by when again select newest first it shows different product.

* #2851 [fixed] - Fix date picker icon layout at dashboard

* #2850 [fixed] - admin crash on save configration

* #2849 [fixed] - Can not add my stylesheet to Velocity theme

* #2847 [fixed] - Class 'Faker\Factory' not found

* #2846 [fixed] - does not show next step

* #2845 [fixed] - Implement custom RegistrationController

* #2840 [fixed] - Velocity theme is not available on fresh install

* #2837 [fixed] - subscription bar content source code is not visible in text editor

* #2834 [fixed] - Layout issue in compare page in pt_BR locale

* #2832 [fixed] - Illegal mix of collations

* #2829 [fixed] - changing home page content in velocity and npm

* #2828 [fixed] - currency change error on velocity theme

* #2827 [fixed] - default local not changing in storefront in velocity theme

* #2825 [fixed] - PHP Notice:

* #2821 [fixed] - Address Line is Null in Emails

* #2818 [fixed] - Not able to view menu in velocity theme on storefront

* #2814 [fixed] - variant product's name aren't update when select their options in Front

* #2813 [fixed] - Ui issue if there is only one product in compare page.

* #2812 [fixed] - getting timezone error while setup

* #2811 [fixed] - how to change checkout proccess

* #2810 [fixed] - UI issue on compare similar item page.

* #2808 [fixed] - Correct the spelling on registration page.

* #2807 [fixed] - Illegal mix of collations

* #2801 [fixed] - Address with more than 2 lines is not added correctly to the cart_address table

* #2796 [fixed] - Try to create category in windows 10 getting exception

* #2794 [fixed] - When allow backorder is enabled, display a message available for order rather than in stock.

* #2793 [fixed] - Stock Check Incorrect for Configurable Items

* #2792 [fixed] - Weight Validation Inconsistencies

* #2790 [fixed] - Minicart disable when use new languages only velocity theme

* #2788 [fixed] - guest_checkout is missing from edit product

* #2786 [fixed] - Getting error message on adding product to compare product from search page.

* #2785 [fixed] - missing address details in checkout page

* #2784 [fixed] - One booking for many days slot time issue

* #2781 [fixed] - Mobile menu is not showing correct sub-menu

* #2780 [fixed] - Sidebar layout issue.

* #2779 [fixed] - Issue on checkout page, email should ask first as in default theme.

* #2778 [fixed] - Issue in customer profile dropdown.

* #2776 [fixed] - compare option in side bar menu at customer panel should be available

* #2775 [fixed] - compare icon is missing in each product for default theme

* #2774 [fixed] - How to add new icon in bagisto admin panel?

* #2769 [fixed] - Can't delete Exchange Rates data

* #2768 [fixed] - Getting exception in cart when remove one ticket from event booking from backend

* #2765 [fixed] - Email settings configuration values are not write in .env file

* #2764 [fixed] - fix UI when select back_date of booking product,the calendar icon is set on another place

* #2763 [fixed] - error to add rental booking into cart

* #2752 [fixed] - Error when you create or update a new catalog under root

* #2726 [fixed] - is shop.js the vue framework ??

* #2713 [fixed] - fix the invoice header in pdf

* #2708 [fixed] - Able to create booking product from back date.

* #2706 [fixed] - Getting exception on editing category for pt_BR locale in php 7.4

* #2691 [fixed] - Shipping and Payment methods automatically selected on Checkout oage

* #2684 [fixed] - API checkout/cart returns null for guest user

* #2619 [fixed] - Issue when category slug & product slug are same

* #2558 [fixed] - Sliders Text should be translatable

* #2543 [fixed] - Sliders Text should be translatable

* #2354 [fixed] - possible integrate this payment

* #2329 [fixed] - Getting exception on frontend after updating meta data.

* #2152 [fixed] - Product images are not showing

* #826 [fixed] - Impossible to create the root directory "".

* #797 [fixed] - Add new module

## **v1.1.0 (24th of March 2020)** - *Release*

* [feature] Added new booking type product.

* [feature] Impletment compare product feature.

* [feature] Impletment compare product feature.

* #2732 [fixed] - missing product's quick view in category page

* #2726 [fixed] - is shop.js the vue framework ??

* #2724 [fixed] - table bookings quantity should update in existing booking added in cart for same slot/date

* #2723 [fixed] - Compare product icon on header showing counts of compare product but there are no product in compare list.

* #2722 [fixed] - warning showing when update event booking cart quantity from the product page

* #2717 [fixed] - Getting error message on adding rental product in cart if rental booking is not available for that day.

* #2716 [fixed] - After saving the default booking time product selected time for date range changes to 00:00:00 ,because of which not able to book appointment on frontend.

* #2715 [fixed] - Error message should throw if "To" time is less than "From".

* #2707 [fixed] - Getting exception when generate invoice in appointment booking

* #2704 [fixed] - product's assigned category can't be removed

* #2693 [fixed] - Booking product page - add to cart button js error

* #2678 [fixed] - UI issue in rental booking product page

* #2677 [fixed] - error on cart when rental booking update from backend

* #2674 [fixed] - Rental booking added to cart without selecting date in velocity

* #2672 [fixed] - wrong price calculated in cart for rental booking

* #2671 [fixed] - Error on moving booking product to wishlist

* #2670 [fixed] - Booking product should be removed from the cart when selected slot time expired

* #2669 [fixed] - Browser compatibility issue

* #2667 [fixed] - By default wishlist option is selected in cart

* #2666 [fixed] - fix the UI for booking product in cart page

* #2661 [fixed] - Charged_per drop down value is not updating for table booking

* #2660 [fixed] - guest capacity value is not saved in table booking

* #2658 [fixed] - slot, duration, break time are not saved for appointment booking

* #2654 [fixed] - warning should be removed once slot field is selected

* #2650 [fixed] - remove slot duration from the booking product page

* #2649 [fixed] - Incorrect slot time for one booking many days in product page

* #2646 [fixed] - error missing wishlist or compare icon on mobile view

* #2645 [fixed] - Error on adding product to cart

* #2644 [fixed] - Add an option to set encryption to none during installation

* #2643 [fixed] - Getting exception when add appointment booking

* #2641 [fixed] - Issue on wishlist page for guest user

* #2640 [fixed] - product moved to cart still showing in wishlist

* #2639 [fixed] - category slug field should show warning if saved blank header content

* #2638 [fixed] - customer status is not translated in customer list

* #2637 [fixed] - blank admin page if username contains whitespaces in email configuration

* #2636 [fixed] - Error alert when add to cart a simple product from the home page

* #2635 [fixed] - Default Booking details remove from edit page for many booking of one day

* #2634 [fixed] - console error when select slots in default booking

* #2630 [fixed] - Error exception when add booking product

* #2626 [fixed] - Tax rates zipcode is still required when enable zip range is disabled

* #2621 [fixed] - i create a site and it is up kind of noting works

* #2619 [fixed] - Issue when category slug & product slug are same

* #2616 [fixed] - Tiny Bug on Admin Pages

* #2614 [fixed] - table booking slot time is expired still exist in cart

* #2613 [fixed] - Propaganistas/Laravel-Intl is abandoned

* #2612 [fixed] - available slots are not showing for current date even if slot time is not expired

* #2611 [fixed] - installer error

* #2610 [fixed] - some of the attribute values aren't visible in comparison page

* #2609 [fixed] - product removed from comparison page when update product by name

* #2608 [fixed] - Getting exception on creating category.

* #2607 [fixed] - Getting exception on editing category for pt_BR locale in php 7.4

* #2606 [fixed] - custom attributes are not Visible on Product View Page on Front-end

* #2605 [fixed] - Attribute is comparable (yes/no) option is missing when add new attribute

* #2604 [fixed] - Not able to make product as comparable from the category page as logged In user

* #2602 [fixed] - Catalog default image height should be equal to the original image in Velocity

* #2601 [fixed] - all comparable product remove from list only when single product remove

* #2599 [fixed] - login required when add compare product from the category page

* #2597 [fixed] - Not getting email for "Send Inventory Source Notification E-mail".

* #2596 [fixed] - Allow Email Verification field is given twice, once in Configure->Customers->Setting and in Configure->Admin->Email.Currently if field is enable from any one grid and disable from other grid, then its not working.

* #2595 [fixed] - Category image size issue in velocity theme.

* #2594 [fixed] - After refund quantity of product increases.

* #2593 [fixed] - Cannot read property 'disabled' of undefined" on filter price

* #2592 [fixed] - No menu for the logged in user when clicking over comparison

* #2589 [fixed] - Getting exception on editing header content on php 7.4.

* #2587 [fixed] - Getting some warning during installation.

* #2586 [fixed] - APP_TIMEZONE and APP_LOCALE values should be available in env file.

* #2585 [fixed] - Product name , description and short description gets removed on editing the product.

* #2584 [fixed] - Not getting root category name, in categories.

* #2583 [fixed] - Display 3D product preivew image

* #2581 [fixed] - admin/configuration/general/design

* #2580 [fixed] - error recently viewed products in mobile

* #2579 [fixed] - error menu mobile

* #2578 [fixed] - Impossible to create the root directory

* #2577 [fixed] - GUI installer stuck at Migration & Seed

* #2576 [fixed] - Compare icon is missing for new products

* #2575 [fixed] - compare feature is not working from the product page for logged In customer

* #2574 [fixed] - Quick view popup should be closed when click add to compare

* #2573 [fixed] - Add to wishlist icon is missing with each product in comparison page

* #2572 [fixed] - custom attribute values are not show in comparison product

* #2571 [fixed] - compare icon should classify the total compare product added in the comparison page

* #2568 [fixed] - Getting exception when update to default theme from the comparison page

* #2567 [fixed] - Error 404 found when click on compare product image

* #2563 [fixed] - error add in cart

* #2562 [fixed] - error catalog/categories/create

* #2556 [fixed] - Logo and favicon broken

* #2552 [fixed] - error mysql 8

* #2549 [fixed] - Invoices aren't legally valid.

* #2541 [fixed] - Showing product's price with the price including tax

* #2525 [fixed] - Add more settings to the installers

* #2517 [fixed] - Product description text gets selected if click on drop down icon on product page

* #2468 [fixed] - Guest user is able to checkout if guest checkout is disabled.

* #2284 [fixed] - Layout issue in pt_BR locale.

## **v1.0.0 (24th of February 2020)** - *Release*

* #2540 [fixed] - add to cart and whitelist button overlap.

* #2538 [fixed] - unable to place order for virtual & downloadable product.

* #2533 [fixed] - Shipment email notification is not sending to customer.

* #2529 [fixed] - [Webkul\Admin] Customer firstname & lastname are using wrong translations

* #2527 [fixed] - Order datagrid is using static text.

* #2526 [fixed] - Velocity backend route is not accessible in arabic locale.

* #2519 [fixed] - filter price attribute throwing an exception.

* #2500 [fixed] - Database reset fails.

* #2494 [fixed] - Product total inventory for all locale is showing wrong.

* #2491 [fixed] - Exception on Create/Edit bundle product.

* #2490 [fixed] - missing zip code & country field in checkout page.

* #2488 [fixed] - ErrorException When Editing product in different language.

* #2480 [fixed] - Exception is thrown by mini cart when catalog rule is applied on configurable product.

* #2479 [fixed] - showing total review in recent view product list.

* #2469 [fixed] - Displaying wrong amount for bundle product in cart.

* #2468 [fixed] - Guest user is able to checkout if guest checkout is disabled.

* #2463 [fixed] - Tax rate is not update on same product.

* #2459 [fixed] - shipping address field warning for guest customer not translated.

* #2458 [fixed] - Payment method is not updating on checkout page.

* #2451 [fixed] - Invoice totals don't tally when using non-base currency.

* #2449 [fixed] - error clicking empty cart.

* #2440 [fixed] - Advertisement Three Images is not working.

* #2439 [fixed] - can't process for further checkout steps until all the address line filled.

* #2438 [fixed] - Add hyphen - with cart discount amount.

* #2436 [fixed] - error velocity/meta-data.

* #2435 [fixed] - error composer install --no-dev.

* #2424 [fixed] - Exception on frontend when default currency is not selected in currencies.

* #2417 [fixed] - Inactive payment method or shipping method are showing in velocity footer content.

* #2410 [fixed] - error button update cart.

* #2403 [fixed] - Ratings icons show in category product view list for 0 rating.

* #2400 [fixed] - Whole product price should be in bold in bundle product.

* #2399 [fixed] - Layout issue in bundle product.

* #2398 [fixed] - Mark all mandatory field in customer's billing address form.

* #2397 [fixed] - Company Name field is not available in Billing Address form in velocity theme.

* #2395 [fixed] - Can not add grouped product in cart more than one time, getting error message.

* #2393 [fixed] - Getting exception on adding grouped product to cart.

* #2391 [fixed] - toogle footer configuration is always true in velocity.

* #2390 [fixed] - Add hyphen in Orders->Information section of customer.

* #2388 [fixed] - order placed with blank billing address.

* #2386 [fixed] - bundle product details in cart page should contains item details.

* #2384 [fixed] - Vat id validation rule was changed, since then test action has failed.

* #2382 [fixed] - If customer use shipping address other than billing address then also its showing the billing address in shipping address section.

* #2381 [fixed] - Fix UI for linked product (related/upsell/cross sell).

* #2378 [fixed] - Exception when adding velocity content page list.

* #2377 [fixed] - Getting exception on creating a new category under any other category.

## **v1.0.0-BETA1 (5th of February 2020)** - *Release*

* [feature] Updated to laravel version 6.

* [feature] Added four new product types - Group, Bundle, Downloadable and Virtual.

* [feature] Provided new theme (Velocity).

* #2371 [fixed] - Getting exception on updating Category.

* #2366 [fixed] - Not able to add logo for category, after saving the category logo gets removed.

* #2362 [fixed] - Page Link Target of header content always save with self option.

* #2357 [fixed] - Broken image link for locale logo.

* #2355 [fixed] - UI issue when update product policy

* #2346 [fixed] - Exception when search product based on selected category from search bar

* #2341 [fixed] - wish listed items should be labeled by move to cart instead of add to cart if product already added in cart

* #2340 [fixed] - Correct the success message on deleting content.

* #2339 [fixed] - Selected content type is not showing in Content Pages List

* #2337 [fixed] - Not getting category logo for 3rd level category.

* #2336 [fixed] - Issue with multi level category.

* #2335 [fixed] - Success alert should be shown while adding product into cart

* #2330 [fixed] - different route found for customer profile edit page for velocity theme

* #2328 [fixed] - Only first three viewed product display in recently viewed section, when customer view 4th product it doesn't get updated.

* #2325 [fixed] - Left arrow should be out of the image area.

* #2320 [fixed] - UI issue in sort by functionality.

* #2319 [fixed] - UI issue when customer redirect to reset password page through received email.

* #2318 [fixed] - Slider content is not showing properly on slider in velocity theme.

* #2317 [fixed] - Multiple pop-up opens at a time if product is added in cart and customer click on Welcome Guest to sign-up or login.

* #2300 [fixed] - Alignment issue on Billing Information page if user enter an email that already exist.

* #2299 [fixed] - Vat Id field is not given is customer address form.

* #2297 [fixed] - Always showing 0 review for product in recently viewed product even if multiple reviews are given to that product.

* #2295 [fixed] - Admin not able to add address for customer if he add data in Vat id field.

* #2293 [fixed] - On mouse hover, remove filter should be display as clickable.

* #2288 [fixed] - Getting exception on mass delete of review from admin end.

* #2285 [fixed] - Layout issue if category length is large.

* #2282 [fixed] - Not getting any validation error message if current date of birth is selected in customer profile.

* #2281 [fixed] - In minicart, whole minicart container is showing clickable but only image section gets clicked to redirect to product page.

* #2280 [fixed] - Getting small checkbox on refreshing the product page.

* #2279 [fixed] - Sort By functionality is not working in velocity theme.

* #2275 [fixed] - Not able to place order for virtual product.

* #2274 [fixed] - Not able to proceed for checkout after checkout/onepage in case of downloadable product.

* #2273 [fixed] - Not getting password reset email for velocity theme

* #2271 [fixed] - When clicking on dropdown icon of all categories, category list didn't get open.

* #2265 [fixed] - Migrate issues with Velocity

* #2264 [fixed] - Getting internal server error when place an order

* #2263 [fixed] - Fix issue at review page

* #2262 [fixed] - Issue with multiple images of same product

* #2261 [fixed] - Not getting option to delete review if customer has reviewed only single product.

* #2260 [fixed] - Not getting the header content.

* #2259 [fixed] - Not getting category image on category page in velocity theme.

* #2258 [fixed] - Need space between highlighted text.

* #2257 [fixed] - User profile drop down option should be highlighted on mouse hover

* #2256 [fixed] - close previous popup if clicks on another item

* #2255 [fixed] - Theme page search bar passing string value

* #2254 [fixed] - Fix layout for remove button in cart page for guest customer

* #2253 [fixed] - Customer is not able to update his/her profile in velocity theme.

* #2252 [fixed] - Customer is not able to save his/her address in velocity theme.

* #2251 [fixed] - configurable product options could not get select

* #2248 [fixed] - Provide an option to remove filter in velocity theme.

* #2246 [fixed] - slider disable functionality is not working

* #2245 [fixed] - slider content is not showing in velocity theme

* #2243 [fixed] - Remove Image's Height [in Pixel], Image's Width [in Pixel] ,Image Alignment and Number of Subcategory from Configure->velocity theme.

* #2242 [fixed] - Velocity Header content status should be enabled by default

* #2241 [fixed] - Getting exception when save a category with category logo

* #2239 [fixed] - User should not be able to create multiple channel with same hostname.

* #2237 [fixed] - Error when trying to login with app.php locale set to ja

* #2227 [fixed] - Grand total column is not visible in invoice pdf, also getting incorrect currency symbol for grand total.

* #2226 [fixed] - Wrong price of product in case of multiple exchange rates.

* #2225 [fixed] - Not able to export products according to locale.

* #2207 [fixed] - Unable to delete Category.

* #2204 [fixed] - category tree view doesn't visible in catalog rule condition

* #2203 [fixed] - saved categories are not checked in condition of catalog/cart rule

* #2202 [fixed] - catalog rule is not applied on product if product's special price date expired

* #2198 [fixed] - Remove vat id column from customer address list

* #2196 [fixed] - No data is visible in state field, issue exist at all section where state field is used.

* #2192 [fixed] - For all grid of sales section when you export data in csv file order id heading is mentioned as increment id.

* #2190 [fixed] - sku should be shown in product list if new product created

* #2186 [fixed] - Ui issue in cart for pt_BR locale. Quantity is not visible properly.

* #2185 [fixed] - Issue with configurable product in case of multi-locale. Variation option are not visible.

* #2183 [fixed] - Add toolkit for add address.

* #2182 [fixed] - missing option in Customer's Gender at admin end

* #2181 [fixed] - Getting exception when creating/editing customer address from Admin end.

* #2177 [fixed] - Category image can be add from anywhere

* #2176 [fixed] - product price section is not getting highlighted if the warning exists

* #2173 [fixed] - While creating locales value in direction dropdown is in small letters, but when you edit any locale it display in caps.

* #2168 [fixed] - locale direction drop down always select ltr.

* #2167 [fixed] - Translation issue in Payment description field.

* #2165 [fixed] - Incorrect error message for password field of email configuration.

* #2164 [fixed] - Redirect to incorrect url when click on finish button after installing through installer.

* #2162 [fixed] - product's special price should not greater than price

* #2149 [fixed] - Ui issue when installing through installer.Getting issue on all steps.

* #2147 [fixed] - Sort order of bundle product doesn't work.

* #2146 [fixed] - Getting exception on creating bundle product without any option.

* #2145 [fixed] - Emails don't work on registration.

* #2143 [fixed] - Attributes filterable checkbox - those who do not know will think that a bug!

* #2139 [fixed] - Logic error in exchange rate calculation

* #2132 [fixed] - Price range slider not displaying.

* #2128 [fixed] - Click on add attribute, error is thrown.

* #2124 [fixed] - Able to make all product as default while creating bundle product in select type option.

* #2120 [fixed] - Not able to add new user as while creating user password its giving error confirm password doesn't match.

* #2119 [fixed] - confirm password is not matching even if admin is entering similar password in password and confirm password.

* #2118 [fixed] - Installation issue, getting exception on migrate command.

* #2114 [fixed] - getting exception while recovering admin password in case admin did not enter the details in env.

* #2089 [fixed] - Info missing on printing invoice at customer and admin end.

* #2088 [fixed] - Getting exception on customer login.

* #2087 [fixed] - Getting exception while adding configurable/bundle/grouped/Downloadable Type product to cart.

* #2075 [fixed] - Getting exception if trying to select any parent category of root.

* #2074 [fixed] - Getting exception while creating bundle type product.

* #2071 [fixed] - Customer is not getting forget password email.

* #2066 [fixed] - Exception while writing product review.

* #2058 [fixed] - Not getting any validation message if entered admin credentials are wrong.

* #2054 [fixed] -Automatically 1st item of bundle is getting selected as a default after saving product.

* #2051 [fixed] - Forgot password not working due to recent changes in mail keys.

* #2045 [fixed] - Login option is not coming while checkout with existing customer mail id.

* #2033 [fixed] - API route for products throws error

* #2012 [fixed] - Getting exception when clicking on view all under review section at product page.

* #2001 [fixed] - php artisan route:list throws error.

* #1998 [fixed] - Showing product sale amount as zero when creating a product, and a existing catalog rule apply on it.

* #1997 [fixed] - Getting exception on adding attribute or creating product in bagisto on php version 7.4 .

* #1994 [fixed] - Tax rate should only depend on zip code, state field should not be mandatory.

* #1988 [fixed] - Country and City Names in Create Address is not coming based on Locale

* #1987 [fixed] - MySQL query very slow if products in category is around 3000

* #1986 [fixed] - Subscribe to newsletter does not work.

* #1983 [fixed] - Getting exception on deleting admin logo.

* #1980 [fixed] - UI issue in cart on changing locale.

* #1979 [fixed] - Wrong calculation at customer as well as at admin end in due amount and grandtotal.

* #1978 [fixed] - Getting exception if changing the locale from cart page, if translation is not written for that product.

* #1977 [fixed] - On editing the product, selected category for that product is not checked.

* #1976 [fixed] - Default attribute set should be selected in root category.

* #1971 [fixed] - Filter is not working properly for id column in autogenerated coupon codes in cart rule.
